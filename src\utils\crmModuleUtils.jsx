import {
  DashboardOutlined,
  ThunderboltOutlined,
  ContainerOutlined,
  ContactsOutlined,
  TrophyOutlined,
  BuildOutlined,
  ScheduleOutlined,
  GlobalOutlined,
  StarOutlined,
  FileTextOutlined,
  BarChartOutlined,
  SettingOutlined,
  ProjectOutlined,
  SolutionOutlined,
  CalendarOutlined,
} from "@ant-design/icons";
import crmRoutes from "../routes/crm-routes";

// Module mapping configuration
export const MODULE_CONFIG = {
  dashboard: {
    title: "Tableau de bord",
    icon: <DashboardOutlined />,
    description: "Vue d'ensemble et statistiques principales"
  },
  activities: {
    title: "Gestion des activités",
    icon: <ThunderboltOutlined />,
    description: "Types, plans et statuts des activités"
  },
  leads: {
    title: "Génération des leads",
    icon: <ContainerOutlined />,
    description: "Gestion et suivi des prospects"
  },
  contacts: {
    title: "Gestion des contacts",
    icon: <ContactsOutlined />,
    description: "Annuaire et gestion des contacts"
  },
  opportunities: {
    title: "Gestion des opportunités",
    icon: <TrophyOutlined />,
    description: "Pipelines et suivi des ventes"
  },
  companies: {
    title: "Gestion des entreprises",
    icon: <BuildOutlined />,
    description: "Entreprises et interactions"
  },
  events: {
    title: "Séminaires et évènements",
    icon: <ScheduleOutlined />,
    description: "Organisation et suivi des événements"
  },
  geography: {
    title: "Gestion géographique",
    icon: <GlobalOutlined />,
    description: "Gouvernorats, délégations et codes postaux"
  },
  diaspora: {
    title: "Gestion de la diaspora",
    icon: <StarOutlined />,
    description: "Profils et interactions diaspora"
  },
  documents: {
    title: "Gestion documentaire",
    icon: <FileTextOutlined />,
    description: "Bibliothèque et modèles d'emails"
  },
  reports: {
    title: "Rapports et statistiques",
    icon: <BarChartOutlined />,
    description: "Analyses et tableaux de bord"
  },
  settings: {
    title: "Paramètres et configuration",
    icon: <SettingOutlined />,
    description: "Configuration système et paramètres"
  },
  projects: {
    title: "Gestion des projets",
    icon: <ProjectOutlined />,
    description: "Suivi et gestion des projets"
  },
  scoring: {
    title: "Gestion du scoring",
    icon: <SolutionOutlined />,
    description: "Système de notation et évaluation"
  },
  calendar: {
    title: "Agenda partagé",
    icon: <CalendarOutlined />,
    description: "Calendrier et planification"
  }
};

// Function to extract unique modules from CRM routes
export const extractModulesFromRoutes = (routes) => {
  const moduleSet = new Set();
  routes.forEach(route => {
    if (route.module && MODULE_CONFIG[route.module]) {
      moduleSet.add(route.module);
    }
  });
  return Array.from(moduleSet);
};

// Function to get the primary route for a module
export const getPrimaryRouteForModule = (routes, moduleName) => {
  // Find the first route for this module, preferring dashboard routes
  const moduleRoutes = routes.filter(route => route.module === moduleName);
  const dashboardRoute = moduleRoutes.find(route => route.path.includes('dashboard'));
  return dashboardRoute ? `/${dashboardRoute.path}` : `/${moduleRoutes[0]?.path || 'crm/dashboard'}`;
};

// Function to handle module selection and store in localStorage
export const handleModuleSelection = (moduleName, navigate) => {
  // Store selected module in localStorage for sidebar filtering
  localStorage.setItem('selectedCrmModule', moduleName);
  
  // Get the primary route for this module
  const primaryRoute = getPrimaryRouteForModule(crmRoutes, moduleName);
  
  // Dispatch storage event to notify other components
  window.dispatchEvent(new Event('storage'));
  
  // Dispatch custom event for CRM module change
  window.dispatchEvent(new Event('crmModuleChanged'));
  
  // Navigate to the module's primary route
  navigate(primaryRoute);
};

// Function to clear module selection and show all routes
export const clearModuleSelection = () => {
  localStorage.removeItem('selectedCrmModule');
  window.dispatchEvent(new Event('storage'));
  window.dispatchEvent(new Event('crmModuleChanged'));
};

// Function to get currently selected module
export const getSelectedModule = () => {
  return localStorage.getItem('selectedCrmModule');
};

// Function to check if a module is selected
export const isModuleSelected = (moduleName) => {
  return getSelectedModule() === moduleName;
};
