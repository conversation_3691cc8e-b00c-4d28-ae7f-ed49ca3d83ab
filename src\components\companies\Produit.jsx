import {
  FileOutlined,
  MinusCircleOutlined,
  PlusCircleOutlined,
  UploadOutlined,
} from "@ant-design/icons";
import { Col, DatePicker, Form, Input, message, Row, Select, Upload } from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";

const Produit = ({ form, view }) => {
  const { t } = useTranslation();
  const handleAdd = () => {
    const currentmotifs = form.getFieldValue("motifs") || [];

    const motif = form.getFieldValue("motif");
    if (motif) {
      const key = Date.now();
      form.setFieldsValue({
        motifs: [
          ...currentmotifs,
          {
            key: key,
            motif: motif,
          },
        ],
      });
      form.resetFields(["motif"]);
    } else {
      message.error(t("companyMsg.msg_required"));
    }
  };
  return (
    <>
      <Row gutter={16} style={{ marginBottom: "10px" }}>
        <Col span={3}>Année</Col>
        <Col span={11}>Designation du Produit</Col>
        <Col span={3}>Unité</Col>
        <Col span={3}>Qté</Col>
        <Col span={3}> Valeur DT</Col>
        <Col span={1}></Col>
      </Row>
      <Form.List name="motifs">
        {(fields, { remove }) => (
          <>
            {fields.map((field, index) => (
              <Row gutter={16} align="middle" key={field.key}>
                <Col span={18}>
                  <Form.Item name={[index, "motif"]} rules={[]}>
                    <Input allowClear={true} disabled={view} />
                  </Form.Item>
                </Col>
                <Col span={1}>
                  {!view && (
                    <Form.Item>
                      <MinusCircleOutlined
                        style={{
                          color: "red",
                          fontSize: "18px",
                          cursor: "pointer",
                        }}
                        onClick={() => remove(index)}
                      />
                    </Form.Item>
                  )}
                </Col>
              </Row>
            ))}
          </>
        )}
      </Form.List>

      {!view && (
        <Row gutter={16} align="middle" style={{ marginBottom: "5px" }}>
          <Col span={3}>
            <Form.Item name={`year`} rules={[]}>
              <Input allowClear={true} />
            </Form.Item>
          </Col>
          <Col span={11}>
            <Form.Item name={`poste`} rules={[]}>
              <Input allowClear={true} />
            </Form.Item>
          </Col>
          <Col span={3}>
            <Form.Item name={`def`} rules={[]}>
              <Select allowClear={true} />
            </Form.Item>
          </Col>
          <Col span={3}>
            <Form.Item name={`montant`} rules={[]}>
              <Input allowClear={true} />
            </Form.Item>
          </Col>
          <Col span={3}>
            <Form.Item name={`montant_reel`} rules={[]}>
              <Input allowClear={true} />
            </Form.Item>
          </Col>
       
          <Col span={1}>
            <Form.Item>
              <PlusCircleOutlined
                style={{
                  color: "green",
                  fontSize: "18px",
                  cursor: "pointer",
                }}
                onClick={() => handleAdd()}
              />
            </Form.Item>
          </Col>
        </Row>
      )}
    </>
  );
};

export default Produit;
