import {updateModeReglement} from "@/modules/finance/features/mode-reglement/ModeReglementSlice";
import {DeleteOutlined, PlusOutlined} from "@ant-design/icons";
import {Button, Col, Drawer, Form, Input, Row, Select, Space, Table, message} from "antd";
import {useEffect, useState} from "react";
import {useDispatch} from "react-redux";
import {useTranslation} from "react-i18next";


const EditModeReglment: React.FC<{
  visible: boolean,
  setVisible: React.Dispatch<React.SetStateAction<boolean>>,
  dataRecord: any;
  tableRef: any;
  moyenPaiements: any;
  show: any,
  messageApi,
}> = ({visible, setVisible, dataRecord, tableRef, moyenPaiements, show, messageApi}) => {
  const dispatch = useDispatch();
  const {t} = useTranslation();
  const [buttonLoading, setButtonLoading] = useState(false);
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const [data, setData] = useState([{id: 1}]);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const handleAddRow = () => {
    const newData = [...data, {id: data.length + 1}];
    setData(newData);
    console.log(data);
    setSelectedRowKeys([newData.length]);
  };

  const handleDeleteRows = (id) => {
    const newData = data.filter((item) => !selectedRowKeys.includes(item.id));
    setData(newData);
    setSelectedRowKeys([]);
  };
  const columns = [
    {
      title: t("mode_reglement.pourcentage"),
      dataIndex: 'pourcentage',
      render: (_, record) => (
        <Input style={{width: '80px'}}
               placeholder="Pourcentage %" name="pourcentage"
               type='number' min={0} max={100}
               required
               onChange={(e) => handleInputChange(record.id, "pourcentage", e.target.value)}
               value={record.pourcentage}
        />
      ),
    },
    {
      title: t("mode_reglement.nbr_jours"),
      dataIndex: 'nbr_jours',
      render: (_, record) => (
        <Input style={{width: '80px'}}
               placeholder="Nombre de jours" name="nbr_jour" required
               onChange={(e) => handleInputChange(record.id, "nbr_jours", e.target.value)}
               value={record.nbr_jours}

        />
      ),
    },
    {
      title: t("mode_reglement.type_reglement"),
      dataIndex: 'type_reglement',
      render: (_, record) => (
        <Select style={{width: '130px'}}
                placeholder="Type de règlement" aria-required
                options={
                  [
                    {label: "comptant", value: "comptant"},
                    {label: "net", value: "net"},
                    {label: "net_le", value: "net_le"},
                  ]
                }
                onChange={(value) => handleInputChange(record.id, "type_reglement", value)}
                allowClear
                value={record.type_reglement}
        >
        </Select>
      ),
    },
    {
      title: t("mode_reglement.jour_reglement"),
      dataIndex: 'jour_reglement',
      render: (_, record) => (
        <Input style={{width: '120px'}}
               placeholder="Jour de règlement" name="jour_reglement"
               type="number"
               min={0}
               max={31}
               onChange={(e) => handleInputChange(record.id, "jour_reglement", e.target.value)}
               required={record.type_reglement === "net_le"}
               disabled={record.type_reglement !== "net_le"}
               value={record.jour_reglement}
        />
      ),
    },
    {
      title: t("mode_reglement.moyen_paiement"),
      dataIndex: 'moyen_paiement_id',
      render: (_, record) => (
        <Select style={{width: '130px'}}
                placeholder="Type" aria-required
                options={moyenPaiements}
                onChange={(value) => handleInputChange(record.id, "moyen_paiement_id", value)}
                allowClear
                value={record.moyen_paiement_id}
        >
        </Select>
      ),
    },
    {
      title: t("action"),
      dataIndex: 'actions',
      render: (_, record) => (
        <Space size="middle">
          <Button icon={<DeleteOutlined/>} danger title="supprimer" onClick={() => handleDeleteRows(record.id)}>
          </Button>
        </Space>
      ),
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedKeys) => setSelectedRowKeys(selectedKeys),
  };
  const onClose = () => {
    setVisible(false);
    form.resetFields();
  };
  const handleInputChange = (id, field, value) => {
    const updatedData = data.map((item) =>
      item.id === id ? {...item, [field]: value} : item
    );
    setData(updatedData);
    console.log(data);
  };
  const handleUpdateMoyenPaiement = (values) => {
    values = {
      ...values,
      id: dataRecord.id,
      data: data,
    };
    let pourcentageSum = 0;
    data.forEach((item, index) => {
      pourcentageSum += parseInt(item['pourcentage']);
    });
    if (pourcentageSum !== 100) {
      messageApi.open({
        key: "updatable",
        error: "error",
        type: "error",
        content: "Somme du pourcentage doit être égal à 100%",
        duration: 2,
      });
      return;
    }
    setButtonLoading(true);
    setLoading(true);
    dispatch(updateModeReglement(values))
      .unwrap()
      .then((res) => {
        setVisible(false);
        setButtonLoading(false);
        messageApi.open({
          key: "updatable",
          type: "success",
          content: res.message,
          duration: 2,
        });
        tableRef.current.reload();
      })
      .catch((err) => {
        setLoading(false);
        setButtonLoading(false);
        console.log(err);
      });
  };
  const onFinish = (values) => {
    handleUpdateMoyenPaiement(values);
  };
  useEffect(() => {
    setData((prevData) => {
      return dataRecord.mode_reglement_values;
    });
    if (visible && dataRecord) {
      form.setFieldsValue({
        code: dataRecord?.code,
        libelle: dataRecord?.libelle
      });
    }
  }, [visible, dataRecord]);


  return (
    <Drawer
      title={t("mode_reglement.modifier")}
      className="CautionForm"
      onClose={onClose}
      open={visible}
      bodyStyle={{
        paddingBottom: 80,
      }}
      width={850}
    >
      <Form layout="vertical"
            onFinish={onFinish}
            form={form}
            disabled={show}
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
                name="code"
                label={t("mode_reglement.fields.code")}
                 rules={[
                   {
                     required: true,
                     message: "Veuillez entrer un code",
                   },
                 ]}
            >
              <Input placeholder="Veuillez entrer un code"/>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="libelle"
              label={t("mode_reglement.fields.label")}
              rules={[
                {
                  required: true,
                  message: "Veuillez entrer un Libellé",
                },
              ]}
            >
              <Input placeholder="Veuillez entrer un Libellé"/>
            </Form.Item>
          </Col>
          <Col span={24}>
            <Table
              rowKey={(record) => record.id}
              dataSource={data}
              columns={columns}
              rowSelection={rowSelection}
              pagination={false}
            />
            <Space>
              <Button type="dashed" icon={<PlusOutlined/>} onClick={handleAddRow}>
                {t("mode_reglement.ajouter_ligne")}
              </Button>
              <Button danger icon={<DeleteOutlined/>} onClick={handleDeleteRows}>
                {t("mode_reglement.supprimer_ligne")}
              </Button>
            </Space>
          </Col>
          <Col span={24} style={{marginTop: "10px"}}>
            <Form.Item style={{textAlign: "right"}}>
              <Button
                className="btnAnnuler"
                htmlType="button"
                onClick={onClose}
                style={{marginRight: "10px"}}
                disabled={false}
              >
                {t("annuler")}
              </Button>
              {!show && (
                <Button type="primary" htmlType="submit" style={{marginRight: "10px"}} loading={buttonLoading}>
                  {t("envoyer")}
                </Button>
              )}
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Drawer>
  );
};

export default EditModeReglment;
