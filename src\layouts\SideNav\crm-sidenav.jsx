import { useState, useEffect } from 'react';
import crmRoutes from '../../routes/crm-routes';

// Route label mapping for better French labels
const ROUTE_LABELS = {
  'crm/dashboard': 'Tableau de bord',
  'crm/typeactivities': 'Types d\'activités',
  'crm/plansactivities': 'Plans d\'activités',
  'crm/activitystatuses': 'Statuts des activités',
  'crm/leads': 'Génération des leads',
  'crm/contacts': 'Contacts/Prospects',
  'crm/piplines': 'Gestion des pipelines',
  'crm/equipescommerciales': 'Équipes commerciales',
  'crm/dashboardevents': 'Tableau de bord événements',
  'crm/events': 'Événements',
  'crm/gouvernorats': 'Gouvernorats',
  'crm/delegation': 'Délégations',
  'crm/code-postaux': 'Codes postaux',
  'crm/train-locaux': 'Trains locaux',
  'crm/demandes': 'Demandes',
  'crm/stat_terain': 'Statistiques terrain',
  'crm/companies-dashboard': 'Tableau de bord entreprises',
  'crm/entreprises-etrangeres': 'Entreprises étrangères',
  'crm/entreprises-interactions': 'Interactions entreprises',
  'crm/entreprises-demandes': 'Demandes entreprises',
  'crm/entreprises-contacts': 'Contacts entreprises',
  'crm/entreprises': 'Entreprises',
  'crm/parametres/activites': 'Paramètres activités',
  'crm/parametres/secteurs': 'Paramètres secteurs',
  'crm/parametres/pays': 'Paramètres pays',
  'crm/parametres/group-pays': 'Groupes de pays',
  'crm/parametres/departements': 'Départements',
  'crm/parametres/category': 'Catégories',
  'crm/gestion_profil_dias': 'Profils diaspora',
  'crm/interraction_dias': 'Interactions diaspora',
  'crm/report_analyse_dias': 'Analyses diaspora',
  'crm/seminaires/pays': 'Séminaires par pays',
  'crm/seminaires/secteur': 'Séminaires par secteur',
  'crm/seminaires/salons': 'Salons',
  'crm/seminaires/media': 'Médias',
  'crm/seminaires/ctes': 'CTEs',
  'crm/seminaires/delegations': 'Délégations séminaires',
  'crm/seminaires/demarchage-direct': 'Démarchage direct',
  'crm/seminaires/autre-source': 'Autres sources',
  'crm/seminaires/visites': 'Visites',
  'crm/gestion_calendrier': 'Gestion calendrier',
  'crm/projets': 'Projets',
  'crm/entreprises-statistiques': 'Statistiques entreprises',
  'crm/gestion_scorring': 'Gestion scoring',
  'crm/gestion_documents': 'Gestion documents',
  'crm/templates_emails': 'Modèles d\'emails'
};

// Function to generate dynamic sidebar navigation based on selected module
const generateDynamicSideNav = () => {
  const selectedModule = localStorage.getItem('selectedCrmModule');

  if (!selectedModule) {
    // Return all routes if no module is selected
    return crmRoutes.map((route, index) => ({
      base_nav: "crm",
      id: 5000 + index,
      label: ROUTE_LABELS[route.path] || route.path,
      order: index,
      menu_parent: null,
      active: route.path === 'crm/dashboard' ? 1 : 0,
      link: `/${route.path}`,
      permission_id: null,
      icon: `<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M3 4C3 3.44772 3.44772 3 4 3H16C16.5523 3 17 3.44772 17 4V6C17 6.55228 16.5523 7 16 7H4C3.44772 7 3 6.55228 3 6V4Z" fill="currentColor"></path><path d="M3 10C3 9.44771 3.44772 9 4 9H10C10.5523 9 11 9.44771 11 10V16C11 16.5523 10.5523 17 10 17H4C3.44772 17 3 16.5523 3 16V10Z" fill="currentColor"></path><path d="M14 9C13.4477 9 13 9.44771 13 10V16C13 16.5523 13.4477 17 14 17H16C16.5523 17 17 16.5523 17 16V10C17 9.44771 16.5523 9 16 9H14Z" fill="currentColor"></path></svg>`,
      created_at: null,
      updated_at: null,
      child_recursive: []
    }));
  }

  // Filter routes by selected module
  const filteredRoutes = crmRoutes.filter(route => route.module === selectedModule);

  return filteredRoutes.map((route, index) => ({
    base_nav: "crm",
    id: 5000 + index,
    label: ROUTE_LABELS[route.path] || route.path,
    order: index,
    menu_parent: null,
    active: route.path === 'crm/dashboard' ? 1 : 0,
    link: `/${route.path}`,
    permission_id: null,
    icon: `<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M3 4C3 3.44772 3.44772 3 4 3H16C16.5523 3 17 3.44772 17 4V6C17 6.55228 16.5523 7 16 7H4C3.44772 7 3 6.55228 3 6V4Z" fill="currentColor"></path><path d="M3 10C3 9.44771 3.44772 9 4 9H10C10.5523 9 11 9.44771 11 10V16C11 16.5523 10.5523 17 10 17H4C3.44772 17 3 16.5523 3 16V10Z" fill="currentColor"></path><path d="M14 9C13.4477 9 13 9.44771 13 10V16C13 16.5523 13.4477 17 14 17H16C16.5523 17 17 16.5523 17 16V10C17 9.44771 16.5523 9 16 9H14Z" fill="currentColor"></path></svg>`,
    created_at: null,
    updated_at: null,
    child_recursive: []
  }));
};

// Hook to listen for storage changes and regenerate navigation
const useDynamicSideNav = () => {
  const [sideNavItems, setSideNavItems] = useState(generateDynamicSideNav());

  useEffect(() => {
    const handleStorageChange = () => {
      setSideNavItems(generateDynamicSideNav());
    };

    // Listen for storage events (when selectedCrmModule changes)
    window.addEventListener('storage', handleStorageChange);

    // Also listen for custom events dispatched when module selection changes
    window.addEventListener('crmModuleChanged', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('crmModuleChanged', handleStorageChange);
    };
  }, []);

  return sideNavItems;
};

// Export both the hook and the static navigation for backward compatibility
const SideNavCRM = generateDynamicSideNav();

export default SideNavCRM;
export { useDynamicSideNav, generateDynamicSideNav };
