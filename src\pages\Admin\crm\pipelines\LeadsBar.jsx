import React, { useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, ResponsiveContainer } from "recharts";
import { Select } from "antd";

const { Option } = Select;

const leadData = [
  { category: "Leads Convertis", count: 80, color: "#52c41a", year: 2024, equipe: "Équipe A" },
  { category: "Opportunités Perdues", count: 40, color: "#ff4d4f", year: 2024, equipe: "Équipe B" },
  { category: "Opportunités Gagnées", count: 70, color: "#faad14", year: 2025, equipe: "Équipe A" },
];

export const LeadsBarChart = () => {
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [selectedYear, setSelectedYear] = useState("all");
  const [selectedEquipe, setSelectedEquipe] = useState("all");

  // Filter data based on selected category, year, and sales team
  const filteredData = leadData.filter(item => {
    return (
      (selectedCategory === "all" || item.category === selectedCategory) &&
      (selectedYear === "all" || item.year === selectedYear) &&
      (selectedEquipe === "all" || item.equipe === selectedEquipe)
    );
  });

  // Extract unique years and equipes
  const years = Array.from(new Set(leadData.map(item => item.year)));
  const equipes = Array.from(new Set(leadData.map(item => item.equipe)));

  return (
    <div style={{ textAlign: "center", padding: "20px" }}>
      {/* Category Filter */}
      <Select defaultValue="all" style={{ width: 200, marginBottom: 20 }} onChange={setSelectedCategory}>
        <Option value="all">Toutes les Opportunités</Option>
        {leadData.map((item) => (
          <Option key={item.category} value={item.category}>{item.category}</Option>
        ))}
      </Select>
      
      {/* Year Filter */}
      <Select defaultValue="all" style={{ width: 200, marginBottom: 20, marginLeft: 10 }} onChange={setSelectedYear}>
        <Option value="all">Toutes les Années</Option>
        {years.map(year => (
          <Option key={year} value={year}>{year}</Option>
        ))}
      </Select>

      {/* Sales Team Filter */}
      <Select defaultValue="all" style={{ width: 200, marginBottom: 20, marginLeft: 10 }} onChange={setSelectedEquipe}>
        <Option value="all">Toutes les Équipes</Option>
        {equipes.map(equipe => (
          <Option key={equipe} value={equipe}>{equipe}</Option>
        ))}
      </Select>
      
      <ResponsiveContainer width="100%" height={300}>
        <BarChart data={filteredData}>
          <XAxis dataKey="category" />
          <YAxis />
          <Tooltip />
          <Legend />
          <Bar dataKey="count" fill={filteredData.length === 0 ? filteredData[0]?.color : "#8884d8"} />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};
