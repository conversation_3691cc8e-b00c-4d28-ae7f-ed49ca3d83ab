import dayjs from "dayjs";

const demandes = [
  {
    id: 1,
    entreprise_id: 1,
    date_demande:dayjs("2025-01-10"),
    description: "test exchange description",
    type_intercation_id: 1,
    responsable_id : 1 
  
  } ,
  {
  id: 2,
  entreprise_id: 1,
  date_demande: dayjs("2025-02-18"),
  description: "Appel téléphonique pour discuter des conditions de paiement.",
  type_intercation_id: 2,
  responsable_id: 3
},
{
  id: 3,
  entreprise_id: 2,
  date_demande: dayjs("2025-03-05"),
  description: "Réunion de suivi sur le projet logistique en cours.",
  type_intercation_id: 3,
  responsable_id: 2
},
{
  id: 4,
  entreprise_id: 3,
  date_demande: dayjs("2025-04-01"),
  description: "Échange par email concernant une proposition commerciale.",
  type_intercation_id: 1,
  responsable_id: 4
}

];

const responsables = [
  { id: 1, nom: "<PERSON>" },
  { id: 2, nom: "<PERSON>" },
  { id: 3, nom: "<PERSON>" },
  { id: 4, nom: "<PERSON>" },
  { id: 5, nom: "<PERSON>" },
];

const companies = [
  {
    id: 1,
    nom: "TechNova Solutions",
  },
  {
    id: 2,
    nom: "AgriGrow",
  },
  {
    id: 3,
    nom: "EcoEnergy",
  },
  {
    id: 4,
    nom: "MediCare Pharma",
  },
];

export {
  companies,
  demandes,
  responsables
};
