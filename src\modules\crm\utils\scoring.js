export const calculateLeadScore = (lead) => {
  let score = 0;

  let sector = "Autre";
  if (lead.projectType?.includes("INDUSTRIE MANUFACTURIÈRE") || lead.projectType?.includes("Industrie")) {
    sector = "Industrie";
  } else if (lead.projectType?.includes("TIC")) {
    sector = "TIC";
  } else if (lead.projectType?.includes("Tourisme")) {
    sector = "Tourisme";
  }
  switch (sector) {
    case "TIC":
      score += 20;
      break;
    case "Industrie":
      score += 15;
      break;
    case "Tourisme":
      score += 10;
      break;
    default:
      score += 5;
  }

  let location = "Other";
  const strategicRegions = {
    Europe: ["Germany", "France"],
    "Amérique du Nord": [],
    "Moyen-Orient": ["Qatar", "Jordan"],
    "Afrique du Nord": ["Tunisia", "Algeria"],
  };
  if (lead.locations) {
    for (const loc of lead.locations) {
      if (strategicRegions.Europe.includes(loc)) {
        location = "Europe";
        break;
      } else if (strategicRegions["Moyen-Orient"].includes(loc)) {
        location = "Moyen-Orient";
        break;
      } else if (strategicRegions["Afrique du Nord"].includes(loc)) {
        location = "Afrique du Nord";
        break;
      }
    }
  }
  if (["Europe", "Amérique du Nord"].includes(location)) {
    score += 15;
  } else if (["Moyen-Orient", "Afrique du Nord"].includes(location)) {
    score += 10;
  } else {
    score += 5;
  }

  let projectSize = "Petit";
  const investmentAmount = parseFloat(lead.investmentAmount?.replace(" m EUR", "") || 0) * 1000000;
  if (investmentAmount > 10000000) {
    projectSize = "Grand";
  } else if (investmentAmount >= 1000000) {
    projectSize = "Moyen";
  }
  if (projectSize === "Grand") {
    score += 20;
  } else if (projectSize === "Moyen") {
    score += 15;
  } else {
    score += 5;
  }

  // Type d’investissement
  let investmentType = lead.investmentType || "Non spécifié";
  if (investmentType.includes("Nouveau")) {
    investmentType = "Direct"; 
  }
  if (investmentType === "Direct") {
    score += 15;
  } else if (investmentType === "Partenariat") {
    score += 10;
  } else {
    score += 5;
  }

  const jobCreation = parseInt(lead.jobsDirect) || 0;
  if (jobCreation > 50) {
    score += 20;
  } else if (jobCreation >= 10) {
    score += 10;
  }

  const techTransfer = false; 
  if (techTransfer) {
    score += 15;
  }

  if (investmentAmount > 1000000) {
    score += 15;
  }

  let infrastructureImpact = "Faible";
  if (jobCreation > 50) {
    infrastructureImpact = "Élevé";
  }
  if (infrastructureImpact === "Élevé") {
    score += 10;
  }

  // Engagement (inferred from notes)
  const interactions = lead.notes?.length > 0 ? [{ type: "email_response" }] : [];
  if (interactions.some((i) => i.type === "meeting")) {
    score += 10;
  }
  if (interactions.some((i) => i.type === "email_response")) {
    score += 5;
  }
  if (interactions.some((i) => i.type === "document_submission")) {
    score += 15;
  }

  return Math.min(score, 100);
};