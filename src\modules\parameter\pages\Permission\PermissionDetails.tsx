import React, { useEffect, useState } from "react";
import {
  Button,
  Drawer,
  Input,
  Form,
  Row,
  Col,
  Select,
  Divider,
  Space,
  Popconfirm,
} from "antd";
import { PlusOutlined } from "@ant-design/icons";
import { useDispatch, useSelector } from "react-redux";
import { deletePermission, updatePermission } from "@/modules/parameter/features/permissions/permissionSlice";
const { Option } = Select;
const PermissionDetails = ({
  visible,
  setVisible,
  permissions,
  permission,
  messageApi,
}) => {


  const dispatch = useDispatch();
  const [groupeForm] = Form.useForm();
  const [buttonLoading, setButtonLoading] = useState<boolean>(false);
  const [groupes, setGroupes] = useState([]);
  const [modify, setModify] = useState(false);
  const [fields, setFields] = useState([]);
  //select search and sort
  const filterOption = (input, option) =>
    (option?.label ?? "").toLowerCase().includes(input.toLowerCase());
  const filterSort = (optionA, optionB) =>
    (optionA?.label ?? "")
      .toLowerCase()
      .localeCompare((optionB?.label ?? "").toLowerCase());

  const onClose = () => {
    setVisible(false);
    setModify(false)
  };

  const handleAddGroupe = (e) => {
    setGroupes([...groupes, e.groupe]);
    groupeForm.resetFields();
  };

  const handleUpdatePermission = (e) => {
    setButtonLoading(true)
    messageApi.open({
      key:'updatable',
      type: 'loading',
      content: 'Loading...',
    });
    dispatch(updatePermission({id:permission.id, name:e.name, groupe:e.groupe, label:e.label, related_permission:e.related_permission}))
    .unwrap()
    .then((originalPromiseResult) => {
      messageApi.open({
        key:'updatable',
        type: 'success',
        content: 'Success ',
        duration: 2,
      });
      setButtonLoading(false)
      onClose()
    })
    .catch((rejectedValueOrSerializedError) => {
      messageApi.open({
        key:'updatable',
        type: 'error',
        content: 'Error',
        duration: 2,
      });
      setButtonLoading(false)
      console.log(rejectedValueOrSerializedError);
      return [];
    });
  }

  const handleDeletePermission = (e) => {
    setButtonLoading(true)
    messageApi.open({
      key:'updatable',
      type: 'loading',
      content: 'Loading...',
    });
    dispatch(deletePermission({id:permission.id}))
    .unwrap()
    .then((originalPromiseResult) => {
      messageApi.open({
        key:'updatable',
        type: 'success',
        content: 'Success ',
        duration: 2,
      });
      setButtonLoading(false)
      onClose()
    })
    .catch((rejectedValueOrSerializedError) => {
      messageApi.open({
        key:'updatable',
        type: 'error',
        content: 'Error',
        duration: 2,
      });
      setButtonLoading(false)
      console.log(rejectedValueOrSerializedError);
      return [];
    });
  }


  useEffect(() => {
    permissions &&
    setGroupes(
        permissions.map((item) => {
          return item.title;
        })
      );
    permission &&
        setFields([
            {
              name: ["name"],
              value: permission.name,
            },
            {
              name: ["label"],
              value: permission.title,
            },
            {
              name: ["groupe"],
              value: permission.groupe,
            },
            {
              name: ["related_permission"],
              value: permission.related_permission?.map((item) => item.id),
            }
          ]);
  }, [permission]);

  return (
    <Drawer
      title="Nouveau permission"
      width={window.innerWidth > 580 ? 560 : "90%"}
      className="CautionForm"
      onClose={onClose}
      open={visible}
      styles={{ body: { paddingBottom: 80 } }}

    >
      <Form layout="vertical"
        disabled={!modify}
        onFinish={handleUpdatePermission}
        fields={fields}
      >
        <Row gutter={16}>
          <Col xs={24} sm={24} md={24} lg={24} xl={24} xxl={24}>
            <Form.Item
              name="groupe"
              label="Groupe"
              rules={[
                {
                  required: true,
                  message: "Veuillez entrer le Rôle",
                },
              ]}
            >
              <Select
                showSearch
                placeholder="Groupe"
                optionFilterProp="children"
                filterOption={filterOption}
                filterSort={filterSort}
                options={groupes?.map((item) => {
                  return {
                    value: item,
                    label: item,
                  };
                })}
                dropdownRender={(menu) => (
                  <>
                    {menu}
                    <Divider style={{ margin: "8px 0" }} />
                    <Form
                      form={groupeForm}
                      style={{ marginBottom: "-20px" }}
                      onFinish={handleAddGroupe}
                    >
                      <Row gutter={6}>
                        <Col span={21}>
                          <Form.Item name="groupe">
                            <Input placeholder="Nouveau groupe" />
                          </Form.Item>
                        </Col>
                        <Col span={3}>
                          <Form.Item>
                            <Button
                              className="w-full"
                              type="primary"
                              htmlType="submit"
                              icon={<PlusOutlined />}
                            />
                          </Form.Item>
                        </Col>
                      </Row>
                    </Form>
                  </>
                )}
              />
            </Form.Item>
          </Col>

          <Col xs={24} sm={24} md={24} lg={24} xl={24} xxl={24}>
            <Form.Item
              name="label"
              label="Permission Label"
            >
              <Input placeholder="Nom du permission" />
            </Form.Item>
          </Col>
          <Col xs={24} sm={24} md={24} lg={24} xl={24} xxl={24}>
            <Form.Item
              name="name"
              label="Permission"
              rules={[
                {
                  required: true,
                  message: "Veuillez entrer le nom du permission",
                },
              ]}
            >
              <Input placeholder="Nom du permission" disabled/>
            </Form.Item>
          </Col>

          <Col xs={24} sm={24} md={24} lg={24} xl={24} xxl={24}>
            <Form.Item
              name="related_permission"
              label="Permissions relatives"
            >
              <Select
                mode="multiple"
                showSearch
                placeholder="Permissions"
                optionFilterProp="children"
                filterOption={filterOption}
                filterSort={filterSort}
                options={permissions?.map((item) => {
                  return {
                    value: item.key,
                    label: item.title,
                    options: item.children?.map((p) => {
                      if (p.id == permission.id) {
                        return {
                          value: p.id,
                          label: p.title,
                          disabled: true,
                        };
                      }
                      return {
                        value: p.id,
                        label: p.title,
                      };
                    }),
                  };
                })}
              />
            </Form.Item>
          </Col>
          {modify && (
            <Col span={24} style={{ textAlign: "right" }}>
              <Form.Item>
                <Button
                  onClick={() => {
                    setModify(false);
                  }}
                  style={{ marginRight: "10px" }}
                >
                  Annuler
                </Button>
                <Button type="primary" htmlType="submit" loading={buttonLoading}>
                  Envoyer
                </Button>
              </Form.Item>
            </Col>
          )}
        </Row>
      </Form>
      {!modify && (
        <div className="w-full text-right space-x-2">
          <Button
            onClick={() => {
              setModify(true);
            }}
          >
            Modifier
          </Button>
          {/* <Popconfirm
            placement="topRight"
            title="voulez-vous vraiment supprimer cette permission ?"
            onConfirm={handleDeletePermission}
            okText="Oui"
            cancelText="Non"
          >
            <Button loading={buttonLoading} danger>
              Supprimer
            </Button>
          </Popconfirm> */}
        </div>
      )}
    </Drawer>
  );
};

export default PermissionDetails;
