import {createAsyncThunk, createSlice} from "@reduxjs/toolkit";
import { default as api } from "@/apis/axiosInterceptor";

export const getAllDepartments : any = createAsyncThunk(
  "departments/fetchAll",
  async (_, thunkAPI) => {
    try {
      const response = await api.get(`/departments-all`);
      return response.data;
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error?.message || "An error occurred.");
    }
  }
);


export const departmentSlice = createSlice({
  name: 'departments',
  initialState: {
    loading: false,
    error: null,
    data: [],
  },
  reducers: {},
  extraReducers: builder => {
       builder
      .addCase(getAllDepartments.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getAllDepartments.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
        state.data = action.payload;
      })
      .addCase(getAllDepartments.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  }
})

export default departmentSlice.reducer

