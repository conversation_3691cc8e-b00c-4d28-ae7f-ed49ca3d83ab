import {
  CheckCircleOutlined,
  CheckCircleTwoTone,
  PlusCircleFilled,
} from "@ant-design/icons";
import { ModalForm } from "@ant-design/pro-components";
import {
  <PERSON><PERSON>,
  Card,
  Col,
  DatePicker,
  Divider,
  Form,
  Input,
  message,
  Modal,
  Popover,
  Radio,
  Row,
  Select,
  Steps,
} from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";

function CreateCTE({ open, onCancel, onSuccess }) {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const handleSubmit = (values) => {};


  return (
    <ModalForm
      title={t("Ajouter un contact CTE")}
      form={form}
      open={open}
      modalProps={{
        style: { top: 20 },
      }}
      onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.save"),
          resetText: t("common.actions.cancel"),
        },
      }}
    >
      <Card className="mt-2">
       
        <Row gutter={16}>
          <Col md={12} xs={24}>
            <Form.Item name={""} label={t("Nom")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name={""} label={t("Prénom")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={24} xs={24}>
            <Form.Item name={""} label={t("Adresse")}>
              <Input />
            </Form.Item>
          </Col>
             <Col md={12} xs={24}>
            <Form.Item name={""} label={t("Tél.")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name={""} label={t("Fax")}>
              <Input />
            </Form.Item>
          </Col>
             <Col md={12} xs={24}>
            <Form.Item name={""} label={t("E-mail")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name={""} label={t("Age")}>
              <Input />
            </Form.Item>
          </Col>
        </Row>
      </Card>
    
      <Card className="mt-2">
        <Row gutter={16}>
          <Col md={16} xs={24}>
            <Form.Item name={""} label={t("Initiateur")}>
              <Select />
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item name={""} label={t("Date de contact")}>
              <DatePicker />
            </Form.Item>
          </Col>
          <Col md={24} xs={24}>
            <Form.Item name={""} label={t("Poste")}>
              <Input.TextArea />
            </Form.Item>
          </Col>
          <Col md={24} xs={24}>
            <Form.Item name={""} label={t("Diplôme")}>
              <Input.TextArea />
            </Form.Item>
          </Col>
          <Col md={24} xs={24}>
            <Form.Item name={""} label={t("Sté/Instit")}>
              <Input.TextArea />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name={""} label={t("Pays")}>
              <Select />
            </Form.Item>
          </Col>

          <Col md={12} xs={24}>
            <Form.Item name={""} label={t("Lieu")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name={""} label={t("Secteur")}>
              <Select />
            </Form.Item>
          </Col>
         
        </Row>
      </Card>

    </ModalForm>
  );
}
export default CreateCTE;
