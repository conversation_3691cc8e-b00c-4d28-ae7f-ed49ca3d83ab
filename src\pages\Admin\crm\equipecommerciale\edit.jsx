import { useEffect } from "react";
import { ProForm, ProFormText, ProFormSelect } from "@ant-design/pro-form";
import { message } from "antd";
import { datas } from "./datas";

const EditCommercial = ({ record, onCancel, setDataSource }) => {
  const [form] = ProForm.useForm();

  useEffect(() => {
    if (record) {
      form.setFieldsValue(record);
    }
  }, [record, form]);

  const handleSubmit = (values) => {
    const updatedData = datas.map((item) =>
      item.key === record.key ? { ...item, ...values } : item
    );
    setDataSource(updatedData);
    message.success("Commercial mis à jour avec succès");
    onCancel();
  };

  return (
    <ProForm form={form} onFinish={handleSubmit}>
      <ProFormText name="name" label="Nom" rules={[{ required: true }]} />
      <ProFormText name="chef" label="Chef d'équipe" rules={[{ required: true }]} />
      <ProFormSelect 
        name="membres" 
        label="Membres" 
        mode="multiple" 
        options={[
          { label: "Alice", value: "Alice" }, 
          { label: "<PERSON>", value: "<PERSON>" },
          { label: "Charlie", value: "Charlie" },
          { label: "David", value: "David" }
        ]} 
      />
      <ProFormText name="alias" label="Alias" rules={[{ required: true }]} />
      <ProFormText name="objectif" label="Objectif" rules={[{ required: true }]} />
    </ProForm>
  );
};

export default EditCommercial;
