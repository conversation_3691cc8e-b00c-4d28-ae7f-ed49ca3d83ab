

.breadcrumb-scroll-container {
  width: 100%;
  overflow-x: auto;  /* Barre de défilement si nécessaire */
  overflow-y: hidden;  /* Pas de défilement vertical */
  white-space: nowrap;  /* Empêche le retour à la ligne */
}

.breadcrumb-arrows {
    margin-bottom: 25px;
    text-align: center;
  }
  
  .breadcrumb-arrows li {
    display: inline-block;
    line-height: 30px;
    position: relative;
    margin-right: 2px;
  }
  
  .breadcrumb-arrows li:before {
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    left: -2px;
    border-style: solid;
    border-width: 15px 0 15px 15px;
    border-color: transparent transparent transparent #fff;
    z-index: 0;
  }
  
  .breadcrumb-arrows li:first-child:before {
    border-color: transparent;
  }
  
  .breadcrumb-arrows a:after, .activechoise a:after {
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    right: -15px;
    border-style: solid;
    border-width: 15px 0 15px 15px;
    border-color: transparent transparent transparent #ccc;
    z-index: 10;
  }
  
  .breadcrumb-arrows .last > a:after {
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    right: -15px;
    border-style: solid;
    border-width: 15px 0 15px 15px;
    border-color: #ccc #ccc #ccc #ccc;
    z-index: 10;
  }
  
   .breadcrumb-arrows a {
    display: block;
    background: #ccc;
    padding: 0 20px;
  }
  
  .breadcrumb-arrows a:focus {
    background: #800020 !important;
    color: white !important;
  }
  
  .breadcrumb-arrows li.a:focus {
    background: #800020 !important;
    color: white !important;
  }
  
   .activeitem a, .activeitem li {
    background: #800020 !important;
    color: white !important;
    border-color: transparent transparent transparent #800020 !important;
  }
  
  .activeitemfinale a, .activeitemfinale li {
    background: #800020 !important;
    color: white !important;
    border-color: #800020 #800020 #800020 #800020 !important;
  }
  
   .activechoise a, .activechoise li {
    background: #800020 !important;
    color: white !important;
    border-color: transparent transparent transparent #800020 !important;
  }
  
  .activechoise a:after {
    border-color: transparent transparent transparent #800020 !important;
  }
  
  .breadcrumb-arrows a:focus:after {
    border-color: transparent transparent transparent #800020 !important;
    color: white !important;
  }
  
  .breadcrumb-arrows .activeitemfinale > a:focus:after {
    border-color: #800020 #800020 #800020 #800020 !important;
    color: white !important;
  }
  
  .activeitem a:after {
    border-color: transparent transparent transparent #800020 !important;
    color: white !important;
  }
  
  .activeitemfinale a:after {
    border-color: #800020 #800020 #800020 #800020 !important;
    color: white !important;
  }
  