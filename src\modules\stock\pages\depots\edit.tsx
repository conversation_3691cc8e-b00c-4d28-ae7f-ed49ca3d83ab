import { ModalForm } from "@ant-design/pro-components";
import { Col, Divider, Form, Input, Radio, Row, Select } from "antd";

import TextArea from "antd/es/input/TextArea.js";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { DepotForm } from "../../components/DepotForm";



function EditDepotForm({  open, setOpen, principale, tableRef, entreprises, responsables , record}){
    const {t} = useTranslation();
    const [loading, setLoading] = useState(false);
    const [form] = Form.useForm();
    const [value, setValue] = useState(2);
      useEffect(() => {
        if (open) {
            form.setFieldsValue(record);
            console.log('aaaaa',principale);
            
        }
      }, [open]);
      const options={
        principale, entreprises, responsables,form
      }
    return(
    <ModalForm
      title={t("depots.edit")}
    
      form={form}
      open={open}
    //   onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          setOpen(false);
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.edit"),
          resetText: t("common.actions.cancel"),
        },
      }}
    >
          <DepotForm {...options}/>
    </ModalForm>
    )
}
export default EditDepotForm;