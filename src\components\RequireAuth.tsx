import { Navigate, useLocation, Outlet } from "react-router-dom";


const RequireAuth = () => {
  const auth = true;
  const role = ["admin"];
  const location = useLocation();

  function checkLinkExists(menuArray) {
    for (const item of menuArray) {
      if (item.link === location.pathname) return true;
      if (
        item.child_recursive_active &&
        checkLinkExists(item.child_recursive_active)
      ) {
        return true;
      }
    }
    return false;
  }

  return localStorage.getItem("token") ? (
    checkLinkExists(JSON.parse(localStorage.getItem("menu"))) ? (
      <Outlet />
    ) : (
      <Navigate to="/unauthorized" />
    )
  ) : (
    <Navigate to="/accueil" state={{ path: location.pathname }} replace />
  );
};

export default RequireAuth;
