import { useState, useRef } from "react";
import { ProTable } from "@ant-design/pro-components";
import { Button, Popconfirm, Space, Input, Tooltip, Typography, Card, Modal } from "antd";
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined, SearchOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import demandes from "./datas"; // Importation des données
import CreateDemande from "./create";  // Créez ce composant pour gérer la création d'une demande
import EditDemande from "./edit";    // Créez ce composant pour gérer la modification d'une demande
import ViewDemande from "./view";    // Créez ce composant pour visualiser les détails d'une demande

function Demandes() {
  const { t } = useTranslation();
  const tableRef = useRef();

  const [dataSource, setDataSource] = useState(demandes);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [viewModalVisible, setViewModalVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState(null);
  const [viewingRecord, setViewingRecord] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleCancel = () => {
    setViewModalVisible(false);
  };

  const handleEdit = (record) => {
    setEditingRecord(record);
    setEditModalVisible(true);
  };

  // Colonnes à afficher (les plus importantes)
  const columns = [
    { title: "Date d'Arrivée", dataIndex: "dateArrivee", key: "dateArrivee" },
    { title: "Demande Satisfaite", dataIndex: "demandeSatisfaite", key: "demandeSatisfaite" },
    { title: "Date de Réponse", dataIndex: "dateReponse", key: "dateReponse" },
    { title: "RS Promoteur", dataIndex: "rsPromoteur", key: "rsPromoteur" },
    { title: "Adresse", dataIndex: "adresse", key: "adresse" },
    { title: "Secteur", dataIndex: "secteur", key: "secteur" },
    { title: "Email / Site Web", dataIndex: "emailSiteWeb", key: "emailSiteWeb" },
    {
      title: "Actions",
      key: "action",
      align: "center",
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title="Voir">
            <Button type="link" icon={<EyeOutlined />} onClick={() => {
              setViewingRecord(record);
              setViewModalVisible(true);
            }} />
          </Tooltip>
          <Tooltip title="Modifier">
            <Button type="link" style={{ color: "#f5b041" }} icon={<EditOutlined />} onClick={() => handleEdit(record)} />
          </Tooltip>
          <Tooltip title="Supprimer">
            <Popconfirm title="Confirmer la suppression ?" onConfirm={() => {
              const newDataSource = dataSource.filter(item => item.key !== record.key);
              setDataSource(newDataSource);
            }}>
              <Button type="link" style={{ color: "#ec7063" }} icon={<DeleteOutlined />} />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <Card
      bordered
      title={<Typography.Title level={4}>Liste des Demandes</Typography.Title>}
      extra={
        <Space>
          <Input size="large" placeholder="Rechercher" suffix={<SearchOutlined style={{ color: "#bfbfbf" }} />} />
          <Button type="primary" icon={<PlusOutlined />} onClick={() => setCreateModalVisible(true)}>
            Ajouter
          </Button>
        </Space>
      }
    >
      <ProTable
        actionRef={tableRef}
        loading={isLoading}
        columns={columns}
        dataSource={dataSource}
        rowKey="key"
        search={false}
        options={false}
        pagination={{ showSizeChanger: true, defaultPageSize: 5 }}
      />
      {/* Modal pour créer une demande */}
      <Modal
        title={t("crm.tasks.add")}
        visible={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        footer={null}
        width={1000}
      >
        <CreateDemande
          onCancel={() => setCreateModalVisible(false)}
          setDataSource={setDataSource}
        />
      </Modal>

      {/* Modal pour modifier une demande */}
      <Modal
        title={t("crm.tasks.edit")}
        visible={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        footer={null}
        width={800}
      >
        <EditDemande
          record={editingRecord}
          onCancel={() => setEditModalVisible(false)}
          setDataSource={setDataSource}
        />
      </Modal>

      {/* Modal pour visualiser une demande */}
      <Modal
        title={t("crm.tasks.view")}
        visible={viewModalVisible}
        onCancel={handleCancel}
        footer={null}
        width={800}
      >
        <ViewDemande
          record={viewingRecord}
          onCancel={() => setViewModalVisible(false)}
          setDataSource={setDataSource}
        />
      </Modal>
    </Card>
  );
}

export default Demandes;
