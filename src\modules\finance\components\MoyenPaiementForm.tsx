import { Col, Form, Input, Row } from "antd";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";

const MoyenPaiementForm = ({ disabled, moyenPaiement }) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();

  useEffect(() => {
    if (moyenPaiement) {
      form.setFieldsValue(moyenPaiement);
    }
  }, [moyenPaiement]);

  return (
    <Form layout="vertical" disabled={disabled} form={form}>
      <Row gutter={4} className="mt-2">
        <Col className="gutter-row" span={24}>
          <Form.Item
            name="code"
            label={t("moyens-paiement.fields.code")}
            rules={[
              {
                required: true,
                message: "moyens-paiement.validations.required_code",
              },
            ]}
          >
            <Input placeholder={t("moyens-paiement.fields.code")} />
          </Form.Item>
        </Col>
        <Col className="gutter-row" span={24}>
          <Form.Item
            name="libelle"
            label={t("moyens-paiement.fields.libelle")}
            rules={[
              {
                required: true,
                message: "moyens-paiement.validations.required_libelle",
              },
            ]}
          >
            <Input placeholder={t("moyens-paiement.fields.libelle")} />
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );
};

export default MoyenPaiementForm;
