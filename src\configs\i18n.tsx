import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector'; // Optional: for detecting language based on browser settings
import Backend from 'i18next-http-backend'; // Optional: to load translations from external files

// Import your translation files
import enTranslation from '../translation/locales/en/en.json';
import frTranslation from '../translation/locales/fr/fr.json';

import ahmedEnTranslation from '../translation/locales/en/ahmed.json';
import ahmedFrTranslation from '../translation/locales/fr/ahmed.json';

import crmEnTranslation from '../translation/locales/en/crm.json';
import crmFrTranslation from '../translation/locales/fr/crm.json';
import haifaEnTranslation from '../translation/locales/en/haifa.json';
import haifaFrTranslation from '../translation/locales/fr/haifa.json';
import amaniFrTranslation from '../translation/locales/fr/amani.json';
import amaniEnTranslation from '../translation/locales/en/amani.json';
import achrefFrTranslation from '../translation/locales/fr/achref.json';
import achrefEnTranslation from '../translation/locales/en/achref.json';
import waelFrTranslation from '../translation/locales/fr/wael.json';
import waelEnTranslation from '../translation/locales/en/wael.json';


import salesFrTranslation from '../translation/locales/fr/sales.json'
import salesEnTranslation from '../translation/locales/en/sales.json'
i18n
  .use(Backend)
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources: {
      en: { translation: {...enTranslation , ...ahmedEnTranslation,...crmEnTranslation, ...haifaEnTranslation  ,...achrefEnTranslation,...salesEnTranslation , ...amaniEnTranslation, ...waelEnTranslation  } },
      fr: { translation: {...frTranslation , ...ahmedFrTranslation,...crmFrTranslation,...haifaFrTranslation,...achrefFrTranslation,...salesFrTranslation , ...amaniFrTranslation, ...waelFrTranslation } },
    },
    fallbackLng: 'fr', // Default language if detection fails
    debug: true, // Turn off in production
    interpolation: {
      escapeValue: false, // React already escapes values
    },
  });
