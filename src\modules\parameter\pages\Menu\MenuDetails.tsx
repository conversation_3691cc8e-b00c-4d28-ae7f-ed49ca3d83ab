import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  Col,
  Drawer,
  Form,
  Input,
  Row,
  Select,
  Popconfirm,
  TreeSelect,
  Switch,
} from "antd";
import { EyeOutlined, EyeInvisibleOutlined } from "@ant-design/icons";
import { useDispatch, useSelector } from "react-redux";
import { activeMenu, deleteMenu, updateMenu } from "@/modules/parameter/features/menus/menuSlice";
import { ordreOptions } from "./MenuCreate";

import { IPermission } from "@/modules/parameter/pages/Role/interfaces";
import { getPermissions } from "@/modules/parameter/features/permissions/permissionSlice";
import { icons } from "lucide-react";

const { Option } = Select;

const MenuDetails: React.FC<{
  visible: boolean;
  setVisible: React.Dispatch<React.SetStateAction<boolean>>;
  node: any;
  menu: any;
}> = ({ visible, setVisible, node, menu }) => {
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const [value, setValue] = useState<string>();
  const [update, setUpdate] = useState(false);
  const [buttonLoading, setButtonLoading] = useState(false);
  const [switchLoading, setSwitchLoading] = useState(false);
  const [activeNode, setActiveNode] = useState<boolean>();
  const [parentMenu, setParentMenu] = useState<any>(null);
  const [fields, setFields] = useState([]);
  const [permissions, setPermissions] = useState<IPermission[]>([]);
  const [data, setData] = useState<IPermission[]>([]);
  const [optionsIcons, setOptionsIcons] = useState([]);
  const onClose = () => {
    setVisible(false);
    setUpdate(false);
  };
  const onChange = (newValue: string) => {
    setValue(newValue);
  };
  const Icon = ({ name, color = "#888", size = 15 }: any) => {
    try {
      const LucideIcon = icons[name];
      if (!LucideIcon) {
        console.log("icon not defined in library - fix by ch");
      } else {
        return (
          <LucideIcon
            color={color}
            size={size}
            style={{ display: "inline-block", marginRight: 4 }}
          />
        );
      }
    } catch (ex) {
      console.log(ex);
    }
  };
  const handleDeleteMenu = (values) => {
    setButtonLoading(true);
    dispatch(deleteMenu(node.id))
      .unwrap()
      .then((originalPromiseResult) => {
        onClose();
        setButtonLoading(false);
      })
      .catch((rejectedValueOrSerializedError) => {
        setButtonLoading(false);
        console.log(rejectedValueOrSerializedError);
        return [];
      });
  };
  //select search and sort
  const filterOption = (input, option) =>
    (option?.label ?? "").toLowerCase().includes(input.toLowerCase());
  const filterSort = (optionA, optionB) =>
    (optionA?.label ?? "")
      .toLowerCase()
      .localeCompare((optionB?.label ?? "").toLowerCase());

  const langs = JSON.parse(localStorage.getItem("languages"));
  const handleSubmit = (values) => {
    console.log(values);
    setButtonLoading(true);
    const titles = langs.reduce((acc, lang) => {
      const fieldName = `title_${lang.code}`;
      const inputValue = values[fieldName];

      acc[lang.code] = inputValue;
      return acc;
    }, {});
    dispatch(
      updateMenu({
        id: node.id,
        designation_fr: titles,
        ordre: values.ordre,
        menu_parant: values.parent ? values.parent : null,
        link: values.path,
        icon: values.icon,
        permission_id: values.permission,
      }))
      .unwrap()
      .then((originalPromiseResult) => {
        console.log(originalPromiseResult);
        setButtonLoading(false);
        onClose();
      })
      .catch((rejectedValueOrSerializedError) => {
        setButtonLoading(false);
        console.log(rejectedValueOrSerializedError);
      });
  };
  const handleActiveMenu = () => {
    setSwitchLoading(true);
    dispatch(activeMenu(node.id))
      .unwrap()
      .then((originalPromiseResult) => {
        console.log(originalPromiseResult);
        setActiveNode(!activeNode);
        setSwitchLoading(false);
      })
      .catch((rejectedValueOrSerializedError) => {
        setSwitchLoading(false);
        console.log(rejectedValueOrSerializedError);
      });
  };
  const allPermissions = useSelector((state: any) => state.permission.data);

  const handleGetPermissions = () => {
    if (typeof allPermissions == "object") {
      setPermissions(
        Object.keys(allPermissions).reduce((acc, title, index) => {
          acc.push({
            title: <div style={{ color: "black" }}>{title}</div>,
            value: title,
            disabled: true,
            children: allPermissions[title].map((item, cp) => {
              return {
                value: item.id,
                title: item.name,
              };
            }),
          });
          return acc;
        }, [])
      );
      setData(
        Object.keys(allPermissions).reduce((acc, title, index) => {
          acc.push({
            title: <div style={{ color: "black" }}>{title}</div>,
            value: title,
            disabled: true,
            children: allPermissions[title].map((item, cp) => {
              return {
                value: item.id,
                title: item.name,
              };
            }),
          });
          return acc;
        }, [])
      );
    }
  };


  useEffect(() => {
    setOptionsIcons(
      Object.keys(icons).map((k) => {
        return { label: k, value: k };
      })
    );
  }, []);

  useEffect(() => {
    if (allPermissions && typeof allPermissions == "object") {
      console.log(allPermissions);
      handleGetPermissions();
    }
  }, [allPermissions]);

  useEffect(() => {
    node && setParentMenu(node);
    node && setActiveNode(node.activer === 1); /* console.log(node.name) */

    setFields([
      ...langs?.map((lang, index) => ({
        name: ["title_" + lang.code],
        value: node.name ? node.name[lang.code] : "",
      })),
      {
        name: ["path"],
        value: node.path,
      },
      {
        name: ["parent"],
        value: node.parent,
      },
      {
        name: ["ordre"],
        value: node.ordre,
      },
      {
        name: ["icon"],
        value: node.iconName,
      },
      {
        name: ["permission"],
        value: node.permission_id,
      },
    ]);
  }, [visible, node]);

  return (
    <Drawer
      title={node.title}
      className="MenuDetails"
      width={window.innerWidth > 580 ? 560 : "90%"}
      onClose={onClose}
      open={visible}
      style={{
        paddingBottom: 80,
      }}
      extra={
        <Switch
          checkedChildren={<EyeOutlined />}
          unCheckedChildren={<EyeInvisibleOutlined />}
          checked={activeNode}
          onChange={handleActiveMenu}
          loading={switchLoading}
        />
      }
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        hideRequiredMark
        disabled={!update}
        fields={fields}
      >
        <Row gutter={16}>
          {/* <Col span={24}>
            <Form.Item
              name="title"
              label="Titre"
              rules={[
                {
                  required: true,
                  message: "Veuillez entrer le titre",
                },
              ]}
            >
              <Input placeholder="Veuillez entrer le titre" />
            </Form.Item>
          </Col> */}
          <Col span={24}>
            {langs.map((lang, index) => (
              <Form.Item
                key={index}
                name={`title_${lang.code}`}
                label={`Titre - ${lang.name}`}
                rules={[
                  {
                    required: true,
                    message: `Veuillez entrer le titre pour ${lang.name}`,
                  },
                ]}
              >
                <Input
                  placeholder={`Veuillez entrer le titre pour ${lang.name}`}
                />
              </Form.Item>
            ))}
          </Col>
          <Col span={24}>
            <Form.Item name="path" label="Path">
              <Input placeholder="Veuillez entrer le path" />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item name="parent" label="Menu Parent">
              <TreeSelect
                allowClear
                treeData={menu?.map((item, index) => {
                  return {
                    title: item.title,
                    value: item.id,
                    long: item.children.length,
                    children: item.children.map((child, cp) => {
                      return {
                        title: child.title,
                        value: child.id,
                        long: child.children.length,
                      };
                    }),
                  };
                })}
                placeholder="Veuillez choisir le menu parent"
                treeDefaultExpandAll
                onSelect={(e, values) => {
                  setParentMenu(values);
                  form.setFieldsValue({
                    ordre: values.long + 1,
                  });
                  if (values.children === undefined) {
                    form.setFieldsValue({
                      icon: null,
                    });
                  }
                }}
                onClear={() => {
                  setParentMenu(null);
                  form.setFieldsValue({
                    icon: null,
                    parent: null,
                  });
                }}
              />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item
              name="ordre"
              label="Ordre"
              rules={[
                {
                  required: true,
                  message: "Veuillez choisir l'ordre",
                },
              ]}
            >
              {parentMenu
                ? ordreOptions(parentMenu.long)
                : ordreOptions(menu.length)}
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item name="permission" label="Permission">
              <TreeSelect
                showSearch
                onSearch={(value) => {
                  setPermissions(
                    data
                      .map((item) => {
                        const filteredChildren = item.children.filter(
                          (child) =>
                            child.title
                              .toUpperCase()
                              .indexOf(value.toUpperCase()) > -1
                        );
                        return { ...item, children: filteredChildren };
                      })
                      .filter((item) => item.children.length)
                  );
                }}
                filterTreeNode={(value) => {
                  return true;
                }}
                style={{ width: "100%" }}
                value={value}
                dropdownStyle={{ maxHeight: 400, overflow: "auto" }}
                placeholder="Veuillez choisir les permissions"
                allowClear
                onChange={onChange}
                treeData={permissions}
              />
            </Form.Item>
            <Form.Item name="icon" label="Icône">
              <Select
                showSearch
                filterOption={filterOption}
                filterSort={filterSort}
                // disabled={
                //   !update || !parentMenu || parentMenu.children === undefined
                // }
              >
                {optionsIcons.map((v, i) => (
                  <Option key={i} value={v.value} label={v.label}>
                    <Icon name={v.value} />
                    <span className="ml-2">{v.value}</span>
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>
        {update && (
          <Form.Item style={{ textAlign: "right" }}>
            <>
              <Button
                style={{ marginRight: "10px" }}
                onClick={() => {
                  form.setFieldsValue({
                    title: node.title,
                    path: node.path,
                    parent: node.parent,
                    ordre: node.ordre,
                    icon: node.iconName,
                  });
                  setUpdate(false);
                }}
              >
                Annuler
              </Button>
              <Button type="primary" htmlType="submit" loading={buttonLoading}>
                Envoyer
              </Button>
            </>
          </Form.Item>
        )}
      </Form>
      {!update && (
        <div style={{ width: "100%", textAlign: "right" }}>
          <Button
            style={{ marginRight: "10px" }}
            onClick={() => {
              setUpdate(true);
            }}
          >
            Modifier
          </Button>
          <Popconfirm
            title="Supprimer Menu"
            description="Voulez-vous vraiment supprimer ce menu ?"
            onConfirm={handleDeleteMenu}
            okText="Oui"
            cancelText="Non"
            placement="topRight"
          >
            <Button loading={buttonLoading} danger>
              Supprimer
            </Button>
          </Popconfirm>
        </div>
      )}
    </Drawer>
  );
};

export default MenuDetails;
