import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { default as api } from "@/apis/axiosInterceptor";
import { hideLoader, showLoader } from "@features/loadingSlice";

interface MenuState {
  menus: {}[];
  homeSideMenu: {}[];
  rhSideMenu: {}[];
  achatSideMenu: {}[];
  financeSideMenu: {}[];
  immoSideMenu: {}[];
  projetSideMenu: {}[];
  ticketingSideMenu: {}[];
  accountSideMenu: {}[];
  configSideMenu: {}[];
  profileMenu: {}[];
  nothing: boolean;
}
export interface MenuRecursive {
  id: number;
  designation_fr: string;
  designation_ar?: any;
  ordre?: any;
  menu_parant: number;
  active: number;
  link?: string;
  created_at?: any;
  updated_at?: any;
  deleted_at?: any;
  child_recursive?: MenuRecursive[] | null;
}
export const getAllMenu: any = createAsyncThunk(
  "menus",
  async (_, thunkAPI) => {
    thunkAPI.dispatch(showLoader());
    try {
      let url = `/menus`;
      const resp = await api.get(url);
      thunkAPI.dispatch(hideLoader());
      return resp.data;
    } catch (error: any) {
      thunkAPI.dispatch(hideLoader());
      return thunkAPI.rejectWithValue("something went wrong");
    }
  }
);
export const myMenus: any = createAsyncThunk("menus", async (_, thunkAPI) => {
  try {
    let url = `/menus/myMenu`;
    const resp = await api.get(url);
    localStorage.setItem("menu", JSON.stringify(resp));
    return resp;
  } catch (error) {
    return thunkAPI.rejectWithValue("something went wrong");
  }
});
export const AddMenu: any = createAsyncThunk(
  "menus",
  async (data, thunkAPI) => {
    try {
      let url = `/menus`;
      const resp = await api.post(url, data);
      thunkAPI.dispatch(getAllMenu());
      thunkAPI.dispatch(myMenus());
      
      return resp.data;

    } catch (error) {
      return thunkAPI.rejectWithValue("something went wrong");
    }
  }
);
export const updateMenu: any = createAsyncThunk(
  "menus",
  async (
    data: {
      id: number;
      designation_fr: string;
      ordre: number;
      menu_parant: number;
      link: string;
      icon: string;
    },
    thunkAPI
  ) => {
    try {
      let url = `/menus/${data.id}`;
      const resp = await api.put(url, data);
      thunkAPI.dispatch(getAllMenu());
      thunkAPI.dispatch(myMenus());

      return resp.data;

    } catch (error) {
      return thunkAPI.rejectWithValue("something went wrong");
    }
  }
);
export const deleteMenu: any = createAsyncThunk(
  "menus",
  async (id, thunkAPI) => {
    try {
      let url = `/menus/${id}`;
      const resp = await api.delete(url);
      thunkAPI.dispatch(getAllMenu());
      thunkAPI.dispatch(myMenus());

      return resp.data;

    } catch (error) {
      return thunkAPI.rejectWithValue("something went wrong");
    }
  }
);
export const activeMenu: any = createAsyncThunk(
  "menus",
  async (id, thunkAPI) => {
    try {
      let url = `/menus/activeMenu/${id}`;
      const resp = await api.get(url);
      thunkAPI.dispatch(getAllMenu());
      thunkAPI.dispatch(myMenus());
      return resp.data;
    } catch (error) {
      return thunkAPI.rejectWithValue("something went wrong");
    }
  }
);

const menuSlice = createSlice({
  name: "menu",
  initialState: {
    loading: false,
    error: null,
    data: null,
  },
  reducers: {
    closeDrawer: (state) => {
      state.nothing = false;
    },
    openDrawer: (state) => {
      state.nothing = true;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getAllMenu.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.data = null;
      })
      .addCase(getAllMenu.fulfilled, (state, action) => {
        state.data = action.payload.data;
        state.loading = false;
        state.error = null;
      })
      .addCase(getAllMenu.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        state.data = null;
      });
  },
});

export const { closeDrawer, openDrawer } = menuSlice.actions;

export default menuSlice.reducer;
