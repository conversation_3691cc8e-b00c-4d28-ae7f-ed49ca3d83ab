import dayjs from 'dayjs';

export const eventsData = [
  {
    id: 1,
    title: "Séminaire TIC",
    sector: "Technologie",
    location: "Tunisie",
    start: "2025-03-03T09:00:00",
    end: "2025-03-03T17:00:00",
    status: "planned",
    attendees: ["<EMAIL>"],
    color: "#1890ff",
    type: "event"
  },
];

export const tasks = [
  {
    id: 101,
    title: "Appeler client",
    type: "call",
    start: "2025-06-01T14:00:00",
    end: "2025-06-01T15:00:00",
    status: "done",
    color: "#52c41a",
    assignee: "<PERSON>",
    description: "Confirmation de commande"
  },
];

export const getAllCalendarItems = () => {
  return [...eventsData, ...tasks];
};

export const createEvent = (newEvent) => {
  eventsData = [...eventsData, newEvent];
};

export const updateEvent = (updatedEvent) => {
  if (updatedEvent.type === 'event') {
    eventsData = eventsData.map(event => 
      event.id === updatedEvent.id ? updatedEvent : event
    );
  } else {
    tasks = tasks.map(task => 
      task.id === updatedEvent.id ? updatedEvent : task
    );
  }
};
