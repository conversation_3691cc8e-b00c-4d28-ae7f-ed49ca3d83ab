import { lazy } from "react";
const Tva = lazy(() => import("../pages/tva"));
const MoyensPaiement = lazy(() => import("../pages/moyens-paiement"));
const Vente = lazy(() => import("../pages/vente/index"));
const Clients = lazy(() => import("../pages/vente/gestion-clients"));
const Prospects = lazy(() => import("../pages/vente/gestion-prospects"));
const Achat = lazy(() => import("../pages/achat"));
const Fournisseurs = lazy(() => import("../pages/achat/gestion-fournisseurs"));
const Quotes = lazy(() => import('../pages/sales/quotes'));
const Purchases = lazy(() => import('../pages/sales/purchases'));
const Deliveries = lazy(() => import('../pages/sales/deliveries'));
const Banques = lazy(() => import("../pages/banque"));
const Ribs = lazy(() => import("../pages/ribs"));
const ManageModeReglement = lazy(() => import('../pages/mode-reglement/ManageModeReglement'));

const routes = [
  {
    path: "finance/banque",
    element: <Banques />,
  },
  {
    path: "finance/rib",
    element: <Ribs />,
  },
  {
    path: "finance/tva",
    element: <Tva />,
  },
  {
    path: "finance/moyen-paiement",
    element: <MoyensPaiement />,
  },
  {
    path: "finance/vente",
    element: <Vente />,
  },
  {
    path: "finance/vente/clients",
    element: <Clients />,
  },
  {
    path: "finance/vente/prospects",
    element: <Prospects />,
  },
  {
    path: "finance/achat",
    element: <Achat />,
  },
  {
    path: "finance/achat/fournisseurs",
    element: <Fournisseurs />,
  },
  {
    path: '/finance/sales/quotes',
    element: (<Quotes/>),
  }, 
  {
    path: '/finance/sales/purchases',
    element: (<Purchases/>),
  }, 
  {
    path: '/finance/sales/deliveries',
    element: (<Deliveries/>),
  }, 
  {
    path: "finance/mode-reglement",
    element: <ManageModeReglement />,
  },
];

export default routes;
