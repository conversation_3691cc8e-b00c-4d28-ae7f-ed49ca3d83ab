import { useEffect, useState } from "react";
import { ProForm, ProFormText } from "@ant-design/pro-form";
import { Button, Collapse, message, Spin, Divider } from "antd";
import { DeleteOutlined } from "@ant-design/icons";

const EditScoring = ({ record, onCancel, setDataSource }) => {
  const [scoring, setScoring] = useState(null);
  const [conditions, setConditions] = useState([]);
  const [activeKey, setActiveKey] = useState(["1"]);
  const [form] = ProForm.useForm();

  useEffect(() => {
    if (record) {
      setScoring(record);
      setConditions(record.conditions || [{ id: Date.now(), value: "", points: 0 }]);
      form.resetFields();
    } else {
      message.error("Enregistrement non trouvé");
      onCancel();
    }
  }, [record, form, onCancel]);

  const addCondition = () => {
    const newCondition = { id: Date.now(), value: "", points: 0 };
    setConditions([...conditions, newCondition]);
    setActiveKey([newCondition.id]);
  };

  const removeCondition = (keyToRemove) => {
    setConditions((conditions) => conditions.filter((cond) => cond.id !== keyToRemove));
  };

  const handleSubmit = async (values) => {
    try {
      const updatedScoring = {
        key: values.key,
        label: values.label,
        conditions: conditions.map((cond, index) => ({
          value: values[`condition_value_${index}`] || "",
          points: Number(values[`condition_points_${index}`]) || 0,
        })),
      };

      const updatedData = scoringData.map((item) =>
        item.key === record.key ? updatedScoring : item
      );

      setDataSource(updatedData);
      message.success("Score modifié avec succès!");
      onCancel();
    } catch (error) {
      console.log(error);
      message.error("Erreur lors de la modification");
    }
  };

  if (!scoring) {
    return <Spin size="large" />;
  }

  return (
    <>
      <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", marginBottom: "15px" }}>
        <h2>Modifier un score</h2>
        <Button onClick={addCondition} type="primary">+</Button>
      </div>

      <ProForm
        form={form}
        onFinish={handleSubmit}
        initialValues={{
          key: scoring.key,
          label: scoring.label,
          ...scoring.conditions.reduce((acc, cond, index) => ({
            ...acc,
            [`condition_value_${index}`]: Array.isArray(cond.value) ? cond.value.join(", ") : cond.value,
            [`condition_points_${index}`]: cond.points,
          }), {}),
        }}
      >
        <ProFormText
          name="key"
          label="Clé"
          placeholder="Saisissez la clé"
          rules={[{ required: true }]}
        />
        <ProFormText
          name="label"
          label="Étiquette"
          placeholder="Saisissez l'étiquette"
          rules={[{ required: true }]}
        />

        <Divider>Conditions</Divider>

        {conditions.map((condition, index) => (
          <div key={condition.id} style={{ display: "flex", alignItems: "center", gap: "10px", width: "100%" }}>
            <Collapse
              style={{ flex: 1, marginBottom: "10px" }}
              activeKey={activeKey}
              onChange={(keys) => setActiveKey(keys)}
              items={[
                {
                  key: condition.id,
                  label: `Condition ${index + 1}`,
                  children: (
                    <div className="condition-group">
                      <ProFormText
                        name={`condition_value_${index}`}
                        label="Valeur"
                        placeholder="Ex: TIC ou >50"
                        rules={[{ required: true }]}
                      />
                      <ProFormText
                        name={`condition_points_${index}`}
                        label="Points"
                        placeholder="Ex: 20"
                        rules={[{ required: true, type: "number" }]}
                      />
                    </div>
                  ),
                },
              ]}
            />
            {index !== 0 && (
              <Button onClick={() => removeCondition(condition.id)} danger icon={<DeleteOutlined />} />
            )}
          </div>
        ))}
      </ProForm>
    </>
  );
};

export default EditScoring;