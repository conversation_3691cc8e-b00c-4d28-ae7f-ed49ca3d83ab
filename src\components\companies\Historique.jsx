import {
    FileOutlined,
    MinusCircleOutlined,
    PlusCircleOutlined,
    UploadOutlined,
  } from "@ant-design/icons";
  import { Col, DatePicker, Form, Input, message, Row, Upload } from "antd";
  import { useState } from "react";
  import { useTranslation } from "react-i18next";
  
  const Historique = ({ form, view }) => {
    const { t } = useTranslation();
    const handleAdd = () => {
      const currentdata= form.getFieldValue("historiques") || [];
      const situation = form.getFieldValue("situation");
      const date_info = form.getFieldValue("date_info");
      if (situation &&  date_info) {
        const key = Date.now();
        form.setFieldsValue({
          historiques: [
            ...currentdata,
            {
              key: key,
              situation:situation ,
             date_info:date_info 
            }
          ],
        });
        form.resetFields([
          "situation" ,
          "date_info"
        ]);
      } else {
        message.error(t("Merci de bien vouloir remplir tous les champs."));
      }
    };
    return (
      <>
        <Row gutter={16} style={{  marginBottom: "10px" }}>
         
          <Col span={18}>Situation</Col>
          <Col span={4}>Date</Col>
          <Col span={1}></Col>
        </Row>
        <Form.List name="historiques">
          {(fields, { remove }) => (
            <>
              {fields.map((field, index) => (
                <Row
                  gutter={16}
                  align="middle"
                  key={field.key}
                >
                 
                  <Col span={18}>
                    <Form.Item name={[index, "situation"]} rules={[]}>
                      <Input
                        allowClear={true}
                        disabled={view}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={4}>
                    <Form.Item name={[index, "date_info"]} rules={[]}>
                      <DatePicker
                        allowClear={true}
                        disabled={view}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={1}>
                    {!view && (
                      <Form.Item>
                        <MinusCircleOutlined
                          style={{
                            color: "red",
                            fontSize: "18px",
                            cursor: "pointer",
                          }}
                          onClick={() => remove(index)}
                        />
                      </Form.Item>
                    )}
                  </Col>
                </Row>
              ))}
            </>
          )}
        </Form.List>
  
        {!view && (
          <Row gutter={16} align="middle" style={{ marginBottom: "5px" }}>
            
            <Col span={18}>
              <Form.Item name={`situation`} rules={[]}>
                <Input
                
                  allowClear={true}
                />
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item name={`date_info`} rules={[]}>
                <DatePicker
                
                  allowClear={true}
                />
              </Form.Item>
            </Col>
            <Col span={1}>
              <Form.Item>
                <PlusCircleOutlined
                  style={{
                    color: "green",
                    fontSize: "18px",
                    cursor: "pointer",
                  }}
                  onClick={() => handleAdd()}
                />
              </Form.Item>
            </Col>
          </Row>
        )}
      </>
    );
  };
  
  export default Historique;
  