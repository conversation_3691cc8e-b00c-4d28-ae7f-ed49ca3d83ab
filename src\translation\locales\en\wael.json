{"projects": {"title": "Projects Management", "create": {"title": "Create new project"}, "edit": {"title": "Edit project"}, "show": {"title": "View project"}, "actions": {"add_project": "Add project", "export_projects_steps_progress_between_two_dates_to_excel": "Export projects steps progress between two dates to Excel"}, "columns": {"enterprise": "Enterprise", "department": "Department", "project": "Project", "client": "Client", "parent_project": "Parent project", "project_name": "Project name", "tender_number": "Tender number", "service_order_date": "Service order date", "execution_duration": "Execution duration (days)", "share_link": "Share link", "contract_signature": "Contract signature", "provisional_reception": "Provisional reception", "execution_provisional_duration": "Execution provisional duration (days)", "final_reception": "Final reception", "warranty_duration": "Warranty duration", "maintenance_contract_option": "With maintenance contract", "maintenance_contract": "Maintenance contract", "participation_letter": "Participation letter", "good_execution_letter": "Good execution letter", "date_range": "Date range"}, "messages": {"enterprise_required": "Please select an enterprise", "department_required": "Please select a department", "client_required": "Please select a client", "project_name_required": "Please enter project name", "date_range_required": "Please select a date range"}, "placeholders": {"select_enterprise": "Select enterprise", "select_department": "Select department", "select_client": "Select client", "select_parent_project": "Select parent project", "enter_project_name": "Enter project name", "enter_tender_number": "Enter tender number", "enter_execution_duration": "Enter execution duration", "enter_provisional_execution_duration": "Enter provisional execution duration", "enter_share_link": "Enter share link", "enter_warranty_duration": "Enter warranty duration"}}, "project_steps": {"title": "Project steps for", "actions": {"add_step": "Add step", "close": "Close", "stop": "Stop", "advance": "Advance"}, "columns": {"order": "Order", "title": "Title", "start_date": "Start date", "end_date": "End date", "duration": "Duration (days)", "payment": "Payment", "status": "Status", "closing_date": "Closing date", "stop_date": "Stop date", "description": "Description", "progress": "Progress", "progression_type": "Progression Type", "advance_percentage": "Advancement Percentage", "advance_date": "Advancement Date", "suspension_date": "Suspension Date", "resumption_date": "Resumption Date", "suspension_reason": "Suspension Reason"}, "status": {"unfinished": "Unfinished", "closed_on": "Closed on "}, "amount_with_value": "Dinars", "percentage_with_value": "%", "messages": {"confirm_close": "Confirm closing", "confirm_stop": "Confirm stop", "stop_warning": "Are you sure you want to stop this step?", "close_warning": "Are you sure you want to close this step?", "advance_warning": "Are you sure you want to advance this step?", "closing_date_required": "Closing date is required", "stop_date_required": "Stop date is required", "order_required": "Order is required", "title_required": "Title is required", "start_date_required": "Start date is required", "end_date_required": "End date is required", "duration_positive": "Duration must be positive", "confirm_advance": "Confirm advancement", "progression_type_required": "Please select a progression type.", "advance_percentage_required": "Please enter an advancement percentage.", "advance_date_required": "Please select an advancement date.", "suspension_reason_required": "Please enter the suspension reason.", "progression": "Progression"}, "create": {"title": "Create step for project "}, "edit": {"title": "Edit project step"}, "placeholders": {"select_order": "Select order", "enter_title": "Enter title", "duration_calculated": "Duration calculated automatically", "enter_percentage": "Enter percentage", "enter_amount": "Enter amount", "progression_type": "Select a type", "percentage": "Enter percentage", "suspension_reason": "Enter reason"}, "options": {"advancement": "Advancement", "suspension": "Suspension", "resumption": "Resumption"}, "currency": "Dinars"}, "common": {"columns": {"action": "Actions"}, "actions": {"steps": "Steps", "view": "View", "edit": "Edit", "delete": "Delete", "save": "Save", "cancel": "Cancel", "export_excel": "Export to Excel"}, "messages": {"confirm_delete": "Are you sure you want to delete this item?", "success": "Operation successful", "error": "An error occurred", "loading": "Processing..."}, "placeholders": {"select": "Select"}, "yes": "Yes", "no": "No", "with": "With", "without": "Without"}}