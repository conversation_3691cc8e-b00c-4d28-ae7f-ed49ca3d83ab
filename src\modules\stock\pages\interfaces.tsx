import React from "react";

export interface IArticle {
  key: React.Key;
  id: number;
  code: string;
  designation: string;
  type: string;
  status: string;
  category_id?: number;
  sub_category_id?: number;
  purchase_unit_id?: number;
  stock_unit_id?: number;
  sale_unit_id?: number;
  description?: string;
  supplier_id?: number;
  purchase_price?: number;
  prime_cost?: number;
  price_excluding_tax?: number;
  price_including_tax?: number;
  marge?:string;
  vat_rate?: number;
  costs?: number;
  min_threshold?: number;
  max_threshold?: number;
  // deleted_at?: Date;
  // created_at?: Date;
  // updated_at?: Date;
}

export interface IDepot{
  key?: React.Key;
  id: number;
  designation: string;
  entreprise_id: number;
  adresse: string;
  manager_id: number;
  type_depot_id:number;
  phone:string;
}

export interface IFamille {
  id: number;
  code: string;
  libelle: string;
  tva_id: number;
  allow_negative_stock: boolean;
}


export interface ISousFamille {
  id: number;
  code: string;
  libelle: string;
  famille_id:number;
  tva_id: number;
  allow_negative_stock: boolean;
}