import { Card, Spin } from "antd";

const ViewPlanActivity = ({ record }) => {
  if (!record) return <Spin size="large" />;

  return (
    <>
      <h2>Détails du plan d'activité</h2>
      <Card title={record.name} bordered={false}>
        <p><strong>Nom:</strong> {record.name}</p>
        <p><strong>Description:</strong> {record.description}</p>

        {record.activities && record.activities.length > 0 ? (
          <>
            <h3>Activités</h3>
            {record.activities.map((activity, index) => (
              <Card key={index} style={{ marginBottom: "10px" }}>
                <p><strong>Nom:</strong> {activity.nom}</p>
                <p><strong>Résumé:</strong> {activity.resume}</p>
                <p><strong>Planifié pour:</strong> {activity.planifier ? activity.planifier.toString() : "Non défini"}</p>
              </Card>
            ))}
          </>
        ) : (
          <p>Aucune activité enregistrée.</p>
        )}
      </Card>
    </>
  );
};

export default ViewPlanActivity;
