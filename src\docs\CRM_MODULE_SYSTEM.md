# CRM Module-Based Navigation System

## Overview

This document describes the implementation of a dynamic, module-based navigation system for the CRM application that leverages the module properties added to the routes for better organization and user experience.

## Architecture

### 1. Core Components

#### `src/utils/crmModuleUtils.jsx`

- **Purpose**: Central utility functions for module management
- **Key Functions**:
  - `MODULE_CONFIG`: Configuration mapping for all CRM modules
  - `extractModulesFromRoutes()`: Extracts unique modules from route definitions
  - `getPrimaryRouteForModule()`: Gets the main route for a module
  - `handleModuleSelection()`: Manages module selection and navigation
  - `clearModuleSelection()`: Clears module filter
  - `getSelectedModule()`: Gets currently selected module

#### `src/pages/Home/index.jsx` (Updated MenusCRM)

- **Purpose**: Dynamic home page that generates module cards from CRM routes
- **Features**:
  - Automatically generates module cards based on available routes
  - Handles module selection with localStorage persistence
  - Provides French titles and descriptions for each module
  - Maintains backward compatibility with non-CRM modules

#### `src/layouts/SideNav/crm-sidenav.jsx` (Updated)

- **Purpose**: Dynamic sidebar navigation that filters based on selected module
- **Features**:
  - `useDynamicSideNav()` hook for reactive navigation
  - Automatic route filtering based on selected module
  - Real-time updates when module selection changes
  - French labels for all routes

#### `src/components/CrmModuleSelector.jsx`

- **Purpose**: Reusable module selector component
- **Features**:
  - Dropdown selector for CRM modules
  - Clear button to show all modules
  - Real-time updates with localStorage changes
  - Customizable styling and behavior

### 2. Module Configuration

The system supports 15 distinct CRM modules:

1. **dashboard** - Tableau de bord
2. **activities** - Gestion des activités
3. **leads** - Génération des leads
4. **contacts** - Gestion des contacts
5. **opportunities** - Gestion des opportunités
6. **companies** - Gestion des entreprises
7. **events** - Séminaires et évènements
8. **geography** - Gestion géographique
9. **diaspora** - Gestion de la diaspora
10. **documents** - Gestion documentaire
11. **reports** - Rapports et statistiques
12. **settings** - Paramètres et configuration
13. **projects** - Gestion des projets
14. **scoring** - Gestion du scoring
15. **calendar** - Agenda partagé

## Usage

### 1. Module Selection from Home Page

```javascript
// User clicks on a module card
// System automatically:
// 1. Stores selection in localStorage
// 2. Filters sidebar navigation
// 3. Navigates to module's primary route
// 4. Updates all components listening to changes
```

### 2. Sidebar Navigation Filtering

```javascript
// Sidebar automatically shows only routes for selected module
// If no module selected, shows all routes
// Updates in real-time when selection changes
```

### 3. Using the Module Selector Component

```jsx
import CrmModuleSelector from '../components/CrmModuleSelector';

// Basic usage
<CrmModuleSelector />

// With custom styling
<CrmModuleSelector 
  style={{ margin: '10px' }}
  placeholder="Choisir un module"
  showClearButton={true}
  size="large"
/>
```

## Integration Points

### 1. localStorage Keys

- `selectedCrmModule`: Stores the currently selected module name

### 2. Events

- `storage`: Standard storage event for cross-component communication
- `crmModuleChanged`: Custom event for CRM-specific module changes

### 3. Route Structure

Each route must have a `module` property matching one of the configured modules:

```javascript
{
  path: "crm/contacts",
  module: "contacts", // Must match MODULE_CONFIG key
  element: <ContactsComponent />
}
```

## Benefits

1. **Dynamic Organization**: Navigation automatically organizes based on functional modules
2. **Reduced Clutter**: Users see only relevant menu items for their current task
3. **Improved UX**: Logical grouping makes it easier to find related functionality
4. **Maintainable**: Adding new routes automatically updates navigation
5. **Flexible**: Module selection persists across sessions
6. **Scalable**: Easy to add new modules or reorganize existing ones

## Future Enhancements

1. **Role-based Module Access**: Filter modules based on user permissions
2. **Module Analytics**: Track module usage patterns
3. **Custom Module Views**: Allow users to create custom module groupings
4. **Module Search**: Add search functionality within modules
5. **Module Favorites**: Allow users to mark frequently used modules
