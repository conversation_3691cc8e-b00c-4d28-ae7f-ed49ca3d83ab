import React, { useEffect, useState } from "react";
import {
  Button,
  Drawer,
  Input,
  Form,
  Row,
  Col,
  Select,
  Divider,
  Space,
} from "antd";
import { PlusOutlined } from "@ant-design/icons";
import { useDispatch, useSelector } from "react-redux";
import { addPermission } from "@/modules/parameter/features/permissions/permissionSlice";
const { Option } = Select;
const PermissionCreate = ({
  visible,
  setVisible,
  permissions,
  messageApi,
}) => {
  const dispatch = useDispatch();
  const [groupeForm] = Form.useForm();
  const [buttonLoading, setButtonLoading] = useState<boolean>(false);
  const [groupes, setGroupes] = useState([]);
  //select search and sort
  const filterOption = (input, option) =>
    (option?.label ?? "").toLowerCase().includes(input.toLowerCase());
  const filterSort = (optionA, optionB) =>
    (optionA?.label ?? "")
      .toLowerCase()
      .localeCompare((optionB?.label ?? "").toLowerCase());

  const onClose = () => {
    setVisible(false);
  };

  const handleAddGroupe = (e) => {
    setGroupes([...groupes, e.groupe]);
    groupeForm.resetFields();
  };

  const handleAddPermission = (e) => {
    setButtonLoading(true);
    messageApi.open({
      key: "updatable",
      type: "loading",
      content: "Loading...",
    });
    dispatch(addPermission(e))
      .unwrap()
      .then((originalPromiseResult) => {
        messageApi.open({
          key: "updatable",
          type: "success",
          content: "Success ",
          duration: 2,
        });
        setButtonLoading(false);
        setVisible(false);
      })
      .catch((rejectedValueOrSerializedError) => {
        messageApi.open({
          key: "updatable",
          type: "error",
          content: "Error",
          duration: 2,
        });
        setButtonLoading(false);
        console.log(rejectedValueOrSerializedError);
        return [];
      });
  };

  useEffect(() => {
    permissions &&
      setGroupes(
        permissions.map((item) => {
          return item.title;
        })
      );
  }, [visible]);
  return (
    <Drawer
      title="Nouvelle permission"
      width={window.innerWidth > 580 ? 560 : "90%"}
      className="CautionForm"
      onClose={onClose}
      open={visible}
      styles={{ body: { paddingBottom: 80 } }}
    >
      <Form layout="vertical" requiredMark={false} onFinish={handleAddPermission}>
        <Row gutter={16}>
          <Col xs={24} sm={24} md={24} lg={24} xl={24} xxl={24}>
            <Form.Item
              name="groupe"
              label="Groupe"
              rules={[
                {
                  required: true,
                  message: "Veuillez entrer le Rôle",
                },
              ]}
            >
              <Select
                showSearch
                placeholder="Groupe"
                optionFilterProp="children"
                filterOption={filterOption}
                filterSort={filterSort}
                options={groupes?.map((item) => {
                  return {
                    value: item,
                    label: item,
                  };
                })}
                dropdownRender={(menu) => (
                  <>
                    {menu}
                    <Divider style={{ margin: "8px 0" }} />
                    <Form
                      form={groupeForm}
                      style={{ marginBottom: "-20px" }}
                      onFinish={handleAddGroupe}
                    >
                      <Row gutter={6}>
                        <Col span={21}>
                          <Form.Item name="groupe">
                            <Input placeholder="Nouveau groupe" />
                          </Form.Item>
                        </Col>
                        <Col span={3}>
                          <Form.Item>
                            <Button
                              className="w-full"
                              type="primary"
                              htmlType="submit"
                              icon={<PlusOutlined />}
                            />
                          </Form.Item>
                        </Col>
                      </Row>
                    </Form>
                  </>
                )}
              />
            </Form.Item>
          </Col>
          <Col xs={24} sm={24} md={24} lg={24} xl={24} xxl={24}>
            <Form.Item
              name="label"
              label="Permission Label"
            >
              <Input placeholder="Nom du permission" />
            </Form.Item>
          </Col>
          <Col xs={24} sm={24} md={24} lg={24} xl={24} xxl={24}>
            <Form.Item
              name="name"
              label="Permission"
              rules={[
                {
                  required: true,
                  message: "Veuillez entrer le nom du permission",
                },
              ]}
            >
              <Input placeholder="Nom du permission" />
            </Form.Item>
          </Col>
          <Form.Item
            style={{
              width: "100%",
              textAlign: "right",
              marginRight: "10px",
            }}
          >
            <Button htmlType="reset" style={{ marginRight: "10px" }}>
              Annuler
            </Button>
            <Button type="primary" htmlType="submit" loading={buttonLoading}>
              Ajouter
            </Button>
          </Form.Item>
        </Row>
      </Form>
    </Drawer>
  );
};

export default PermissionCreate;
