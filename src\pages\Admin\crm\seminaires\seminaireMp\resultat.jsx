import { EyeFilled, UploadOutlined } from "@ant-design/icons";
import { ModalForm } from "@ant-design/pro-components";
import { <PERSON><PERSON>, Card, Col, DatePicker, Form, Input, Row, Upload } from "antd";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

function ResultatForm({ open, onCancel, record }) {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const props = {
    name: "file",
     accept :".pdf" ,
    maxCount :1,
    action: "https://660d2bd96ddfa2943b33731c.mockapi.io/api/upload",
    headers: {
      authorization: "authorization-text",
    },
    onChange(info) {
      if (info.file.status !== "uploading") {
        console.log(info.file, info.fileList);
      }
      if (info.file.status === "done") {
        message.success(`${info.file.name} file uploaded successfully`);
      } else if (info.file.status === "error") {
        message.error(`${info.file.name} file upload failed.`);
      }
    },
  };
  return (
    <ModalForm
      title={t("Résultat du séminaire")}
      form={form}
      open={open}
      width="70%"
      modalProps={{
        style: { top: 20 },
      }}
      //   onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.save"),
          resetText: t("common.actions.cancel"),
        },
      }}
    >
      <Card className="mt-2">
        <Row gutter={16} style={{ marginBottom: "10px" }}>
          <Col span={8}> Nombre d'entreprises présentes </Col>
          <Col span={4}>
            <Input />
          </Col>
        </Row>
        <Row gutter={16} style={{ marginBottom: "10px" }}>
          <Col span={8}>Multiplicateurs présents </Col>
          <Col span={16}>
            <Input.TextArea rows={2} />
          </Col>
        </Row>
        <Row gutter={16} style={{ marginBottom: "10px" }}>
          <Col span={8}>Institutionnels présents</Col>
          <Col span={16}>
            <Input.TextArea rows={2} />
          </Col>
        </Row>
        <Row gutter={16} style={{ marginBottom: "10px" }}>
          <Col span={8}>Publication dans la presse locale</Col>
          <Col span={16}>
            <Input.TextArea rows={2} />
          </Col>
        </Row>
       
          <Row gutter={16} style={{ marginBottom: "10px" }}>
          <Col span={8}>Evaluation et recommandations</Col>
          <Col span={8}>
             <Upload {...props}>
              <Button icon={<UploadOutlined />}>Déposer la liste des présents(PDF)</Button>
            </Upload>
          </Col>
          <Col span={8}>
          <Button icon={<EyeFilled />}>Afficher la liste des présents</Button>
          </Col>
        </Row>
        <Row gutter={16} style={{ marginBottom: "10px" }}>
          <Col span={8}>Evaluation et recommandations</Col>
          <Col span={16}>
            <Input.TextArea rows={3} />
          </Col>
          
        </Row>
           <Row gutter={16} style={{ marginBottom: "10px" }}>
                  <Col span={8}>Nombre de contacts réalisés</Col>
                  <Col span={4}>
                    <Input  />
                  </Col>
                  
                </Row>
      </Card>
    </ModalForm>
  );
}
export default ResultatForm;
