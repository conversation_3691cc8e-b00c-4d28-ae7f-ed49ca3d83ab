import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Select, DatePicker, Row, Col, Collapse, Divider } from 'antd';
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import dayjs from 'dayjs'; // Import dayjs for date handling

const { Option } = Select;
const { TextArea } = Input;

const ProfileEdit = ({ initialValues, onUpdate }) => {
  const [form] = Form.useForm();
  const [positions, setPositions] = useState(initialValues.positionHistory || [{ id: Date.now() }]);
  const [activeKey, setActiveKey] = useState([]);

  useEffect(() => {
    if (initialValues.positionHistory && initialValues.positionHistory.length > 0) {
      const positionsWithIds = initialValues.positionHistory.map(pos => ({
        ...pos,
        id: pos.id || Date.now() + Math.random(),
        startDate: pos.startDate ? dayjs(pos.startDate) : null, // Convert to dayjs
        endDate: pos.endDate ? dayjs(pos.endDate) : null, // Convert to dayjs
      }));
      setPositions(positionsWithIds);
    } else {
      setPositions([{ id: Date.now() }]);
    }
  }, [initialValues]);

  useEffect(() => {
    if (positions.length > 0) {
      const initialFormValues = { ...initialValues };
      positions.forEach(pos => {
        if (pos.id) {
          initialFormValues[`startDate_${pos.id}`] = pos.startDate ? dayjs(pos.startDate) : null;
          initialFormValues[`endDate_${pos.id}`] = pos.endDate ? dayjs(pos.endDate) : null;
          initialFormValues[`company_${pos.id}`] = pos.company;
          initialFormValues[`position_${pos.id}`] = pos.position;
          initialFormValues[`description_${pos.id}`] = pos.description;
        }
      });
      form.setFieldsValue(initialFormValues);
    }
  }, [positions, initialValues, form]);

  const handleFinish = (values) => {
    const formattedValues = {
      ...values,
      positionHistory: positions.map((pos) => ({
        startDate: values[`startDate_${pos.id}`] ? values[`startDate_${pos.id}`].format('YYYY-MM-DD') : null,
        endDate: values[`endDate_${pos.id}`] ? values[`endDate_${pos.id}`].format('YYYY-MM-DD') : null,
        company: values[`company_${pos.id}`],
        position: values[`position_${pos.id}`],
        description: values[`description_${pos.id}`],
      })),
    };
    onUpdate(formattedValues);
  };

  const addPosition = () => {
    const newPosition = { id: Date.now() };
    setPositions([...positions, newPosition]);
    setActiveKey([newPosition.id.toString()]);
  };

  const removePosition = (idToRemove) => {
    setPositions(positions.filter(pos => pos.id !== idToRemove));
  };

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleFinish}
      initialValues={initialValues}
    >
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            label="Prénom"
            name="firstName"
            rules={[{ required: true, message: 'Ce champ est requis' }]}
          >
            <Input placeholder="Prénom de la compétence" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label="Nom"
            name="lastName"
            rules={[{ required: true, message: 'Ce champ est requis' }]}
          >
            <Input placeholder="Nom de la compétence" />
          </Form.Item>
        </Col>
      </Row>

      <Form.Item label="Adresse" name="address">
        <TextArea rows={2} placeholder="Adresse complète" />
      </Form.Item>

      <Row gutter={16}>
        <Col span={8}>
          <Form.Item label="Téléphone" name="phone">
            <Input placeholder="Numéro de téléphone" />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item label="Email" name="email" rules={[{ type: 'email' }]}>
            <Input placeholder="Adresse email" />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item label="Âge" name="age">
            <Input placeholder="Âge approximatif" type="number" />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item label="Poste actuel" name="currentPosition">
            <Input placeholder="Poste occupé actuellement" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Entreprise" name="company">
            <Input placeholder="Nom de l'entreprise actuelle" />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item label="Pays" name="country">
            <Select placeholder="Sélectionner le pays">
              <Option value="Tunisie">Tunisie</Option>
              <Option value="France">France</Option>
              <Option value="Canada">Canada</Option>
              <Option value="Allemagne">Allemagne</Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Ville" name="city">
            <Input placeholder="Ville de résidence" />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item label="Secteur" name="sector">
            <Select placeholder="Sélectionner le secteur">
              <Option value="TIC">TIC</Option>
              <Option value="Finance">Finance</Option>
              <Option value="Industrie">Industrie</Option>
              <Option value="Recherche">Recherche</Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Expérience" name="experience">
            <Select placeholder="Niveau d'expérience">
              <Option value="Junior">Junior</Option>
              <Option value="Senior">Senior</Option>
            </Select>
          </Form.Item>
        </Col>
      </Row>

      <Form.Item label="Compétences" name="competences">
        <Select
          mode="tags"
          style={{ width: '100%' }}
          placeholder="Entrez les compétences (séparées par des virgules)"
        />
      </Form.Item>

      <Divider>Historique des postes occupés</Divider>

      <div style={{ display: 'flex', justifyContent: 'flex-end', marginBottom: 16 }}>
        <Button onClick={addPosition} icon={<PlusOutlined />} type="primary">
        </Button>
      </div>

      {positions?.map((position, index) => (
        <div key={position.id} style={{ display: 'flex', alignItems: 'flex-start', gap: '10px', width: '100%', marginBottom: 16 }}>
          <Collapse
            style={{ flex: 1 }}
            activeKey={activeKey}
            onChange={(keys) => setActiveKey(keys)}
            items={[
              {
                key: position?.id?.toString(),
                label: position.position ? position.position : `Poste ${index + 1}`,
                children: (
                  <>
                    <Row gutter={16}>
                      <Col span={12}>
                        <Form.Item
                          name={`startDate_${position.id}`}
                          label="Date début"
                        >
                          <DatePicker style={{ width: '100%' }} format="YYYY-MM-DD" />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          name={`endDate_${position.id}`}
                          label="Date fin"
                        >
                          <DatePicker style={{ width: '100%' }} format="YYYY-MM-DD" />
                        </Form.Item>
                      </Col>
                    </Row>
                    <Form.Item
                      name={`position_${position.id}`}
                      label="Poste occupé"
                    >
                      <Input placeholder="Intitulé du poste" />
                    </Form.Item>
                    <Form.Item
                      name={`company_${position.id}`}
                      label="Entreprise"
                    >
                      <Input placeholder="Nom de l'entreprise" />
                    </Form.Item>
                    <Form.Item
                      name={`description_${position.id}`}
                      label="Description"
                    >
                      <TextArea rows={3} placeholder="Description des responsabilités" />
                    </Form.Item>
                  </>
                ),
              },
            ]}
          />
          <Button
            onClick={() => removePosition(position.id)}
            danger
            icon={<DeleteOutlined />}
            style={{ marginTop: 16 }}
          />
        </div>
      ))}

      <Form.Item>
        <div style={{ textAlign: 'right' }}>
          <Button type="primary" htmlType="submit">
            Mettre à jour
          </Button>
        </div>
      </Form.Item>
    </Form>
  );
};

export default ProfileEdit;