import { ModalForm } from "@ant-design/pro-components";
import { useTranslation } from "react-i18next";
import { DatePicker, Form } from "antd";
import dayjs from "dayjs";
import { useDispatch } from "react-redux";
import { useState } from "react";
import { exportProjects } from "../../features/projects/projectSlice";

const { RangePicker } = DatePicker;

const ExportStepProgress = ({ visible, setVisible, formRef, messageApi }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const [exportLoading, setExportLoading] = useState(false);

  // export excel
  const handleExportExcel = async (values) => {
    setExportLoading(true);
    messageApi.loading({ content: t("common.messages.loading") });
    try {
      // Récupérer les paramètres de filtrage actuels de la table
      const params = formRef.current?.getFieldsValue();
      let dateRange = values.dateRange.map((date) =>
        dayjs(date).format("YYYY-MM-DD")
      );
      params.start_date = dateRange[0];
      params.end_date = dateRange[1];
      // Appeler l'action d'export avec les mêmes paramètres que la table
      const response = await dispatch(exportProjects(params)).unwrap();

      // Créer un lien de téléchargement
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute(
        "download",
        `steps_progress_export_${new Date().toISOString().slice(0, 10)}.xlsx`
      );
      document.body.appendChild(link);
      link.click();

      // Nettoyer
      link.parentNode?.removeChild(link);
      window.URL.revokeObjectURL(url);

      setVisible(false);
      messageApi.success(t("common.messages.success"));
    } catch (error) {
      console.error("Export error:", error);
      messageApi.error(t("common.messages.error"));
    } finally {
      setExportLoading(false);
    }
  };

  return (
    <ModalForm
      title={t(
        "projects.actions.export_projects_steps_progress_between_two_dates_to_excel"
      )}
      open={visible}
      onFinish={handleExportExcel}
      modalProps={{
        onCancel: () => {
          setVisible(false);
        },
        width: 650,
        destroyOnClose: true,
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.export_excel"),
          resetText: t("common.actions.cancel"),
        },
      }}
      loading={exportLoading}
    >
      <Form.Item
        name="dateRange"
        label={t("projects.columns.date_range")}
        rules={[
          {
            required: true,
            message: t("projects.messages.date_range_required"),
          },
        ]}
      >
        <RangePicker
          style={{ width: "100%" }}
          // disabledDate={(current) => current && current > dayjs().endOf('day')}
        />
      </Form.Item>
    </ModalForm>
  );
};

export default ExportStepProgress;
