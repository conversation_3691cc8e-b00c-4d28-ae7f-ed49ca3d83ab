import {createAsyncThunk, createSlice} from "@reduxjs/toolkit";
import { default as api } from "@/apis/axiosInterceptor";

export const getAllTiers : any = createAsyncThunk(
  "tiers/fetchAll",
  async (_, thunkAPI) => {
    try {
      const response = await api.get(`/tier-all`);
      return response.data;
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error?.message || "An error occurred.");
    }
  }
);

export const tierSlice = createSlice({
  name: 'tiers',
  initialState: {
    loading: false,
    error: null,
    data: [],
  },
  reducers: {},
  extraReducers: builder => {
       builder
      .addCase(getAllTiers.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getAllTiers.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
        state.data = action.payload;
      })
      .addCase(getAllTiers.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  }
})

export default tierSlice.reducer

