import { useTranslation } from "react-i18next";
import { Form, Modal, Tabs, TabsProps } from "antd";
import TierInfoGeneralForm from "@src/modules/finance/components/TierInfoGeneralForm";
import ContactForm from "@src/modules/finance/components/ContactForm";
import { useEffect } from "react";

const ShowFournisseurModal = ({ open, onCancel, fournisseur }) => {
  const { t } = useTranslation();
  const [formRef] = Form.useForm();
  const items: TabsProps["items"] = [
    {
      key: "1",
      label: (
        <span style={{ fontSize: "16px" }}>
          {t("common.steps.general_info")}
        </span>
      ),
      children: <TierInfoGeneralForm disabled={true} tier={fournisseur} />,
    },
    fournisseur?.contacts && {
      key: "2",
      label: (
        <span style={{ fontSize: "16px" }}>
          {t("common.steps.contacts_info")}
        </span>
      ),
      children: (
        <ContactForm disabled={true} contacts={fournisseur?.contacts} />
      ),
    },
  ];

  useEffect(() => {
    if (fournisseur) {
      formRef.setFieldsValue(fournisseur);
    }
  }, [fournisseur]);

  return (
    <Modal
      title={t("achat.fournisseur.view")}
      open={open}
      onOk={onCancel}
      onCancel={onCancel}
      width={900}
    >
      <div className="px-2">
        <Form layout="vertical" form={formRef} disabled>
          <Tabs defaultActiveKey="1" items={items} />
        </Form>
      </div>
    </Modal>
  );
};

export default ShowFournisseurModal;
