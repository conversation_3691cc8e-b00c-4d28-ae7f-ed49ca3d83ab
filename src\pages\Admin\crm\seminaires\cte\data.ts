import dayjs from "dayjs";

const ctes = [
  {
    id: 1,
    nom_prenom: "Nom Prénom",
    pays_id: 1,
    poste_id : 1 ,
    secteur_id : 1 ,
    region: "nom de la région ",
    responsable : "resposable Fipa",
    propose_par : "Proposé par",
    objectifs : " Ad distinctio cupiditate et accusantium perferendis quo nihil voluptas ea delectus corrupti ut praesentium quisquam sit earum obcaecati sit doloribus culpa. ",
    theme: "Test",
    lieu: "lieu du séminaire ",
    action_conjointe : 1 ,
    date_debut: dayjs("2025-01-10"),
    date_fin: dayjs("2025-01-10"),
  
  },
];
const pays = [
  { id: 1, nom: "France" },
  { id: 2, nom: "Italie" },
  { id: 3, nom: "Allemagne" },
  { id: 4, nom: "Espagne" },
  { id: 5, nom: "États-Unis" },
  { id: 6, nom: "Canada" },
  { id: 7, nom: "<PERSON><PERSON><PERSON>" },
  { id: 8, nom: "Maroc" },
  { id: 9, nom: "Libye" },
  { id: 10, nom: "Turquie" },
  { id: 11, nom: "Arabie Saoudite" },
  { id: 12, nom: "Émirats arabes unis" },
  { id: 13, nom: "Qatar" },
  { id: 14, nom: "Égypte" },
  { id: 15, nom: "Chine" },
  { id: 16, nom: "Japon" },
  { id: 17, nom: "Corée du Sud" },
  { id: 18, nom: "Belgique" },
  { id: 19, nom: "Suisse" },
  { id: 20, nom: "Russie" },
];

const postes =[
   { id: 1, nom: "poste" },
] ;
const secteurs = [
  { id: 1, secteur: "Industries diverses" },
  { id: 2, secteur: "Technologie" },
  { id: 3, secteur: "Services" },
  { id: 4, secteur: "Tourisme" },
  { id: 5, secteur: "Agriculture" },
  { id: 6, secteur: "Energie" },
];
export {secteurs, ctes, pays ,postes };
