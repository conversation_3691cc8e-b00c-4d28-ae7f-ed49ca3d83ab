import React, { useState, useEffect, useRef } from 'react';
import { Card, Form, Input, Button, Select, Space, Divider, Tag, Tooltip, Alert } from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';
import RichTextEditor from './RichTextEditor';

const { TextArea } = Input;
const { Option } = Select;

const VARIABLES = {
  system: [
    { name: 'company.name', description: 'Nom de votre entreprise' },
    { name: 'company.phone', description: 'Numéro de téléphone de votre entreprise' },
    { name: 'company.email', description: 'Email de support' },
    { name: 'user.name', description: 'Nom de l\'utilisateur actuel' },
    { name: 'date.current', description: 'Date actuelle' }
  ],
  contacts: [
    { name: 'contact.first_name', description: 'Prénom du contact' },
    { name: 'contact.last_name', description: 'Nom du contact' },
    { name: 'contact.email', description: 'Email du contact' },
    { name: 'contact.phone', description: 'Téléphone du contact' },
    { name: 'contact.company', description: 'Entreprise du contact' }
  ],
  accounts: [
    { name: 'account.name', description: 'Nom du compte' },
    { name: 'account.industry', description: 'Secteur d\'activité' },
    { name: 'account.website', description: 'Site web' },
    { name: 'account.primary_contact', description: 'Contact principal' }
  ],
  leads: [
    { name: 'lead.name', description: 'Nom du prospect' },
    { name: 'lead.company', description: 'Entreprise du prospect' },
    { name: 'lead.status', description: 'Statut du prospect' },
    { name: 'lead.score', description: 'Score du prospect' }
  ]
};

const EmailTemplateEdit = ({ template, onUpdate, onCancel }) => {
  const [form] = Form.useForm();
  const [selectedModule, setSelectedModule] = useState(template?.module || null);
  const [availableVariables, setAvailableVariables] = useState([]);
  const [cursorPosition, setCursorPosition] = useState(null);
  const editorRef = useRef();

  useEffect(() => {
    if (template) {
      form.setFieldsValue({
        name: template.name,
        description: template.description,
        subject: template.subject,
        module: template.module,
        content: template.content,
      });
      setSelectedModule(template.module);
    }
  }, [template, form]);

  useEffect(() => {
    const variables = [
      ...VARIABLES.system,
      ...(selectedModule ? VARIABLES[selectedModule] : [])
    ];
    setAvailableVariables(variables);
  }, [selectedModule]);

  const handleSubmit = (values) => {
    const usedVariables = [];
    const variableRegex = /\{\{([^}]+)\}\}/g;
    let match;
    
    while ((match = variableRegex.exec(values.content))) {
      if (match[1] && !usedVariables.includes(match[1])) {
        usedVariables.push(match[1]);
      }
    }

    const variableMap = {};
    Object.values(VARIABLES).forEach(category => {
      category.forEach(variable => {
        variableMap[variable.name] = variable.description;
      });
    });

    function replaceVariables(text) {
      return text.replace(/\{\{([^}]+)\}\}/g, (match, variableName) => {
        return variableMap[variableName] || match; 
      });
    }

    const outputText = replaceVariables(values.content);

    const updatedTemplate = {
      ...template,
      ...values,
      module: selectedModule,
      variables: usedVariables,
      content: outputText,
      updatedAt: new Date().toISOString(),
      category: selectedModule || 'general'
    };
    
    onUpdate(updatedTemplate);
  };

  const handleEditorClick = (editorInstance) => {
    if (editorInstance) {
      editorRef.current = editorInstance;
      const selection = editorInstance.getSelection();
      setCursorPosition(selection.getRng());
    }
  };

  const insertVariable = (variableName) => {
    if (!editorRef.current) return;
    
    const editor = editorRef.current;
    const variableTag = `{{${variableName}}}`;
    
    if (cursorPosition) {
      editor.selection.setRng(cursorPosition);
    }
    
    editor.insertContent(variableTag);
    editor.focus();
  };

  return (
    <div style={{ padding: '0 24px' }}>
      <h2 style={{ marginBottom: 24 }}>Modifier le modèle d'email</h2>
      
      <Alert
        message="Guide des variables"
        description={
          <div>
            <p>Ajoutez du contenu dynamique en utilisant des variables entre doubles accolades :</p>
            <ul>
              <li><strong>{'{{contact.first_name}}'}</strong> - Prénom du contact</li>
              <li><strong>{'{{company.phone}}'}</strong> - Téléphone de votre entreprise</li>
              <li><strong>{'{{date.current}}'}</strong> - Date actuelle</li>
            </ul>
            <p>Cliquez dans l'éditeur où vous voulez insérer, puis cliquez sur un tag de variable.</p>
          </div>
        }
        type="info"
        showIcon
        icon={<InfoCircleOutlined />}
        style={{ marginBottom: 24 }}
      />
      
      <Form form={form} layout="vertical" onFinish={handleSubmit}>
        <Card title="Détails du modèle" bordered={false} style={{ marginBottom: 24 }}>
          <Form.Item
            name="name"
            label="Nom du modèle"
            rules={[{ required: true, message: 'Veuillez entrer un nom pour le modèle' }]}
          >
            <Input placeholder="ex: Email de bienvenue, Relance" />
          </Form.Item>

          <Form.Item
            name="description"
            label="Description"
          >
            <TextArea rows={2} placeholder="Brève description de quand utiliser ce modèle" />
          </Form.Item>

          <Form.Item
            name="subject"
            label="Objet de l'email"
            rules={[{ required: true, message: 'Veuillez entrer un objet pour l\'email' }]}
          >
            <Input placeholder="ex: Bienvenue dans notre service" />
          </Form.Item>
        </Card>

        <Card title="Contenu & Variables" bordered={false} style={{ marginBottom: 24 }}>
          <Form.Item
            name="module"
            label="Type d'enregistrement (pour variables)"
          >
            <Select
              placeholder="Sélectionnez un type d'enregistrement"
              onChange={setSelectedModule}
              value={selectedModule}
              allowClear
              style={{ width: '100%' }}
            >
              <Option value="contacts">Contacts</Option>
              <Option value="accounts">Comptes</Option>
              <Option value="leads">Prospects</Option>
            </Select>
          </Form.Item>

          <div style={{ marginBottom: 16 }}>
            <p style={{ fontWeight: 500, marginBottom: 8 }}>Variables disponibles :</p>
            <div style={{ display: 'flex', flexWrap: 'wrap', gap: 8 }}>
              {availableVariables.map(variable => (
                <Tooltip key={variable.name} title={variable.description}>
                  <Tag 
                    style={{ cursor: 'pointer', margin: 0 }}
                    onClick={() => insertVariable(variable.name)}
                    color="blue"
                  >
                    {`{{${variable.name}}}`}
                  </Tag>
                </Tooltip>
              ))}
            </div>
          </div>

          <Form.Item
            name="content"
            label="Contenu de l'email"
            rules={[{ required: true, message: 'Veuillez entrer un contenu pour l\'email' }]}
          >
            <RichTextEditor 
              initialValue={template?.content}
              onChange={(content) => form.setFieldsValue({ content })}
              onCursorChange={handleEditorClick}
              placeholder="Composez votre email ici. Cliquez pour positionner le curseur, puis cliquez sur les variables ci-dessus."
            />
          </Form.Item>
        </Card>

        <div style={{ textAlign: 'right', marginTop: 24 }}>
          <Space>
            <Button onClick={onCancel}>Annuler</Button>
            <Button type="primary" htmlType="submit">Enregistrer</Button>
          </Space>
        </div>
      </Form>
    </div>
  );
};

export default EmailTemplateEdit;