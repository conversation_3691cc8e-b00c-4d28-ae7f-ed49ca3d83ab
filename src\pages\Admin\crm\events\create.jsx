import React, { useState } from "react";
import {
  Modal,
  Form,
  Input,
  Radio,
  Select,
  Button,
  Upload,
  Checkbox,
  DatePicker,
   Row,
   InputNumber,
   Col
} from "antd";
import TextArea from "antd/es/input/TextArea";


const { RangePicker } = DatePicker;
const { Option } = Select;

const CreateEvent = ({ visible, onCreate, onCancel }) => {
  const [form] = Form.useForm();
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [selectedSubCategory, setSelectedSubCategory] = useState(null);

  // Gestion des catégories
  const handleCategoryChange = (e) => {
    setSelectedCategory(e.target.value);
    setSelectedSubCategory(null); // Réinitialiser la sous-catégorie
  };

  // Gestion des sous-catégories
  const handleSubCategoryChange = (value) => {
    setSelectedSubCategory(value);
  };
  const onChangeDate = (date, dateString) => {
    console.log(date, dateString);
  };
  const categories = [
    {
      id: 1,
      name: "Marketing Secteurs",
      subCategories: [
        { id: 1, name: "Séminaires et Journées d'Information(MS)" },
        { id: 2, name: "Salons Sectoriels" },
        { id: 3, name: "Démarchage Direct" },
        { id: 4, name: "Autres sources contacts" },
        { id: 4, name: "Locomotives" },


      ],
    },
    {
      id: 2,
      name: "Marketing Pays",
      subCategories: [
        { id: 1, name: "Séminaires et Journées d'Information(MP)" },
        { id: 2, name: "Salons Multisectoriels" },
        { id: 3, name: "Compétences Tunisiennes à l'Étranger (CTE)" },
        { id: 4, name: "Media" },

      ],
    },
      {
      id: 3,
      name: "Visites et délégations",
      subCategories: [
        { id: 1, name: "Visites et encadrement" },
        { id: 2, name: "Délégations" },
        { id: 3, name: "Tous les contacts" },
        { id: 4, name: "Contacts->Excel" },
      ],
    },
  ];

  // Fonction pour afficher le formulaire spécifique
  const renderFormBySubCategory = () => {
    switch (selectedSubCategory) {
      case "Séminaires et Journées d'Information(MP)":
        return (
          <>

    <Form layout="vertical">
      {/* Cases à cocher */}
      <Form.Item name="action" label="Action">
  <Checkbox.Group>
    <Row>
      <Col span={8}>
        <Checkbox value="proposee">Proposée</Checkbox>
      </Col>
      <Col span={8}>
        <Checkbox value="nonProgrammee">Non Programmée</Checkbox>
      </Col>
      <Col span={8}>
        <Checkbox value="validee">Validée</Checkbox>
      </Col>
      <Col span={8}>
        <Checkbox value="realisee">Réalisée</Checkbox>
      </Col>
      <Col span={8}>
        <Checkbox value="reportee">Reportée</Checkbox>
      </Col>
      <Col span={8}>
        <Checkbox value="annulee">Annulée</Checkbox>
      </Col>
    </Row>
  </Checkbox.Group>
</Form.Item>

      {/* Champs Texte et Listes déroulantes */}
      <Form.Item name="motif" label="Motif">
        <TextArea rows={3} />
      </Form.Item>
      <Form.Item name="responsableFipa" label="Responsable FIPA" rules={[{ required: true }]}> 
        <Select>
          <Option value="">Sélectionner</Option>
          {/* Options dynamiques ici */}
        </Select>
      </Form.Item>
      <Form.Item name="intitule" label="Intitulé" rules={[{ required: true }]}> 
        <Input />
      </Form.Item>
      <Form.Item name="theme" label="Thème" rules={[{ required: true }]}> 
        <Input />
      </Form.Item>
      <Form.Item name="dateDebut" label="Date début" rules={[{ required: true }]}> 
        <DatePicker />
      </Form.Item>
      <Form.Item name="dateFin" label="Date fin"> 
        <DatePicker />
      </Form.Item>
      <Form.Item name="pays" label="Pays" rules={[{ required: true }]}> 
        <Input />
      </Form.Item>
      <Form.Item name="region" label="Région"> 
        <Select>
          <Option value="">Sélectionner</Option>
        </Select>
      </Form.Item>
      <Form.Item name="actionConjointe" label="Action conjointe"> 
        <Select>
          <Option value="">Sélectionner</Option>
        </Select>
      </Form.Item>
      <Form.Item name="binome" label="Binôme"> 
        <Select>
          <Option value="">Sélectionner</Option>
        </Select>
      </Form.Item>
      <Form.Item name="proposePar" label="Proposée par"> 
        <TextArea rows={2} />
      </Form.Item>
      <Form.Item name="objectifs" label="Objectifs recherchés"> 
        <TextArea rows={2} />
      </Form.Item>
      <Form.Item name="lieu" label="Lieu"> 
        <TextArea rows={2} />
      </Form.Item>
      <Form.Item name="typeParticipation" label="Type de participation"> 
        <Select>
          <Option value="">Sélectionner</Option>
        </Select>
      </Form.Item>
      <Form.Item name="siActive" label="Si active"> 
        <TextArea rows={2} />
      </Form.Item>
      <Form.Item name="typeOrganisation" label="Type d’organisation"> 
        <Select>
          <Option value="">Sélectionner</Option>
        </Select>
      </Form.Item>
      <Form.Item name="partenairesTunisiens" label="Partenaires tunisiens"> 
        <TextArea rows={2} />
      </Form.Item>
      <Form.Item name="partenairesEtrangers" label="Partenaires étrangers"> 
        <TextArea rows={2} />
      </Form.Item>
      <Form.Item name="presenceOfficiels" label="Présence d’officiels"> 
        <TextArea rows={2} />
      </Form.Item>
      <Form.Item name="dgFipa" valuePropName="checked"> 
        <Checkbox>DG FIPA</Checkbox>
      </Form.Item>
      <Form.Item name="programme" label="Programme et déroulement"> 
        <TextArea rows={3} />
      </Form.Item>
      <Form.Item name="budgetPrevu" label="Budget prévu en DT"> 
        <InputNumber min={0} />
      </Form.Item>
      <Form.Item name="budgetRealise" label="Budget réalisé en DT"> 
        <InputNumber min={0} />
      </Form.Item>
      <Form.Item name="fichierListePresents" label="Liste des présents scannée"> 
        <Upload>
          <Button>Uploader un fichier PDF</Button>
        </Upload>
      </Form.Item>

      {/* Boutons */}
      <Form.Item style={{ textAlign: 'right' }}>
        <Button type="primary" htmlType="submit">Enregister</Button>
      </Form.Item>
    </Form>


          </>
        );

      case "Salons Sectoriels":
        return (
          <>
            <Form.Item
              label="Nom du Salon *"
              name="nomSalon"
              rules={[{ required: true, message: "Ce champ est obligatoire" }]}
            >
              <Input placeholder="Nom du salon" />
            </Form.Item>

            <Form.Item
              label="Édition *"
              name="edition"
              rules={[{ required: true, message: "Ce champ est obligatoire" }]}
            >
              <Input placeholder="Numéro de l'édition" />
            </Form.Item>

            <Form.Item
              label="Date de début *"
              name="dateDebut"
              rules={[{ required: true, message: "Ce champ est obligatoire" }]}
            >
              <DatePicker style={{ width: "100%" }} />
            </Form.Item>

            <Form.Item label="Date de fin" name="dateFin">
              <DatePicker style={{ width: "100%" }} />
            </Form.Item>

            <Form.Item
              label="Pays *"
              name="pays"
              rules={[{ required: true, message: "Ce champ est obligatoire" }]}
            >
              <Select placeholder="Sélectionnez un pays">
                <Option value="Tunisie">Tunisie</Option>
                <Option value="France">France</Option>
              </Select>
            </Form.Item>

            <Form.Item label="Thème du salon" name="themeSalon">
              <Input placeholder="Thème du salon" />
            </Form.Item>

            <Form.Item label="Objectifs de participation" name="objectifs">
              <Checkbox.Group>
                <Checkbox value="contacts">Contacts</Checkbox>
                <Checkbox value="veilleConcurrentielle">Veille concurrentielle</Checkbox>
                <Checkbox value="veilleTechnologique">Veille technologique</Checkbox>
              </Checkbox.Group>
            </Form.Item>
          </>
        );

      case "Démarchage Direct":
        return (
          <>
            <Form.Item
              label="Présentation *"
              name="presentation"
              rules={[{ required: true, message: "Ce champ est obligatoire" }]}
            >
              <Input.TextArea placeholder="Description de l'action" rows={4} />
            </Form.Item>

            <Form.Item
              label="Initiateur *"
              name="initiateur"
              rules={[{ required: true, message: "Ce champ est obligatoire" }]}
            >
              <Select placeholder="Sélectionnez un initiateur">
              <Option value="FIPA">Ezzidine jilliti </Option>

                <Option value="FIPA">FIPA Bruxelles</Option>
                <Option value="Partenaire">FIPA Cologne</Option>
              </Select>
            </Form.Item>

            <Form.Item
              label="Secteur *"
              name="secteur"
              rules={[{ required: true, message: "Ce champ est obligatoire" }]}
            >
              <Select placeholder="Sélectionnez un secteur">
                <Option value="Aéronautique">Aéronautique</Option>
                <Option value="Automobile">Automobile</Option>
              </Select>
            </Form.Item>

            <Form.Item label="Nombre de contacts prévus" name="contactsPrevu">
              <Input type="number" placeholder="Nombre de contacts prévus" />
            </Form.Item>
          </>
        );

      default:
        return null;
    }
  };

  return (
    <Modal
      title="Ajouter un Événement"
      visible={visible}
      onCancel={() => {
        form.resetFields();
        onCancel();
      }}
      footer={null}
      width={800}
      bodyStyle={{ padding: "20px" }}
    >
      <Form layout="vertical">
        {/* Sélection de la catégorie */}
        <Row gutter={16} >
            <Form.Item
            style={{marginRight:'10px'}}
              label="Initiateur *"
              name="initiateur"
              rules={[{ required: true, message: "Ce champ est obligatoire" }]}
            >
              <Select placeholder="Sélectionnez un initiateur">
              <Option value="FIPA">Ezzidine jilliti </Option>

                <Option value="FIPA">FIPA Bruxelles</Option>
                <Option value="Partenaire">FIPA Cologne</Option>
              </Select>
            </Form.Item>
            <Form.Item
              label="Année *"
              name="annee"
              rules={[{ required: true, message: "Ce champ est obligatoire" }]}
            >
              <DatePicker onChange={onChangeDate} picker="year" />
            </Form.Item>
    
  </Row>
        <Form.Item label="Thèmes">
          <Radio.Group onChange={handleCategoryChange} value={selectedCategory}>
            {categories.map((cat) => (
              <Radio key={cat.id} value={cat.id}>
                {cat.name}
              </Radio>
            ))}
          </Radio.Group>
        </Form.Item>
      

           

        {/* Sélection de la sous-catégorie */}
        {selectedCategory && (
          <Form.Item label="Sous-Catégorie">
            <Select
              placeholder="Sélectionnez une sous-catégorie"
              onChange={handleSubCategoryChange}
              value={selectedSubCategory}
            >
              {categories
                .find((cat) => cat.id === selectedCategory)
                .subCategories.map((subCat) => (
                  <Option key={subCat.id} value={subCat.name}>
                    {subCat.name}
                  </Option>
                ))}
            </Select>
          </Form.Item>
        )}

        {/* Afficher le formulaire spécifique */}
        {selectedSubCategory && renderFormBySubCategory()}

        {/* Bouton de soumission */}
      
        <Form.Item>
        <div style={{ textAlign: 'right' }}>
        <Button key="back"  style={{marginRight:'15px'}} >
            Annuler
          </Button>
          <Button type="primary" htmlType="submit">
            Enregistrer
          </Button>
        </div>
      </Form.Item>
      </Form>
    </Modal>
  );
};

export default CreateEvent;