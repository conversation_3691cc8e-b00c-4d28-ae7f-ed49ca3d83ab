import React from 'react';
import { Descriptions, Tag, Button } from 'antd';
import { DownloadOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';

const DocumentView = ({ document }) => {
  if (!document) return null;

  return (
    <div>
      <Descriptions bordered column={1}>
        <Descriptions.Item label="Name">{document.name}</Descriptions.Item>
        <Descriptions.Item label="Summary">{document.summary}</Descriptions.Item>
        <Descriptions.Item label="Category">
          <Tag color="blue">{document.category}</Tag>
        </Descriptions.Item>
        <Descriptions.Item label="Format">
          <Tag>{document.format.toUpperCase()}</Tag>
        </Descriptions.Item>
        <Descriptions.Item label="File">
          <Button 
            icon={<DownloadOutlined />} 
            href={`/files/${document.file}`} 
            download
          >
            Download {document.file}
          </Button>
        </Descriptions.Item>
        <Descriptions.Item label="Created At">
          {dayjs(document.createdAt).format('YYYY-MM-DD HH:mm')}
        </Descriptions.Item>
        <Descriptions.Item label="Last Updated">
          {dayjs(document.updatedAt).format('YYYY-MM-DD HH:mm')}
        </Descriptions.Item>
      </Descriptions>
    </div>
  );
};

export default DocumentView;