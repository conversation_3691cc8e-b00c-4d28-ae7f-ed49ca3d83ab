export const documents = [
  {
    id: 1,
    name: 'Contract Agreement',
    summary: 'Annual contract with suppliers',
    category: 'Contracts',
    format: 'PDF',
    createdAt: '2025-01-15T10:00:00Z',
    updatedAt: '2025-01-15T10:00:00Z',
    file: 'contract_agreement.pdf',
    history: [
      { id: 1, action: 'Created', date: '2025-01-15T10:00:00Z', person: '<PERSON>' },
    ],
  },
  {
    id: 2,
    name: 'Annual Report',
    summary: 'Yearly financial report',
    category: 'Reports',
    format: 'DOCX',
    createdAt: '2025-02-01T09:00:00Z',
    updatedAt: '2025-02-01T09:00:00Z',
    file: 'annual_report.docx',
    history: [
      { id: 1, action: 'Created', date: '2025-02-01T09:00:00Z', person: '<PERSON>' },
    ],
  },
];

// Derive categories with history
export const categories = [
  {
    id: 1,
    name: 'Contracts',
    type: 'Document',
    history: [
      { id: 1, action: 'Created', date: '2025-01-10T08:00:00Z', person: 'Admin' },
    ],
  },
  {
    id: 2,
    name: 'Reports',
    type: 'Document',
    history: [
      { id: 1, action: 'Created', date: '2025-01-10T08:00:00Z', person: 'Admin' },
    ],
  },
];