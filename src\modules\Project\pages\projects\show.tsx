import React, { useEffect } from "react";
import {
  Input,
  Form,
  Row,
  Col,
  Select,
  DatePicker,
  InputNumber,
  Switch,
} from "antd";
import dayjs from "dayjs";
import { IClient, IEntreprise, IDepartement, IProject } from "../interfaces";
import { ModalForm } from "@ant-design/pro-components";
import { useTranslation } from "react-i18next";
const { Option } = Select;
const Show: React.FC<{
  visible: boolean;
  setVisible: React.Dispatch<React.SetStateAction<boolean>>;
  entreprises: IEntreprise[];
  departements: IDepartement[];
  clients: IClient[];
  projects: IProject[];
  project: IProject;
}> = ({
  visible,
  setVisible,
  entreprises,
  departements,
  clients,
  projects,
  project,
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();

  const onClose = (open) => {
    if (!open) {
      setVisible(false);
    }
  };

  useEffect(() => {
    form.setFieldsValue({
      ...project,
      date_ordre_service:
        project?.date_ordre_service &&
        dayjs(project.date_ordre_service, "YYYY-MM-DD"),
      duree_exec: project?.duree_exec,
      lien_partage: project?.lien_partage,
      signature_contrat:
        project?.signature_contrat &&
        dayjs(project.signature_contrat, "YYYY-MM-DD"),
      reception_definitive:
        project?.reception_definitive &&
        dayjs(project.reception_definitive, "YYYY-MM-DD"),
      reception_provisoire:
        project?.reception_provisoire &&
        dayjs(project.reception_provisoire, "YYYY-MM-DD"),
      contract_maintenance:
        project?.contract_maintenance &&
        dayjs(project.contract_maintenance, "YYYY-MM-DD"),
      lettre_participation: !!project?.lettre_participation,
      lettre_bonne_execution: !!project?.lettre_bonne_execution,
    });
  }, [project]);

  return (
    <ModalForm
      title={t("projects.show.title") + " " + project?.designation}
      width={850}
      className="pt-2"
      onOpenChange={onClose}
      open={visible}
      layout="vertical"
      form={form}
      disabled
      submitter={false}
    >
      <Row gutter={16}>
        <Col xs={24} sm={12} md={12} lg={12} xl={12} xxl={12}>
          <Form.Item
            name="enterprise_id"
            label={t("projects.columns.enterprise")}
          >
            <Select
              placeholder={t("projects.placeholders.select_enterprise")}
              showSearch
            >
              {entreprises?.map((item) => (
                <Option key={item.id} value={item.id} label={item.designation}>
                  {item.designation}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={12} lg={12} xl={12} xxl={12}>
          <Form.Item
            name="department_id"
            label={t("projects.columns.department")}
          >
            <Select
              placeholder={t("projects.placeholders.select_department")}
              showSearch
            >
              {departements?.map((item) => (
                <Option key={item.id} value={item.id} label={item.designation}>
                  {item.designation}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={12} lg={12} xl={12} xxl={12}>
          <Form.Item name="tier_id" label={t("projects.columns.client")}>
            <Select
              placeholder={t("projects.placeholders.select_client")}
              showSearch
            >
              {clients?.map((item) => (
                <Option key={item.id} value={item.id} label={item.designation}>
                  {item.designation}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={12} lg={12} xl={12} xxl={12}>
          <Form.Item
            name="project_id"
            label={t("projects.columns.parent_project")}
          >
            <Select
              placeholder={t("projects.placeholders.select_parent_project")}
              showSearch
              allowClear
            >
              {projects?.map((item) => (
                <Option key={item.id} value={item.id} label={item.designation}>
                  {item.designation}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={12} lg={12} xl={12} xxl={12}>
          <Form.Item
            name="designation"
            label={t("projects.columns.project_name")}
          >
            <Input
              placeholder={t("projects.placeholders.enter_project_name")}
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={12} lg={12} xl={12} xxl={12}>
          <Form.Item
            name="reference"
            label={t("projects.columns.tender_number")}
          >
            <Input
              placeholder={t("projects.placeholders.enter_tender_number")}
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={12} lg={12} xl={12} xxl={12}>
          <Form.Item
            name="date_ordre_service"
            label={t("projects.columns.service_order_date")}
          >
            <DatePicker
              style={{ width: "100%" }}
              format={"YYYY-MM-DD"}
              allowClear
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={12} lg={12} xl={12} xxl={12}>
          <Form.Item
            name="duree_exec"
            label={t("projects.columns.execution_duration")}
          >
            <InputNumber
              style={{ width: "100%" }}
              placeholder={t("projects.placeholders.enter_execution_duration")}
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={12} lg={12} xl={12} xxl={12}>
          <Form.Item
            name="lien_partage"
            label={t("projects.columns.share_link")}
          >
            <Input placeholder={t("projects.placeholders.enter_share_link")} />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={12} lg={12} xl={12} xxl={12}>
          <Form.Item
            name="signature_contrat"
            label={t("projects.columns.contract_signature")}
          >
            <DatePicker
              style={{ width: "100%" }}
              format={"YYYY-MM-DD"}
              allowClear
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={12} lg={12} xl={12} xxl={12}>
          <Form.Item
            name="reception_provisoire"
            label={t("projects.columns.provisional_reception")}
          >
            <DatePicker
              style={{ width: "100%" }}
              format="YYYY-MM-DD"
              allowClear
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={12} lg={12} xl={12} xxl={12}>
          <Form.Item
            name="duree_exec_prov"
            label={t("projects.columns.execution_provisional_duration")}
          >
            <InputNumber
              style={{ width: "100%" }}
              placeholder={t(
                "projects.placeholders.enter_provisional_execution_duration"
              )}
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={12} lg={12} xl={12} xxl={12}>
          <Form.Item
            name="reception_definitive"
            label={t("projects.columns.final_reception")}
          >
            <DatePicker
              style={{ width: "100%" }}
              format="YYYY-MM-DD"
              allowClear
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={12} lg={12} xl={12} xxl={12}>
          <Form.Item
            name="warranty_period"
            label={t("projects.columns.warranty_duration")}
          >
            <InputNumber
              style={{ width: "100%" }}
              placeholder={t("projects.placeholders.enter_warranty_duration")}
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={12} lg={12} xl={12} xxl={12}>
          <Form.Item
            name="contract_maintenance_option"
            label={t("projects.columns.maintenance_contract_option")}
          >
            <Switch />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={12} lg={12} xl={12} xxl={12}>
          <Form.Item
            name="contract_maintenance"
            label={t("projects.columns.maintenance_contract")}
          >
            <DatePicker
              style={{ width: "100%" }}
              format={"YYYY-MM-DD"}
              allowClear
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={12} lg={12} xl={12} xxl={12}>
          <Form.Item
            name="lettre_participation"
            label={t("projects.columns.participation_letter")}
          >
            <Select placeholder={t("common.placeholders.select")} allowClear>
              <Option key={0} value={false} label={t("common.no")}>
                {t("common.no")}
              </Option>
              <Option key={1} value={true} label={t("common.yes")}>
                {t("common.yes")}
              </Option>
            </Select>
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={12} lg={12} xl={12} xxl={12}>
          <Form.Item
            name="lettre_bonne_execution"
            label={t("projects.columns.good_execution_letter")}
          >
            <Select placeholder={t("common.placeholders.select")} allowClear>
              <Option key={0} value={false} label={t("common.no")}>
                {t("common.no")}
              </Option>
              <Option key={1} value={true} label={t("common.yes")}>
                {t("common.yes")}
              </Option>
            </Select>
          </Form.Item>
        </Col>
      </Row>
    </ModalForm>
  );
};

export default Show;
