import { useState, useRef, useEffect } from "react";
import { ProColumns, ProTable } from "@ant-design/pro-components";
import {
  Button,
  Popconfirm,
  Space,
  Input,
  Tooltip,
  Typography,
  Card,
  Tag,
} from "antd";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { tvas } from "./data";
import AddTvaModal from "./create";
import ShowTvaModal from "./view";
import EditTvaModal from "./edit";
import { ITva } from "./interfaces";
import useDebounce from "@src/hooks/useDebounce";

function Tvas() {
  const { t } = useTranslation();
  const tableRef = useRef();
  const [dataSource, setDataSource] = useState<ITva[]>(tvas);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [tva, setTva] = useState<ITva | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [tableParams, setTableParams] = useState([]);
  const [total, setTotal] = useState(dataSource.length);
  const [filteredData, setFilteredData] = useState<ITva[]>(dataSource);
  const [pageNumber, setPageNumber] = useState(1);
  const [showEditModal, setShowEditModal] = useState(false);

  const handleSearchData = (params) => {
    const filtered = dataSource.filter((item) => {
      return (
        (params.default === undefined ||
          item.default === JSON.parse(params.default)) &&
        (!params.code ||
          item.code?.toLowerCase().includes(params.code.toLowerCase())) &&
        (!params.designation ||
          item.designation
            ?.toLowerCase()
            .includes(params.designation.toLowerCase())) &&
        (params.type === undefined || item.type === params.type) &&
        (!params.valeur ||
          item.valeur?.toLowerCase().includes(params.valeur.toLowerCase()))
      );
    });

    setFilteredData(filtered); // Update table with filtered results
  };

  const columns: ProColumns<ITva>[] = [
    {
      title: t("tvas.fields.default"),
      dataIndex: "default",
      key: "default",
      valueType: "select",
      valueEnum: {
        true: t("tvas.fields.default"),
        false: t("tvas.default.non-default"),
      },

      render: (_, record) => {
        return record.default ? (
          <Tag color="green">{t("tvas.fields.default")}</Tag>
        ) : (
          <Tag color="blue">{t("tvas.default.non-default")}</Tag>
        );
      },
    },
    {
      title: t("tvas.fields.code"),
      dataIndex: "code",
      key: "code",
      filters: dataSource.map((item) => ({
        text: item.code,
        value: item.code,
      })),
      onFilter: (value, record) => record.code === value,
      render: (text, record) => {
        return (
          <Typography.Text
            ellipsis={{ tooltip: text }}
            style={{ maxWidth: "auto" }}
          >
            {text}
          </Typography.Text>
        );
      },
    },
    {
      title: t("tvas.fields.designation"),
      dataIndex: "designation",
      key: "designation",
      filters: dataSource.map((item) => ({
        text: item.designation,
        value: item.designation,
      })),
      onFilter: (value, record) => record.designation === value,
    },
    {
      title: t("tvas.fields.type"),
      dataIndex: "type",
      key: "type",
      valueType: "select",
      valueEnum: {
        Achat: t("tvas.type.achat"),
        Vente: t("tvas.type.vente"),
        "Achat et Vente": t("tvas.type.achat_et_vente"),
      },
    },
    {
      title: t("tvas.fields.valeur"),
      dataIndex: "valeur",
      key: "valeur",
    },
    {
      title: t("common.actions.title"),
      key: "action",
      align: "center",
      width: 200,
      sorter: false,
      search: false,
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title={t("common.actions.view")}>
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => {
                console.log(record);

                setTva(record);
                setShowViewModal(true);
              }}
            />
          </Tooltip>
          <Tooltip title={t("common.actions.edit")}>
            <Button
              type="link"
              style={{ color: "#f5b041" }}
              icon={<EditOutlined />}
              onClick={() => {
                setShowEditModal(true);
                setTva(record);
              }}
            />
          </Tooltip>
          <Tooltip title={t("common.actions.delete")}>
            <Popconfirm
              title={t("common.messages.confirm_delete")}
              onConfirm={() => {
                setIsLoading(true);
                const newDataSource = dataSource.filter(
                  (item) => item.key !== record.key
                );
                setDataSource(newDataSource);
                setFilteredData(newDataSource);
                setIsLoading(false);
              }}
            >
              <Button
                type="link"
                style={{ color: "#ec7063" }}
                icon={<DeleteOutlined />}
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  const handleSearch = (e) => {
    const searchValue = e.target.value;
    setTableParams({ ...tableParams, search: searchValue });
  };

  const debouncedOnChange = useDebounce(handleSearch, 700);

  useEffect(() => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  }, [dataSource, filteredData]);

  return (
    <Card
      bordered
      title={<Typography.Title level={4}>{t("tvas.list")}</Typography.Title>}
      extra={
        <Space>
          <Input
            size="large"
            placeholder={t("common.search")}
            suffix={<SearchOutlined style={{ color: "#bfbfbf" }} />}
            onChange={debouncedOnChange}
          />
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setShowAddModal(true)}
          >
            {t("tvas.add")}
          </Button>
        </Space>
      }
    >
      <ProTable
        actionRef={tableRef}
        loading={isLoading}
        columns={columns}
        dataSource={filteredData}
        rowKey="key"
        search={{
          searchText: t("common.filter"),
          labelWidth: "auto",
          defaultCollapsed: false,
        }}
        options={false}
        params={tableParams}
        scroll={{ x: "max-content" }}
        onSubmit={handleSearchData}
        onReset={() => handleSearchData({})}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: 5,
          total: total,
          onChange: (page) => setPageNumber(page),
        }}
      />
      <AddTvaModal
        open={showAddModal}
        onCancel={() => setShowAddModal(false)}
      />
      <ShowTvaModal
        open={showViewModal}
        onCancel={() => {
          setShowViewModal(false);
          setTva(null);
        }}
        tva={tva}
      />
      <EditTvaModal
        open={showEditModal}
        onCancel={() => {
          setShowEditModal(false);
          setTva(null);
        }}
        tva={tva}
      />
    </Card>
  );
}

export default Tvas;
