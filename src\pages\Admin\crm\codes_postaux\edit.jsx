import { ProForm, ProFormText } from "@ant-design/pro-form";
import { message } from "antd";

const EditPostalCode = ({ record, onCancel, setDataSource }) => {
  const handleSubmit = async (values) => {
    try {
      const updatedData = postalCodes.map(item => item.key === record.key ? { ...item, ...values } : item);
      setDataSource(updatedData);
      message.success("Code Postal modifié avec succès!");
      onCancel();
    } catch (error) {
      console.error(error);
      message.error("Erreur lors de la modification du Code Postal");
    }
  };

  return (
    <ProForm initialValues={record} onFinish={handleSubmit}>
      <ProFormText name="code" label="Code Postal" placeholder="Saisissez le code postal" rules={[{ required: true }]} />
      <ProFormText name="city" label="Ville" placeholder="Saisissez la ville" rules={[{ required: true }]} />
      <ProFormText name="governorate" label="Gouvernorat" placeholder="Saisissez le gouvernorat" rules={[{ required: true }]} />
    </ProForm>
  );
};

export default EditPostalCode;
