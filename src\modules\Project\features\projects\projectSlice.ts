import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { default as api } from "@/apis/axiosInterceptor";

export const getProjects: any = createAsyncThunk(
  "getProjects",
  async (data: { pageSize: number, current: number, enterprise_id: number, department_id: number, tier_id: number, parent_id: number, id: number, reception_provisoire: string, reception_definitive: string, contract_maintenance: string }, thunkAPI) => {
    try {
      let url = `/project?page=${data.current}&perPage=${data.pageSize}`;
      if (data.enterprise_id) {
        url += `&enterprise_id=${data.enterprise_id}`;
      }
      if (data.department_id) {
        url += `&department_id=${data.department_id}`;
      }
      if (data.id) {
        url += `&id=${data.id}`;
      }
      if (data.tier_id) {
        url += `&tier_id=${data.tier_id}`;
      }
      if (data.parent_id) {
        url += `&parent_id=${data.parent_id}`;
      }
      if (data.reception_provisoire) {
        url += `&reception_provisoire=${data.reception_provisoire}`;
      }
      if (data.reception_definitive) {
        url += `&reception_definitive=${data.reception_definitive}`;
      }
      if (data.contract_maintenance) {
        url += `&contract_maintenance=${data.contract_maintenance}`;
      }
      const resp = await api.get(url);
      return resp.data;
    } catch (error) {
      return thunkAPI.rejectWithValue("something went wrong");
    }
  }
);

export const getAllProjects: any = createAsyncThunk(
  "projects/fetchAll",
  async (_, thunkAPI) => {
    try {
      const response = await api.get(`/project-all`);
      return response.data;
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error?.message || "An error occurred.");
    }
  }
);

export const createProject: any = createAsyncThunk(
  "projects/create",
  async (data, thunkAPI) => {
    try {
      const response = await api.post('/project', data);
      return response.data;
    } catch (error: any) {
      return thunkAPI.rejectWithValue(
        error.response?.data || "An error occurred."
      );
    }
  }
);

export const updateProject: any = createAsyncThunk(
  "projects/update",
  async (data: any, thunkAPI) => {
    try {
      const response = await api.put(`/project/${data.id}`, data);
      return response.data;
    } catch (error: any) {
      return thunkAPI.rejectWithValue(
        error.response?.data || "An error occurred."
      );
    }
  }
);

export const deleteProject: any = createAsyncThunk(
  "projects/delete",
  async (id, thunkAPI) => {
    try {
      const response = await api.delete(`/project/${id}`);
      return response.data;
    } catch (error: any) {
      return thunkAPI.rejectWithValue(
        error.response?.data || "An error occurred."
      );
    }
  }
);

export const exportProjects: any = createAsyncThunk(
  'projects/export',
  async (params: any, { rejectWithValue }) => {
    try {
      const response = await api.post('/projects-export', params, {
        responseType: 'blob', // Important pour les fichiers
      });
      return response;
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data || "An error occurred."
      );
    }
  }
);

export const projectSlice = createSlice({
  name: 'projects',
  initialState: {
    loading: false,
    error: null,
    data: [],
  },
  reducers: {},
  extraReducers: builder => {
    builder
      .addCase(getAllProjects.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getAllProjects.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
        state.data = action.payload;
      })
      .addCase(getAllProjects.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
  }
})

export default projectSlice.reducer

