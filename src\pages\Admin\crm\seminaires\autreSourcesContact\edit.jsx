import { ModalForm } from "@ant-design/pro-components";
import {
  Card,
  Checkbox,
  Col,
  DatePicker,
  Divider,
  Form,
  Input,
  Radio,
  Row,
  Select,
} from "antd";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { pays, secteurs, type_contact } from "./data";

function EditAutreSource({ open, onCancel, record, show }) {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  useEffect(() => {
    if (open) {
      form.setFieldsValue(record);
    }
  }, [open]);

  return (
    <ModalForm
      title={
        show
          ? t("Afficher Autre Source de contacts")
          : t("Modifier Autre Source de contacts")
      }
      form={form}
      open={open}
      modalProps={{
        style: { top: 20 },
      }}
      //   onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={
        show
          ? {
              submitButtonProps: { hidden: true },
              searchConfig: {
                resetText: t("common.actions.cancel"),
              },
            }
          : {
              searchConfig: {
                submitText: t("common.actions.edit"),
                resetText: t("common.actions.cancel"),
              },
            }
      }
    >
      <Card className="mt-2">
        <Row gutter={4}>
          <Col md={24} xs={24}>
            <Form.Item>
              <div style={{ display: "flex", alignItems: "center", gap: 24 }}>
                <label style={{ marginBottom: 0 }}>{t("Action")}</label>
                <Checkbox disabled={show}>Réalisée</Checkbox>
              </div>
            </Form.Item>
          </Col>

          <Col md={16} xs={24}>
            <Form.Item name="" label={t("Initiateur")}>
              <Select disabled={show} />
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item name="" label={t("Inclure")}>
              <Select disabled={show} />
            </Form.Item>
          </Col>
        </Row>
      </Card>
      <Divider>Données générales</Divider>
      <Card className="mt-2">
        <Row gutter={4}>
          <Col md={16} xs={24}>
            <Form.Item name="intitule" label={t("Intitulé")}>
              <Input disabled={show} />
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item name="type_contact_id" label={t("Type Contact")}>
              <Select
                options={type_contact.map((item) => ({
                  value: item.id,
                  label: item.type,
                }))}
                disabled={show}
              />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name="date_debut" label={t("Date Début")}>
              <DatePicker disabled={show} />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name="date_fin" label={t("Date Fin")}>
              <DatePicker disabled={show} />
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item name="pays_id" label={t("Pays")}>
              <Select
                disabled={show}
                options={pays.map((item) => ({
                  value: item.id,
                  label: item.nom,
                }))}
              />
            </Form.Item>
          </Col>
          <Col md={16} xs={24}>
            <Form.Item name="" label={t("Région")}>
              <Input disabled={show}></Input>
            </Form.Item>
          </Col>
          <Col md={24} xs={24}>
            <Form.Item name="" label={t("Lieu")}>
              <Input disabled={show}></Input>
            </Form.Item>
          </Col>
          <Col md={16} xs={24}>
            <Form.Item name="secteur_id" label={t("Secteur")}>
              <Select
                options={secteurs.map((item) => ({
                  value: item.id,
                  label: item.secteur,
                }))}
                disabled={show}
              />
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item name="" label={t("Groupe")}>
              <Select disabled={show}></Select>
            </Form.Item>
          </Col>
        </Row>
      </Card>
    </ModalForm>
  );
}
export default EditAutreSource;
