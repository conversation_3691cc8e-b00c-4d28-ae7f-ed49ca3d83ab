import { MinusCircleOutlined, PlusCircleOutlined } from "@ant-design/icons";
import { ModalForm } from "@ant-design/pro-components";
import {
  Card,
  Col,
  DatePicker,
  Divider,
  Form,
  Input,
  InputNumber,
  message,
  Radio,
  Row,
  Select,
} from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";

function TrackProjet({ open, onCancel, onSuccess }) {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const handleSubmit = (values) => {};
  const handleAdd = () => {
    const currentdata = form.getFieldValue("tracks") || [];
    const date_suivi = form.getFieldValue("date_suivi");
    const action_suivi = form.getFieldValue("action_suivi");
    const date_relance = form.getFieldValue("date_relance");
    const responsable = form.getFieldValue("responsable");
    const rel_effectue = form.getFieldValue("rel_effectue");

    if (date_suivi && action_suivi && date_relance && responsable ) {
      const key = Date.now();
      form.setFieldsValue({
        tracks: [
          ...currentdata,
          {
            key: key,
            date_suivi: date_suivi,
            action_suivi:action_suivi ,
            date_relance: date_relance ,
            responsable : responsable ,
            rel_effectue : rel_effectue 
          },
        ],
      });
      form.resetFields(["date_suivi","action_suivi", "date_relance" , "responsable" , "rel_effectue"]);
    } else {
      message.error(t("Merci de bien vouloir remplir tous les champs."));
    }
  };

  return (
    <ModalForm
      title={t("Suivi du projet")}
      width="70%"
      form={form}
      open={open}
      modalProps={{
        style: { top: 20 },
      }}
      onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.save"),
          resetText: t("common.actions.cancel"),
        },
      }}
    >
      <Card className="mt-2">
        <Row gutter={16} style={{ marginBottom: "10px" }}>
          <Col span={4}>Date Suivi</Col>
          <Col span={8}>Action de Suivi</Col>
          <Col span={5}>Responsable suivi</Col>
          <Col span={4}>Date Relance</Col>
          <Col span={3}>Rel Effectuée</Col>
        </Row>
        <Form.List name="tracks">
          {(fields, { remove }) => (
            <>
              {fields.map((field, index) => (
                <Row gutter={16} align="middle" key={field.key}>
                  <Col span={4}>
                    <Form.Item name={[index, "date_suivi"]} rules={[]}>
                      <DatePicker allowClear={true} />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item name={[index, "action_suivi"]} rules={[]}>
                      <Input allowClear={true} />
                    </Form.Item>
                  </Col>
                   <Col span={5}>
                    <Form.Item name={[index, "responsable"]} rules={[]}>
                      <Select allowClear={true}   options={[{ value: 1, label: "Responsable Test" }]} />
                    </Form.Item>
                  </Col>
                    <Col span={4}>
                    <Form.Item name={[index, "date_relance"]} rules={[]}>
                      <DatePicker/>
                    </Form.Item>
                  </Col>
                  <Col span={1}></Col>
                  <Col span={1}>
                    <Form.Item name={[index, "rel_effectue"]} rules={[]}>
                      <Radio/>
                    </Form.Item>
                  </Col>
                  <Col span={1}>
                    <Form.Item>
                      <MinusCircleOutlined
                        style={{
                          color: "red",
                          fontSize: "18px",
                          cursor: "pointer",
                        }}
                        onClick={() => remove(index)}
                      />
                    </Form.Item>
                  </Col>
                </Row>
              ))}
            </>
          )}
        </Form.List>

        <Row gutter={16} align="middle" style={{ marginBottom: "5px" }}>
          <Col span={4}>
            <Form.Item name={`date_suivi`} rules={[]}>
              <DatePicker allowClear={true} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name={`action_suivi`} rules={[]}>
              <Input allowClear={true} />
            </Form.Item>
          </Col>
          <Col span={5}>
            <Form.Item name={`responsable`} rules={[]}>
              <Select
                allowClear={true}
                options={[{ value: 1, label: "Responsable Test" }]}
              />
            </Form.Item>
          </Col>
          <Col span={4}>
            <Form.Item name={`date_relance`} rules={[]}>
              <DatePicker allowClear={true} />
            </Form.Item>
          </Col>
          <Col span={1}></Col>
          <Col span={1}>
            {/* <Form.Item name={`rel_effectue`} rules={[]}>
              <Radio />
            </Form.Item> */}
          </Col>
          <Col span={1}>
            <Form.Item>
              <PlusCircleOutlined
                style={{
                  color: "green",
                  fontSize: "18px",
                  cursor: "pointer",
                }}
                onClick={() => handleAdd()}
              />
            </Form.Item>
          </Col>
        </Row>
      </Card>
    </ModalForm>
  );
}
export default TrackProjet;
