import { useState, useRef, useEffect } from "react";
import { ProColumns, ProTable } from "@ant-design/pro-components";
import {
  Button,
  Popconfirm,
  Space,
  Input,
  Tooltip,
  Typography,
  Card,
  Tag,
} from "antd";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { clients } from "./data";
import AddClientModal from "./create";
import ShowClientModal from "./view";
import EditClientModal from "./edit";
import { ITier } from "../interfaces";
// import useDebounce from "@src/hooks/useDebounce";

function Clients() {
  const { t } = useTranslation();
  const tableRef = useRef();
  const [dataSource, setDataSource] = useState<ITier[]>(clients);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [client, setClient] = useState<ITier | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [tableParams, setTableParams] = useState([]);
  const [total, setTotal] = useState(dataSource.length);
  const [filteredData, setFilteredData] = useState<ITier[]>(dataSource);
  const [pageNumber, setPageNumber] = useState(1);
  const [showEditModal, setShowEditModal] = useState(false);
  const [entreprises, setEntreprises] = useState([]);
  const [departements, setDepartements] = useState([]);
  const [gouvernorats, setGouvernorats] = useState([]);
  const [countries, setCountries] = useState([]);
  const [raisonSociale, setSaisonSociale] = useState<string>();
  const [entrepriseId, setEntrepriseId] = useState<number>();
  const [paysId, setPaysId] = useState<number>();
  const [code, setCode] = useState(null);

  const handleSearchData = (params) => {
    const filtered = dataSource.filter((item) => {
      return (
        (!params.code ||
          item.code?.toLowerCase().includes(params.code.toLowerCase())) &&
        (!params.type ||
          item.type?.toLowerCase().includes(params.type.toLowerCase())) &&
        (!params.entrepriseId || item.entrepriseId === params.entrepriseId) &&
        (!params.departementId ||
          item.departementId === params.departementId) &&
        (!params.paysId || item.paysId === params.paysId)
      );
    });

    setFilteredData(filtered); // Update table with filtered results
  };

  const columns: ProColumns<ITier>[] = [
    {
      title: t("vente.info_general.code"),
      dataIndex: "code",
      key: "code",
      filters: dataSource.map((item) => ({
        text: item.code,
        value: item.code,
      })),
      onFilter: (value, record) => record.code === value,
      valueType: "select",
      valueEnum: Object.fromEntries(
        dataSource.map((d) => [d.code, { text: d.code }])
      ),
      render: (text, record) => {
        return (
          <Typography.Text
            ellipsis={{ tooltip: text }}
            style={{ maxWidth: "auto" }}
          >
            {text}
          </Typography.Text>
        );
      },
    },
    {
      title: t("vente.info_general.type"),
      dataIndex: "type",
      key: "type",
      filters: dataSource.map((item) => ({
        text: item.type,
        value: item.type,
      })),
      onFilter: (value, record) => record.type === value,
      valueType: "select",
      valueEnum: Object.fromEntries(
        dataSource.map((d) => [d.type, { text: d.type }])
      ),
    },
    {
      title: t("vente.info_general.raison_sociale"),
      dataIndex: "raisonSociale",
      key: "raisonSociale",
      search: false,
    },
    {
      title: t("vente.info_general.adresse_1"),
      dataIndex: "adresse_1",
      key: "adresse_1",
      search: false,
    },
    {
      title: t("vente.info_general.adresse_2"),
      dataIndex: "adresse_2",
      key: "adresse_2",
      search: false,
      hideInTable: true,
    },
    {
      title: t("vente.info_general.code_postal"),
      dataIndex: "codePostal",
      key: "codePostal",
      search: false,
      hideInTable: true,
    },
    {
      title: t("vente.info_general.entreprise"),
      dataIndex: "entrepriseId",
      key: "entrepriseId",
      filters: dataSource.map((item) => ({
        text: item.entrepriseId,
        value: item.entrepriseId,
      })),
      onFilter: (value, record) => record.entrepriseId === value,
      valueType: "select",
      valueEnum: Object.fromEntries(
        dataSource.map((d) => [d.entrepriseId, { text: d.entrepriseId }])
      ),
    },
    {
      title: t("vente.info_general.departement"),
      dataIndex: "departementId",
      key: "departementId",
      valueType: "select",
      valueEnum: Object.fromEntries(
        dataSource.map((d) => [d.departementId, { text: d.departementId }])
      ),
    },
    {
      title: t("vente.info_general.pays"),
      dataIndex: "paysId",
      key: "paysId",
      filters: dataSource.map((item) => ({
        text: item.paysId,
        value: item.paysId,
      })),
      onFilter: (value, record) => record.paysId === value,
      valueType: "select",
      valueEnum: Object.fromEntries(
        dataSource.map((d) => [d.paysId, { text: d.paysId }])
      ),
    },
    {
      title: t("vente.info_general.phone"),
      dataIndex: "phone",
      key: "phone",
      search: false,
    },
    {
      title: t("vente.info_general.email"),
      dataIndex: "email",
      key: "email",
      search: false,
    },
    {
      title: t("vente.info_general.matricule_fiscale"),
      dataIndex: "matriculeFiscale",
      key: "matriculeFiscale",
      search: false,
    },
    {
      title: t("vente.info_general.assujetti_tva"),
      dataIndex: "assujettiTVA",
      key: "assujettiTVA",
      render: (_, record) =>
        record?.assujettiTVA ? (
          <Tag color="green">Oui</Tag>
        ) : (
          <Tag color="red">Non</Tag>
        ),
      search: false,
    },
    {
      title: t("common.actions.title"),
      key: "action",
      align: "center",
      width: 200,
      sorter: false,
      search: false,
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title={t("common.actions.view")}>
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => {
                console.log(record);

                setClient(record);
                setShowViewModal(true);
              }}
            />
          </Tooltip>
          <Tooltip title={t("common.actions.edit")}>
            <Button
              type="link"
              style={{ color: "#f5b041" }}
              icon={<EditOutlined />}
              onClick={() => {
                setShowEditModal(true);
                setClient(record);
              }}
            />
          </Tooltip>
          <Tooltip title={t("common.actions.delete")}>
            <Popconfirm
              title={t("common.messages.confirm_delete")}
              onConfirm={() => {
                setIsLoading(true);
                const newDataSource = dataSource.filter(
                  (item) => item.key !== record.key
                );
                setDataSource(newDataSource);
                setFilteredData(newDataSource);
                setIsLoading(false);
              }}
            >
              <Button
                type="link"
                style={{ color: "#ec7063" }}
                icon={<DeleteOutlined />}
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  // const handleSearch = (e) => {
  //   const searchValue = e.target.value;
  //   setTableParams({ ...tableParams, search: searchValue });
  // };

  // const debouncedOnChange = useDebounce(handleSearch, 700);

  useEffect(() => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  }, [dataSource, filteredData]);

  return (
    <Card
      bordered
      title={
        <Typography.Title level={4}>{t("vente.client.list")}</Typography.Title>
      }
      extra={
        <Space>
          <Input
            size="large"
            placeholder={t("common.search")}
            suffix={<SearchOutlined style={{ color: "#bfbfbf" }} />}
            // onChange={debouncedOnChange}
          />
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setShowAddModal(true)}
          >
            {t("vente.client.add")}
          </Button>
        </Space>
      }
    >
      <ProTable
        actionRef={tableRef}
        loading={isLoading}
        columns={columns}
        dataSource={filteredData}
        rowKey="key"
        search={{
          searchText: t("common.filter"),
          labelWidth: "auto",
          defaultCollapsed: false,
        }}
        options={false}
        params={tableParams}
        scroll={{ x: "max-content" }}
        onSubmit={handleSearchData}
        onReset={() => handleSearchData({})}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: 5,
          total: total,
          onChange: (page) => setPageNumber(page),
        }}
      />
      <AddClientModal
        open={showAddModal}
        onCancel={() => setShowAddModal(false)}
      />
      <ShowClientModal
        open={showViewModal}
        onCancel={() => {
          setShowViewModal(false);
          setClient(null);
        }}
        client={client}
      />
      <EditClientModal
        open={showEditModal}
        onCancel={() => {
          setShowEditModal(false);
          setClient(null);
        }}
        client={client}
      />
    </Card>
  );
}

export default Clients;
