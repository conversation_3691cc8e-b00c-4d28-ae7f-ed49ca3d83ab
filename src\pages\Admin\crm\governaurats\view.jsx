import { Form, Input, Modal } from "antd";

const ViewGouvernorat = ({ record, onCancel }) => {
  return (
    <Modal
      title="Détails du Gouvernorat"
      visible={true}
      onCancel={onCancel}
      footer={null}
    >
      <Form layout="vertical" initialValues={record}>
        <Form.Item label="Code Gouvernorat">
          <Input value={record.code} disabled />
        </Form.Item>
        <Form.Item label="Libellé">
          <Input value={record.libelle} disabled />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ViewGouvernorat;
