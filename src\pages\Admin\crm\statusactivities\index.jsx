// src/pages/activities/ActivityStatuses.jsx
import { useState, useRef } from "react";
import { ProTable } from "@ant-design/pro-components";
import { Button, Popconfirm, Space, Input, Tooltip, Typography, Card, Modal, Table } from "antd";
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined, SearchOutlined } from "@ant-design/icons";

import CreateActivityStatus from "./createStatus";
import EditActivityStatus from "./editStatus";
import ViewActivityStatus from "./viewStatus";
import { datas } from "./datas";

function ActivityStatuses() {
  const tableRef = useRef();
  const [dataSource, setDataSource] = useState(datas);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [viewModalVisible, setViewModalVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState(null);
  const [viewingRecord, setViewingRecord] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleCancel = () => setViewModalVisible(false);

  const handleEdit = (record) => {
    setEditingRecord(record);
    setEditModalVisible(true);
  };

  // Fonction pour rendre le sous-tableau des étapes
  const expandedRowRender = (record) => {
    const columns = [
      { title: "Nom de l'étape", dataIndex: "name", key: "name" },
    ];
    return record.stages && record.stages.length > 0 ? (
      <Table
        columns={columns}
        dataSource={record.stages}
        pagination={false}
        rowKey={(stage) => stage.id}
      />
    ) : (
      <Typography.Text type="secondary">Aucune étape disponible</Typography.Text>
    );
  };

  const columns = [
  
    {
      title: "Nom de la phase",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "Description",
      dataIndex: "description",
      key: "description",
    },
    {
      title: "Actions",
      key: "action",
      align: "center",
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title="Voir">
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => {
                setViewingRecord(record);
                setViewModalVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title="Modifier">
            <Button
              type="link"
              style={{ color: "#f5b041" }}
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="Supprimer">
            <Popconfirm
              title="Êtes-vous sûr de vouloir supprimer cette phase ?"
              onConfirm={() => {
                const newDataSource = dataSource.filter((item) => item.key !== record.key);
                setDataSource(newDataSource);
              }}
            >
              <Button type="link" style={{ color: "#ec7063" }} icon={<DeleteOutlined />} />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <Card
      bordered
      title={<Typography.Title level={4}>Liste des phases</Typography.Title>}
      extra={
        <Space>
          <Input
            size="large"
            placeholder="Rechercher"
            suffix={<SearchOutlined style={{ color: "#bfbfbf" }} />}
          />
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setCreateModalVisible(true)}
          >
            Ajouter une phase
          </Button>
        </Space>
      }
    >
      <ProTable
        actionRef={tableRef}
        loading={isLoading}
        columns={columns}
        dataSource={dataSource}
        rowKey="key"
        search={false}
        options={false}
        pagination={{ showSizeChanger: true, defaultPageSize: 5 }}
        expandable={{ expandedRowRender }}
      />

      {/* Create */}
      <Modal
        title="Ajouter une phase"
        visible={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        footer={null}
        width={800}
      >
        <CreateActivityStatus 
          onCancel={() => setCreateModalVisible(false)} 
          setDataSource={setDataSource} 
        />
      </Modal>

      {/* Edit */}
      <Modal
        title="Modifier une phase"
        visible={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        footer={null}
        width={800}
      >
        <EditActivityStatus
          record={editingRecord}
          onCancel={() => setEditModalVisible(false)}
          setDataSource={setDataSource} 
        />
      </Modal>

      {/* View */}
      <Modal
        title="Détails de la phase"
        visible={viewModalVisible}
        onCancel={handleCancel}
        footer={null}
        width={800}
      >
        <ViewActivityStatus
          record={viewingRecord}
          onCancel={() => setViewModalVisible(false)}
        />
      </Modal>
    </Card>
  );
}

export default ActivityStatuses;