import { Col, Form, Input, Row, Select, Switch } from "antd";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";

const TvaForm = ({ disabled, tva }) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();

  useEffect(() => {
    if (tva) {
      form.setFieldsValue(tva);
    }
  }, [tva]);
  console.log(tva?.default);

  return (
    <Form layout="vertical" disabled={disabled} form={form}>
      <Row gutter={4} className="mt-2">
        <Col className="gutter-row d-flex justify-content-center" span={24}>
          <Form.Item
            label={t("tvas.fields.default")}
            name="default"
            valuePropName="checked" 
            initialValue={tva?.default} 
            rules={[
              {
                required: true,
                message: t("common.validations.required_default"),
              },
            ]}
          >
            <Switch />
          </Form.Item>
        </Col>
        <Col className="gutter-row" span={24}>
          <Form.Item
            name="code"
            label={t("tvas.fields.code")}
            rules={[
              {
                required: true,
                message: "common.validations.required_code",
              },
            ]}
          >
            <Input placeholder={t("tvas.fields.code")} />
          </Form.Item>
        </Col>
        <Col className="gutter-row" span={24}>
          <Form.Item
            name="designation"
            label={t("tvas.fields.designation")}
            rules={[
              {
                required: true,
                message: "common.validations.required_designation",
              },
            ]}
          >
            <Input placeholder={t("tvas.fields.designation")} />
          </Form.Item>
        </Col>
        <Col className="gutter-row" span={24}>
          <Form.Item
            name="type"
            label={t("tvas.fields.type")}
            rules={[
              {
                required: true,
                message: "common.validations.required_type",
              },
            ]}
          >
            <Select
              placeholder={t("tvas.fields.type")}
              allowClear
              defaultValue={!tva?.type && t("tvas.type.achat_et_vente")}
              options={[
                { label: t("tvas.type.achat"), value: "Achat" },
                { label: t("tvas.type.vente"), value: "Vente" },
                {
                  label: t("tvas.type.achat_et_vente"),
                  value: "Achat et Vente",
                },
              ]}
            />
          </Form.Item>
        </Col>
        <Col className="gutter-row" span={24}>
          <Form.Item
            name="valeur"
            label={t("tvas.fields.valeur")}
            rules={[
              {
                required: true,
                message: "common.validations.required_valeur",
              },
            ]}
          >
            <Input placeholder={t("tvas.fields.valeur")} type="number" />
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );
};

export default TvaForm;
