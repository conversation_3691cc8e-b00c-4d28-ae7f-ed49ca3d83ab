
import React, { useState, useRef, useEffect, useMemo } from 'react';
import {

  Descriptions,
  Collapse
} from 'antd';
import {

  InfoCircleOutlined
} from '@ant-design/icons';

const { Panel } = Collapse;

const VariableInfo = ({ variables }) => {
  const variableDescriptions = {
    'name': "USER NAME ",
    'email': "Adresse email de l'utilisateur",
    'resetLink': "Lien unique pour réinitialiser le mot de passe",
  };

  return (
    <div style={{ marginTop: 16 }}>
      <Collapse bordered={false} ghost>
        <Panel 
          header={
            <span>
              <InfoCircleOutlined style={{ marginRight: 8 }} />
              Variables à remplacer
            </span>
          } 
          key="1"
        >
          <Descriptions bordered size="small" column={1}>
            {variables.map(variable => (
              <Descriptions.Item 
                key={variable} 
                label={`{{${variable}}}`}
              >
                {variableDescriptions[variable] || "Aucune description disponible"}
              </Descriptions.Item>
            ))}
          </Descriptions>
        </Panel>
      </Collapse>
    </div>
  );
};
export default VariableInfo;