import React from "react";
import fipaLogo from "./fipa.png";
const styles = {
  container: {
    fontFamily: "Arial, sans-serif",
    padding: "20px",
    backgroundColor: "#fff",
  },
  header: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: "20px",
  },
  logoContainer: {
    flex: 1,
  },
  logo: {
    width: "100px", // Adjust based on your logo size
    height: "auto",
  },
  titleContainer: {
    flex: 2,
    textAlign: "right",
  },
  headerTitle: {
    fontSize: "24px",
    fontWeight: "bold",
  },
  body: {
    textAlign: "center",
  },
  bodyTitle: {
    fontSize: "28px",
    fontWeight: "bold",
    marginBottom: "20px",
  },
  bodyContent: {
    fontSize: "16px",
    lineHeight: "1.6",
  },
};
const FicheASC = React.forwardRef(({ record }, ref) => {
  const ascData = {
    donnees: {
      intutile: "0420029F01",
      date_debut: "10/05/2024",
      date_fin: "10/12/2025",
      pays: "France",
      region: "Région test",
      secteur: "Secteur",
      groupe: "Groupe",
    },
    contact: {
      responsable: "M.GIRVES",
      fonction: "D.G",
      nationalite : "Francais" ,
      date_contact: "10/12/2024",
      adresse: "RTE DE FOUCHANA KM 8-2082 FOUCHANA",
      tel: "71 245 716",
      fax: "71 256 256",
      email : "<EMAIL>" ,
      secteur : "ITO"
    },
    activite: {
      secteur: "Agro-alimentaire",
      activite: "AUTRES INDUSTRIES AGRICOLES ET ALIMENTAIRES",
      production: "",
    },
  };
  return (
    <div ref={ref} style={styles.container}>
      <div style={styles.header}>
        <div style={styles.logoContainer}>
          <img src={fipaLogo} alt="Logo" style={styles.logo} />
        </div>
        <div style={styles.titleContainer}>
          <h1 style={styles.headerTitle}>FIPA</h1>
        </div>
      </div>

      <main>
        <div style={{ textAlign: "center", marginBottom: "20px" }}>
          <h1 style={{ margin: 0 }}>FICHE AUTRE SOURCE DE CONTACTS</h1>
        </div>

        {/* Identification Section */}
        <Section title="Données générales">
          <DataRow label="Intutilé" value={ascData.donnees.intutile} />
          <DataRow label="Date Début" value={ascData.donnees.date_debut} />
          <DataRow label="Date Fin" value={ascData.donnees.date_fin} />
          <DataRow label="Pays" value={ascData.donnees.pays} />
          <DataRow label="Secteur" value={ascData.donnees.secteur} />
          <DataRow label="Groupe" value={ascData.donnees.groupe} />
        </Section>
        <section>
          <h3>Les contacts effectués</h3>
        </section>
        {/* Coordonnées Section */}
        <Section title="Im bus AG">
          <DataRow label="Responsable contacté" value={ascData.contact.responsable}/>
          <DataRow label="Fonction" value={ascData.contact.fonction} />
          <DataRow label="Nationalié" value={ascData.contact.nationalite} />
          <DataRow label="Date du contact" value={ascData.contact.date_contact} />
          <DataRow label="Adresse" value={ascData.contact.adresse} />
          <DataRow label="Tél " value={ascData.contact.tel} />
          <DataRow label="Fax " value={ascData.contact.fax} />
          <DataRow label="E-mail " value={ascData.contact.email} />
          <DataRow label="Secteur " value={ascData.contact.secteur} />
        </Section>
      </main>

      {/* Footer (optional) */}
      <footer
        style={{
          marginTop: "20px",
          borderTop: "1px solid #ddd",
          paddingTop: "10px",
          textAlign: "center",
          fontSize: "12px",
          color: "#666",
        }}
      >
        <p> {new Date().getFullYear()} FIPA</p>
      </footer>
    </div>
  );
});
const Section = ({ title, children }) => (
  <div
    style={{
      margin: "15px 0",
      padding: "10px 0",
      borderBottom: "1px solid #ddd",
    }}
  >
    <h4
      style={{
        margin: "0 0 10px 0",
        fontSize: "16px",
        fontWeight: "bold",
        color: "#333",
      }}
    >
      {title}
    </h4>
    {children}
  </div>
);
// Reusable Data Row Component
const DataRow = ({ label, value }) => (
  <div
    style={{
      display: "grid",
      gridTemplateColumns: "150px 1fr",
      margin: "5px 0",
      alignItems: "center",
    }}
  >
    <span style={{ fontWeight: "600", color: "#666" }}>{label}</span>
    <span style={{ paddingLeft: "10px" }}>{value || "-"}</span>
  </div>
);
export default FicheASC;
