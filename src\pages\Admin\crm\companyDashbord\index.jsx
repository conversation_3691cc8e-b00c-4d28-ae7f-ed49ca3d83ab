import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, ResponsiveContainer } from 'recharts';
import { Row, Col, Card, Statistic } from 'antd';

// Sample data
const sectorData = [
  { name: 'Industries diverses', value: 27 },
  { name: 'Technologie', value: 25 },
  { name: 'Services', value: 18 },
  { name: 'Agriculture', value: 15 },
  { name: 'ReTourismetail', value: 10 },
];

const governorateData = [
  { governorate: 'Tunis', count: 45 },
  { governorate: 'Sfax', count: 30 },
  { governorate: 'Sousse', count: 25 },
  { governorate: 'Gabes', count: 20 },
  { governorate: 'Nabeul', count: 15 },
];

const CompanyDashboard = () => {
  return (
    <div style={{ padding: '24px', background: '' }}>
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <h1>Tableau de bord des entreprises</h1>
        </Col>

        {/* Total Companies */}
        <Col span={24}>
          <Card>
            <Statistic
              title="Nombre total d'entreprises"
              value={sectorData.reduce((sum, item) => sum + item.value, 0)}
            />
          </Card>
        </Col>

        {/* Companies by Sector */}
        <Col xs={24} md={24}>
          <Card title="Nombre d'entreprises par secteur">
            <ResponsiveContainer width="100%" height={300}>
              <PieChart margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
                <Pie
                  data={sectorData}
                  dataKey="value"
                  nameKey="name"
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  fill="#8884d8"
                  label={({
                    cx,
                    cy,
                    midAngle,
                    innerRadius,
                    outerRadius,
                    value,
                    index,
                  }) => {
                    const RADIAN = Math.PI / 180;
                    const radius = 25 + innerRadius + (outerRadius - innerRadius);
                    const x = cx + radius * Math.cos(-midAngle * RADIAN);
                    const y = cy + radius * Math.sin(-midAngle * RADIAN);

                    return (
                      <text
                        x={x}
                        y={y}
                        fill="#8884d8"
                        textAnchor={x > cx ? 'start' : 'end'}
                        dominantBaseline="central"
                      >
                        {sectorData[index].name} ({value})
                      </text>
                    );
                  }}
                />
                <Tooltip />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </Card>
        </Col>

        {/* Companies by Governorate */}
        <Col xs={24} md={24}>
          <Card title="Nombre d'entreprises par gouvernorat">
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={governorateData} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
                <XAxis dataKey="governorate" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar 
                  dataKey="count" 
                  fill="#82ca9d" 
                  name="Nombre d'entreprises"
                />
              </BarChart>
            </ResponsiveContainer>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default CompanyDashboard;
