import dayjs from "dayjs";

const projets = [
  {
    id: 1,
    rs_tunis: "Dalphimetal-tunisie",
    rs_etranger: "Dalphimetal",
    responsable: "resposable Fipa",
    secteur_id: 2,
    type_id: 1,
    date_maj : dayjs("2025-03-20") ,
    date_category : dayjs("2025-01-20") 
  },
];
const secteurs = [
  { id: 1, secteur: "Industries diverses" },
  { id: 2, secteur: "Technologie" },
  { id: 3, secteur: "Services" },
  { id: 4, secteur: "Tourisme" },
  { id: 5, secteur: "Agriculture" },
  { id: 6, secteur: "Energie" },
] ;
const gouvernorats = [
  { id: 1, nom: "Tunis" },
  { id: 2, nom: "Ariana" },
  { id: 3, nom: "Ben Arous" },
  { id: 4, nom: "Manou<PERSON>" },
  { id: 5, nom: "Na<PERSON><PERSON>" },
  { id: 6, nom: "<PERSON><PERSON><PERSON><PERSON>" },
  { id: 7, nom: "Bizerte" },
  { id: 8, nom: "<PERSON><PERSON>" },
  { id: 9, nom: "<PERSON><PERSON><PERSON>" },
  { id: 10, nom: "<PERSON> Kef" },
  { id: 11, nom: "Siliana" },
  { id: 12, nom: "<PERSON>rouan" },
  { id: 13, nom: "Kasserine" },
  { id: 14, nom: "Sidi Bouzid" },
  { id: 15, nom: "Sousse" },
  { id: 16, nom: "Monastir" },
  { id: 17, nom: "Mahdia" },
  { id: 18, nom: "Sfax" },
  { id: 19, nom: "Gafsa" },
  { id: 20, nom: "Tozeur" },
  { id: 21, nom: "Kebili" },
  { id: 22, nom: "Gabes" },
  { id: 23, nom: "Medenine" },
  { id: 24, nom: "Tataouine" },
];
const types = [
  { id: 1, nom: "Projets suivis" },
  { id: 2, nom: "Projets exclus" },
];
const pays = [
  { id: 1, nom: "France" },
  { id: 2, nom: "Italie" },
  { id: 3, nom: "Allemagne" },
  { id: 4, nom: "Espagne" },
  { id: 5, nom: "États-Unis" },
  { id: 6, nom: "Canada" },
  { id: 7, nom: "Algérie" },
  { id: 8, nom: "Maroc" },
  { id: 9, nom: "Libye" },
  { id: 10, nom: "Turquie" },
  { id: 11, nom: "Arabie Saoudite" },
  { id: 12, nom: "Émirats arabes unis" },
  { id: 13, nom: "Qatar" },
  { id: 14, nom: "Égypte" },
  { id: 15, nom: "Chine" },
  { id: 16, nom: "Japon" },
  { id: 17, nom: "Corée du Sud" },
  { id: 18, nom: "Belgique" },
  { id: 19, nom: "Suisse" },
  { id: 20, nom: "Russie" },
];

export { projets, gouvernorats ,secteurs , types };
