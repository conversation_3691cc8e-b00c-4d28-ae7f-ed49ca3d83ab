import { ModalForm } from "@ant-design/pro-components";
import { message } from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import MoyenPaiementForm from "../../components/MoyenPaiementForm";

const AddMoyenPaiementModal = ({ open, onCancel }) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (values) => {
    setLoading(true);
    try {
      const submissionData = {
        ...values,
      };
      await new Promise((resolve) => setTimeout(resolve, 2000));

      message.success("Moyen de paiement ajouté avec succès !");
    } catch (error) {
      console.log(error);
      message.error("Erreur lors de l'ajout du moyen de paiement !");
    } finally {
      setLoading(false);
      onCancel();
    }
  };

  return (
    <ModalForm
      title={t("moyens-paiement.add")}
      open={open}
      onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.save"),
          resetText: t("common.actions.cancel"),
        },
        submitButtonProps: {
          loading,
        },
      }}
      modalProps={{
        destroyOnClose: true,
      }}
    >
      <MoyenPaiementForm disabled={false} moyenPaiement={null} />
    </ModalForm>
  );
};

export default AddMoyenPaiementModal;
