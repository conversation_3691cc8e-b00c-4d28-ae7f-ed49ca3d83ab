import React from 'react';
import { Row, Col, Form, Input, Select, Upload, Button, message } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import { categories, formats } from './data';

const { Option } = Select;
const { TextArea } = Input;

const DocumentCreate = ({ onCreate }) => {
  const [form] = Form.useForm();
  const [fileList, setFileList] = React.useState([]);

  const onFinish = (values) => {
    if (fileList.length === 0) {
      message.error('Please upload a file');
      return;
    }

    const newDocument = {
      ...values,
      file: fileList[0].name,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    onCreate(newDocument);
    form.resetFields();
    setFileList([]);
  };

  const getFileExtension = (filename) => {
    return filename.split('.').pop().toLowerCase();
  };

  const uploadProps = {
    onRemove: () => {
      setFileList([]);
    },
    beforeUpload: (file) => {
      // Get selected format from form
      const selectedFormat = form.getFieldValue('format')?.toLowerCase();
      
      if (!selectedFormat) {
        message.error('Please select a format first');
        return false;
      }

      const fileExtension = getFileExtension(file.name);
      const formatToExtension = {
        'pdf': 'pdf',
        'docx': 'docx',
        'xlsx': 'xlsx',
        'pptx': 'pptx',
        'txt': 'txt',
      };

      if (formatToExtension[selectedFormat] !== fileExtension) {
          message.error(`L'extension du fichier (.${fileExtension}) ne correspond pas au format sélectionné (${selectedFormat.toUpperCase()})`);
        return false;
      }

      setFileList([file]);
      return false;
    },
    fileList,
    maxCount: 1
  };

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={onFinish}
    >
      <Form.Item
        name="name"
        label="Nom de la Document"
        rules={[{ required: true, message: 'Please input the document name!' }]}
      >
        <Input placeholder="e.g. Client FAQ" />
      </Form.Item>

      <Form.Item
        name="summary"
        label="Discription"
        rules={[{ required: true, message: 'Please input a summary!' }]}
      >
        <TextArea rows={3} placeholder="Brief description of the document" />
      </Form.Item>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            name="category"
            label="Catégorie"
            rules={[{ required: true, message: 'Please select a category!' }]}
          >
            <Select placeholder="Select category">
              {categories.map(category => (
                <Option key={category} value={category}>{category}</Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="format"
            label="Format"
            rules={[{ required: true, message: 'Please select a format!' }]}
          >
            <Select placeholder="Select format" onChange={() => setFileList([])}>
              {formats.map(format => (
                <Option key={format} value={format}>{format.toUpperCase()}</Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
      </Row>

      <Form.Item
        label="Télécharger un fichier"
        rules={[{ required: true, message: 'Please upload a file!' }]}
      >
        <Upload {...uploadProps}>
          <Button icon={<UploadOutlined />}>Choisir un fichier</Button>
        </Upload>
        <div style={{ marginTop: 8, fontSize: 12, color: '#666' }}>
          {form.getFieldValue('format') && `Expected format: .${form.getFieldValue('format').toLowerCase()}`}
        </div>
      </Form.Item>

      <Form.Item>
        <div style={{ textAlign: 'right' }}>
          <Button type="primary" htmlType="submit">
            Enregistrer
          </Button>
        </div>
      </Form.Item>
    </Form>
  );
};

export default DocumentCreate;