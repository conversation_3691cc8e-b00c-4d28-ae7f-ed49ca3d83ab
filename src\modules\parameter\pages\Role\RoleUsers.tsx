import React, { useEffect, useState, useCallback } from "react";
import {
  <PERSON>er,
  Button,
  AutoComplete,
  List,
  Avatar,
  Form,
  Tooltip,
  Row,
  Input,
} from "antd";
import { SearchOutlined, UserOutlined } from "@ant-design/icons";
import VirtualList from "rc-virtual-list";
import { useSelector } from "react-redux";
const RoleUsers = ({ setVisible, visible, role }) => {
  let [users, setUsers] = useState([]);

  const onChange = (e) => {
    const { value } = e.target;
    setUsers(
      role.users.filter(
        (item) =>
          item.name?.toUpperCase().includes(value.toUpperCase()) ||
          item.prenom?.toUpperCase().includes(value.toUpperCase()) ||
          item.email?.toUpperCase().includes(value.toUpperCase())
      )
    );
  };

  useEffect(() => {
    setUsers(role?.users);
  }, [visible]);
  return (
    <Drawer
      className="Users has rôle"
      title={"Liste des utilsateurs ayant le rôle " + role?.role}
      placement="right"
      width={window.innerWidth > 620 ? 580 : "90%"}
      onClose={() => {
        setVisible(false);
      }}
      open={visible}
    >
      {/* <Form
          form={form}
        >
          <span style={{ fontWeight: "bold" }}>Ajouter un employé</span>
          <Row style={{ display: "flex" }} className="mt-3">
            <Form.Item
              name="selectEmploye"
              style={{
                width: "77%",
                marginRight:"3%"
              }}
              rules={[
                {
                  required: true,
                  message: "Please select member!",
                },
              ]}
            >
              <AutoComplete options={options} placeholder="Employe" />
            </Form.Item>
            <Button
              htmlType="submit"
              type="primary"
              style={{
                width: "20%",
              }}
            >
              Ajouter
            </Button>
          </Row>
        </Form> */}
      {/* <div style={{ marginTop: "20px" }}> */}
      {/* <span style={{ fontWeight: "bold"}}>Liste des utilisateurs</span> */}
      <Input
        style={{ marginBottom: 20 }}
        placeholder=" Rechercher"
        onChange={onChange}
        allowClear
        prefix={<SearchOutlined className="text-[#BDC3C7]" />}
      />
      <List>
        <VirtualList data={users} /* height={420} */ itemHeight={42} itemKey="id">
          {(item) => (
            <List.Item key={item.email}>
              <List.Item.Meta
                avatar={
                  <Avatar
                    src={item.photo ? item.photo : null}
                    icon={<UserOutlined />}
                  />
                }
                title={item.prenom ? item.name + " " + item.prenom : item.name}
                description={item.email}
              />
              {/* <Tooltip title='Supprimer'>
            <UserDeleteOutlined className="deleteMember"/>
            </Tooltip> */}
            </List.Item>
          )}
        </VirtualList>
      </List>
      {/* </div> */}
    </Drawer>
  );
};

export default RoleUsers;
