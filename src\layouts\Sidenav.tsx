import { Menu, Space } from "antd";
import { NavLink, useLocation } from "react-router-dom";
import { useEffect, useState } from "react";
import sideNavLogo from "../assets/images/logo_erp.png";
import FipaLogo from "../assets/images/fipa.png";
import { icons } from "lucide-react";
import { useTranslation } from "react-i18next";
import routes from "../routes/crm-routes"; // Import your routes

function SidenavExample({ color }) {
  const { t } = useTranslation();
  const { pathname } = useLocation();
  const Icon = ({ name, color = "#005086", size = 17 }) => {
    try {
      const LucideIcon = icons[name];
      if (!LucideIcon) {
      } else {
        return (
          <LucideIcon
            color={color}
            size={size}
            style={{ display: "inline-block" }}
          />
        );
      }
    } catch (ex) {
      console.log(ex);
    }
  };

  // Function to get open keys for the menu
  const getOpenKeys = () => {
    const openKeys = [];
    return openKeys;
  };

  // Generate menu items from routes
  const generateMenuItemsFromRoutes = () => {
    // Filter routes that should appear in the menu
    const menuRoutes = routes.filter((route) => !route.path.includes("*"));

    // Group routes by their base path for sub-menus
    const groupedRoutes = menuRoutes.reduce<Record<string, typeof routes>>((acc, route) => {
      const basePath = route.path.split("/").slice(0, 3).join("/");
      if (!acc[basePath]) {
        acc[basePath] = [];
      }
      acc[basePath].push(route);
      return acc;
    }, {});

    // Create menu items
    return Object.entries(groupedRoutes).map(([basePath, routes]) => {
      // If there's only one route in this group, create a single menu item
      const route = routes[0];
      if (routes.length === 1) {
        return {
          key: route.path,
          label: (
            <NavLink to={"/" + route.path}>
              <span
                className="icon"
                style={{ background: pathname === route.path ? color : "" }}
              >
                {route.icon ? (
                  <Icon
                    name={route.icon}
                    color={pathname === route.path ? color : "#005086"}
                  />
                ) : (
                  <Icon name="FileText" />
                )}
              </span>
              <span className="label">{t(route.path.split("/").pop())}</span>
            </NavLink>
          ),
        };
      }

      // If there are multiple routes, create a submenu
      return {
        key: basePath,
        label: (
          <span>
            <span className="icon">
              {route.icon ? (
                <Icon
                  name={route.icon}
                  color={pathname === route.path ? color : "#005086"}
                />
              ) : (
                <Icon name="FileText" />
              )}
            </span>
            <span className="label">{t(basePath.split("/").pop())}</span>
          </span>
        ),
        children: routes?.map((route) => ({
          key: route.path,
          label: (
      <NavLink className={pathname === route.path ? "active mx-5" : "mx-5"} to={"/" + route.path}>
              <span
                className="icon"
                style={{ background: pathname === route.path ? color : "" }}
              >
                {route.icon ? (
                  <Icon
                    name={route.icon}
                    color={pathname === route.path ? color : "#005086"}
                  />
                ) : (
                  <Icon name="FileText" />
                )}
              </span>
              <span className="label">{t(route.path.split("/").pop())}</span>
            </NavLink>
          ),
        })),
      };
    });
  };

  return (
    <>
      <Space className="brand ml-2">
        <img src={FipaLogo} width="100%" alt="logo" className="fipa" />
      </Space>
      <hr />
      <Menu
        theme="light"
        mode="inline"
        items={generateMenuItemsFromRoutes()}
        defaultOpenKeys={getOpenKeys()}
        className="navMenu"
      />
    </>
  );
}

export default SidenavExample;
