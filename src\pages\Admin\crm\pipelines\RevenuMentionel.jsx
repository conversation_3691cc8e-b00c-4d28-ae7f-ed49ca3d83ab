import { useState } from "react";
import { <PERSON><PERSON><PERSON>, Line, CartesianGrid, XAxis, YAxis, <PERSON>lt<PERSON>, Legend, ResponsiveContainer } from "recharts";
import { Select } from "antd";

const revenueData = {
  2023: [
    { month: "Jan", revenue: 12000 },
    { month: "Feb", revenue: 15000 },
    { month: "Mar", revenue: 13000 },
    { month: "Apr", revenue: 18000 },
    { month: "May", revenue: 22000 },
    { month: "Jun", revenue: 25000 },
    { month: "Jul", revenue: 27000 },
    { month: "Aug", revenue: 24000 },
    { month: "Sep", revenue: 21000 },
    { month: "Oct", revenue: 23000 },
    { month: "Nov", revenue: 28000 },
    { month: "Dec", revenue: 30000 },
  ],
  2024: [
    { month: "Jan", revenue: 14000 },
    { month: "Feb", revenue: 16000 },
    { month: "Mar", revenue: 15000 },
    { month: "Apr", revenue: 19000 },
    { month: "May", revenue: 23000 },
    { month: "Jun", revenue: 26000 },
    { month: "Jul", revenue: 28000 },
    { month: "Aug", revenue: 25000 },
    { month: "Sep", revenue: 22000 },
    { month: "Oct", revenue: 24000 },
    { month: "Nov", revenue: 29000 },
    { month: "Dec", revenue: 31000 },
  ],
};

const RevenueChart = () => {
  const [selectedYear, setSelectedYear] = useState(2024);

  return (
    <div style={{ width: "100%", padding: "20px" }}>
      <h2 style={{ textAlign: "center" }}>📊 Revenus Mensuels</h2>
      <Select
        defaultValue={selectedYear}
        onChange={(value) => setSelectedYear(value)}
        style={{ width: 120, marginBottom: 20 }}
      >
        {Object.keys(revenueData).map((year) => (
          <Select.Option key={year} value={Number(year)}>
            {year}
          </Select.Option>
        ))}
      </Select>
      <ResponsiveContainer width="100%" height={350}>
        <LineChart data={revenueData[selectedYear]}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="month" />
          <YAxis />
          <Tooltip />
          <Legend />
          <Line type="monotone" dataKey="revenue" stroke="#1890ff" strokeWidth={2} />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};

export default RevenueChart;
