import {
  CheckCircleOutlined,
  CheckCircleTwoTone,
  PlusCircleFilled,
  UploadOutlined,
} from "@ant-design/icons";
import { ModalForm } from "@ant-design/pro-components";
import {
  Button,
  Card,
  Checkbox,
  Col,
  DatePicker,
  Divider,
  Form,
  Input,
  message,
  Modal,
  Popover,
  Radio,
  Row,
  Select,
  Steps,
  Upload,
} from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";

function CreateSMDelegation({ open, onCancel, onSuccess }) {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const handleSubmit = (values) => {};
  const [showMotifModal, setShowMotifModal] = useState(false);
 
  return (
    <ModalForm
      title={t("Ajouter une visite de délégation")}
      form={form}
      open={open}
      modalProps={{
        style: { top: 20 },
      }}
      onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.save"),
          resetText: t("common.actions.cancel"),
        },
      }}
    >
      <Card className="mt-2">
        <Row gutter={16}>
          <Col md={12} xs={24}>
            <Form.Item name={""} label={t("Responsable FIPA")}>
              <Select />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name={""} label={t("Date de la visite")}>
              <DatePicker />
            </Form.Item>
          </Col>

          <Col md={24} xs={24}>
            <Form.Item name={""} label={t("Délégation")}>
              <Input />
            </Form.Item>
          </Col>

          <Col md={18} xs={24}>
            <Form.Item name={""} label={t("Initiateur")}>
              <Select />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name={""} label={t("Contact")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name={""} label={t("Fonction")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={24} xs={24}>
            <Form.Item name={""} label={t("Adresse")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item name={""} label={t("Téléphone")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item name={""} label={t("Fax")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item name={""} label={t("Nationalité")}>
              <Select />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name={""} label={t("E-mail")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name={""} label={t("site web")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name={""} label={t("Activité")}>
              <Input.TextArea rows={"2"} />
            </Form.Item>
          </Col>
          <Col md={6} xs={24}>
            <Form.Item name={""} label={t("Secteur")}>
              <Select />
            </Form.Item>
          </Col>
          <Col md={6} xs={24}>
            <Form.Item name={""} label={t("Groupe")}>
              <Select />
            </Form.Item>
          </Col>

          <Col md={24} xs={24}>
            <Form.Item name={""} label={t("Programme de la visite")}>
              <Input.TextArea rows={"2"} />
            </Form.Item>
          </Col>
          <Col md={24} xs={24}>
            <Form.Item name={""} label={t("Evaluation et suivi")}>
              <Input.TextArea rows={"2"} />
            </Form.Item>
          </Col>
         
        </Row>
      </Card>

    
    </ModalForm>
  );
}
export default CreateSMDelegation;
