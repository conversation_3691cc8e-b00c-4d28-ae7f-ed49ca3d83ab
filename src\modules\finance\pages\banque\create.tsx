import { ModalForm } from "@ant-design/pro-components";
import { Form, Input, message } from "antd";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

function CreateBanque({ open, onCancel, isEditing, show }) {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  useEffect(() => {
    console.log(show)
    form.setFieldsValue(isEditing);
  }, [isEditing, open]);

  const handleSubmit = async (values) => {
    setLoading(true);
    try {
      await new Promise((resolve) => setTimeout(resolve, 2000));
      message.success(t("finance.banques.add") + " " + t("common.actions.save"));
    } catch (error) {
      console.log(error);
      message.error(t("finance.banques.validations.required_libelle"));
    } finally {
      setLoading(false);
      onCancel();
    }
  };

  return (
    <ModalForm
      title={
        (isEditing && !show)
          ? t("finance.banques.edit")
          : show
          ? t("finance.banques.view")
          : t("finance.banques.add")
      }
      open={open}
      onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={show ? false : {  // Désactiver le footer si show est true
        searchConfig: {
          submitText: t("common.actions.save"),
          resetText: t("common.actions.cancel"),
        },
        submitButtonProps: {
          loading,
        },
      }}
      modalProps={{
        destroyOnClose: true,
      }}
    >
      <Form layout="vertical" disabled={show} form={form}>
        <Form.Item
          name="libelle"
          label={t("finance.banques.fields.libelle")}
          rules={[
            {
              required: true,
              message: t("finance.banques.validations.required_libelle"),
            },
          ]}
        >
          <Input placeholder={t("finance.banques.fields.libelle")} />
        </Form.Item>
      </Form>
    </ModalForm>
  );
}

export default CreateBanque;
