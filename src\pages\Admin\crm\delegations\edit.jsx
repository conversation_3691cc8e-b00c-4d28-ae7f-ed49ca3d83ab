import { ProForm, ProFormText, ProFormSwitch } from "@ant-design/pro-form";
import { message } from "antd";

const EditDelegation = ({ record, onCancel, setDataSource }) => {
  const handleSubmit = async (values) => {
    try {
      const updatedData = delegations.map(item => item.key === record.key ? { ...item, ...values } : item);
      setDataSource(updatedData);
      message.success("Délégation modifiée avec succès!");
      onCancel();
    } catch (error) {
      console.error(error);
      message.error("Erreur lors de la modification de la délégation");
    }
  };

  return (
    <ProForm initialValues={record} onFinish={handleSubmit}>
      <ProFormText name="code" label="Code" placeholder="Saisissez le code" rules={[{ required: true }]} />
      <ProFormText name="libelle" label="Libellé" placeholder="Saisissez le libellé" rules={[{ required: true }]} />
      <ProFormSwitch name="decentralisee" label="Décentralisée" />
      <ProFormText name="tauxPrime" label="Taux Prime" placeholder="Saisissez le taux prime" rules={[{ required: true }]} />
    </ProForm>
  );
};

export default EditDelegation;
