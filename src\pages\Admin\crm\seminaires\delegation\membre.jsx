
import { DownloadOutlined, UploadOutlined } from "@ant-design/icons";
import { ModalForm } from "@ant-design/pro-components";
import {  <PERSON>ton, Card, Col, DatePicker, Form, Input, Row, Select, Space, Upload } from "antd";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

function MembreForm({ open, onCancel, record }) {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
 

    const [file, setFile] = useState(null);
  const [fileUrl, setFileUrl] = useState("");

  const handleUpload = (info) => {
    if (info.file.status === "done") {
      const uploadedFile = info.file.originFileObj;
      const url = URL.createObjectURL(uploadedFile);
      setFile(uploadedFile);
      setFileUrl(url);
      message.success(`${info.file.name} est importé avec succès`);
    }
  };
  const beforeUpload = (file) => {
    const isPDF = file.type === "application/pdf";
    if (!isPDF) {
      message.error("Vous pouvez uniquement importer des fichiers PDF!");
      return Upload.LIST_IGNORE;
    }
    return true;
  };
  const handleDownload = () => {
    if (!fileUrl || !file) return;

    const link = document.createElement("a");
    link.href = fileUrl;
    link.download = file.name || "document.pdf";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };
  return (
    <ModalForm
      title={t("Liste des membres")}
      form={form}
      open={open}
  
      modalProps={{
        style: { top: 20 },
      }}
      //   onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.save"),
          resetText: t("common.actions.cancel"),
        },
      }}
    >
   
      <Card className="mt-2">
        <Space direction="horizontal" style={{ width: "100%" }}>
          <Upload
            accept=".pdf"
            beforeUpload={beforeUpload}
            customRequest={({ onSuccess }) =>
              setTimeout(() => onSuccess("ok"), 0)
            }
            onChange={handleUpload}
            maxCount={1}
            showUploadList={false}
          >
            <Button icon={<UploadOutlined />}>
              Sélectionner la liste (PDF)
            </Button>
          </Upload>
         {file && <Button
            key="download"
            type="primary"
            icon={<DownloadOutlined />}
            onClick={handleDownload}
            disabled={!file}
          >
            Télécharger la liste des membres (PDF)
          </Button>}
        </Space>
      </Card>
    </ModalForm>
  );
}
export default MembreForm;
