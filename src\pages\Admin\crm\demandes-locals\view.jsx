// ViewDemande.jsx
import React from 'react';
import { Modal, Button, Form, Input, Divider } from 'antd';

const ViewDemande = ({ record, onCancel }) => {
  return (
   
      <Form layout="vertical" initialValues={record} disabled>
        <Divider>Informations sur la demande</Divider>
        <Form.Item label="Date d'arrivée">
          <Input value={record.dateArrivee} disabled />
        </Form.Item>
        <Form.Item label="Demande satisfaite">
          <Input value={record.demandeSatisfaite} disabled />
        </Form.Item>
        <Form.Item label="Date de réponse">
          <Input value={record.dateReponse} disabled />
        </Form.Item>
        <Form.Item label="Raison sociale du promoteur">
          <Input value={record.rsPromoteur} disabled />
        </Form.Item>
        <Form.Item label="Secteur d'activité">
          <Input value={record.secteur} disabled />
        </Form.Item>
        <Form.Item label="Adresse">
          <Input value={record.adresse} disabled />
        </Form.Item>
        <Form.Item label="Gouvernorat">
          <Input value={record.gouvernorats} disabled />
        </Form.Item>
        <Form.Item label="Source de la demande">
          <Input value={record.sourceDemande} disabled />
        </Form.Item>
        <Form.Item label="Observations sur la demande">
          <Input.TextArea value={record.observationDemande} disabled />
        </Form.Item>
        <Form.Item label="Observations sur la réponse">
          <Input.TextArea value={record.observationReponse} disabled />
        </Form.Item>
      </Form>
  );
};

export default ViewDemande;
