
export const responsables = [
    { id: 101, nom: "<PERSON>" },
    { id: 102, nom: "<PERSON>" },
    { id: 103, nom: "<PERSON>" },
    { id: 104, nom: "<PERSON>" },
    { id: 105, nom: "<PERSON>" },
];
export const produits = [
  { key: 1, id: 1, code: "P001", designation: "Produit A", type: 'bien', status: "Actif", category_id: 1, sub_category_id: 1, purchase_unit_id: 1, 
    stock_unit_id: 2, sale_unit_id: 3, description: "Description du produit A", supplier_id: 1, purchase_price: 10, prime_cost: 12, price_excluding_tax: 15, 
    price_including_tax: 18, frais_type: 2, frais: 5, marge_type:2, marge: "20", vat_rate: 5, costs: 2, allow_negative_stock:1, min_threshold: 5, max_threshold: 50,  },
  { key: 2, id: 2, code: "P002", designation: "Produit B", type: 'bien', status: "Inactif", category_id: 2, sub_category_id: 3, purchase_unit_id: 2, 
    stock_unit_id: 3, sale_unit_id: 1, description: "Description du produit B", supplier_id: 2, purchase_price: 8, prime_cost: 10, price_excluding_tax: 13, 
    price_including_tax: 16, frais_type: 2, frais: 5, marge_type:2, marge: "18", vat_rate: 5, costs: 1.5, allow_negative_stock:1, min_threshold: 3, max_threshold: 40, },
  { key: 3, id: 3, code: "P003", designation: "Produit C", type: 'bien', status: "Actif", category_id: 3, sub_category_id: 2, purchase_unit_id: 3, 
    stock_unit_id: 1, sale_unit_id: 2, description: "Description du produit C", supplier_id: 3, purchase_price: 12, prime_cost: 14, price_excluding_tax: 17, 
    price_including_tax: 20, frais_type: 2, frais: 5, marge_type:2, marge: "22", vat_rate: 5, costs: 2.5, allow_negative_stock:1, min_threshold: 4, max_threshold: 45, },
  { key: 4, id: 4, code: "P004", designation: "Produit D", type: 'bien', status: "Actif", category_id: 4, sub_category_id: 4, purchase_unit_id: 4, 
    stock_unit_id: 2, sale_unit_id: 3, description: "Description du produit D", supplier_id: 4, purchase_price: 15, prime_cost: 18, price_excluding_tax: 22, 
    price_including_tax: 26, frais_type: 2, frais: 5, marge_type:2, marge: "25", vat_rate: 5, costs: 3, allow_negative_stock:1, min_threshold: 6, max_threshold: 55, },
  { key: 5, id: 5, code: "P005", designation: "Produit E", type: 'bien', status: "Inactif", category_id: 5, sub_category_id: 5, purchase_unit_id: 1, 
    stock_unit_id: 3, sale_unit_id: 2, description: "Description du produit E", supplier_id: 5, purchase_price: 9, prime_cost: 11, price_excluding_tax: 14, 
    price_including_tax: 17, frais_type: 2, frais: 5, marge_type:2, marge: "19", vat_rate: 5, costs: 2, allow_negative_stock:1, min_threshold: 3, max_threshold: 35, },
];

export const articleEmplacements=[
  {article_id:1, emplacement_id:1, quantity:10},
  {article_id:3, emplacement_id:2, quantity:5},
  {article_id:2, emplacement_id:1, quantity:11},
  {article_id:2, emplacement_id:2, quantity:12},
  {article_id:1, emplacement_id:3, quantity:13},
  {article_id:1, emplacement_id:4, quantity:14},
  {article_id:1, emplacement_id:5, quantity:15},
];

export const articleMouvements = [
  {
    id: "1",
    article_id:1,
    type_mouvement: "Entrée",
    quantity: 50,
    date: "2024-04-01",
  },
  {
    id: "2",
    article_id:1,
    type_mouvement: "Sortie",
    quantity: 20,
    date: "2024-04-02",
  },
  {
    id: "3",
    article_id:1,
    type_mouvement: "Entrée",
    quantity: 100,
    date: "2024-04-03",
  },
  {
    id: "4",
    article_id:1,
    type_mouvement: "Sortie",
    quantity: 30,
    date: "2024-04-04",
  },
  {
    id: "5",
    article_id:1,
    type_mouvement: "Entrée",
    quantity: 75,
    date: "2024-04-05",
  },
  {
    id: "6",
    article_id: 1,
    type_mouvement: "Sortie",
    quantity: 40,
    date: "2024-04-06",
  },
  {
    id: "7",
    article_id: 1,
    type_mouvement: "Entrée",
    quantity: 60,
    date: "2024-04-07",
  },
  {
    id: "8",
    article_id: 3,
    type_mouvement: "Sortie",
    quantity: 25,
    date: "2024-04-08",
  },
  {
    id: "9",
    article_id: 1,
    type_mouvement: "Entrée",
    quantity: 90,
    date: "2024-04-09",
  },
  {
    id: "10",
    article_id: 4,
    type_mouvement: "Sortie",
    quantity: 35,
    date: "2024-04-10",
  },
  {
    id: "11",
    article_id: 2,
    type_mouvement: "Entrée",
    quantity: 200,
    date: "2024-04-01",
  },
  {
    id: "12",
    article_id: 2,
    type_mouvement: "Sortie",
    quantity: 75,
    date: "2024-04-03",
  },
  {
    id: "13",
    article_id: 5,
    type_mouvement: "Entrée",
    quantity: 150,
    date: "2024-04-05",
  },
  {
    id: "14",
    article_id: 3,
    type_mouvement: "Entrée",
    quantity: 300,
    date: "2024-04-02",
  },
  {
    id: "15",
    article_id: 3,
    type_mouvement: "Sortie",
    quantity: 120,
    date: "2024-04-04",
  },
  {
    id: "16",
    article_id: 3,
    type_mouvement: "Sortie",
    quantity: 80,
    date: "2024-04-06",
  }
];


