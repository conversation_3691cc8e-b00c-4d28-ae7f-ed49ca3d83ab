// src/pages/activities/index.jsx
import { useState, useRef } from "react";
import { ProTable } from "@ant-design/pro-components";
import { Button, Popconfirm, Space, Input, Tooltip, Typography, Card, Modal } from "antd";
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined, SearchOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import useDebounce from "@hooks/useDebounce";

import CreateScoring from "./create";
import EditScoring from "./edit";
import ViewScoring from "./view";
import { scoringData } from "./datas";

function Scoring() {
  const { t } = useTranslation();
  const tableRef = useRef();

  const [dataSource, setDataSource] = useState(scoringData);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [viewModalVisible, setViewModalVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState(null);
  const [viewingRecord, setViewingRecord] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [tableParams, setTableParams] = useState([]);

  const handleCancel = () => {
    setViewModalVisible(false);
  };

  const handleEdit = (record) => {
    setEditingRecord(record);
    setEditModalVisible(true);
  };

  const columns = [
    {
      title: "Clé",
      dataIndex: "key",
      key: "key",
    },
    {
      title: "Label",
      dataIndex: "label",
      key: "label",
    },
    {
      title: t("common.actions.title"),
      key: "action",
      align: "center",
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title={t("common.actions.view")}>
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => {
                setViewingRecord(record);
                setViewModalVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title={t("common.actions.edit")}>
            <Button
              type="link"
              style={{ color: "#f5b041" }}
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title={t("common.actions.delete")}>
            <Popconfirm
              title={t("common.messages.confirm_delete")}
              onConfirm={() => {
                const newDataSource = dataSource.filter(item => item.key !== record.key);
                setDataSource(newDataSource);
              }}
            >
              <Button type="link" style={{ color: "#ec7063" }} icon={<DeleteOutlined />} />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <Card
      bordered
      title={<Typography.Title level={4}>{"Liste des scorings"}</Typography.Title>}
      extra={
        <Space>
          <Input
            size="large"
            placeholder={t("common.search")}
            suffix={<SearchOutlined style={{ color: "#bfbfbf" }} />}
          />
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setCreateModalVisible(true)}
          >
            {"Ajouter"}
          </Button>
        </Space>
      }
    >
      <ProTable
        actionRef={tableRef}
        loading={isLoading}
        columns={columns}
        dataSource={dataSource}
        rowKey="key"
        search={false}
        options={false}
        pagination={{ showSizeChanger: true, defaultPageSize: 5 }}
      />

      {/* Create */}
      <Modal
        title={t("crm.scoring.add")}
        visible={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        footer={null}
        width={800}
      >
        <CreateScoring
          onCancel={() => setCreateModalVisible(false)}
          setDataSource={setDataSource}
        />
      </Modal>

      {/* Edit */}
      <Modal
        title={t("crm.scoring.edit")}
        visible={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        footer={null}
        width={800}
      >
        <EditScoring
          record={editingRecord}
          onCancel={() => setEditModalVisible(false)}
          setDataSource={setDataSource}
        />
      </Modal>

      {/* View */}
      <Modal
        title={t("crm.scoring.view")}
        visible={viewModalVisible}
        onCancel={handleCancel}
        footer={null}
        width={800}
      >
        <ViewScoring
          record={viewingRecord}
          onCancel={() => setViewModalVisible(false)}
        />
      </Modal>
    </Card>
  );
}

export default Scoring;