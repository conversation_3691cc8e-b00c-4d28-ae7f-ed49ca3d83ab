import { useEffect, useState } from "react";
import { EyeInvisibleOutlined, EyeTwoTone } from "@ant-design/icons";
import logo_signIn from "../../assets/images/logo_erp.png";
import logo_Fipa from "../../assets/images/fipa.png";
import { Layout, Button, Card, Form, Input } from "antd";
import { Link, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { login } from "@features/auth/authSlice";
import { toast } from "sonner";
import { RootState } from "@src/features/store";
import { Label } from "recharts";
const { Header, Content } = Layout;

export default function SignIn() {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const isLoggedIn = useSelector((state: RootState) => state.auth.isLoggedIn);

  const [loadingButton, setLoadingButton] = useState(false);

  const onFinish = (values) => {
    setLoadingButton(true);
    dispatch(login(values))
      .unwrap()
      .then((originalPromiseResult) => {
        console.log(originalPromiseResult);
        // location.reload();

        navigate("/home");
      })
      .catch((rejectedValueOrSerializedError) => {
        setLoadingButton(false);
        toast.error("Verifier vos coordonnes");
        console.log(rejectedValueOrSerializedError);
        return [];
      });
  };

  useEffect(() => {
    if (isLoggedIn) {
      //location.reload();
      navigate("/home");
    }
  }, []);

  const onFinishFailed = (errorInfo) => {
    console.log("Failed:", errorInfo);
  };

  return (
    <div className="min-h-screen bg-login-layout bg-center bg-cover bg-no-repeat relative">
      {/* Overlay with white color and blur effect */}
      <div className="absolute inset-0 bg-white bg-opacity-80 backdrop-blur-sm"></div>

      <div className="relative z-10 flex flex-col justify-center py-12 sm:px-6 lg:px-8 min-h-screen">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          {/* Logo Placeholder - Replace with your actual logo */}
          <div className="flex justify-center">
            <div className="w-[250px] rounded-full flex items-center justify-center text-white text-2xl font-bold">
              <img src={logo_Fipa} alt="Logo" />
            </div>
          </div>

          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Bienvenue sur FIPA !
          </h2>
        </div>

        <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
          <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10 bg-opacity-90">
            {/* <form className="space-y-6">
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                  Adresse mail
                </label>
                <div className="mt-1">
                  <input
                    id="email"
                    name="email"
                    type="email"
                    autoComplete="email"
                    required
                    className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                  Mot de passe
                </label>
                <div className="mt-1">
                  <input
                    id="password"
                    name="password"
                    type="password"
                    autoComplete="current-password"
                    required
                    className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  />
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="text-sm">
                  <a href="#" className="font-medium text-indigo-600 hover:text-indigo-500">
                    Mot de passe oublié ?
                  </a>
                </div>
              </div>

              <div>
                <button
                  type="submit"
                  className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Se connecter
                </button>
              </div>
            </form> */}
            <Form
              layout="vertical"
              name="basic"
              initialValues={{ remember: true }}
              onFinish={onFinish}
              onFinishFailed={onFinishFailed}
              className="row-col"
            >
              <div
                style={{
                  maxHeight: "150px",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  marginBottom: "20px",
                }}
              ></div>
              <Form.Item
                name="email"
                label="Email"
                rules={[{ required: true, message: "Please enter your email" }]}
              >
                <Input size="large" placeholder="Email" />
              </Form.Item>
              <Form.Item
                label="Mot de passe"
                name="password"
                rules={[
                  { required: true, message: "Please enter your password" },
                ]}
              >
                <Input.Password
                  size="large"
                  placeholder="********"
                  iconRender={(visible) =>
                    visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
                  }
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: "10px" }}>
                <Link
                  to="/forgot-password"
                  className="text-dark font-semibold text-muted"
                >
                  Mot de passe oublie?
                </Link>
              </Form.Item>
              <Form.Item style={{ marginBottom: "10px" }}>
                <Button
                  style={{ width: "100%" }}
                  type="primary"
                  htmlType="submit"
                  loading={loadingButton}
                >
                  Se connecter
                </Button>
              </Form.Item>
            </Form>
          </div>
        </div>
      </div>
    </div>
  );
}
