import { ModalForm } from "@ant-design/pro-components";
import { Card,
  Col,
  DatePicker,
  Divider,
  Form,
  Input,
  InputNumber,
  Radio,
  Row,
  Select,
 
} from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";

function InfoProjet({ open, onCancel, onSuccess }) {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const handleSubmit = (values) => {};


  return (
    <ModalForm
      title={t("Informations")}
      width="60%"
      form={form}
      open={open}
      modalProps={{
        style: { top: 20 },
      }}
      onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.save"),
          resetText: t("common.actions.cancel"),
        },
      }}
    >
      <Divider>Projet déclaré</Divider>
      <Card className="mt-2">
        <Row gutter={16}>
          <Col md={8} xs={24}>
            <Form.Item name={""} label={t("Date de la déclaration")}>
              <DatePicker />
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item name={""} label={t("Code API")}>
             <Input />
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item name={""} label={t("%PE")}>
               <InputNumber />
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item name={""} label={t("Investissement décl. (MDT)")}>
              <InputNumber />
            </Form.Item>
          </Col>

          <Col md={8} xs={24}>
            <Form.Item name={""} label={t("Emplois")}>
              <InputNumber />
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item name={""} label={t("NB cadres")}>
               <InputNumber />
            </Form.Item>
          </Col>
        </Row>
      </Card>
      <Divider>1er investissement</Divider>
      <Card className="mt-2">
        <Row gutter={16}>
          <Col md={12} xs={24}>
            <Form.Item name={""} label={t("Date 1er invest.")}>
              <DatePicker />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name={""} label={t("Code en douane")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name={""} label={t("Nature du 1er invest.")}>
              <Input />
            </Form.Item>
          </Col>

          <Col md={12} xs={24}>
            <Form.Item name={""} label={t("Lieu")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name={""} label={t("Coût du 1er invest.(MDT)")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name={""} label={"Superficie (M2)"}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={24} xs={24}>
            <Form.Item name={""} label={"Motivation"}>
              <Input />
            </Form.Item>
          </Col>
        </Row>
      </Card>
      <Divider>Entrée en production</Divider>
      <Card className="mt-2">
        <Row gutter={16}>
          <Col md={12} xs={24}>
            <Form.Item name={""} label={t("Date E.P.")} > 
              <DatePicker /> 
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name={""} label={t("Date 1ère opér.fact.")}>
              <DatePicker />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item  name={""} label={t("Invest. Réalisé(MDT)")} >
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item   name={""} label={t("Empl. réalisés")} >
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item   name={""} label={t("Invest. Total(MDT)")} >
              <InputNumber />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name={""} label={t("Nbre Cadres/Encadrement")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={24} xs={24}>
            <Form.Item name={""}   label={t("Obs % P.E.")} >
              <Input.TextArea />
            </Form.Item>
          </Col>
          
        </Row>
      </Card>

    </ModalForm>
  );
}
export default InfoProjet;
