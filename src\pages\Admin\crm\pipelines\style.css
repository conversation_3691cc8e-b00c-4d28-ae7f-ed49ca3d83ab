/***********BEGIN CRM CSS************/
.breadcrumb-arrows{
    margin-bottom: 25px;
  }
  .breadcrumb-arrows li {
    display: inline-block;
    line-height: 30px;
    position: relative;
    margin-right: 2px;
  }
    .breadcrumb-arrows li:before  {
      content: " ";
      height: 0;
      width: 0;
      position: absolute;
      left: -2px;
      border-style: solid;
      border-width: 15px 0 15px 15px;
      border-color: transparent transparent transparent #fff;
      z-index: 0; }
    .breadcrumb-arrows li:first-child:before {
      border-color: transparent; }
  
      .breadcrumb-arrows a:after ,.activechoise a:after{
        content: " ";
        height: 0;
        width: 0;
        position: absolute;
        right: -15px;
        border-style: solid;
        border-width: 15px 0 15px 15px;
        border-color: transparent transparent transparent #ccc;
        z-index: 10; }
  
  
        
  
        .breadcrumb-arrows .last>a:after{
          content: " ";
          height: 0;
          width: 0;
          position: absolute;
          right: -15px;
          border-style: solid;
          border-width: 15px 0 15px 15px;
          border-color: #ccc #ccc #ccc #ccc;
          z-index: 10; }
  
  
  .breadcrumb-arrows a {
    display: block;
    background: #ccc;
    padding: 0 20px; 
  
  }
  
  .breadcrumb-arrows a:focus  {
  background: #6CB14F!important;
  color:white!important;
  }
  
  
  .breadcrumb-arrows li.a:focus{
    background: #6CB14F!important;
    color:white!important;
    
    }
    
  .activeitem a,.activeitem li{
      background: #6CB14F!important;
      color:white!important;
      border-color: transparent transparent transparent #6CB14F!important;
      
      }
      .activeitemfinale a,.activeitemfinale li{
        background: #6CB14F!important;
        color:white!important;
        border-color: #6CB14F #6CB14F #6CB14F #6CB14F!important;
        
        }
      .activechoise a,.activechoise li{
        background: #6CB14F!important;
        color:white!important;
        border-color: transparent transparent transparent #6CB14F!important;
        color:white!important;
        
        }
        .activechoise a:after{
          border-color: transparent transparent transparent #6CB14F!important;
          color:white!important;
  
        
        }
        
  .breadcrumb-arrows a:focus:after{
    border-color: transparent transparent transparent #6CB14F!important;
    color:white!important;
  
  }
  
  .breadcrumb-arrows .activeitemfinale>a:focus:after{
    border-color: #6CB14F #6CB14F #6CB14F #6CB14F!important;
    color:white!important;
  
  }
  
  .activeitem a:after{
    border-color: transparent transparent transparent #6CB14F!important;
    color:white!important;
  
  }
  
  
  .activeitemfinale a:after{
    border-color: #6CB14F #6CB14F #6CB14F #6CB14F!important;
    color:white!important;
    
  }
  .card {
    /* // border-left: 3px solid #4096ff; */
    width: 100%;
    float: left;
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    padding: 15px;
    background: #fff;
    box-shadow: 3px 1px 12px #0000001a;
    border-radius: 4px;
    margin-bottom: 15px;
    cursor: move;
  }
  button.ant-btn.css-dev-only-do-not-override-qnu6hi.ant-btn-default.ant-btn-icon-only {
    margin-left: 10px;
    border: none;
    color: white;
    background-color: #6cb14f;
   padding-bottom: 0px !important; 
  }
  .ant-flex.css-dev-only-do-not-override-qnu6hi.ant-flex-justify-center {
    margin-bottom: 30px;
    margin-top: 30px;
  }
  
  .ant-flex.css-dev-only-do-not-override-qnu6hi.ant-flex-justify-center .ant-btn-primary:hover, 
  .ant-flex.css-dev-only-do-not-override-qnu6hi.ant-flex-justify-center .ant-btn-primary:focus, 
  .ant-flex.css-dev-only-do-not-override-qnu6hi.ant-flex-justify-center .ant-btn-primary:active, 
  .ant-flex.css-dev-only-do-not-override-qnu6hi.ant-flex-justify-center .ant-btn-primary:focus-visible, 
  .ant-flex.css-dev-only-do-not-override-qnu6hi.ant-flex-justify-center .ant-btn-primary:visited, 
  .ant-flex.css-dev-only-do-not-override-qnu6hi.ant-flex-justify-center .ant-btn-primary::selection {
    color: #1677ff;
    border: 1px solid #1677ff;
    background-color: #4096ff2b !important;
  }
  
  .ant-flex.css-dev-only-do-not-override-qnu6hi.ant-flex-justify-center .ant-btn-primary:focus {
    outline: 2px solid #1677ff;
    box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.4);
  }
  
  .ant-flex.css-dev-only-do-not-override-qnu6hi.ant-flex-justify-center .ant-btn-primary:active {
    background-color: #0055cc !important;
    border: 1px solid #0055cc;
  }
  
  .ant-flex.css-dev-only-do-not-override-qnu6hi.ant-flex-justify-center .ant-btn-primary:focus-visible {
    outline: 2px solid #1677ff;
    box-shadow: 0 0 0 3px rgba(22, 119, 255, 0.5);
  }
  
  .ant-flex.css-dev-only-do-not-override-qnu6hi.ant-flex-justify-center .ant-btn-primary:visited {
    color: #1677ff;
  }
  
  .img img {
    width: 40px;
    height: 40px;
  }
  .status,
  .days,
  .time {
    font-size: 14px;
    margin-bottom: 7px;
    text-align: left;
  }
  .card_right {
    width: auto;
    float: left;
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    align-items: flex-start;
  }
  .order0 .status {
    background: #fff1de;
    color: #494949;
    padding: 3px 10px;
    border-radius: 18px;
  }
  .order1 .status {
    background: #c8ebdd;
    color: #494949;
    padding: 3px 10px;
    border-radius: 18px;
  }
  .order2 .status {
    background: #cfd8f0;
    color: #494949;
    padding: 3px 10px;
    border-radius: 18px;
  }
  .order3 .status {
    background: #eac8d7;
    color: #494949;
    padding: 3px 10px;
    border-radius: 18px;
  }
  .App {
    font-family: sans-serif;
    text-align: center;
    background: #fbfcfd;
    height: 100vh;
    padding: 40px 0px;
  }
  h4 {
    font-family: sans-serif;
    font-size: 12px;
    text-transform: uppercase;
    margin-top: 0px;
    color: #416183;
  }
  .container {
    height: 100%;
    width: 100%;
    margin: 0 auto;
    padding: 0;
    display: flex;
    /* //justify-content: space-evenly; */
  }
  .drag_column {
    width: 335px;
    float: left;
    display: flex;
    padding: 30px;
    height: fit-content;
    margin-left: 5px;
    margin-right: 5px;
    background-color: #91caff29;
  }
  .drag_row {
    width: 100%;
  
    /* //display: flex; */
    flex-wrap: wrap;
  }
  
  .drag_row h5 {
    font-weight: 700;
    color:#214984;;
  }
  
  .btn-rec{
   border-radius: 0;
  }
  .ant-col.ant-col-6.css-dev-only-do-not-override-qnu6hi {
    font-weight: 500;
    font-size: 18px;
    color: #2577ef;
  
  }
  
  .small-box {
    width: 22%;
    background: #f7f8fa;
    height: max-content;
  }
  
  .task {
    border: 1px solid white;
    margin: 10px;
    box-shadow: 1px 3px 10px gray;
    height: 8vh;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  
  
  .bt-op:focus,.bt-op:active {
    background-color: #6CB14F;
    transform: translateY(4px);
  }
  
  