import { ModalForm } from "@ant-design/pro-components";
import {
  <PERSON><PERSON>,
  <PERSON>,
  Col,
  DatePicker,
  Divider,
  Form,
  Input,
  InputNumber,
  Modal,
  Popconfirm,
  Radio,
  Row,
  Select,
  Steps,
  Table,
} from "antd";

import TextArea from "antd/es/input/TextArea.js";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { pays } from "./data";
import { CheckCircleTwoTone } from "@ant-design/icons";
import Pays from "../../pays";

function EditSalon({ open, onCancel, record, show }) {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [steps, setSteps] = useState(record.initialSteps);
  const [form] = Form.useForm();
  useEffect(() => {
    if (open) {
      //setSteps(record.initialSteps)
      form.setFieldsValue(record);
    }
  }, [open]);
  const [completedSteps, setCompletedSteps] = useState([]);
  const [showMotifModal, setShowMotifModal] = useState(false);
  const [selectedStepId, setSelectedStepId] = useState(null);
  const [reason, setReason] = useState("");
  const handleStepClick = (step) => {
    if (step.motif && step.status === "wait") {
      setSelectedStepId(step.id);
      setShowMotifModal(true);
    } else {
      toggleStepStatus(step.id);
    }
  };
  const toggleStepStatus = (id) => {
    const updated = steps.map((step) => {
      if (step.id === id) {
        const newStatus = step.status === "finish" ? "wait" : "finish";
        return { ...step, status: newStatus, reason: reason };
      }
      return step;
    });
    setSteps(updated);
  };
  return (
    <ModalForm
      title={
        show ? t("Afficher le salon") : t("Modifier le salon")
      }
      form={form}
      open={open}
      width="80%"
      modalProps={{
        style: { top: 20 },
      }}
      //   onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={ show ? {
         submitButtonProps: { hidden: true },
        searchConfig: {
        
          resetText: t("common.actions.cancel"),
        },
      } : { searchConfig: {
          submitText: t("common.actions.edit"),
          resetText: t("common.actions.cancel"),
        }}}
    >
      <Card className="mt-2">
        <Steps
          direction="horizontal"
          type="navigation"
          className="site-navigation-steps"
          current={-1}
        >
          {steps.map((step, index) => (
            <Steps.Step
              key={step.id}
              title={step.title}
              style={{ cursor: "pointer" }}
              onClick={(index) => {
                handleStepClick(step);
              }}
              status={step.status}
              icon={
                step.status === "finish" ? (
                  <CheckCircleTwoTone twoToneColor="#52c41a" />
                ) : undefined
              }
            />
          ))}
        </Steps>    </Card>
        <Card className="mt-2">

    
        <Row gutter={16}>
          <Col md={16} xs={24}>
            <Form.Item name={"responsable"} label={t("Responsable FIPA")} >
              <Input disabled={show} />
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item name={"inclure"} label={t("Inclure")} disabled={show}>
              <Select />
            </Form.Item>
          </Col>
        </Row>
      </Card>
 
    <Divider>Données générales </Divider>
       <Card className="mt-2">
         <Row gutter={16}>
           <Col md={24} xs={24}>
             <Form.Item name={"intitule"} label={t("Intitulé")}>
               <Input />
             </Form.Item>
           </Col>
           <Col md={12} xs={24}>
             <Form.Item name={"num_edition"} label={t("N° édition")}>
               <Input />
             </Form.Item>
           </Col>
           <Col md={12} xs={24}>
             <Form.Item name={"site_web"} label={t("Site Web")}>
               <Input />
             </Form.Item>
           </Col>
           <Col md={12} xs={24}>
             <Form.Item name={"organisateur"} label={t("Organisateur")}>
               <Input />
             </Form.Item>
           </Col>
           <Col md={12} xs={24}>
             <Form.Item name={"convention"} label={t("Convention d'affaire")}>
               <Select />
             </Form.Item>
           </Col>
           <Col md={12} xs={24}>
             <Form.Item name={"date_debut"} label={t("Date début")}>
               <DatePicker />
             </Form.Item>
           </Col>
 
           <Col md={12} xs={24}>
             <Form.Item name={"date_fin"} label={t("Date fin")}>
               <DatePicker />
             </Form.Item>
           </Col>
           <Col md={8} xs={24}>
             <Form.Item name={"pays"} label={t("Pays")}>
               <Select />
             </Form.Item>
           </Col>
           <Col md={16} xs={24}>
             <Form.Item name={"region"} label={"Région"}>
               <Input />
             </Form.Item>
           </Col>
 
           <Col md={24} xs={24}>
             <Form.Item name={"theme"} label={t("Thème du salon")}>
               <Input />
             </Form.Item>
           </Col>
           <Col md={16} xs={24}>
             <Form.Item name={"secteur"} label={t("Secteur")}>
               <Select />
             </Form.Item>
           </Col>
           <Col md={8} xs={24}>
             <Form.Item name={"groupe"} label={t("Groupe")}>
               <Select />
             </Form.Item>
           </Col>
           <Col md={8} xs={24}>
             <Form.Item name={"categorie"} label={t("Catégorie")}>
               <Select />
             </Form.Item>
           </Col>
           <Col md={16} xs={24}></Col>
 
           <Col md={16} xs={24}>
             <Form.Item
               name={"action_conjointe"}
               label={t("Présence conjointe/Non conjointe")}
             >
               <Select />
             </Form.Item>
           </Col>
           <Col md={8} xs={24}>
             <Form.Item
               name={"propose_par"}
               label={t("Contacts prévus initiateur")}
             >
               <Select />
             </Form.Item>
           </Col>
           <Col md={8} xs={24}>
             <Form.Item name={"binome"} label={t("Binôme")}>
               <Input />
             </Form.Item>
           </Col>
 
           <Col md={8} xs={24}>
             <Form.Item name={"objectifs"} label={t("Contacts prévus binôme")}>
               <Input></Input>
             </Form.Item>
           </Col>
           <Col md={8} xs={24}>
             <Form.Item name={"objectifs"} label={t("Total contacts prévus ")}>
               <Input></Input>
             </Form.Item>
           </Col>
 
           <Col md={12} xs={24}>
             <Form.Item
               name={"objectifs"}
               label={t("Objectif contacts  intéressants initiateur")}
             >
               <Input></Input>
             </Form.Item>
           </Col>
           <Col md={12} xs={24}>
             <Form.Item
               name={"objectifs"}
               label={t("Objectif contacts intéressants binôme")}
             >
               <Input></Input>
             </Form.Item>
           </Col>
         </Row>
       </Card>
       <Divider>Objectifs de la participation </Divider>
       <Card className="mt-2">
         <Row gutter={16}>
           <Col md={6} xs={24}>
             <Form.Item name={"type_participation"} label={t(" ")}>
               <Radio>Contacts </Radio>
             </Form.Item>
           </Col>
           <Col md={6} xs={24}>
             <Form.Item name={""} label={t(" ")}>
               <Radio>Veille concurrentielle</Radio>
             </Form.Item>
           </Col>
           <Col md={6} xs={24}>
             <Form.Item name={""} label={t(" ")}>
               <Radio>Veille technologique </Radio>
             </Form.Item>
           </Col>
           <Col md={6} xs={24}>
             <Form.Item name={""} label={t(" ")}>
               <Radio>Relation avec relais </Radio>
             </Form.Item>
           </Col>
         </Row>
       </Card>
       <Divider>Editions précédentes</Divider>
       <Card className="mt-2">
         <Row gutter={16}>
           <Col md={24} xs={24}>
             <Form.Item
               name={"officiel"}
               label={t("Nbre visiteurs et qualité/nombre exposants qualité")}
             >
               <Input.TextArea />
             </Form.Item>
           </Col>
         </Row>
       </Card>
 

      <Modal
        title="Justification du changement d'état"
        open={showMotifModal}
        onCancel={() => {
          setShowMotifModal(false);
          setReason("");
        }}
        onOk={() => {
          if (!reason.trim()) {
            message.error("Veuillez remplir le champ de justification.");
            return;
          }
          toggleStepStatus(selectedStepId);
          setShowMotifModal(false);
          setReason("");
        }}
        okText="Valider"
        cancelText="Annuler"
      >
        <p>Veuillez justifier ce changement :</p>
        <Input.TextArea
          rows={4}
          value={reason}
          onChange={(e) => setReason(e.target.value)}
          placeholder="Saisissez votre justification ici"
        />
      </Modal>
    </ModalForm>
  );
}
export default EditSalon;
