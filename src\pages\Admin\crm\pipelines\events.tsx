import React from "react";
import { useState, useEffect } from 'react';
import moment from "moment";
import './style.css'
import {
  ArrowsAltOutlined,
  ColumnWidthOutlined,
  DownOutlined,
  EditOutlined,
  MenuUnfoldOutlined, PlusOutlined,
  SettingOutlined
} from "@ant-design/icons";
import {
  Rate,
  Flex, Tooltip, Divider, Row, Button, Space, Popover, Image,
  Modal,
  Form,
  Radio,
  Card,
  Col,
  Select,
  DatePicker,
  Input,
  Dropdown,
  ColorPicker
} from "antd";
import { useDispatch, useSelector } from "react-redux";
import CreateActivity from "../activities/create";
import { update } from "lodash";
import UpdateActivity from "../activities/edit";
import { RadioChangeEvent } from "antd/lib";
const { Option } = Select;

var sourceid = 0;

class TaskList extends React.Component<any, any> {

  state = { tasks: [], etapes: [], taskreserve: [] };


  componentDidMount() {
    const { tasks } = this.props;
    const { taskreserve } = this.props;

    const { etapes } = this.props;
    //const { etapeact } = this.props;

    this.setState({
      tasks,
      etapes,
      //  etapeact,

      taskreserve
    });
  }





  onDragStart = (evt, id) => {
    let element = evt.currentTarget;
    element.classList.add("dragged");
    evt.dataTransfer.setData("text/plain", evt.currentTarget.id);
    evt.dataTransfer.effectAllowed = "move";
    //console.log("id", id);
    sourceid = id;

  };
  onDragEnd = (evt) => {
    evt.currentTarget.classList.remove("dragged");
    //console.log("CURR1", evt.dataTransfer);

  };
  onDragEnter = (evt) => {
    evt.preventDefault();
    let element = evt.currentTarget;
    element.classList.add("dragged-over");
    evt.dataTransfer.dropEffect = "move";
    //console.log("CURR1", evt.dataTransfer);

  };
  onDragLeave = (evt) => {
    let currentTarget = evt.currentTarget;
    let newTarget = evt.relatedTarget;
    if (newTarget.parentNode === currentTarget || newTarget === currentTarget)
      return;
    evt.preventDefault();
    let element = evt.currentTarget;
    element.classList.remove("dragged-over");
    //console.log("CURR1", evt.dataTransfer);

  };
  onDragOver = (evt) => {
    evt.preventDefault();
    evt.dataTransfer.dropEffect = "move";
    //console.log("CURR1", evt.dataTransfer);

  };


  onDrop = (evt, value, status, CURR) => {

    const { dispatch } = this.props;


    


    evt.preventDefault();
    evt.currentTarget.classList.remove("dragged-over");
    let data = evt.dataTransfer.getData("text/plain");
    let tasks = this.state.tasks;

    //console.log("DROP", status);
    //console.log("sourceid", sourceid);






    let updated = tasks?.map((task) => {

      if (task.id.toString() === data.toString()) {
        task.etape.status = status;
      }
      return task;
    });
    this.setState({
      tasks: updated
    });
  };


  render() {
    let { tasks } = this.props;
    let { taskreserve } = this.props;

    let { forceRefresh } = this.props;
    let { refresh } = this.props;

    let { etapes } = this.props;





    let { setIsModalOpen } = this.props;
    let { setIsModalOpenActivity } = this.props;
    let { isModalOpenActivity } = this.props;
    let { setetapes } = this.props;
    let { setetapeschanges } = this.props;
    let { etapeschanges } = this.props;


    let { sethidedate } = this.props;
    let { hidedate } = this.props;


    let { setContent } = this.props;
    let { content } = this.props;
    let { visibleForm } = this.props;
    let { employees } = this.props;
    let { planactivities } = this.props;
    let { entreprises } = this.props;
    let { settestref } = this.props;
    let { testref } = this.props;

    let { typeactivities } = this.props;
    let { clients } = this.props;
    let { setVisibleForm } = this.props;
    let { setStep } = this.props;
    let { step } = this.props;
    let { buttonLoading } = this.props;


    let { setOpen } = this.props;
    let { message } = this.props;

    let { form } = this.props;
    let { setValue } = this.props;

    let { valswitch } = this.props;

    let { setvalswitch } = this.props;





    let { value } = this.props;
    let { filterOption } = this.props;
    let { filterSort } = this.props;

    let { dispatch } = this.props;
    let { setButtonLoading } = this.props;
    let { items } = this.props;
    let { can } = this.props;

    let { itemsdelete } = this.props;
    let { itemsmodif } = this.props;
    let { itemsmodifdelete } = this.props;



    let { arrayhidensteps } = this.props;
    let { setFormatHex } = this.props;
    let { setColorHex } = this.props;
    let { colorHex } = this.props;
    let { hexString } = this.props;


    let { formatHex } = this.props;


    let { nameetape } = this.props;
    let { setnameetape } = this.props;
    let { coloretape } = this.props;
    let { setcoloretape } = this.props;





    let { setarrayhidensteps } = this.props;
    let { etapeidchange } = this.props;
    let { setetapeidchange } = this.props;

    let changecontentetape = this.props;
    let setChangeEtapeCont = this.props;



    //console.log("tasks: ", tasks);
    //console.log("etapes: ", etapes);
    function constructdivcont(plans) {

      return (
        <div>
          <h1>Liste des plans</h1>
          {plans.map((plan) => (
            <div>
              plan nom :  +{plan?.plan?.name != undefined ? plan?.plan?.name : " "}  Date de planification :  {plan?.date_planifier != undefined ? plan?.date_planifier : " "}


            </div>
          ))}
        </div>
      );
    }

    let showModal = (types, plans, etape_id, task) => {





      setContent(
        <>
          <div style={{ maxHeight: '200px', overflow: 'scroll', overflowX: 'hidden' }}>

            <div>

              <Divider>Activités</Divider>

              <ul>
                {types?.map((type) =>
                  <>


                    <li>

                      <div className="d-flex align-items-center flex-wrap mx-3">
                        <Image className="me-2 rounded" style={{ maxWidth: '1.5rem', maxHeight: '1.5rem;' }} src="https://zos.alipayobjects.com/rmsportal/ODTLcjxAfvqbxHnVXCYX.png">

                        </Image>
                        <small className="text-truncate" style={{ marginLeft: '2px' }}>{type?.employer?.user?.name + '  ' + type?.employer?.user?.prenom}</small>
                        <div className="mt-1">

                          <>
                            <small className="text-truncate">
                              <li>
                                <b> Activité:  </b> {type?.type?.name != undefined ? type?.type?.name : " "}  <b> Action : </b> {type?.type?.action != undefined ? type?.type?.action?.name : " "}

                              </li>


                            </small><small className="mx-1" style={{ color: "red" }}>*</small>

                            <small className="fw-bold" style={{ color: "red" }} title={type?.date_planifier}>


                              {(moment(new Date()).format('YYYY-MM-DD')) > (moment(new Date(type?.date_planifier)).format('YYYY-MM-DD')) ? (<b style={{ color: "red" }}>{moment(new Date(type?.date_planifier)).diff((moment(new Date())), 'days')} jours  de retard</b>) : (<b style={{ color: "green" }}>

                                {moment(new Date(type?.date_planifier)).diff(moment(new Date()), 'days')}
                                jours restant </b>)} </small>

                          </>

                        </div></div>



                    </li>


                  </>

                )}
              </ul>
            </div>
            <div>

              <Divider>Plan d'activités</Divider>


              <ul>
                {plans?.map((plan) => (
                  <>
                    <li>

                      <div className="d-flex align-items-center flex-wrap mx-3">
                        <Image className="me-2 rounded" style={{ maxWidth: '1.5rem', maxHeight: '1.5rem;' }} src="http://localhost:8069/web/image?field=avatar_128&amp;id=2&amp;model=res.users">

                        </Image>

                        <div className="mt-1">
                          {plan?.plan?.details.map((detail) => (
                            <>
                              <small className="text-truncate" style={{ marginLeft: '2px' }}>{detail?.employer?.user?.name + '  ' + detail?.employer?.user?.prenom}</small>

                              <small className="text-truncate">
                                <li>
                                  <b> Résumé:  </b> {detail?.resume != undefined ? detail?.resume : " "}  <b> Note : </b> {detail?.note != undefined ? detail?.note : " "}

                                </li>



                              </small>
                              <small className="mx-1" style={{ color: "red" }}>*</small><small style={{ color: "red" }} className="fw-bold" title={plan?.date_planifier}>

                                {(moment(new Date()).format('YYYY-MM-DD')) > (moment(new Date(plan?.date_planifier)).format('YYYY-MM-DD')) ? (<b style={{ color: "red" }}>
                                  {moment(new Date(plan?.date_planifier)).diff(moment(new Date()), 'days')}
                                  jours  de retard</b>) : (<b style={{ color: "green" }}>
                                    {moment(new Date(plan?.date_planifier)).diff(moment(new Date()), 'days')}   jours restant </b>)} </small>
                              <Divider></Divider>

                            </>
                          ))}
                        </div></div>



                    </li>


                  </>

                ))}
              </ul>

            </div>







          </div>

          {can("add activity for opportunity") && (<Button
            onClick={() => {
              setvalswitch(1);
              //  this.setState({
              //   valswitch:  
              //   })
              setStep(task?.id);
              form.setFieldValue('activity_id', task?.id);
              setIsModalOpenActivity(true);

            }}

          >

            Planifier une activité

          </Button>)}

        </>

      );


      setIsModalOpen(true);
    };

    const handleOk = () => {
      setIsModalOpenActivity(false);
    };

   

    const handleDropdownItemClick = (key, etape, etapeinfo) => {
      if (key == 0) {
        arrayhidensteps?.push(etape);
        //setarrayhidensteps(arraysteps);
        this.setState({
          arrayhidensteps: arrayhidensteps,
        })
        console.log('arraysteps1', arrayhidensteps);
      } else if (key == 1) {
        //  this.setState({
        //   setvalswitch:2,
        // })
        console.log('valswitch', valswitch);
        setcoloretape(etapeinfo?.color);
        setnameetape(etapeinfo?.status);
        setetapeidchange(etapeinfo?.id);
        //form.setFieldValue("status", etape?.status);
        // form.setFieldValue("color", etape?.color);
        setIsModalOpenActivity(true);


      } else {
        setvalswitch(3);
        setetapeidchange(etapeinfo?.id);
        setIsModalOpenActivity(true);

      }
      // console.log('etape', etape);
      // setk(etape);
      // setVisibleEtape(true);
    }
    const onChangeRadio = (e: RadioChangeEvent) => {

      //console.log('radio checked', e.target.value);
      setValue(e.target.value);
      if (e.target.value == 1) {
        sethidedate(true);
      } else {
        sethidedate(false);
      }

    };
    const handleCancel = () => {
      setIsModalOpenActivity(false);
    };
    let etapename;

    // let Proposition = tasks?.filter((data) => data?.etape?.status === "Proposition");
    // let gagne = tasks?.filter((data) => data?.etape?.status === "Gagné");
    // let qualifie = tasks?.filter((data) => data?.etape?.status === "Qualifié");
    let desc = ['terrible', 'bad', 'normal', 'good', 'wonderful'];
    var sum = 0;
    // var  sum1=0;
    // var  sum2=0;
    // var  sum3=0;


    // var  sum=0;
    // var  sum1=0;
    // var  sum2=0;
    // var  sum3=0;


    // for(var i = 0; i < Proposition.length; i++)
    //   {
    //       sum1 += parseInt(Proposition[i].revenu);
    //   }
    //   for(var i = 0; i < qualifie.length; i++)
    //     {
    //         sum2 += parseInt(qualifie[i].revenu);
    //     }
    //     for(var i = 0; i < gagne.length; i++)
    //       {
    //           sum3 += parseInt(gagne[i].revenu);
    //       }
    function sumrev(etapename, ar) {


      var sum = 0;
      for (var j = 0; j < ar?.length; j++) {
        if (ar[j]?.etape?.status == etapename) {
          sum += ar[j]?.revenu;
          //  //console.log('etape',sum);

        }
      }


      //console.log('tsk verif rrrr1', sum)
      //constsum2=parseFloat(sum);
      //let res=sum2.toFixed(3);
      return sum.toFixed(3);
    }
    function sumalltasks() {
      let alltsks = 0;
      for (var i = 0; i < tasks?.length; i++) {
        alltsks += tasks[i]?.plans?.length;
        alltsks += tasks[i]?.types?.length;

      }
      //console.log('alltsks', alltsks)
      return alltsks;
    }




    function sumtasksplans(etapename) {
      let tasksplansarray = [];
      let tasksdonearray = [];
      let Nouveau = tasks?.filter((data) => data?.etape?.status === etapename);

      //console.log("nouv", etapename);
      let sumtasks = [];

      let tasksplans = 0;
      let tasksdone = 0;
      var j = 0;
      var k = 0;

      for (var i = 0; i < Nouveau?.length; i++) {
        let today = moment().format('YYYY-MM-DD');;
        //console.log("today", today);

        if (Nouveau[i]?.plans.length > 0) {
          if (Nouveau[i]?.plans[0]?.date_planifier >= today) {
            tasksplans++;
            tasksplansarray[j] = Nouveau[i];
            j++;
          } else {
            //console.log(Nouveau[i]?.plans[0]?.date_planifier);
            tasksdonearray[k] = Nouveau[i];
            tasksdone++;
            k++;
          }
        }
        if (Nouveau[i]?.types?.length > 0) {
          if (Nouveau[i]?.types[0]?.date_planifier >= today) {
            tasksplans++;
            tasksplansarray[j] = Nouveau[i];
            j++;
          } else {
            tasksdonearray[k] = Nouveau[i];
            tasksdone++;
            k++;
          }
        }





      }

      sumtasks[0] = tasksplans;
      sumtasks[1] = tasksdone;
      sumtasks[2] = tasksplansarray;
      sumtasks[3] = tasksdonearray;

      //console.log("sum", sumtasks)
      return sumtasks;
    }

    const changetasks = (array, event, etapename) => {

      let tsksreste = tasks?.filter((data) => data?.etape?.status !== etapename);
      let tsks = tasks;
      //console.log('reste', tsksreste);
      this.setState({ taskreserve: tasks });
      let c;

      if (tsks.length > 0) {
        c = [];
        //console.log("c:", tsks);

        //console.log('updates4');
        for (let i = 0; i < tsks.length; i++) {
          let plans = tsks[i].plans;
          let types = tsks[i].types;





          if ((tsks[i].etape.status == etapename)) {

            let planstsks = [];
            let typestsks = [];
            if (event == 1) {
              planstsks = plans?.filter((data) => ((moment(new Date(data['date_planifier'])).format('YYYY-MM-DD')) >= (moment(new Date()).format('YYYY-MM-DD'))) ? data : '');
              typestsks = types?.filter((data) => ((moment(new Date(data['date_planifier'])).format('YYYY-MM-DD')) >= (moment(new Date()).format('YYYY-MM-DD'))) ? data : '');

            } else {
              planstsks = plans?.filter((data) => ((moment(new Date(data['date_planifier'])).format('YYYY-MM-DD')) < (moment(new Date()).format('YYYY-MM-DD'))) ? data : '');
              typestsks = types?.filter((data) => ((moment(new Date(data['date_planifier'])).format('YYYY-MM-DD')) < (moment(new Date()).format('YYYY-MM-DD'))) ? data : '');

            }

            //console.log("plans:", planstsks);
            //console.log("types:", typestsks);
            //console.log('pl-s', planstsks);

            if ((planstsks.length > 0) || ((typestsks.length > 0))) {


              c.push(tsks[i]);
            }
          }






        }
        // c = tasks.filter(x => !array.filter(y => ((y.id !== x.id) && (y.etape.status == x.etape.status))).length);


      }

      //console.log("c:", c);

      if (c.length > 0) {
        this.setState({ taskreserve: c.concat(tsksreste) });

      } else {
        this.setState({ taskreserve: tasks });

      }
      //console.log("updates now", c.concat(tsksreste));

      //console.log("updates nnn", taskreserve);
    };


    //console.log("step", step);

    let taskbyname = (etapename) => {

      let allNouveau;

      allNouveau = taskreserve?.filter((data) => data?.etape?.status === etapename);

      return allNouveau;
    }
    //console.log('step2', datadetail);


    //  const detailsObj = {
    //   visibleDetails: visibleDetails,
    //   setVisibleDetails: setVisibleDetails,  
    //   steppip:step,
    //   etapes:etapes,
    //   datadetail:datadetail,
    //   tableRef: [],
    //   diplomes:[],
    //   setData:[],
    //   employees:employees,
    //   clients:clients,
    //   typeactivities:typeactivities,
    //   planactivities:planactivities,
    //   entreprises:entreprises
    // };
    const obj = {
      visible: visibleForm,
      steppip: step,
      etapes: etapes,
      refresh: (Math.random()),
      setVisible: setVisibleForm,
      tableRef: [],
      diplomes: [],
      disable: true,
      employees: employees,
      clients: clients,
      typeactivities: typeactivities,
      planactivities: planactivities,
      entreprises: entreprises,



    };
    return (
      <div className="container" style={{ overflow: 'scroll', overflowY: 'scroll', height: '90vh' }}>

        {
          etapes?.map((etape, i) => (

            <>
              <div hidden={arrayhidensteps?.includes(i) ? false : true}
              >
                <section className="drag_container">
                  <div className="container">
                    <div className="drag_row">
                      <Space direction='vertical' style={{ textAlign: 'center' }}>
                        <ColumnWidthOutlined onClick={() => {
                          const index = arrayhidensteps.indexOf(i);
                          if (index > -1) {
                            arrayhidensteps.splice(index, 1);
                          }
                          this.setState({
                            arrayhidensteps: arrayhidensteps,
                          })
                          setarrayhidensteps(arrayhidensteps)
                          console.log('arraysteps2', arrayhidensteps);

                        }} style={{ marginBottom: '30px' }} />
                        <h5 style={{
                          marginTop: '15px',
                          transform: 'rotate(90deg)',
                          overflow: 'visible',
                        }}
                        >
                          {(etape?.status).charAt(0).toUpperCase() + (etape?.status).slice(1)
                          }
                          1
                        </h5>
                      </Space>
                    </div>
                  </div>
                </section>

              </div>

              <div
                className={"order" + 1 + " small-box"}
                onDragLeave={(e) => this.onDragLeave(e)}
                onDragEnter={(e) => this.onDragEnter(e)}
                onDragEnd={(e) => this.onDragEnd(e)}
                onDragOver={(e) => this.onDragOver(e)}
                onDrop={(e) => this.onDrop(e, false, etape.status, tasks)

                }
                hidden={arrayhidensteps?.includes(i) ? true : false}
              >
                <section className="drag_container">
                  <div className="container">

                    <div className="drag_column">

                      <div className="drag_row"  >

                        <h5 style={{ display: 'flex' }}>{(etape?.status).charAt(0).toUpperCase() + (etape?.status).slice(1)}   ({sumrev(etape.status, (tasks?.length > 0 ? tasks : taskreserve))} DT)

                          <Dropdown menu={{
                            items,
                            onClick: (event) => {
                              const { key } = event;
                              setvalswitch(2);

                              handleDropdownItemClick(key, i, etape);


                            }
                          }}

                            trigger={['click']}>
                            <a onClick={(e) => e.preventDefault()}>
                              <Space style={{ marginLeft: '15px' }}>
                                <SettingOutlined />
                              </Space>
                            </a>
                          </Dropdown>
                          {(can("add opportunity")) && (<Button
                            style={{ marginLeft: '10px', border: 'none', paddingBottom: '16px' }}

                            icon={<PlusOutlined />}
                            onClick={() => {
                              setStep(etape.id);
                              //  this.setState({
                              //   step:step
                              //  });
                              setVisibleForm(true);

                              //console.log('step ACT:  ', step);
                            }}
                          >

                          </Button>)}


                        </h5>

                        <div className="space-align-block">
                          <Space align="baseline" style={{ columnGap: '0px', marginBottom: '5px' }}>
                            {((sumtasksplans(etape.status)[0] != 0) || (sumtasksplans(etape.status)[1] != 0)) && (/* <Progress percent={sumalltasks()*10} 
                    
                      success={{ percent: (sumtasks(etape.status)*10) }} />*/<>
                                <Tooltip title={"Nombre des activités en cours:  " + (sumtasksplans(etape.status)[2]?.length) + " "}>
                                  <Button
                                    className="btn-rec"
                                    style={{ paddingRight: sumtasksplans(etape.status)[0] + 'px', height: '5px', backgroundColor: 'green' }}

                                    onClick={() => changetasks(sumtasksplans(etape.status)[2], 1, etape.status)}
                                  ></Button>
                                </Tooltip>
                                <Tooltip title={"Nombre des overdue:  " + (sumtasksplans(etape.status)[3]?.length) + " "}>
                                  <Button className="btn-rec" style={{ paddingRight: sumtasksplans(etape.status)[1] + 'px', height: '5px', backgroundColor: 'red' }}
                                    onClick={() => changetasks((sumtasksplans(etape.status)[3]), 2, etape.status)}

                                  ></Button></Tooltip>
                                <Tooltip title={"autres "}>

                                  <Button
                                    onClick={() => changetasks([], 3, etape.status)}
                                    className="btn-rec"

                                    style={{ paddingRight: 10 + 'px', height: '5px', backgroundColor: 'grey' }}></Button>

                                </Tooltip>
                              </>


                            )}
                          </Space>
                        </div>
                        <div
                            className="card"
                            key={i}
                            id={i+1}
                            draggable
                            onDragStart={(e) => this.onDragStart(e, i+1)}
                            onDragEnd={(e) => this.onDragEnd(e)}
                          >

                            <div className="card_right">
                              <Tooltip title="3 done / 3 in progress / 4 to do">
                              </Tooltip>
                              <div className="status">

                               
                              </div>
                              <div className="days"> </div>

                            
                              <div className="time">

                                <Flex gap="middle" vertical>

                                  <Popover content={content}
                                    zIndex={2}


                                    title="Activités/Plans" trigger="click" >
                                    <Button
                                      

                                    >

                                      <MenuUnfoldOutlined />

                                    </Button>

                                  </Popover>
                                  {/* <Modal title={"Activités actuelles/planifiers"}
                                  open={isModalOpenActivity}
                                  onOk={handleOk}
                                  footer={[
                                    <Button key="1"
                                      onClick={handleCancel}

                                    >OK</Button>,


                                  ]}
                                > */}

                                </Flex></div>


                            </div>
                          </div>
                        {taskbyname(etape.status)?.map((task) => (


                          <div
                            className="card"
                            key={task?.name}
                            id={task.id}
                            draggable
                            onDragStart={(e) => this.onDragStart(e, task.id)}
                            onDragEnd={(e) => this.onDragEnd(e)}
                          >

                            <div className="card_right">
                              <Tooltip title="3 done / 3 in progress / 4 to do">
                              </Tooltip>
                              <div className="status">

                                {(task?.etape?.status).charAt(0).toUpperCase() + (task?.etape?.status).slice(1)
                                }
                              </div>
                              <div className="days"> Opportunité : {task?.name}</div>

                              <div className="days">{task?.revenu != undefined ? 'Revenu: ' + parseFloat(task?.revenu).toFixed(3) + 'TND' : ''}</div>
                              <div className="days"> {task?.vender != undefined ? 'Responsable: ' + task?.vender?.user?.name + ' ' + task?.vender?.user?.prenom : ''}</div>
                              <div className="days"> {task?.client != undefined ? 'Client: ' + task?.client?.designation : ''}</div>

                              <div className="time">

                                <Flex gap="middle" vertical>
                                  <Rate disabled tooltips={desc} value={parseInt(task.rating)} />

                                  <Popover content={content}
                                    zIndex={2}


                                    title="Activités/Plans" trigger="click" >
                                    <Button
                                      onClick={() => {
                                        //setdiplome(diplome);
                                        showModal(task.types, task.plans, task?.etape_id, task)
                                      }}

                                    >

                                      <MenuUnfoldOutlined />

                                    </Button>

                                  </Popover>
                                  {/* <Modal title={"Activités actuelles/planifiers"}
                                  open={isModalOpenActivity}
                                  onOk={handleOk}
                                  footer={[
                                    <Button key="1"
                                      onClick={handleCancel}

                                    >OK</Button>,


                                  ]}
                                > */}

                                </Flex></div>


                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                  {/* <UpdateActivity {...detailsObj}></UpdateActivity> */}

                </section>
              </div>
            </>

          ))

        }

        {/* <CreateActivity {...obj}></CreateActivity> */}

        <Modal
          open={isModalOpenActivity}
          title={(valswitch == 2) ? "Etape" : (valswitch == 1) ? "Type/Plan d'activités" : "Supprimer l'étape"}
          onOk={handleOk}
          onCancel={handleCancel}
          width={'50%'}
          footer={

            [
              <Button key="back" onClick={handleCancel}>
                Retour
              </Button>,
              <Button key={1}
                type="primary"
                form="formtypecreate"
                htmlType="submit" loading={buttonLoading} style={{ marginRight: "10px",textAlign: 'right' }}>
                Enregistrer
              </Button>


            ]}
        >
          <Form
            layout="vertical"
            hideRequiredMark
            form={form}
            id="formtypecreate"
            fields={
              [
                {
                  name: ["status"],
                  value: nameetape,
                },
                {
                  name: ["color"],
                  value: coloretape,
                },


              ]
            }
          >
            {(valswitch == 1) ? (
              <>




                <Row gutter={16} style={{ marginBottom: "10px" }}>
                  <Radio.Group onChange={onChangeRadio} value={value}>
                    <Radio value={1}>Type Activité</Radio>
                    <Radio value={2}>Plan Activité</Radio>

                  </Radio.Group>
                </Row>
                <Row gutter={16}>
                  <Card style={{ width: "100%", padding: '10px', backgroundColor: '#f9fafa' }} >
                    <Row gutter={24} >


                      <Card style={{ width: "100%" }}>
                        <Col span={24}>
                          <Form.Item
                            name="plan_id"
                            label="Plan d'activité "

                            rules={[
                              {
                                required: false,
                                message: "Veuillez choisir le plan de Type d'activité",
                              },
                            ]}
                            hidden={hidedate}

                          >

                            <Select
                              style={{ width: '50%' }}
                              allowClear={true}
                              showSearch
                              filterOption={filterOption}
                              filterSort={filterSort}

                              placeholder="Plan d'activité"
                            >
                              {planactivities?.map((item) => (
                                <Option
                                  key={item.id}
                                  value={item.id}
                                  label={item.name}
                                >
                                  {item.name}
                                </Option>
                              ))}
                            </Select>
                          </Form.Item>


                          <Form.Item
                            name="activity_id"
                            label="Etape ID"

                            hidden

                          >
                            <Input />

                          </Form.Item>
                        </Col>

                        <Col span={24}>
                          <Form.Item
                            name="type_id"
                            label="Type d'activité "
                            rules={[
                              {
                                required: false,
                                message: "Veuillez choisir le plan de Type d'activité",
                              },
                            ]}

                            hidden={!hidedate}

                          >

                            <Select
                              style={{ width: '50%' }}
                              allowClear={true}
                              showSearch
                              filterOption={filterOption}
                              filterSort={filterSort}

                              placeholder="Type d'activité"
                            >
                              {typeactivities?.map((item) => (
                                <Option
                                  key={item.id}
                                  value={item.id}
                                  label={item.name}
                                >
                                  {item.name}
                                </Option>
                              ))}
                            </Select>
                          </Form.Item>

                        </Col>


                        <Col span={12}>

                          <Form.Item
                            style={{ width: '100%' }}
                            name="date_planifier"
                            label="Date du plan"
                            rules={[
                              {
                                required: false,
                                message: "Veuillez choisir l'action de Type d'activité",
                              },
                            ]}
                            hidden={hidedate}
                          >
                            <DatePicker style={{ width: '100%' }} />
                          </Form.Item>
                        </Col>
                        <Col span={24}>

                          <Form.Item
                            name="default_user"
                            label="Assigner à "
                            rules={[
                              {
                                required: false,
                                message: "Veuillez choisir l'action de Type d'activité",
                              },
                            ]}
                            hidden={!hidedate}

                          >

                            <Select
                              // mode="multiple"
                              style={{ width: 400 }}
                              allowClear={false}
                              showSearch
                              filterOption={filterOption}
                              filterSort={filterSort}
                              onChange={(e) => { }}
                              placeholder="Utilisateur par défaut"
                            >
                              {employees?.map((item) => (
                                (item?.post_id == 11) && (



                                  <Option

                                    key={item.id}
                                    value={item.id}
                                    label={item.user.name + '  ' + item.user.prenom}
                                  >
                                    {item.user.name + '  ' + item.user.prenom}
                                  </Option>)
                              ))}
                            </Select>
                          </Form.Item>
                        </Col>
                        <Col span={12}>

                          <Form.Item
                            style={{ width: '100%' }}
                            name="date_planif_type"
                            label="Date d'échéance"
                            rules={[
                              {
                                required: false,
                                message: "Veuillez choisir l'action de Type d'activité",
                              },
                            ]}
                            hidden={!hidedate}
                          >
                            <DatePicker style={{ width: '100%' }} />
                          </Form.Item>
                        </Col>
                        <Col>
                          <Form.Item
                            name="resume"
                            label="Résumé"
                            rules={[
                              {
                                required: false,
                                message: "Veuillez entrer le nom de la contact",
                              },
                            ]}
                            hidden={!hidedate}


                          >

                            <Input.TextArea />
                          </Form.Item>

                        </Col>
                        <Col>
                          <Form.Item
                            name="resume_plan"
                            label="Résumé"
                            rules={[
                              {
                                required: false,
                                message: "Veuillez entrer le nom de la contact",
                              },
                            ]}
                            hidden={hidedate}


                          >

                            <Input.TextArea />
                          </Form.Item>

                        </Col>



                      </Card>

                    </Row>

                  </Card>
                </Row>
              </>
            ) : (valswitch == 2) ? (<>
              <Row gutter={16}>
                <Card style={{ width: "100%" }}>
                  {/* <Col span={24}>
                                              <Form.Item
                                                name="color"
                                                label="Couleur"
                                              
                                              >

                                            <Space>
                                              <ColorPicker
                                           
                                                format={formatHex}
                                                value={(colorHex=="") ? coloretape : colorHex}
                                                onChange={setColorHex}
                                              
                                                onFormatChange={setFormatHex}
                                              />
                                              <span>Valeur HEX:{(hexString=="") ? coloretape : hexString}</span>
                                            </Space>

                                              </Form.Item>

                                            </Col> */}
                  <Col span={24}>
                    <Form.Item
                      name="status"
                      label={
                        <span>
                          Nom  <span style={{ color: 'red' }}>*</span>
                        </span>
                      }
                      rules={[
                        {
                          required: true,
                          message: "Veuillez entrer le nom de action type d'activité",
                        },
                      ]}
                      initialValue={nameetape}
                    >

                      <Input

                        style={{ width: "100%" }} placeholder="Veuillez entrer le nom de l'étape" />


                    </Form.Item>

                  </Col>

                </Card>


              </Row>


            </>) : (
              <>
                Voulez vous vraiment supprimer l'étape ?

              </>
            )}


          </Form>


        </Modal>
        <div className="order1 small-box">
          <section className="drag_container">
            <div className="container">

              <div className="drag_column">
                <div className="drag_row" style={{ textAlign: 'center' }}>
                  <h5>





                    {(can("add etapes activity") && (<Button
                      style={{ marginLeft: '10px', paddingBottom: '18px', border: 'none', display: 'block', backgroundColor: '#c8ebdd' }}

                      icon={<PlusOutlined />}
                      onClick={() => {
                        setvalswitch(2);
                        setetapeidchange(null);
                        setnameetape('');

                        //form.setFieldValue("status", etape?.status);
                        // form.setFieldValue("color", etape?.color);

                        setIsModalOpenActivity(true);

                      }}
                    >
                      Etape
                    </Button>))}
                  </h5>
                </div>
              </div>
            </div>
          </section>
        </div>
      </div>
    );
  }
}

export default TaskList;
