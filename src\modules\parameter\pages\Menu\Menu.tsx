import { getAllMenu } from "@/modules/parameter/features/menus/menuSlice";
import { Card, Typography, Tree, Button, Spin } from "antd";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { DownOutlined, EyeInvisibleOutlined } from "@ant-design/icons";
import type { DataNode, TreeProps } from "antd/es/tree";
import MenuDetails from "./MenuDetails";
import MenuCreate from "./MenuCreate";
import { icons } from "lucide-react";
import { useTranslation } from "react-i18next";
import { RootState } from "@features/store";
import { getPermissions } from "@/modules/parameter/features/permissions/permissionSlice";

const { Paragraph, Title, Text } = Typography;

const Menu = () => {
  const dispatch = useDispatch();
  const {t , i18n} = useTranslation()
  const [menu, setMenu] = useState([]);
  const [node, setNode] = useState({});
  const [visibleDetails, setVisibleDetails] = useState<boolean>(false);
  const [visibleCreate, setVisibleCreate] = useState<boolean>(false);
  const local = i18n.language === 'en' ? 'en' : 'fr' ;

  const allMenus = useSelector((state: RootState) => state.menu.data);
  const allPermissions = useSelector((state: RootState) => state.permission.data);


  const loadData = async () => {
    if (!allMenus?.length) {
      await dispatch(getAllMenu());
      console.log("menus fetching");
    }
    if (!allPermissions && typeof allPermissions !== "object") {
      await dispatch(getPermissions());
      console.log("permissions fetching");
    }
  };


  useEffect(() => {
    console.log("menus mounted");
    loadData();
  }, []);

  const Icon = ({ name, color = "#888", size = 15 }: any) => {
    try {
      const LucideIcon = icons[name];
      if (!LucideIcon) {
        // console.log("icon not defined in library - fix by ch");
      } else {
        return (
          <LucideIcon
            color={color}
            size={size}
            style={{ display: "inline-block", marginRight: 4 }}
          />
        );
      }
    } catch (ex) {
      // console.log(ex);
    }
  };
  /* const langS =  */
  const onSelect: TreeProps["onSelect"] = (selectedKeys, info) => {
    setNode(info.node);
    setVisibleDetails(true);
  };
  const onDragEnter: TreeProps["onDragEnter"] = (info) => {
    console.log("onDragEnter", info);
    // expandedKeys 需要受控时设置
    // setExpandedKeys(info.expandedKeys)
  };

  const onDrop: TreeProps["onDrop"] = (info) => {
    console.log("onDrop", info);
    const dropKey = info.node.key;
    const dragKey = info.dragNode.key;
    const dropPos = info.node.pos.split("-");
    const dropPosition =
      info.dropPosition - Number(dropPos[dropPos.length - 1]);

    const loop = (
      data: DataNode[],
      key: React.Key,
      callback: (node: DataNode, i: number, data: DataNode[]) => void
    ) => {
      for (let i = 0; i < data.length; i++) {
        if (data[i].key === key) {
          return callback(data[i], i, data);
        }
        if (data[i].children) {
          loop(data[i].children!, key, callback);
        }
      }
    };
    const data = [...menu];

    // Find dragObject
    let dragObj: DataNode;
    loop(data, dragKey, (item, index, arr) => {
      arr.splice(index, 1);
      dragObj = item;
    });

    if (!info.dropToGap) {
      // Drop on the content
      loop(data, dropKey, (item) => {
        item.children = item.children || [];
        // where to insert 示例添加到头部，可以是随意位置
        item.children.unshift(dragObj);
      });
    } else if (
      ((info.node as any).props.children || []).length > 0 && // Has children
      (info.node as any).props.expanded && // Is expanded
      dropPosition === 1 // On the bottom gap
    ) {
      loop(data, dropKey, (item) => {
        item.children = item.children || [];
        // where to insert 示例添加到头部，可以是随意位置
        item.children.unshift(dragObj);
        // in previous version, we use item.children.push(dragObj) to insert the
        // item to the tail of the children
      });
    } else {
      let ar: DataNode[] = [];
      let i: number;
      loop(data, dropKey, (_item, index, arr) => {
        ar = arr;
        i = index;
      });
      if (dropPosition === -1) {
        ar.splice(i!, 0, dragObj!);
      } else {
        ar.splice(i! + 1, 0, dragObj!);
      }
    }
    setMenu(data);
  };


  useEffect(() => {
    if(allMenus?.length && local){
      let dd = []
      dd = allMenus.map((item, index) => {
        return {
          name: item.designation_fr,
          title:
            item.active === 0 ? (
              <Text type="secondary">
                {item.designation_fr[local]} <EyeInvisibleOutlined />
              </Text>
            ) : (
              <Text>{item.designation_fr[local]}</Text>
            ),
          key: index,
          path: item.link,
          parent: item.menu_parent,
          id: item.id,
          ordre: item.ordre,
          activer: item.active,
          permission_id: item.permission_id,
          children: item.child_recursive.map((child, cp) => {
            return {
              name: child.designation_fr,
              title:
                child.active === 0 ? (
                  <Text type="secondary">
                    {child.designation_fr[local]} <EyeInvisibleOutlined />
                  </Text>
                ) : (
                  <Text>{child.designation_fr[local]}</Text>
                ),
              key: index + "-" + cp,
              icon: <Icon name={child.icon} />,
              iconName: child.icon,
              path: child.link,
              parent: child.menu_parant,
              id: child.id,
              ordre: child.ordre,
              activer: child.active,
              permission_id: child.permission_id,
              children: child.child_recursive.map((children, x) => {
                return {
                  name: children.designation_fr,
                  title:
                    children.active === 0 ? (
                      <Text type="secondary">
                        {children.designation_fr[local]}{" "}
                        <EyeInvisibleOutlined />
                      </Text>
                    ) : (
                      <Text>{children.designation_fr[local]}</Text>
                    ),
                  key: index + "-" + cp + "-" + x,
                  icon: <Icon name={children.icon} />,
                  iconName: children.icon,
                  path: children.link,
                  parent: children.menu_parant,
                  id: children.id,
                  ordre: children.ordre,
                  activer: children.active,
                  permission_id: children.permission_id,
                };
              }),
            };
          }),
        };
      });
      setMenu(dd);
    }
}, [allMenus, local]);

  return (
    <div>
      <Card
        title={<Title level={4}>Gestion de menu</Title>}
        bordered={false}
        extra={
          <Button
            type="primary"
            onClick={() => {
              setVisibleCreate(true);
            }}
          >
            Ajouter
          </Button>
        }
      >
        {menu.length !== 0 ? (
          <Tree
            className="ml-5"
            showIcon
            // showLine
            // draggable
            switcherIcon={<DownOutlined />}
            defaultExpandAll
            onSelect={onSelect}
            onDragEnter={onDragEnter}
            onDrop={onDrop}
            treeData={menu}
            // filterTreeNode={(node) => {
            //   return true;
            // }}
            // expandedKeys={menu.map((node) => node.key)}
          />
        ) : (
          <div className="text-center	h-80 mt-40">
            <Spin size="large" />
          </div>
        )}
      </Card>
      <MenuDetails
        visible={visibleDetails}
        setVisible={setVisibleDetails}
        node={node}
        menu={menu}
      />
      <MenuCreate
        visible={visibleCreate}
        setVisible={setVisibleCreate}
        menu={menu}
      />
    </div>
  );
};

export default Menu;
