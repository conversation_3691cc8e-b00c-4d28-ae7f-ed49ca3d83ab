{"emplacements": {"title": "Locations", "list": "Locations list", "add": "Add location", "edit": "Edit location", "view": "Location details", "fields": {"code": "Code", "libelle": "<PERSON><PERSON><PERSON>", "depot": "<PERSON><PERSON><PERSON><PERSON>"}}, "stock-unities": {"title": "Stock Unities", "list": "Stock Unities list", "add": "Add stock unity", "edit": "Edit stock unity", "view": "Stock unity details", "fields": {"libelle": "<PERSON><PERSON><PERSON>", "symbole": "Symbole", "parentUnity": "Parent unity", "coefficient": "Coefficient"}}, "tvas": {"title": "TVA", "list": "TVA list", "add": "Add TVA", "edit": "Edit TVA", "view": "TVA details", "fields": {"default": "<PERSON><PERSON><PERSON>", "type": "Type", "code": "Code", "designation": "Designation", "valeur": "<PERSON><PERSON>"}, "type": {"achat": "Sale", "vente": "Purchase", "achat_et_vente": "Sale et Purchase"}, "default": {"non-default": "Not default"}}, "moyens-paiement": {"title": "Payment methods", "list": "Payment methods list", "add": "Add payment method", "edit": "Edit payment method", "view": "Payment method details", "fields": {"code": "Code", "libelle": "<PERSON><PERSON><PERSON>"}}, "vente": {"dashboard": {"title": "Sale dashboard"}, "info_general": {"type": "Type", "code": "Code", "raison_sociale": "Company name", "poste": "Postion", "civilite": "Civility", "email": "Email address", "phone": "Phone number", "entreprise": "Entreprise", "departement": "Department", "adresse_1": "Address 1", "adresse_2": "Address 2", "pays": "Country", "code_postal": "Postal code", "matricule_fiscale": "Tax registration number", "assujetti_tva": "TVA subject", "code_placeholder": "Code will be generated automatically", "raison_sociale_placeholder": "Enter company name", "poste_placeholder": "Enter position", "civilite_placeholder": "Select civility", "email_placeholder": "Enter email address", "phone_placeholder": "Enter phone", "entreprise_placeholder": "Select entreprise", "departement_placeholder": "Select department", "adresse_1_placeholder": "Enter address 1", "adresse_2_placeholder": "Enter address 2", "pays_placeholder": "Select country", "code_postal_placeholder": "Enter postal code", "matricule_fiscale_placeholder": "Enter tax registration number", "assujetti_tva_placeholder": "TVA subject"}, "client": {"list": "List of clients", "add": "Add a client", "edit": "Edit client", "view": "Client details"}, "prospect": {"list": "List of prospects", "add": "Add a prospect", "edit": "Edit prospect", "view": "Prospect details"}, "contact": {"add_contact": "Add a contact", "firstName": "FirstName", "lastName": "LastName", "civilite": "Civility", "email": "Email address", "position": "Position", "pays": "Country", "gouvernorat": "Governorate", "delegation": "Delegation", "addresse": "Address", "code_postal": "Postal code", "phone": "Phone number", "phone_fix": "Landline phone number", "fax": "Fax number", "firstName_placeholder": "Enter firstName", "lastName_placeholder": "Enter lastName", "civilite_placeholder": "Select la civilité", "email_placeholder": "Enter email address", "position_placeholder": "Enter position", "pays_placeholder": "Select country", "gouvernorat_placeholder": "Select gouvernorate", "delegation_placeholder": "Enter delegation", "addresse_placeholder": "Enter address", "code_postal_placeholder": "Enter postal code", "phone_placeholder": "Enter phone number", "phone_fix_placeholder": "Enter landline phone number", "fax_placeholder": "Enter fax number"}}, "achat": {"dashboard": {"title": "Purchase dashboard"}, "fournisseur": {"list": "List of suppliers", "add": "Add a supplier", "edit": "Edit supplier", "view": "Supplier details"}}, "common": {"search": "Search...", "filter": "Filter", "actions": {"title": "Actions", "view": "View", "edit": "Edit", "delete": "Delete", "save": "Save", "cancel": "Cancel", "back": "Back", "upload": "Upload", "download": "Download", "confirm": "Confirm", "previous": "Previous", "next": "Next"}, "steps": {"general_info": "General information", "contacts_info": "Contacts information"}, "status": {"active": "Active", "inactive": "Inactive", "non_active": "Not active", "draft": "Draft", "published": "Published"}, "validation": {"required": "This field is required", "invalid_date": "Invalid date", "positive_number": "Number must be positive", "min_length": "Must contain at least {min} characters", "max_length": "Must not exceed {max} characters", "invalid_format": "Invalid format"}, "messages": {"confirm_delete": "Are you sure you want to delete?", "success_delete": "Audit type deleted successfully", "success_create": "Audit type created successfully", "success_update": "Audit type updated successfully", "error_load": "Error loading data", "error_save": "Error saving data"}}}