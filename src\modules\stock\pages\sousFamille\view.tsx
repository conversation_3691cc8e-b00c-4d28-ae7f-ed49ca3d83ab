import { ModalForm, ProTable } from "@ant-design/pro-components";
import { Divider, Form, Table, Typography } from "antd";

import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { DepotForm } from "../../components/DepotForm";
import { emplacements } from "../emplacements/data";
import { articleEmplacements, produits } from "../articles/data";
import SousFamilleForm from "../../components/SousFamilleForm";

function ShowSousFamilleForm({  open, setOpen, record, familles, tvas,}){
    const {t} = useTranslation();
    const [form] = Form.useForm();
  
    useEffect(() => {
      if (open) {
          form.setFieldsValue(record)
      }
    }, [open]);
    return(
    <ModalForm
      title={t("sous_familles.view")}
      disabled
      modalProps={{
        style:{
          top:20,
        },
      }}
      form={form}
      open={open}
      //onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          setOpen(false);

        }
      }}
      submitter={{
        resetButtonProps:{disabled:false},
        submitButtonProps: {hidden:true},
        searchConfig: {
          resetText: t("common.actions.back"),
        },
      }}
    >
      <SousFamilleForm familles={familles} tvas={tvas}/>
    </ModalForm>
    )
}
export default ShowSousFamilleForm;