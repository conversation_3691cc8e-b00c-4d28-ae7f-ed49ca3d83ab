import { useState, useRef } from "react";
import { ProTable } from "@ant-design/pro-components";
import { Button, Popconfirm, Space, Input, Tooltip, Typography, Card, Modal, Table } from "antd";
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined, SearchOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import useDebounce from "@hooks/useDebounce";

import CreatePlanActivity from "./createPlanActivity";
import EditPlanActivity from "./editPlanActivity";
import ViewPlanActivity from "./viewPlanActivity";
import { tasks } from "./datas";

function PlansActivities() {
  const { t } = useTranslation();
  const tableRef = useRef();

  const [dataSource, setDataSource] = useState(tasks);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [viewModalVisible, setViewModalVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState(null);
  const [viewingRecord, setViewingRecord] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [tableParams, setTableParams] = useState([]);

  const handleCancel = () => {
    setViewModalVisible(false);
  };

  const handleEdit = (record) => {
    setEditingRecord(record);  
    setEditModalVisible(true); 
  };

  const expandedRowRender = (record) => {
    console.log(dataSource);
    const columns = [
      { title: t("crm.activities.fields.nom"), dataIndex: "nom", key: "nom" },
      { title: t("crm.activities.fields.resume"), dataIndex: "resume", key: "resume" },
      { title: t("crm.activities.fields.planifier"), dataIndex: "planifier", key: "planifier" },
    ];
    return record.activities && record.activities.length > 0 ? (
      <Table columns={columns} dataSource={record.activities} pagination={false} rowKey={(activity) => activity.nom} />
    ) : (
      <Typography.Text type="secondary">{t("crm.activities.no_activities")}</Typography.Text>
    );
  };

  const columns = [
    {
      title: t("crm.plansActivities.fields.name"),
      dataIndex: "title",
      key: "title",
    },
    {
      title: t("crm.plansActivities.fields.description"),
      dataIndex: "description",
      key: "description",
    },
    {
      title: t("common.actions.title"),
      key: "action",
      align: "center",
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title={t("common.actions.view")}>
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => {
                setViewingRecord(record);
                setViewModalVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title={t("common.actions.edit")}>
            <Button
              type="link"
              style={{ color: "#f5b041" }}
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title={t("common.actions.delete")}>
            <Popconfirm
              title={t("common.messages.confirm_delete")}
              onConfirm={() => {
                const newDataSource = dataSource.filter(item => item.key !== record.key);
                setDataSource(newDataSource);
              }}
            >
              <Button type="link" style={{ color: "#ec7063" }} icon={<DeleteOutlined />} />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <Card
      bordered
      title={<Typography.Title level={4}>{t("crm.plansActivities.list")}</Typography.Title>}
      extra={
        <Space>
          <Input
            size="large"
            placeholder={t("common.search")}
            suffix={<SearchOutlined style={{ color: "#bfbfbf" }} />}
          />
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setCreateModalVisible(true)}
          >
            {t("crm.plansActivities.add")}
          </Button>
        </Space>
      }
    >
      <ProTable
        actionRef={tableRef}
        loading={isLoading}
        columns={columns}
        dataSource={dataSource}
        rowKey="key"
        search={false}
        options={false}
        pagination={{ showSizeChanger: true, defaultPageSize: 5 }}
      />

      {/* Create  */}
      <Modal
        title={t("crm.plansActivities.add")}
        visible={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        footer={null}
        width={800}
      >
        <CreatePlanActivity 
         onCancel={() => setCreateModalVisible(false)}
         setDataSource={setDataSource} 
        />
      </Modal>

      {/* Edit  */}
      <Modal
        title={"Modifier une tâche"}
        visible={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        footer={null}
        width={800}
      >
        <EditPlanActivity
          record={editingRecord}
          onCancel={() => setEditModalVisible(false)}
          setDataSource={setDataSource} 
        />
      </Modal>

      {/* View  */}
      <Modal
        title={t("crm.plansActivities.view")}
        visible={viewModalVisible}
        onCancel={handleCancel}
        footer={null}
        width={800}
      >
        <ViewPlanActivity
          record={viewingRecord}
          onCancel={() => setViewModalVisible(false)}
        />
      </Modal>
    </Card>
  );
}

export default PlansActivities;
