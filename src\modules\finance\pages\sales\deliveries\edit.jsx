import { ModalForm } from "@ant-design/pro-components";
import { <PERSON><PERSON>, <PERSON>, Col, DatePicker, Form, Input, InputNumber, Popconfirm, Row, Select, Table } from "antd";

import TextArea from "antd/es/input/TextArea.js";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";


import { entreprises , departements , depots, clients } from "./data";
import { DeleteOutlined, PlusOutlined } from "@ant-design/icons";
import DeliveryItem from "@src/modules/finance/components/delivery/DeliveryItem";

function EditBonForm({ open, onCancel ,record}){
    const {t} = useTranslation();
    const [loading, setLoading] = useState(false);
    const [entrepriseId , setEntrepriseId] =  useState(null);
    const [departementId , setDepartementId] =  useState(null);
    const [tierId , setTierId] =  useState(null);
    const [depotId, setDepotId] = useState(null);
    const dateFormat = "YYYY-MM-DD";
    const [form] = Form.useForm();
    const [value, setValue] = useState(2);
      useEffect(() => {
        if (open) {
           setEntrepriseId(record?.entreprise_id) ;
           setDepartementId(record?.departement_id) ;
           setTierId(record?.tier_id) ;
           setDepotId(record?.depot_id) ;
           showTierData(record?.tier_id) ;       
            form.setFieldsValue(record);
         
        }
      }, [open]);
      const showTierData = (e) => {
        const tier = clients.find((t) => t.id == e);
        form.setFieldsValue({
          email: tier?.email,
          adresse: tier?.adresse,
          phone: tier?.phone,
          matricule_fiscale: tier?.matricule_fiscale,
        });
      };
  
      const resetTierData = () => {
        form.setFieldsValue({
          email: null ,
          adresse: null,
          phone: null,
          matricule_fiscale:  null,
        });
      } 
      const footer = () => {
        return (
          <>
            <div style={{ textAlign: "right" }}>
              <div
                style={{
                  display: "inline-block",
                  borderRight: "1px solid #ddd",
                  padding: "0 16px",
                }}
              >
                <strong>Total HT : </strong>
                <InputNumber
                  style={{ width: "150px" }}
                  disabled={true}
                 // value={totalHT}
                  min={0}
                  precision={3}
                  step={0.001}
                />
                <br />
                <br />
                <br />
              </div>
              <div
                style={{
                  display: "inline-block",
                  borderRight: "1px solid #ddd",
                  padding: "0 16px",
                }}
              >
                <strong>Remise (%) : </strong>
                <InputNumber
                  min={0}
                  style={{ width: "150px" }}
                 // value={remise}
                  //onChange={(value) => handleRemiseDevis(value)}
                  disabled={true}
                />
                <br />
                <br />
                <strong>Total HT après remise : </strong>
                <InputNumber
                  style={{ width: "150px" }}
                  disabled={true}
              //    value={totalHTRemise}
                  min={0}
                  precision={3}
                  step={0.001}
                />
              </div>
      
              <div
                style={{
                  display: "inline-block",
                  padding: "0 16px",
                }}
              >
                <strong>Total TVA : </strong>
                <InputNumber
                  style={{ width: "150px" }}
                  disabled={true}
                  //value={totalTVA}
                  min={0}
                  precision={3}
                  step={0.001}
                />
                <br />
                <br />
                <strong>Total TTC : </strong>
                <InputNumber
                  style={{ width: "150px" }}
                  disabled={true}
                 // value={totalTTC}
                  min={0}
                  precision={3}
                  step={0.001}
                />
              </div>
            </div>
          </>
        );
      };
    return(
    <ModalForm
      title={t("bon.edit")}
    
      form={form}
      open={open}
        width ='90%'
    //   onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.edit"),
          resetText: t("common.actions.cancel"),
        },
      }}
    >
       <Row className="mt-0" gutter={[12, 24]}>
            <Col span={12}>
              <Card title= {t('sales.general_info')} >
                <Form.Item
                  name={"num_bon"}
                  label={t('bon_cols.num_bon')}
                  style={{
                    display: "inline-block",
                    width: "45%",
                    margin: "0 8px 8px",
                  }}
                >
                  <Input disabled={true} />
                </Form.Item>
             
                <Form.Item
                  name={"entreprise_id"}
                  label={
                    <span>
                      {t('sales.entreprise')} <span style={{ color: "red" }}>*</span>
                    </span>
                  }
                  style={{
                    display: "inline-block",
                    width: "45%",
                    margin: "0 8px 8px",
                  }}
                  rules={[
                    {
                      required: true,
                      message:  t('devis_message.required_field')   , 
                    },
                  ]}
                >
                  <Select  
                    allowClear
                    placeholder= {t('sales.entreprise')}
                    options={entreprises.map((item) => ({ value: item.id, label: item.nom }))}  
                    value={entrepriseId}   
                    onSelect={(e) => {
                     setEntrepriseId(e);
                    }}
                    onClear={() => {
                    setEntrepriseId(null);
                    setDepartementId(null);
                    setTierId(null);
                    }}         
                  />
                </Form.Item>
                <Form.Item
                  name="departement_id"
                  label={
                    <span>
                       {t('sales.departement')}  <span style={{ color: "red" }}>*</span>
                    </span>
                  }
                  style={{
                    display: "inline-block",
                    width: "45%",
                    margin: "0 8px 8px",
                  }}
                  rules={[
                    {
                      required: true,
                      message:  t('devis_message.required_field')   , 
                    },
                  ]}
                >
                  <Select
                    allowClear
                    placeholder= {t('sales.departement')}  
                    value={departementId}   
                    onSelect={(e) => {setDepartementId(e);}}  
                    onClear={() => { setDepartementId(null); }}  
                    options={departements.filter( (dept) => dept.entreprise_id === entrepriseId).map((item) => ({ value: item.id, label: item.nom }))}                 
                  />
                </Form.Item>
                <Form.Item
                  name={"date_bon"}
                  label={
                    <span>
                   {t('bon_cols.date_bon')} <span style={{ color: "red" }}>*</span>
                    </span>
                  }
                  style={{
                    display: "inline-block",
                    width: "45%",
                    margin: "0 8px 8px",
                  }}
                  rules={[
                    { required: true,  message:  t('devis_message.required_field') } 
                  ]}
                >
                  <DatePicker
                    format={dateFormat}
                    style={{ width: "100%" }}
                    placeholder ={t('bon_cols.date_bon')}
                  />
                </Form.Item>
                <Form.Item
                  name={"depot_id"}
                  label={
                    <span>
                      {t('sales.depot')} <span style={{ color: "red" }}>*</span>
                    </span>
                  }
                  style={{
                    display: "inline-block",
                    width: "45%",
                    margin: "0 8px 8px",
                  }}
                  rules={[
                    {
                      required: true,
                      message:  t('devis_message.required_field')   , 
                    },
                  ]}
                >
                  <Select  
                    allowClear
                    placeholder= {t('sales.depot')}
                    options={depots.map((item) => ({ value: item.id, label: item.nom }))}  
                    value={entrepriseId}   
                    onSelect={(e) => {
                     setDepotId(e);
                    }}
                    onClear={() => {
                      setDepotId(null);
                    }}         
                  />
                </Form.Item>
              </Card>
            </Col>
            <Col span={12}>
              <Card title= {t('sales.client_info')}  >
                <Form.Item
                  name={"tier_id"}
                  label={
                    <span>
                     {t('sales.client')} <span style={{ color: "red" }}>*</span>
                    </span>
                  }
                  style={{
                    display: "inline-block",
                    width: "45%",
                    margin: "0 8px 8px",
                  }}
                  rules={[
                    { required: true, message: t('devis_message.required_field') },
                  ]}
                >
                  <Select
                    allowClear
                    placeholder={t('sales.client')}
                    value={tierId}   
                    onSelect={(e) => {setTierId(e);showTierData(e);}}  
                    onClear={() => { setTierId(null);resetTierData() }} 
                    options={clients.filter(tier => tier.entreprise_id === entrepriseId &&  tier.departement_id === departementId).map((item) => ({ value: item.id, label: item.nom }))}  
                  />
                </Form.Item>
                <Form.Item
                  name={"matricule_fiscale"}
                  label={t('sales.matricule')}
                  style={{
                    display: "inline-block",
                    width: "45%",
                    margin: "0 8px 8px",
                  }}
                >
                  <Input autoFocus placeholder={t('sales.matricule')} />
                 </Form.Item>

                <Form.Item
                  name="email"
                  label={t('sales.email')}
                  style={{
                    display: "inline-block",
                    width: "45%",
                    margin: "0 8px 8px",
                  }}
                >
                  <Input autoFocus placeholder={t('sales.email')} />
                </Form.Item>
                <Form.Item
                  name="phone"
                  label={t('sales.tel')}
                  style={{
                    display: "inline-block",
                    width: "45%",
                    margin: "0 8px 8px",
                  }}
                >
                  <Input autoFocus  placeholder={t('sales.tel')}/>
                </Form.Item>
                <Form.Item
                  name="adresse"
                  label={t('sales.address')}
                  style={{
                    display: "inline-block",
                    width: "94%",
                    margin: "0 8px 8px",
                  }}
                >
                  <Input autoFocus placeholder={t('sales.address')} />
                </Form.Item>
              </Card>
            </Col>

           
            <Col span={24}></Col>
            
       </Row>
       <DeliveryItem form={form} record={record} open={open} depot={depotId} />
    </ModalForm>
    )
}
export default EditBonForm;