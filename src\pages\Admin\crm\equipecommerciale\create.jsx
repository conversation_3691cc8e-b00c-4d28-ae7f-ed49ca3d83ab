import { ProForm, ProFormText, ProFormSelect } from "@ant-design/pro-form";
import { message } from "antd";
import { datas } from "./datas";

const CreateCommercial = ({ onCancel, setDataSource }) => {
  const handleSubmit = (values) => {
    const newKey = Date.now();
    const newCommercial = { key: newKey, ...values };
    setDataSource([...datas, newCommercial]);
    message.success("Commercial ajouté avec succès");
    onCancel();
  };

  return (
    <ProForm onFinish={handleSubmit}>
      <ProFormText name="name" label="Nom" rules={[{ required: true }]} />
      <ProFormText name="chef" label="Chef d'équipe" rules={[{ required: true }]} />
      <ProFormSelect name="membres" label="Membres" mode="multiple" options={[{ label: "Je<PERSON><PERSON>", value: "<PERSON><PERSON><PERSON>" }, { label: "<PERSON><PERSON>", value: "<PERSON><PERSON><PERSON>" }]} />
      <ProFormText name="alias" label="Alias" rules={[{ required: true }]} />
      <ProFormText name="objectif" label="Objectif" rules={[{ required: true }]} />
    </ProForm>
  );
};

export default CreateCommercial;
