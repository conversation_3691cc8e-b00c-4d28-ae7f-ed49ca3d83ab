import React from "react";
import { Drawer, Descriptions } from "antd";

const ViewDrawer = ({ visible, onClose, record }) => {
  return (
    <Drawer
      title="Détails de la catégorie"
      placement="right"
      width={500}
      onClose={onClose}
      visible={visible}
    >
      <Descriptions column={1}>
        <Descriptions.Item label="Nom">{record?.name}</Descriptions.Item>
        <Descriptions.Item label="Type">
          {record?.type === "email" ? "Email" : "Document"}
        </Descriptions.Item>
      </Descriptions>
    </Drawer>
  );
};

export default ViewDrawer;