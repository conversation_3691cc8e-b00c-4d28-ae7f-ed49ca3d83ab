import { Form, Input, Button, message } from "antd";
import { useState, useEffect } from "react";

const EditGouvernorat = ({ record, onCancel, setDataSource }) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (record) {
      form.setFieldsValue(record);
    }
  }, [record]);

  const handleSubmit = async (values) => {
    try {
      const updatedData = dataSource.map((item) =>
        item.key === record.key ? { ...item, ...values } : item
      );
      setDataSource(updatedData);

      message.success("Gouvernorat modifié avec succès!");
      onCancel();
    } catch (error) {
      console.error(error);
      message.error("Erreur lors de la modification du Gouvernorat");
    }
  };

  return (
    <Form form={form} layout="vertical" onFinish={handleSubmit}>
      <Form.Item
        name="code"
        label="Code Gouvernorat"
        rules={[{ required: true, message: "Veuillez saisir le code" }]}
      >
        <Input placeholder="Saisissez le code" />
      </Form.Item>
      <Form.Item
        name="libelle"
        label="Libellé"
        rules={[{ required: true, message: "Veuillez saisir le libellé" }]}
      >
        <Input placeholder="Saisissez le libellé" />
      </Form.Item>
   
      <Form.Item>
        <div style={{ textAlign: 'right' }}>
        <Button key="back"  style={{marginRight:'15px'}} >
            Annuler
          </Button>
          <Button type="primary" htmlType="submit">
            Enregistrer
          </Button>
        </div>
      </Form.Item>
    </Form>
  );
};

export default EditGouvernorat;
