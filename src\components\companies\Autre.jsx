import {
    FileOutlined,
    MinusCircleOutlined,
    PlusCircleOutlined,
    UploadOutlined,
  } from "@ant-design/icons";
  import { Col, DatePicker, Form, Input, message, Row, Upload } from "antd";
  import { useState } from "react";
  import { useTranslation } from "react-i18next";
  
  const Autre = ({ form, view }) => {
    const { t } = useTranslation();
    const handleAdd = () => {
      const currentdata= form.getFieldValue("autres") || [];
      const designation = form.getFieldValue("designation");
    
      if (designation) {
        const key = Date.now();
        form.setFieldsValue({
          autres: [
            ...currentdata,
            {
              key: key,
              designation:designation 
            }
          ],
        });
        form.resetFields([
          "designation" 
        
        ]);
      } else {
        message.error(t("Merci de bien vouloir remplir tous les champs."));
      }
    };
    return (
      <>
        <Row gutter={16} style={{  marginBottom: "10px" }}>
         
          <Col span={18}>Designation Activité</Col>
          <Col span={1}></Col>
        </Row>
        <Form.List name="autres">
          {(fields, { remove }) => (
            <>
              {fields.map((field, index) => (
                <Row
                  gutter={16}
                  align="middle"
                  key={field.key}
                >
                 
                  <Col span={18}>
                    <Form.Item name={[index, "designation"]} rules={[]}>
                      <Input
                        allowClear={true}
                        disabled={view}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={1}>
                    {!view && (
                      <Form.Item>
                        <MinusCircleOutlined
                          style={{
                            color: "red",
                            fontSize: "18px",
                            cursor: "pointer",
                          }}
                          onClick={() => remove(index)}
                        />
                      </Form.Item>
                    )}
                  </Col>
                </Row>
              ))}
            </>
          )}
        </Form.List>
  
        {!view && (
          <Row gutter={16} align="middle" style={{ marginBottom: "5px" }}>
            
            <Col span={18}>
              <Form.Item name={`designation`} rules={[]}>
                <Input
                
                  allowClear={true}
                />
              </Form.Item>
            </Col>
            <Col span={1}>
              <Form.Item>
                <PlusCircleOutlined
                  style={{
                    color: "green",
                    fontSize: "18px",
                    cursor: "pointer",
                  }}
                  onClick={() => handleAdd()}
                />
              </Form.Item>
            </Col>
          </Row>
        )}
      </>
    );
  };
  
  export default Autre;
  