import { useState, useEffect, useRef, useMemo } from 'react';
import TaskList from "./events.js";
import { useDispatch, useSelector } from "react-redux";

import {
  Calendar, Badge, Card, Button, Flex, DatePicker, Popover, DatePickerProps,

  Typography,

  Tabs,
  message,
  Form,
  Space
} from "antd";



import type { BadgeProps, CalendarProps, ColorPickerProps } from 'antd';
import { Select, Row, Col } from 'antd';
import { Dayjs } from 'dayjs';

import dayjs from 'dayjs'

import { GrapeIcon } from 'lucide-react';
import { BarChartOutlined, CalendarOutlined, LineChartOutlined, PieChartOutlined,TableOutlined } from '@ant-design/icons';
import moment from "moment";

import { ProColumns, ProTable } from '@ant-design/pro-components';
import { MenuProps } from 'antd/lib/index.js';
import { Color } from 'antd/es/color-picker/index.js';
import Kanban from './kanbans.jsx';
import { LeadsBarChart } from './LeadsBar.jsx';
import OpportunitiesTable from './OpportunitiesTables.jsx';



const Fetch = () => {
  const dispatch = useDispatch();
  let [activities, setactivities] = useState([]);
  const [etapes, setetapes] = useState([]);

  const [pipeline, setpipeline] = useState(1);

  const [steps, setsteps] = useState<any>([]);
  let [seriesall, setsries] = useState<any>();
  let [search, setSearch] = useState<any>(3);


  const [countsteps, setcountsteps] = useState<any>([]);





  const [visibleDetails, setVisibleDetails] = useState(false);
  const tableRef = useRef<any>(14885);
  const [diplomes, setdiplomes] = useState<any>();
  const { Option } = Select;
  const [value, setValue] = useState(1);

  const [isModalOpen, setIsModalOpen] = useState(false);

  const [isModalOpenActivity, setIsModalOpenActivity] = useState(false);
  const [isModalOpent, setIsModalOpent] = useState(false);


  const [visibleForm, setVisibleForm] = useState(false);
  const [buttonLoading, setButtonLoading] = useState(false);

  const [etapeidchange, setetapeidchange] = useState(null);

  const [employees, setGetEmployees] = useState<any>();
  const [planactivities, setGetPlansActivities] = useState<any>();
  const [hidedate, sethidedate] = useState(true);
  const [testref, settestref] = useState<any>();


  const [refresh, forceRefresh] = useState(0);

  const [typeactivities, setGetTypeActivities] = useState<any>();
  const [entreprises, setEntreprises] = useState<any>();

  const [clients, setGetClients] = useState<any>();
  const [open, setOpen] = useState(false);
  const [modify, setModify] = useState(false);
  const [closepop, setclosepop] = useState(false);
  const [loggedUser, setLoggedUser] = useState(
    localStorage.getItem("user") ? JSON.parse(localStorage.getItem("user")) : {}
  );

  const [userselect, setuserid] = useState();
  const [year, setyear] = useState('');
  const [nameplan, setnameplan] = useState<any>();
  const [k, setk] = useState<number>();
  const [arrayhidensteps, setarrayhidensteps] = useState<any>([]);
  const [valswitch, setvalswitch] = useState<any>(1);
  const [colorHex, setColorHex] = useState<Color | string>("");
  const [formatHex, setFormatHex] = useState<ColorPickerProps['format']>('hex');

  const [nameetape, setnameetape] = useState<any>();
  const [coloretape, setcoloretape] = useState<any>();
  const tableRefactivities = useRef<any>();
  const tableRefChild = useRef<any>();



  const hexString = useMemo(
    () => (typeof colorHex === 'string' ? colorHex : colorHex?.toHexString()),
    [colorHex],
  );






  const columns: ProColumns<any>[] = [


    {
      title: "Opportunité",
      dataIndex: "name",
      key: "nameop",
      render: (_, e) => (e.name),

      responsive: ["sm"],

      renderFormItem: (item, { type, defaultRender, ...rest }, form) => {
        return (
          <Select
            showSearch
            allowClear
            placeholder="Choisir une opportunités"
            filterOption={filterOption}
            filterSort={filterSort}
            onChange={(e) => {
              setnameop(e);

            }}

          >
            {activities?.map((item) => (

              <Option key={item.id} value={item.id} label={item.name}>
                {item.name}
              </Option>
            ))}
          </Select>
        );
      },
    },

    {
      title: "Revenu",
      dataIndex: "revenu",
      key: "revenu",
      search: false,

      responsive: ["sm"],
    },
    {
      title: "Probabilité",
      dataIndex: "probability",
      key: "probability",
      search: false,

      responsive: ["sm"],
    },

    ,
    {
      title: "Responsable",
      dataIndex: "vender_respons",
      key: "vender_respons",
      render: (_, e) => ((e?.vender != null) ? (e?.vender?.user?.name + '  ' + e?.vender?.user?.prenom) : _),
      renderFormItem: (item, { type, defaultRender, ...rest }, form) => {
        return (
          <Select
            showSearch
            allowClear
            placeholder="Choisir un collaborateur"
            filterOption={filterOption}
            filterSort={filterSort}
            onChange={(e) => {
              setassigned_to(e);

            }}
          >
            {employees?.map((item) => (
              (item?.post_id == 11) && (



                <Option

                  key={item.id}
                  value={item.id}
                  label={item.user.name + '  ' + item.user.prenom}
                >
                  {item.user.name + '  ' + item.user.prenom}
                </Option>)
            ))}
          </Select>
        );
      },

      responsive: ["sm"],
    },
    {
      title: "Client",
      dataIndex: "client_pros_id",
      key: "client_pros_id",
      render: (_, e) => ((e?.client_pros_id != null) ? e?.client?.designation : '_'),
      renderFormItem: (item, { type, defaultRender, ...rest }, form) => {
        return (
          <Select
            showSearch
            allowClear
            placeholder="Choisir un client"
            filterOption={filterOption}
            filterSort={filterSort}
            onChange={(e) => {
              setclientid(e);

            }}
          >
            {clients?.map((item) => (
              (item?.post_id == 11) && (



                <Option

                  key={item.id}
                  value={item.id}
                  label={item.user.name + '  ' + item.user.prenom}
                >
                  {item.user.name + '  ' + item.user.prenom}
                </Option>)
            ))}
          </Select>
        );
      },
      responsive: ["sm"],

    },



    // {
    //   title: "Action",
    //   valueType: "option",
    //   key: "option",
    //   render: (text, diplome, _, action) => [
    //     windowWidth > 620 && (
    //       <Space size="small">
    //         <a
    //          title="Consulter"
    //           onClick={() => {
    //             setdataplan(diplome);
    //             setVisibleDetails(true);
    //             setModify(false)
    //           }}
    //         >
    //           <EyeOutlined />
    //         </a>
    //         <Divider type="vertical" />
    //         <a
    //          title="Modifier"
    //           onClick={() => {
    //             setdataplan(diplome);
    //             setModify(true);
    //             setVisibleDetails(true);
    //           }}
    //         >
    //           <EditOutlined />
    //         </a>
    //         <Divider type="vertical" />
    //                     <a>
    //                         <Popconfirm
    //                             title="voulez-vous vraiment supprimer cette ligne ?"
    //                             onConfirm={() => { handleDelete(diplome.id) }}
    //                             okText="Oui"
    //                             cancelText="Non"
    //                         >
    //                             <DeleteOutlined />
    //                         </Popconfirm>
    //                     </a> 
    //       </Space>
    //     ) 
    //   ],
    // },
  ];
  const nestedColumns: any = [

    {
      title: "Type d'activité",
      dataIndex: "name",
      key: "name",
      render: (_, e) => (e?.type?.name),

      search: false,
    },

    {
      title: "Assigné à",
      dataIndex: "assigned_to",
      key: "assigned_to",
      render: (_, e) => (e?.employer?.user?.name + ' ' + e?.employer?.user?.prenom),

      search: false,
    },
    {
      title: "Date d'éxécution",
      dataIndex: "date1",
      key: "date1",
      render: (_, e) => (<>{e?.date_planifier_plan != undefined ? e?.date_planifier_plan : e?.date_planifier}   </>),

      search: false,
    },
    {
      title: "Plan",
      dataIndex: "name",
      key: "namep",
      render: (_, e) => (e?.plan?.name != undefined ? e?.plan?.name : '(n\'est pas rattacher à un plan)'),

      search: false,
    },
    ,
    {
      title: "Etat",
      valueType: "option",
      key: "option",


      render: (_, diplome) => [
        (
          (diplome?.plan?.id != undefined) ? (

            (diplome?.status != null) ? (<Space style={{ backgroundColor: diplome?.status == 1 ? 'green' : 'red' }}>
              {diplome?.status == 1 ? 'fait' : 'annuler'}



            </Space>) :
              (<Space size="small">
                (n'est pas encore traité)

              </Space>))
            : (
              (diplome?.status_type_id != null) ? (<Space style={{ backgroundColor: diplome?.status_type_id == 1 ? 'green' : 'red' }}>
                {diplome?.status_type_id == 1 ? 'fait' : 'annuler'}



              </Space>) :
                (<Space size="small">
                  (n'est pas encore traité)


                </Space>




                ))
        )
      ]
    },







  ];
  //select search and sort
  const filterOption = (input, option) =>
    (option?.label ?? "").toLowerCase().includes(input.toLowerCase());
  const filterSort = (optionA, optionB) =>
    (optionA?.label ?? "")
      .toLowerCase()
      .localeCompare((optionB?.label ?? "").toLowerCase());
  const [calandervisible, setcalandervisible] = useState(false);
  const [data, setData] = useState<any>();
  const [totalData, settotalData] = useState<number>();
  const [nameop, setnameop] = useState<number>();
  const [nametype, setnametype] = useState<number>();
  const [status_type_id, setstatus_type_id] = useState<number>();
  const [assigned_to, setassigned_to] = useState<number>();
  const [client_id, setclientid] = useState<number>();


  const [stepvisble, setVisibleEtape] = useState<any>(false);



  const handleReplier = () => {
    setVisibleEtape(true);

  }
  const [loggedUserPermissions, setLoggedUserPermissions] = useState(
    localStorage.getItem("permissions")
      ? JSON.parse(localStorage.getItem("permissions"))
      : []
  );
  const can = (permission) => loggedUserPermissions.includes(permission);

  const items: MenuProps['items'] = [


    {
      label: 'Replier',
      key: '0',
    },
    {
      label: 'modifier',
      key: '1',
    },
    {
      type: 'divider',
    },
    {
      label: 'Supprimer',
      key: '3',
    },
  ];

  const itemsmodif: MenuProps['items'] = [


    {
      label: 'Replier',
      key: '0',
    },
    ,
    {
      label: 'modifier',
      key: '1',
    },
    {
      type: 'divider',
    },

  ];


  const itemsmodifdelete: MenuProps['items'] = [


    {
      label: 'Replier',
      key: '0',
    },


  ];


  const itemsdelete: MenuProps['items'] = [


    {
      label: 'Replier',
      key: '0',
    },

    {
      type: 'divider',
    },
    {
      label: 'Supprimer',
      key: '3',
    },
  ];

  const onChangeYear: DatePickerProps['onChange'] = (date, dateString) => {

    handlesetchart((userselect != undefined ? userselect : handlegetauth(loggedUser.id)), dateString[0], search);
    setyear(dateString[0]);
  };

  const handlegetauth = (e) => {
    let idauth;
    const empl = employees?.filter((empl) => (
      empl?.user_id == e
    ));
    empl?.map((e) => {
      idauth = e?.id;
    });

    return idauth;
  }


  


  const [pageNumber, setPageNumber] = useState<number>();
  const [pageNumbertab, setPageNumberTab] = useState<number>(1);


  





  //console.log("data", activities);
  /*****Begin manage calender********************/

  const getListData = (value: Dayjs) => {
    let listData: { type: string; content }[] = []; // Specify the type of listData
    let d;
    let cur;


    activities?.map(activity => {

      d = new Date(activity?.date_cloture).getDay();
      cur = value.date();
      if ((value.format('YYYY-MM-DD')) == (activity?.date_cloture)) {
        listData = [

          {
            type: 'warning', content: (
              <>
                <Popover

                  content={<>
                    {

                      <div >
                        <li>

                          <div className="d-flex align-items-center flex-wrap mx-3">
                            <Card title={activity?.name}
                              headStyle={{ backgroundColor: activity?.etape?.color }}
                            >

                              <li className="list-group-item">
                                <span className="fw-bold ms-2">
                                  <CalendarOutlined />  {activity?.date_cloture}
                                </span><b>Journée entière</b></li>
                              <li className="list-group-item">
                                <span className="fw-bold ms-2">
                                  Révenu éspiré: <b>{activity?.revenu != undefined ? activity?.revenu?.toFixed(3) : ''}</b>
                                </span></li>
                              <li className="list-group-item">
                                <span className="fw-bold ms-2">
                                  Client: <b>{activity?.client?.designation}</b>
                                </span></li>
                              <li className="list-group-item">
                                <span className="fw-bold ms-2">
                                  Etat: <b>{activity?.etape?.status}</b>
                                </span></li>

                              <li style={{ marginTop: '10px', paddingRight: '10px' }}>
                                {((can("edit opportunity")) && (<Button style={{ backgroundColor: activity?.etape?.status, marginRight: '10px' }}
                                  onClick={() => {
                                    setModify(true);
                                    setcalandervisible(true);
                                    setData(activities);

                                  }}

                                >Modifier</Button>))}
                                <Button style={{ backgroundColor: activity?.etape?.status, marginRight: '10px' }} >Supprimer</Button>


                              </li>

                            </Card>

                          </div>



                        </li>


                      </div>



                    }
                  </>



                  }

                  zIndex={2}

                >
                  <Button style={{ backgroundColor: activity?.etape?.status }}
                    onClick={() => {
                      setclosepop(true);
                    }}
                  >{activity?.name}</Button>
                </Popover>

              </>
            )



          },
        ];
      }

    })





    return listData || [];
  };

  const getMonthData = (value: Dayjs) => {
    if (value.month() === 8) {
      return 1394;
    }
  };
  const monthCellRender = (value: Dayjs) => {
    const num = getMonthData(value);
    return num ? (
      <div className="notes-month">
        <section>{num}</section>
        <span>Backlog number</span>
      </div>
    ) : null;
  };

  const dateCellRender = (value: Dayjs) => {
    const listData = getListData(value);
    return (
      <ul className="events">
        {listData?.map((item) => (
          <li key={item.content}>
            <Badge status={item.type as BadgeProps['status']} text={item.content} />
          </li>
        ))}
      </ul>
    );
  };
  
  const cellRender: CalendarProps<Dayjs>['cellRender'] = (current, info) => {
    if (info.type === 'date') return dateCellRender(current);
    if (info.type === 'month') return monthCellRender(current);
    return info.originNode;
  };

  /*****end manage calender************/
  const handlesetchart = (userid: number, year: string, search) => {
    //alert(userid);
    //alert(year);
    //alert(search);


    let ar = [];
    let activityperstep = [];
    let allseries = [];
    let revenus = [];
    let names = [];


    //create array of status
    etapes?.map((etape) => {
      ar.push(etape?.status);

    });


    activities?.map((activity) => {

      let arstepact;
      arstepact = [];
      let arstepname;
      arstepname = [];
      etapes?.map((step) => {
        arstepact.push(step?.id);
        arstepname.push(step?.id);


      });
      for (let i = 0; i < arstepact.length; i++) {

        if (year != '') {

          if (userid == undefined) {
            if ((arstepact[i] == activity?.etape?.id) && ((new Date(activity?.date_cloture).getFullYear().toString() == year))) {
              //console.log('userid', userid);

              if (search == 1) {
                arstepact[i] = activity?.revenu;

              } else if (search == 2) {
                arstepact[i] = activity?.probability;

              } else if (search == 3) {
                let acts = 0;
                //console.log('102', activity?.plans);
                let typestot = 0;
                if (activity?.plans?.length == 0) {
                  let plansdetp = activity?.plans;
                  let tot = 0
                  plansdetp?.map((p) => {
                    tot += p.plan?.details.length;
                  });
                  acts = tot;
                }
                if (activity?.types?.length) {
                  typestot = activity?.types?.length;
                }

                arstepact[i] = acts + typestot;

              }
              arstepname[i] = activity?.name;

            } else {
              arstepact[i] = 0;
              arstepname[i] = '';

            }
          } else {
            if ((arstepact[i] == activity?.etape?.id) && ((new Date(activity?.date_cloture).getFullYear().toString() == year))

              && (activity?.vender_respons == userid)
            ) {




              if (search == 1) {
                arstepact[i] = activity?.revenu;

              } else if (search == 2) {
                arstepact[i] = activity?.probability;

              } else if (search == 3) {
                let acts = 0;
                //console.log('102', activity?.plans);
                let typestot = 0;
                if (activity?.plans?.length == 0) {
                  let plansdetp = activity?.plans;
                  let tot = 0
                  plansdetp?.map((p) => {
                    tot += p.plan?.details.length;
                  });
                  acts = tot;
                }
                if (activity?.types?.length) {
                  typestot = activity?.types?.length;
                }

                arstepact[i] = acts + typestot;

              }
              arstepname[i] = activity?.name;

            } else {
              arstepact[i] = 0;
              arstepname[i] = '';

            }
          }



        } else {


          if (((arstepact[i] == activity?.etape?.id) && (activity?.vender_respons == userid))) {


            if (search == 1) {
              arstepact[i] = activity?.revenu;

            } else if (search == 2) {
              //console.log('hire');
              arstepact[i] = activity?.probability;

            } else if (search == 3) {
              let acts = 0;
              //console.log('102', activity?.plans);
              let typestot = 0;
              if (activity?.plans?.length == 0) {
                let plansdetp = activity?.plans;
                let tot = 0
                plansdetp?.map((p) => {
                  tot += p.plan?.details.length;
                });
                acts = tot;
              }
              if (activity?.types?.length) {
                typestot = activity?.types?.length;
              }

              arstepact[i] = acts + typestot;

            }
            arstepname[i] = activity?.name;

          } else {
            arstepact[i] = 0;
            arstepname[i] = '';


          }
        }

      }


      revenus.push(arstepact);
      names.push(arstepname);
    })

    //console.log("rev", revenus);
    //console.log("names", names);


    //}
    revenus?.map((rev, i) => {

      allseries.push({
        name: names[i],
        // type: 'column',
        data: rev
      });





    });




    let linearray;
    linearray = [];
    etapes?.map((etape) => {

      let totale = 0;
      let etapesrev = activities?.filter((data) => (
        ((userid == undefined)
          ?
          ((data?.etape?.status == etape?.status) && ((new Date(data?.date_cloture).getFullYear().toString() == year)))

          :
          ((data?.etape?.status == etape?.status) && ((new Date(data?.date_cloture).getFullYear().toString() == year) && (data?.vender_respons == userid)))

        )));


      for (let i = 0; i < etapesrev.length; i++) {

        totale += etapesrev[i]?.revenu;
        // totale+=1;
      }


      linearray.push(totale);
    })


    //console.log('linearray', linearray);


    //   allseries.push(  {
    //   name: 'line',
    //   type: 'line',
    //   data:linearray
    //  });


    etapes?.map((etape) => {
      let datas;
      if (userid != 0) {
        datas = activities?.filter((data) => ((data?.etape?.status == etape?.status) && (data?.vender_respons == userid)));

      } else {
        datas = activities?.filter((data) => data?.etape?.status == etape?.status);

      }

      let datasrevenus = 0;
      datas?.map((data) => {
        datasrevenus += data.revenu;
      })
      activityperstep.push(datasrevenus);

    });

    //console.log('hire2', revenus);
    setsteps(ar);
    setsries(allseries);
    setcountsteps(activityperstep);
  }
  let series: { name: any; data: any[]; }[] = seriesall;


  let options: {
    chart: {

      type: any,
      height: any,
      stacked: any,
      toolbar: {
        show: any,
      },
      zoom: {
        enabled: any,
      }
    },
    responsive: [{
      breakpoint: any,
      options: {
        legend: {
          position: any,
          offsetX: any,
          offsetY: any,
        }
      }
    }],
    plotOptions: {
      bar: {
        horizontal: any,
        borderRadius: any,
        borderRadiusApplication: any, // 'around', 'end'
        borderRadiusWhenStacked: any, // 'all', 'last'
        // dataLabels: {
        //   total: {
        //     enabled: any,
        //     // style: {
        //     //   fontSize: any,
        //     //   fontWeight: any,
        //     // }
        //   }
        // }
      }
    },
    xaxis: {
      type: any,
      categories: any,
    },
    legend: {
      position: any,
      offsetY: any,
    },
    fill: {
      opacity: any
    }



  } = {
    chart: {
      type: 'bar',
      height: 350,
      stacked: true,
      toolbar: {
        show: true
      },
      zoom: {
        enabled: true
      }
    },
    responsive: [{
      breakpoint: 480,
      options: {
        legend: {
          position: 'bottom',
          offsetX: -10,
          offsetY: 0
        }
      }
    }],
    plotOptions: {
      bar: {
        horizontal: false,
        borderRadius: 10,
        borderRadiusApplication: 'end', // 'around', 'end'
        borderRadiusWhenStacked: 'last', // 'all', 'last'
        // dataLabels: {
        //   total: {
        //     enabled: true,
        //     // style: {
        //     //   fontSize: '13px',
        //     //   fontWeight: 900
        //     // }
        //   }
        // }
      }
    },
    xaxis: {
      type: 'string',
      categories: steps,
    },
    legend: {
      position: 'right',
      offsetY: 40
    },
    fill: {
      opacity: 1
    }
  };






  // let options: {

  //   chart: {

  //     events: {
  //       beforeMount:any,
  //     }
  //     height: any,
  //     type: any,
  //     stacked: any
  //   },
  //   dataLabels: {
  //     enabled: any
  //   },
  //   stroke: {
  //     width: any
  //   },
  //   title: {
  //     text: any;
  //     align: any;
  //     offsetX: any;
  //   },
  //   xaxis: {
  //     categories: any;
  //   },
  //   yaxis: [
  //     {
  //       min: any,
  //       seriesName: any,
  //       axisTicks: {
  //         show: any,
  //       },
  //       axisBorder: {
  //         show: any,
  //         color: any
  //       },
  //       labels: {
  //         style: {
  //           colors: any,
  //         }
  //       },
  //       title: {
  //         text: any,
  //         style: {
  //           color: any,
  //         }
  //       },
  //       tooltip: {
  //         enabled: any
  //       }
  //     },
  //     {
  //       min: any,
  //       seriesName: any,
  //       opposite: any,
  //       axisTicks: {
  //         show: any,
  //       },
  //       axisBorder: {
  //         show: any,
  //         color: any
  //       },
  //       labels: {
  //         style: {
  //           colors: any,
  //         }
  //       },
  //       title: {
  //         text: any,
  //         style: {
  //           color: any,
  //         }
  //       },
  //     },
  //     {
  //       seriesName: any,
  //       opposite: any,
  //       axisTicks: {
  //         show: any,
  //       },
  //       axisBorder: {
  //         show: any,
  //         color: any
  //       },
  //       labels: {
  //         style: {
  //           colors: any,
  //         },
  //       },
  //       title: {
  //         text: any,
  //         style: {
  //           color: any,
  //         }
  //       }
  //     },
  //   ],
  //   tooltip: {
  //     fixed: {
  //       enabled: any,
  //       position: any, // topRight, topLeft, bottomRight, bottomLeft
  //       offsetY: any,
  //       offsetX: any
  //     },
  //   },
  //   legend: {
  //     horizontalAlign: any,
  //     offsetX: any
  //   }


  // } = {
  //   chart: {
  //     events: {
  //       beforeMount:()=>{
  //       setsries([]);

  //       }
  //     },
  //     height: 1000,
  //     type: 'line',
  //     stacked: true
  //   },
  //   dataLabels: {
  //     enabled: false
  //   },
  //   stroke: {
  //     width: [4, 4, 4]
  //   },
  //   title: {
  //     text: 'Revenus des activités',
  //     align: 'left',
  //     offsetX: 110
  //   },
  //   xaxis: {
  //     categories: steps,
  //   },
  //   yaxis: [
  //     {
  //       min: 0,
  //       seriesName: 'Activité',
  //       axisTicks: {
  //         show: true,
  //       },
  //       axisBorder: {
  //         show: true,
  //         color: '#008FFB'
  //       },
  //       labels: {
  //         style: {
  //           colors: '#008FFB',
  //         }
  //       },
  //       title: {
  //         text: "Income (thousand crores)",
  //         style: {
  //           color: '#008FFB',
  //         }
  //       },
  //       tooltip: {
  //         enabled: true
  //       }
  //     },
  //     {
  //       min: 0,
  //       seriesName: 'Cashflow',
  //       opposite: true,
  //       axisTicks: {
  //         show: true,
  //       },
  //       axisBorder: {
  //         show: true,
  //         color: '#00E396'
  //       },
  //       labels: {
  //         style: {
  //           colors: '#00E396',
  //         }
  //       },
  //       title: {
  //         text: "pregression revenus des activités",
  //         style: {
  //           color: '#00E396',
  //         }
  //       },
  //     },
  //     {
  //       seriesName: 'Activité',
  //       opposite: true,
  //       axisTicks: {
  //         show: true,
  //       },
  //       axisBorder: {
  //         show: true,
  //         color: '#FEB019'
  //       },
  //       labels: {
  //         style: {
  //           colors: '#FEB019',
  //         },
  //       },
  //       title: {
  //         text: "pregression des revenues des  activités  ",
  //         style: {
  //           color: '#FEB019',
  //         }
  //       }
  //     },
  //   ],
  //   tooltip: {
  //     fixed: {
  //       enabled: true,
  //       position: 'topLeft', // topRight, topLeft, bottomRight, bottomLeft
  //       offsetY: 30,
  //       offsetX: 60
  //     },
  //   },
  //   legend: {
  //     horizontalAlign: 'left',
  //     offsetX: 40
  //   }
  // };





   return (
    
    //(pipeline == 1) ? (

  //   <div>

  //     <Flex justify={'center'} >
  //       {/* <Button type="primary"
  //         onClick={() => {
  //           // //console.log("aa",series);

  //           setpipeline(4);
  //           handlesetchart(handlegetauth(loggedUser.id), new Date().getFullYear().toString(), 3);
  //           //console.log("aa", series);


  //         }}

  //         style={{ margin: '5px' }} icon={<TableOutlined />}>
  //         Tableau avec filtres
  //       </Button> */}
  //       <Button type="primary"

  //         onClick={() => {
  //           setsries([]);
  //           setsteps([]);
  //           setcountsteps([]);
  //           setpipeline(1);
  //         }}
  //         style={{ margin: '5px' }} icon={<PieChartOutlined />} >
  //         Pipeline
  //       </Button>
  //       {/* <Button type="primary"
  //         onClick={() => {
  //           setsries([]);
  //           setsteps([]);
  //           setcountsteps([]);
  //           setpipeline(2);

  //         }}

  //         style={{ margin: '5px' }} icon={<CalendarOutlined />}>
  //         Calendrier des opportunités
  //       </Button> */}
  //       <Button type="primary"
  //         onClick={() => {
  //           // //console.log("aa",series);

  //           setpipeline(3);
  //           //handlegetauth(loggedUser.id);
  //           let empid = handlegetauth(loggedUser?.id);
  //           //alert(empid);
  //           setuserid(empid);
  //           handlesetchart(handlegetauth(loggedUser?.id), new Date().getFullYear().toString(), 3);
  //           //console.log("aa", series);


  //         }}

  //         style={{ margin: '5px' }} icon={<LineChartOutlined />}>
  //         Chart liniére avec des filtres
  //       </Button>



  //     </Flex>
  //     <Kanban />
  //     {/* {etapes?.map((photo, i) => (
  //       [
  //         (i == 1) && (




  //           // <TaskList tasks={activities} taskreserve={activities} dispatch={dispatch} etapes={etapes}
  //           //   isModalOpen={isModalOpen} setIsModalOpen={setIsModalOpen} setContent={setContent}
  //           //   setIsModalOpenActivity={setIsModalOpenActivity}
  //           //   isModalOpenActivity={isModalOpenActivity}
  //           //   message={message}
  //           //   forceRefresh={forceRefresh}
  //           //   refresh={refresh}
  //           //   testref={testref}
  //           //   settestref={settestref}
  //           //   content={content} visibleForm={visibleForm}
  //           //   employees={employees} planactivities={planactivities}
  //           //   typeactivities={typeactivities} clients={clients} setVisibleForm={setVisibleForm}
  //           //   setStep={setStep} step={step} entreprises={entreprises}
  //           //   setModify={setModify}
  //           //   setVisibleDetails={setVisibleDetails}
  //           //   setOpen={setOpen}
  //           //   buttonLoading={buttonLoading}
  //           //   setButtonLoading={setButtonLoading}
  //           //   open={open}
  //           //   form={form}
  //           //   visibleDetails={visibleDetails}
  //           //   setValue={setValue}
  //           //   value={value}
  //           //   sethidedate={sethidedate}
  //           //   hidedate={hidedate}
  //           //   filterOption={filterOption}
  //           //   filterSort={filterSort}
  //           //   hexString={hexString}
  //           //   stepvisble={stepvisble}
  //           //   setVisibleEtape={setVisibleEtape}
  //           //   can={can}
  //           //   isModalOpent={isModalOpent}
  //           //   setisModalOpent={setIsModalOpent}

  //           //   items={(can("edit etapes activity") && can("delete etapes activity")) ? items : (can("edit etapes activity") ? itemsmodif : can("edit etapes activity") ? itemsdelete : itemsmodifdelete)}

  //           //   itemsdelete={itemsdelete}
  //           //   itemsmodif={itemsmodif}
  //           //   itemsmodifdelete={itemsmodifdelete}

  //           //   valswitch={valswitch}
  //           //   setvalswitch={setvalswitch}
  //           //   arrayhidensteps={arrayhidensteps}
  //           //   setarrayhidensteps={setarrayhidensteps}
  //           //   formatHex={formatHex}
  //           //   setColorHex={setColorHex}
  //           //   colorHex={colorHex}

  //           //   setFormatHex={setFormatHex}
  //           //   nameetape={nameetape}
  //           //   setnameetape={setnameetape}
  //           //   coloretape={coloretape}
  //           //   setcoloretape={setcoloretape}
  //           //   etapeidchange={etapeidchange}
  //           //   setetapeidchange={setetapeidchange}

  //           //   setetapes={setetapes}
  //           //   setetapeschanges={setetapeschanges}
  //           //   etapeschanges={etapeschanges}
  //           //   changecontentetape={changecontentetape}
  //           //   setChangeEtapeCont={setChangeEtapeCont}

  //           // />

  //         )
  //       ]
  //     ))} */}
  //   </div>) : ((pipeline == 2) ? (
  //     <div  >
  //       <Flex justify={'center'} >
  //         {/* <Button type="primary"
  //           className='btn-menu-crm'
  //           onClick={() => {
  //             // //console.log("aa",series);

  //             setpipeline(4);
  //             handlesetchart(handlegetauth(loggedUser.id), new Date().getFullYear().toString(), 3);
  //             //console.log("aa", series);


  //           }}

  //           style={{ margin: '5px' }} icon={<TableOutlined />}>
  //           Tableau des filtres
  //         </Button> */}
  //         <Button type="primary"
  //           className='btn-menu-crm'
  //           onClick={() => {
  //             setsries([]);
  //             setsteps([]);
  //             setcountsteps([]);
  //             setpipeline(1);
  //           }}
  //           style={{ margin: '5px' }} icon={<PieChartOutlined />} >
  //           Pipeline
  //         </Button>
  //         {/* <Button type="primary"
  //           className='btn-menu-crm'
  //           onClick={() => {
  //             setsries([]);
  //             setsteps([]);
  //             setcountsteps([]);
  //             setpipeline(2);
  //           }}

  //           style={{ margin: '5px' }} icon={<CalendarOutlined />}>
  //           Calendrier des opportunités

  //         </Button> */}
  //         <Button type="primary"
  //           className='btn-menu-crm'
  //           onClick={() => {
  //             // //console.log("aa",series);

  //             setpipeline(3);
  //             //handlegetauth(loggedUser.id);
  //             let empid = handlegetauth(loggedUser?.id);
  //             //alert(empid);
  //             setuserid(empid);
  //             handlesetchart(handlegetauth(loggedUser?.id), new Date().getFullYear().toString(), 3);
  //             //console.log("aa", series);


  //           }}

  //           style={{ margin: '5px' }} icon={<LineChartOutlined />}>
  //           Chart liniére avec des filtres

  //         </Button>


  //       </Flex>
  //       <Calendar cellRender={cellRender} />
  //       {/* <UpdateActivityType {...detailsObj}></UpdateActivityType> */}

  //     </div>

  //   ) : ((pipeline == 3) ? (
  //     <div  >
  //       <Flex justify={'center'} >
  //         {/* <Button type="primary"
  //           onClick={() => {
  //             // //console.log("aa",series);

  //             setpipeline(4);
  //             let empid = handlegetauth(loggedUser?.id);
  //             //alert(empid);
  //             setuserid(empid);

  //             handlesetchart(handlegetauth(loggedUser.id), new Date().getFullYear().toString(), 3);
  //             //console.log("aa", series);


  //           }}

  //           style={{ margin: '5px' }} icon={<TableOutlined />}>
  //           Tableau des filtres
  //         </Button> */}
  //         <Button type="primary"

  //           onClick={() => {
  //             setsries([]);
  //             setsteps([]);
  //             setcountsteps([]);

  //             setpipeline(1);
  //           }}
  //           style={{ margin: '5px' }} icon={<PieChartOutlined />} >
  //           Pipeline
  //         </Button>
  //         {/* <Button type="primary"
  //           onClick={() => {
  //             setsries([]);
  //             setsteps([]);
  //             setcountsteps([]);
  //             setpipeline(2);
  //           }}

  //           style={{ margin: '5px' }} icon={<CalendarOutlined />}>
  //           Calendrier des opportunités
  //         </Button> */}
  //         <Button type="primary"
  //           onClick={() => {
  //             // //console.log("aa",series);

  //             setpipeline(3);
  //             //handlegetauth(loggedUser.id);
  //             let empid = handlegetauth(loggedUser?.id);
  //             //alert(empid);
  //             setuserid(empid);
  //             handlesetchart(handlegetauth(loggedUser?.id), new Date().getFullYear().toString(), 3);
  //             //console.log("aa", series);


  //           }}

  //           style={{ margin: '5px' }} icon={<LineChartOutlined />}>
  //           Chart liniére avec des filtres

  //         </Button>


  //       </Flex>
       
  //       <LeadsBarChart />

  //       {/* <ReactApexChart options={options} series={series} type='bar' height={650} /> */}
  //       {/* <DualAxes {...config} /> */}
  //     </div>
  //   ) : (
  //     <>
  //       <Flex justify={'center'} >
  //         {/* <Button type="primary"
  //           onClick={() => {
  //             // //console.log("aa",series);

  //             setpipeline(4);
  //             handlesetchart(handlegetauth(loggedUser.id), new Date().getFullYear().toString(), 3);
  //             //console.log("aa", series);


  //           }}

  //           style={{ margin: '5px' }} icon={<TableOutlined />}>
  //           Tableau des filtres
  //         </Button> */}
  //         <Button type="primary"

  //           onClick={() => {
  //             setsries([]);
  //             setsteps([]);
  //             setcountsteps([]);
  //             setpipeline(1);
  //           }}
  //           style={{ margin: '5px' }} icon={<PieChartOutlined />} >
  //           Pipeline
  //         </Button>
  //         {/* <Button type="primary"
  //           onClick={() => {
  //             setsries([]);
  //             setsteps([]);
  //             setcountsteps([]);
  //             setpipeline(2);
  //           }}

  //           style={{ margin: '5px' }} icon={<CalendarOutlined />}>
  //           Calendrier des opportunités
  //         </Button> */}
  //         <Button type="primary"
  //           onClick={() => {
  //             // //console.log("aa",series);

  //             setpipeline(3);
  //             //handlegetauth(loggedUser.id);
  //             let empid = handlegetauth(loggedUser?.id);
  //             //alert(empid);
  //             setuserid(empid);
  //             handlesetchart(handlegetauth(loggedUser?.id), new Date().getFullYear().toString(), 3);
  //             //console.log("aa", series);


  //           }}

  //           style={{ margin: '5px' }} icon={<LineChartOutlined />}>
  //           Line chart avec filtres

  //         </Button>


  //       </Flex>


  //       <Tabs
  //         defaultActiveKey="1"
  //         centered
  //         items={new Array(2).fill(null)?.map((_, i) => {
  //           const id = String(i + 1);
  //           if (i == 0) {
  //             return {
  //               label: `List des opportunités`,
  //               key: id,
  //               children:
                 
  //                       <OpportunitiesTable />


  //                ,
  //             };
  //           }

  //         })}
  //       />




  //     </>



  //   )))

<>
<Kanban />
</>
  


  );

};
export default Fetch;