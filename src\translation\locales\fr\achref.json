{"depots": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "list": "Liste des dépôts", "add": "A<PERSON>ter un dépôt", "edit": "Modifier le dépôt", "view": "<PERSON><PERSON><PERSON> dépôt", "deposit_manager": "Respons<PERSON> <PERSON><PERSON><PERSON>", "fields": {"designation": "Désignation", "adresse": "<PERSON><PERSON><PERSON>", "entreprise": "Entreprise", "manager": "Responsable", "phone": "téléphone"}, "messages": {"confirm_delete": "Êtes-vous sûr de vouloir supprimer?", "success_delete": "Dépôt supprimé avec succès", "success_create": "<PERSON>é<PERSON>ôt créé avec succès", "success_update": "Dépôt modifié avec succès", "error_load": "Erreur lors du chargement des données", "error_save": "Erreur lors de l'enregistrement"}}, "articles": {"title": "Articles", "list": "Liste des articles", "add": "Ajouter un article", "edit": "Modifier l'article", "view": "<PERSON><PERSON><PERSON> d'article", "money": "<PERSON><PERSON>", "percentage": "Pourcentage", "fields": {"code": "Code", "designation": "Désignation", "type_article": "Type d'article", "status": "Statut", "famille": "<PERSON><PERSON><PERSON>", "sous_famille": "Sous-famille", "unite_achat": "<PERSON><PERSON>", "unite_stock": "Unité de stock", "unite_vente": "<PERSON>é de vente", "fournisseur": "Fournisseur", "description": "Description de l'article", "seuil_min": "<PERSON><PERSON> minimum", "seuil_max": "<PERSON><PERSON> maximum", "quantity": "Quantité", "tax": "Tax", "prix_achat": "Prix d'achat", "prix_vente": "Prix de vente", "frais": "<PERSON><PERSON>", "frais_percent": "Frais en pourcentage", "frais_argent": "<PERSON><PERSON> frai<PERSON>", "frais_type": "Type de frais", "prix_revient": "Prix de revient", "marge": "Marge", "marge_type": "Type de marge", "marge_percent": "Marge en pourcentage", "marge_argent": "<PERSON><PERSON>", "prix_ht": "Prix HT", "taux_tva": "Taux de TVA", "tva_argent": "Montant de TVA", "prix_ttc": "Prix TTC", "allow_negative_stock": "Autoriser les stocks négatifs", "replacement_article": "Article de remplacement"}, "messages": {"confirm_delete": "Êtes-vous sûr de vouloir supprimer?", "success_delete": "Article supprimé avec succès", "success_create": "Article créé avec succès", "success_update": "Article modifié avec succès", "error_load": "Erreur lors du chargement des données", "error_save": "Erreur lors de l'enregistrement"}}, "mouvements": {"title": "Mouvement d'article", "fields": {"type_mouvement": "Type mouvement", "quantity": "Quantité", "date": "Date"}}, "familles": {"title": "Familles", "list": "Liste des familles", "add": "Ajouter une famille", "edit": "Modifier la famille", "view": "Dé<PERSON> de la famille", "fields": {"code": "Code", "libelle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tva": "TVA", "remise": "Remise", "allow_negative_stock": "Autoriser les stocks négatifs"}, "messages": {"confirm_delete": "Êtes-vous sûr de vouloir supprimer?", "success_delete": "Famille supprimée avec succès", "success_create": "Famille créée avec succès", "success_update": "Famille modifiée avec succès", "error_load": "Erreur lors du chargement des données", "error_save": "Erreur lors de l'enregistrement"}}, "sous_familles": {"title": "Sous-familles", "list": "Liste des sous-familles", "add": "Ajouter une sous-famille", "edit": "Modifier la sous-famille", "view": "<PERSON>é<PERSON> de la sous-famille", "fields": {"code": "Code", "libelle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tva": "TVA", "remise": "Remise", "allow_negative_stock": "Autoriser les stocks négatifs", "famille": "<PERSON><PERSON><PERSON>"}, "messages": {"confirm_delete": "Êtes-vous sûr de vouloir supprimer?", "success_delete": "Sous-famille supprimée avec succès", "success_create": "Sous-famille créée avec succès", "success_update": "Sous-famille modifiée avec succès", "error_load": "Erreur lors du chargement des données", "error_save": "Erreur lors de l'enregistrement"}}}