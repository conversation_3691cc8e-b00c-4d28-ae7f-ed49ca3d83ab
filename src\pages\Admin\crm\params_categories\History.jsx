import React from 'react';
import { ProTable } from '@ant-design/pro-components';
import { Tag, Input, Button } from 'antd';  // Import from antd instead
import dayjs from 'dayjs';

const History = ({ history }) => {
  const columns = [
    {
      title: 'Action',
      dataIndex: 'action',
      key: 'action',
      render: (action) => {
        const colorMap = {
          Created: 'green',
          Edited: 'blue',
          Viewed: 'cyan',
          Deleted: 'red',
          Archived: 'volcano',
          Downloaded: 'purple',
          Imported: 'magenta',
        };
        return <Tag color={colorMap[action] || 'default'}>{action}</Tag>;
      },
    },
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      render: (date) => (date ? dayjs(date).format('YYYY-MM-DD HH:mm:ss') : '-'),
      sorter: (a, b) => dayjs(a.date).unix() - dayjs(b.date).unix(),
      defaultSortOrder: 'descend', // Sort by date descending by default
    },
    {
      title: 'Personne',
      dataIndex: 'person',
      key: 'person',
      render: (person) => person || '-',
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm }) => (
        <div style={{ padding: 8 }}>
          <Input
            placeholder="Rechercher une personne"
            value={selectedKeys[0]}
            onChange={(e) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={confirm}
            style={{ width: 188, marginBottom: 8, display: 'block' }}
          />
          <Button
            type="primary"
            onClick={confirm}
            size="small"
            style={{ width: 90 }}
          >
            Rechercher
          </Button>
        </div>
      ),
      onFilter: (value, record) =>
        record.person?.toLowerCase().includes(value.toLowerCase()),
    },
  ];

  return (
    <ProTable
      columns={columns}
      dataSource={history}
      rowKey="id"
      pagination={{ pageSize: 5 }}
      search={false}
      options={false}
      size="small"
    />
  );
};

export default History;