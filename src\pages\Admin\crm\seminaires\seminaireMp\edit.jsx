import { ModalForm } from "@ant-design/pro-components";
import {
  <PERSON><PERSON>,
  <PERSON>,
  Col,
  DatePicker,
  Divider,
  Form,
  Input,
  InputNumber,
  Modal,
  Popconfirm,
  Radio,
  Row,
  Select,
  Steps,
  Table,
  Tooltip,
} from "antd";

import TextArea from "antd/es/input/TextArea.js";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { pays } from "./data";
import { CheckCircleTwoTone } from "@ant-design/icons";
import Pays from "../../pays";

function EditSeminaireMP({ open, onCancel, record, show }) {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [steps, setSteps] = useState(record.initialSteps);
  const [form] = Form.useForm();
  useEffect(() => {
    if (open) {
      //setSteps(record.initialSteps)
      form.setFieldsValue(record);
    }
  }, [open]);
  const [completedSteps, setCompletedSteps] = useState([]);
  const [showMotifModal, setShowMotifModal] = useState(false);
  const [selectedStepId, setSelectedStepId] = useState(null);
  const [reason, setReason] = useState("");
  const handleStepClick = (step) => {
    if (step.motif && step.status === "wait") {
      setSelectedStepId(step.id);
      setShowMotifModal(true);
    } else {
      toggleStepStatus(step.id);
    }
  };
  const toggleStepStatus = (id) => {
    const updated = steps.map((step) => {
      if (step.id === id) {
        const newStatus = step.status === "finish" ? "wait" : "finish";
        return { ...step, status: newStatus, reason: reason };
      }
      return step;
    });
    setSteps(updated);
  };
  return (
    <ModalForm
      title={
        show ? t("Afficher la séminaire MP") : t("Modifier la séminaire MP")
      }
      form={form}
      open={open}
      width="80%"
      modalProps={{
        style: { top: 20 },
      }}
      //   onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={ show ? {
         submitButtonProps: { hidden: true },
        searchConfig: {
        
          resetText: t("common.actions.cancel"),
        },
      } : { searchConfig: {
          submitText: t("common.actions.edit"),
          resetText: t("common.actions.cancel"),
        }}}
    >
      <Card className="mt-2">
        <Steps
          direction="horizontal"
          type="navigation"
          className="site-navigation-steps"
          current={-1}
        >
          {steps.map((step, index) => (
            <Steps.Step
              key={step.id}
               title={ <Tooltip title={step.reason}>{step.title}</Tooltip>}
              style={{ cursor: "pointer" }}
              onClick={(index) => {
                handleStepClick(step);
              }}
              status={step.status}
              icon={
                step.status === "finish" ? (
                  <CheckCircleTwoTone twoToneColor="#52c41a" />
                ) : undefined
              }
            />
          ))}
        </Steps>
        </Card>
         <Card className="mt-2">
        <Row gutter={16}>
          <Col md={16} xs={24}>
            <Form.Item name={"responsable"} label={t("Responsable FIPA")} >
              <Input disabled={show} />
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item name={"inclure"} label={t("Inclure")} disabled={show}>
              <Select />
            </Form.Item>
          </Col>
        </Row>
      </Card>
      <Divider>Données générales </Divider>
      <Card className="mt-2">
        <Row gutter={16}>
          <Col md={24} xs={24}>
            <Form.Item name={"intitule"} label={t("Intitulé")} disabled={show}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name={"theme"} label={t("Thème")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={6} xs={24}>
            <Form.Item name={"date_debut"} label={t("Date début")}>
              <DatePicker />
            </Form.Item>
          </Col>

          <Col md={6} xs={24}>
            <Form.Item name={"date_fin"} label={t("Date fin")}>
              <DatePicker />
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item name={"pays"} label={t("Pays")}>
              <Select 
                allowClear
                options={pays.map((item) => ({
                  value: item.id,
                  label: item.nom,
                }))}
                onSelect={(e) => {}}
                onClear={() => {}}/>
            </Form.Item>
          </Col>
          <Col md={16} xs={24}>
            <Form.Item name={"region"} label={"Région"}>
              <Input />
            </Form.Item>
          </Col>

          <Col md={12} xs={24}>
            <Form.Item name={"action_conjointe"} label={t("Action conjointe")}>
              <Select 
               allowClear
                options= {[{value : 1 , label : "Conjointe"} , {value : 2 , label : "Non conjointe"}]}
            
               />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name={"binome"} label={t("Binôme")}>
              <Select
              
              />
            </Form.Item>
          </Col>
          <Col md={24} xs={24}>
            <Form.Item name={"propose_par"} label={t("Proposée par")}>
              <Input></Input>
            </Form.Item>
          </Col>
          <Col md={24} xs={24}>
            <Form.Item name={"objectifs"} label={t("Objectifs recherchés")}>
              <Input></Input>
            </Form.Item>
          </Col>
          <Col md={24} xs={24}>
            <Form.Item
              name={"lieu"}
              label={t("Lieu")}
              style={{
                display: "inline-block",
                width: "100%",
                margin: "0 8px 8px",
              }}
            >
              <Input />
            </Form.Item>
          </Col>
        </Row>
      </Card>
      <Divider>Organisateur et Participation </Divider>
      <Card className="mt-2">
        <Row gutter={16}>
          <Col md={12} xs={24}>
            <Form.Item
              name={"type_participation"}
              label={t("Type departicipation")}
            >
              <Select />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name={"if_active"} label={t("Si active")}>
              <Input.TextArea rows={3} />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item
              name={"type_organisation"}
              label={t("Type d’organisation")}
            >
              <Select />
            </Form.Item>
          </Col>
          <Col md={24} xs={24}>
            <Form.Item
              name={"partenaire_tunisien"}
              label={t("Partenaires tunisiens")}
            >
              <Input />
            </Form.Item>
          </Col>
          <Col md={24} xs={24}>
            <Form.Item
              name={"partenaire_etranger"}
              label={t("Partenaires étrangers")}
            >
              <Input />
            </Form.Item>
          </Col>
        </Row>
      </Card>
      <Divider>Présence d'officiels</Divider>
      <Card className="mt-2">
        <Row gutter={16}>
          <Col md={16} xs={24}>
            <Form.Item name={"officiel"} label={t("Officiel(s")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item name={"dg_fipa"} label={t(" ")}>
              <Radio>DG FIPA</Radio>
            </Form.Item>
          </Col>

          <Col md={24} xs={24}>
            <Form.Item name={"programme"} label={t("Programme et déroulement")}>
              <Input.TextArea rows={2} />
            </Form.Item>
          </Col>
        </Row>
      </Card>
      <Divider>Diaspora</Divider>
      <Card className="mt-2">
        <Row gutter={16}>
          <Col md={24} xs={24}>
            <Form.Item
              name={"avec_ou_pour"}
              label={t("Avec ou pour la diaspora")}
            >
              <Select />
            </Form.Item>
          </Col>
          <Col md={24} xs={24}>
            <Form.Item name={"diaspora"} label={t("Diaspora")}>
              <Input.TextArea rows={3} />
            </Form.Item>
          </Col>
        </Row>
      </Card>

      <Modal
        title="Justification du changement d'état"
        open={showMotifModal}
        onCancel={() => {
          setShowMotifModal(false);
          setReason("");
        }}
        onOk={() => {
          if (!reason.trim()) {
            message.error("Veuillez remplir le champ de justification.");
            return;
          }
          toggleStepStatus(selectedStepId);
          setShowMotifModal(false);
          setReason("");
        }}
        okText="Valider"
        cancelText="Annuler"
      >
        <p>Veuillez justifier ce changement :</p>
        <Input.TextArea
          rows={4}
          value={reason}
          onChange={(e) => setReason(e.target.value)}
          placeholder="Saisissez votre justification ici"
        />
      </Modal>
    </ModalForm>
  );
}
export default EditSeminaireMP;
