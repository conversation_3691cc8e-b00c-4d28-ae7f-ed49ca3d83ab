import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import {
  Row,
  Col,
  Breadcrumb,
  Badge,
  Dropdown,
  Button,
  Space,
  Menu,
} from "antd";
import {
  HomeFilled,
  LogoutOutlined,
  UserOutlined,
  DownOutlined,
  DoubleLeftOutlined,
  DoubleRightOutlined,
  DashboardOutlined,
} from "@ant-design/icons";

import { NavLink, Link, useNavigate, useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { logout } from "@features/auth/authSlice";

const profile = [
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    key={0}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M18 10C18 14.4183 14.4183 18 10 18C5.58172 18 2 14.4183 2 10C2 5.58172 5.58172 2 10 2C14.4183 2 18 5.58172 18 10ZM12 7C12 8.10457 11.1046 9 10 9C8.89543 9 8 8.10457 8 7C8 5.89543 8.89543 5 10 5C11.1046 5 12 5.89543 12 7ZM9.99993 11C7.98239 11 6.24394 12.195 5.45374 13.9157C6.55403 15.192 8.18265 16 9.99998 16C11.8173 16 13.4459 15.1921 14.5462 13.9158C13.756 12.195 12.0175 11 9.99993 11Z"
      fill="#111827"
    ></path>
  </svg>,
];

const toggler = [
  <svg
    width="20"
    height="20"
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 448 512"
    key={0}
  >
    <path d="M16 132h416c8.837 0 16-7.163 16-16V76c0-8.837-7.163-16-16-16H16C7.163 60 0 67.163 0 76v40c0 8.837 7.163 16 16 16zm0 160h416c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H16c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16zm0 160h416c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H16c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16z"></path>
  </svg>,
];

function Header({ onPress, collapseMenu, setCollapseMenu }) {
  const { t, i18n } = useTranslation();
  useEffect(() => window.scrollTo(0, 0));
  const { pathname } = useLocation();
  const dispatch = useDispatch();
  const handleUserSelectHeader = (e?: any) => {};
  const changeLanguage = (lang) => {
    i18n.changeLanguage(lang);
  };
  const handleLogout = (e) => {
    dispatch(logout());
  };
  const local = "fr";

  const user = JSON.parse(localStorage.getItem("user"));
  const items = [
    {
      label: <a href="/profile">Profile</a>,
      key: "0",
      icon: <UserOutlined />,
    },
    {
      label: (
        <a href="#" onClick={(e) => handleLogout(e)}>
          Logout
        </a>
      ),
      key: "1",
      icon: <LogoutOutlined />,
      danger: true,
    },
  ];

  const menu = JSON.parse(localStorage.getItem("menu"));
  const [current, setCurrent] = useState();

  useEffect(() => {
    if (
      menu?.find((item) => item.id === parseInt(localStorage.getItem("module")))
        ?.child_recursive_active?.find(
          (item) => window.location.pathname === item.link
        )
    ) {
      setCurrent(
        menu?.find((item) => item.id === parseInt(localStorage.getItem("module")))
          ?.child_recursive_active?.find(
            (item) => window.location.pathname === item.link
          )
          ?.id.toString()
      );
    }
  }, [window.location.pathname]);

  const handleSelectModule = (info) => {
    localStorage.setItem("module", info.key);
    window.dispatchEvent(new Event("storage"));
  };
  const handleCollapseMen = () => {
    localStorage.setItem("collapseMenu", (!collapseMenu).toString());
    setCollapseMenu(!collapseMenu);
  };

  useEffect(() => {}, [pathname]);

  useEffect(() => {
    handleUserSelectHeader();
  }, []);

  const breadcrumbItems = [
    {
      href: "/home",
      title: (
        <Space>
          <HomeFilled />
          Accueil
        </Space>
      ),
    },
  ];

  return (
    <>
      <Row gutter={[24, 12]} className="px-2">
        <Col span={20}>
          <Space size={"large"}>
            {collapseMenu ? (
              <DoubleRightOutlined
                className={"sidebar-collapse-toggler"}
                onClick={handleCollapseMen}
              />
            ) : (
              <DoubleLeftOutlined
                className={"sidebar-collapse-toggler"}
                onClick={handleCollapseMen}
              />
            )}
            <Breadcrumb items={breadcrumbItems}></Breadcrumb>
            {/* Commented code for FIPA */}
            <Menu
              disabledOverflow={true}
              theme="light"
              mode="horizontal"
              selectedKeys={[localStorage.getItem("module")]}
              onClick={handleSelectModule}
              items={menu
                ?.filter(
                  (item) =>
                    item.designation_fr[local] === "CRM" ||
                    item.designation_fr[local] === "Accueil"
                )
                ?.map((item, index) => ({
                  label: (
                    <NavLink to={item.link}>
                      <Badge
                        count={
                          index === 0 ? (
                            <span className="flex h-1 w-1">
                              <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-red-400 opacity-75"></span>
                              <span className="relative inline-flex rounded-full h-1 w-1 bg-red-500"></span>
                            </span>
                          ) : (
                            0
                          )
                        }
                        offset={[5, 0]}
                      >
                        {item.designation_fr[local]}
                      </Badge>
                    </NavLink>
                  ),
                  key: item.id.toString(),
                }))}
            />
          </Space>
        </Col>
        <Col span={4} className="header-control">
          <Space size={"middle"}>
            {pathname === "/accueil" || pathname === "/profile" ? null : (
              <Button
                type="link"
                className={collapseMenu ? "" : "sidebar-toggler"}
                onClick={() => onPress()}
              >
                {toggler}
              </Button>
            )}
            <Badge size="small" count={0}>
              <Dropdown menu={{ items }} trigger={["click"]} placement="bottom">
                <a
                  className="ant-dropdown-link"
                  onClick={(e) => e.preventDefault()}
                >
                  <Space size={"small"}>
                    <span style={{ color: "#8c8c8c", fontWeight: "600" }}>
                      {" "}
                      {user?.name ?? "User"}
                    </span>
                    {profile}
                  </Space>
                </a>
              </Dropdown>
            </Badge>
          </Space>
        </Col>
      </Row>
    </>
  );
}

export default Header;
