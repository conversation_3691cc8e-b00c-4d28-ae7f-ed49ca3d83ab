import { ModalForm } from "@ant-design/pro-components";
import {
  Card,
  Checkbox,
  Col,
  DatePicker,
  Divider,
  Form,
  Input,
  InputNumber,
  Radio,
  Row,
  Select,
} from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { pays, secteurs, type_contact } from "./data";

function CreateAutreSource({ open, onCancel, onSuccess }) {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  return (
    <ModalForm
      title={t("Autre source de contacts")}
      form={form}
      open={open}
      modalProps={{
        style: { top: 20 },
      }}
      //   onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.save"),
          resetText: t("common.actions.cancel"),
        },
      }}
    >
      <Card className="mt-2">
        <Row gutter={4}>
          <Col md={24} xs={24}>
            <Form.Item>
              <div style={{ display: "flex", alignItems: "center", gap: 24 }}>
                <label style={{ marginBottom: 0 }}>{t("Action")}</label>
                <Checkbox>Réalisée</Checkbox>
              </div>
            </Form.Item>
          </Col>

          <Col md={16} xs={24}>
            <Form.Item name="" label={t("Initiateur")}>
              <Select />
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item name="" label={t("Inclure")}>
              <Select />
            </Form.Item>
          </Col>
        </Row>
      </Card>
      <Divider>Données générales</Divider>
      <Card className="mt-2">
        <Row gutter={4}>
          <Col md={16} xs={24}>
            <Form.Item name="" label={t("Intitulé")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item name="" label={t("Type Contact")}>
              <Select
                options={type_contact.map((item) => ({
                  value: item.id,
                  label: item.type,
                }))}
              />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name="" label={t("Date Début")}>
              <DatePicker />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name="" label={t("Date Fin")}>
              <DatePicker />
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item name="" label={t("Pays")}>
              <Select
                options={pays.map((item) => ({
                  value: item.id,
                  label: item.nom,
                }))}
              />
            </Form.Item>
          </Col>
          <Col md={16} xs={24}>
            <Form.Item name="" label={t("Région")}>
              <Input></Input>
            </Form.Item>
          </Col>
          <Col md={24} xs={24}>
            <Form.Item name="" label={t("Lieu")}>
              <Input></Input>
            </Form.Item>
          </Col>
          <Col md={16} xs={24}>
            <Form.Item name="" label={t("Secteur")}>
              <Select
                options={secteurs.map((item) => ({
                  value: item.id,
                  label: item.secteur,
                }))}
              />
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item name="" label={t("Groupe")}>
              <Select></Select>
            </Form.Item>
          </Col>
        </Row>
      </Card>
    </ModalForm>
  );
}
export default CreateAutreSource;
