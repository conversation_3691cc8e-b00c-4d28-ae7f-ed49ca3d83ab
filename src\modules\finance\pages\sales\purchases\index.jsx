import { useState, useRef, useEffect } from "react";
import { ProTable } from "@ant-design/pro-components";
import {
  Button,
  Popconfirm,
  Space,
  Input,
  Tooltip,
  Typography,
  Card,
  Modal,
  Radio,
  Form,
} from "antd";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  SearchOutlined,
  CopyOutlined,
  SyncOutlined,
  FileSyncOutlined,
} from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { clients, departements, purchases, entreprises } from "./data";

import EditCmdForm from "./edit";
import CreateCmdForm from "./create";
import ShowCmdForm from "./view";

function Purchases() {
  const tableRef = useRef();
  const { t } = useTranslation();
  const [tableParams, setTableParams] = useState({}); // Paramètres de recherche et de filtrage
  const [isLoading, setIsLoading] = useState(false); // Indique si les données sont en cours de chargement
  const [data, setData] = useState(purchases); // Données affichées dans la table
  const [total, setTotal] = useState(data.length); // Nombre total de lignes affichées
  const [pageNumber, setPageNumber] = useState(1); // Numéro de page actuelle
  const [dataSource, setDataSource] = useState([]);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [dupModalVisible, setDupModalVisible] = useState(false);
  const [viewModalVisible, setViewModalVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState(null);
  const [duplicatedRecord, setDuplicatedRecord] = useState(null);
  const [viewingRecord, setViewingRecord] = useState(null);
  const [loadingDuplicate, setLoadingDuplicate] = useState({
    status: false,
    id: 0,
  });
  const [form] = Form.useForm();
  // Déclenchement de la recherche avec un délai (700ms)
  const handleSearch = (e) => {
    const searchValue = e.target.value; // Récupération de la valeur de recherche
    setTableParams({ ...tableParams, search: searchValue }); // Mise à jour des paramètres
  };

  //  const debouncedOnChange = useDebounce(handleSearch, 700);
  const handleDuplicate = (id) => {
    setLoadingDuplicate({ status: true, id: id });
    setTimeout(() => {
      setLoadingDuplicate({ status: false, id: id });
      setDupModalVisible(true);
    }, [1000]);
  };
  const formatter = new Intl.NumberFormat("fr-TN", {
    style: "currency",
    currency: "TND",
    minimumFractionDigits: 3,
  });
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [tansferLoading, setTansferLoading] = useState(false);
  const showModal = (id) => {
   
    setIsModalOpen(true);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };
  const columns = [
    {
      title: t("cmd_cols.num_cmd"),
      dataIndex: "num_cmd",
      key: "num_cmd",
      ellipsis: true,
      order: 1,
      render: (text) => {
        return (
          <Typography.Text ellipsis={{ tooltip: text }}>{text}</Typography.Text>
        );
      },
    },
    {
      title: t("sales.entreprise"),
      dataIndex: "entreprise_id",
      key: "entreprise_id",
      ellipsis: true,
      valueType: "select",
      valueEnum: () =>
        Object.fromEntries(
          entreprises.map((value) => [value.id, { text: value.nom }])
        ),
      order: 1,
      render: (text) => {
        return (
          <Typography.Text ellipsis={{ tooltip: text }}>{text}</Typography.Text>
        );
      },
    },
    {
      title: t("sales.departement"),
      dataIndex: "departement_id",
      key: "departement_id",
      ellipsis: true,
      valueType: "select",
      valueEnum: () =>
        Object.fromEntries(
          departements.map((value) => [value.id, { text: value.nom }])
        ),
      order: 1,
      render: (text) => {
        return (
          <Typography.Text ellipsis={{ tooltip: text }}>{text}</Typography.Text>
        );
      },
    },
    {
      title: t("sales.client"),
      dataIndex: "tier_id",
      key: "tier_id",
      ellipsis: true,
      valueType: "select",
      valueEnum: () =>
        Object.fromEntries(
          clients.map((value) => [value.id, { text: value.nom }])
        ),
      order: 1,
      render: (text) => {
        return (
          <Typography.Text ellipsis={{ tooltip: text }}>{text}</Typography.Text>
        );
      },
    },
    {
      title: t("cmd_cols.date_cmd"),
      dataIndex: "date_cmd",
      key: "date_cmd",
      ellipsis: true,
      valueType: "date",
      order: 1,
      render: (text) => {
        return (
          <Typography.Text ellipsis={{ tooltip: text }}>{text}</Typography.Text>
        );
      },
    },
 
    {
      title: t("sales.total_ttc"),
      dataIndex: "total_ttc",
      key: "total_ttc",
      search: false,
      render: (text) => {
        const amount = parseFloat(text);
        const formattedMoney = isNaN(amount)
          ? "0,000 DT"
          : formatter.format(amount);
        return <Typography.Text ellipsis>{formattedMoney}</Typography.Text>;
      },
    },
    {
      title: t("common.actions.title"),
      key: "action",
      align: "center",
      sorter: false,
      search: false,
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title={t("common.actions.view")}>
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => {
                setViewingRecord(record);
                setViewModalVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title={t("common.actions.edit")}>
            <Button
              type="link"
              style={{ color: "#f5b041" }}
              icon={<EditOutlined />}
              onClick={() => {
                setEditingRecord(record);
                setEditModalVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title={t("cmd.dup")}>
            <Popconfirm
              title={t("cmd_message.duplicate_question")}
              onConfirm={() => {
                setDuplicatedRecord(record) ;
                handleDuplicate(record.id);
              }}
              okText={t("sales.yes")}
              cancelText={t("sales.no")}
            >
              {loadingDuplicate.id == record.id && loadingDuplicate.status ? (
                <SyncOutlined spin />
              ) : (

                <Button
                type="link"
                style={{ color: "blue" }}
                icon={<CopyOutlined />}
           
              />
              )}
            </Popconfirm>
          </Tooltip>
          <Tooltip title={t("cmd.transfer")}>
          
              <Button
              type="link"
              style={{ color: "gray" }}
              icon={<FileSyncOutlined />}
              onClick={() => showModal(record.id)}
            />
          </Tooltip>
          <Tooltip title={t("common.actions.delete")}>
            <Popconfirm
              title={t("common.messages.confirm_delete")}
              onConfirm={() => {
                const newDataSource = dataSource.filter(
                  (item) => item.key !== record.key
                );
                setDataSource(newDataSource);
              }}
            >
              <Button
                type="link"
                style={{ color: "#ec7063" }}
                icon={<DeleteOutlined />}
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];
  const onFinish = (values) => {
   
    setTansferLoading(true);
    setTimeout(() => {setTansferLoading(false);
      setIsModalOpen(false);} , [1000])
      
  };
  return (
    <>
      <Card
        title={<Typography.Title level={4}>{t("cmd.list")}</Typography.Title>}
        extra={
          <Space>
            {/* Champ de recherche */}
            <Input
              size="large"
              placeholder={t("common.search")}
              suffix={<SearchOutlined style={{ color: "#bfbfbf" }} />}
              //    onChange={debouncedOnChange}
            />
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setCreateModalVisible(true)}
            >
              {t("cmd.add")}
            </Button>
          </Space>
        }
      >
        <ProTable
          actionRef={tableRef} // Référence pour accéder aux actions
          search={{
            searchText: t("common.filter"),
            labelWidth: "auto",
            defaultCollapsed: false,
          }}
          loading={isLoading}
          columns={columns}
          dataSource={data}
          rowKey="id"
          params={tableParams}
         scroll={{ x: "max-content" }}
          options={false}
          request={async (params) => {
            setIsLoading(true); // Démarrage du chargement
            console.log(params);
            setTimeout(() => {
              // Simule une requête serveur
              setIsLoading(false);
            }, 500);
            return {
              // Retourne les données
              data: data,
              success: true,
            };
          }}
          pagination={{
            showSizeChanger: true, // Permet de modifier la taille des pages
            defaultPageSize: 3, // Taille de page par défaut
            total: total, // Nombre total de lignes
            onChange: (page) => setPageNumber(page), // Fonction de changement de page
          }}
        />
        {createModalVisible && (
          <CreateCmdForm
            open={createModalVisible}
            onCancel={() => setCreateModalVisible(false)}
            onSuccess={(newDomain) => {
              setDataSource([...dataSource, newDomain]);
              setCreateModalVisible(false);
            }}
          />
        )}
        {editModalVisible && (
          <EditCmdForm
            open={editModalVisible}
            record={editingRecord}
            onCancel={() => {
              setEditModalVisible(false);
              setEditingRecord(null);
            }}
          />
        )}
        {viewModalVisible && (
          <ShowCmdForm
            open={viewModalVisible}
            record={viewingRecord}
            onCancel={() => {
              setViewModalVisible(false);
              setViewingRecord(null);
            }}
          />
        )}
        {dupModalVisible && (
          <EditCmdForm
          open={dupModalVisible}
          record={duplicatedRecord}
          onCancel={() => {
            setDupModalVisible(false);
            setDuplicatedRecord(null);
          }}
        />
        )}
      </Card>
      <Modal
        title={t('cmd.transfer')}
        open={isModalOpen}
        onCancel={handleCancel}
        footer={[
          <Button key="back" onClick={handleCancel}>
            Annuler
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={tansferLoading}
            onClick={() => form.submit()}
          >
            Confirmer
          </Button>,
        ]}
      >
       
        <Form
      onFinish={onFinish}
          layout="vertical"
         form={form}
        >
          <Form.Item
            label={t('sales.type_doc')}
            name="documentType"
            rules={[
              {
                required: true,
                message: t("sales.select_type") ,  //"Veuillez sélectionner un type de document!"
              },
            ]}
          >
            <Radio.Group>
              <Radio value={1}>{t('sales.bon_cmd')}</Radio>
              <Radio value={2} disabled={true}>
              {t('sales.bon_bl')}  
              </Radio>
              <Radio value={3} disabled={true}>
              {t('sales.facture')}  
              </Radio>
            </Radio.Group>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}
export default Purchases;
