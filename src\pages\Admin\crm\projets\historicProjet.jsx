import { MinusCircleOutlined, PlusCircleOutlined } from "@ant-design/icons";
import { ModalForm } from "@ant-design/pro-components";
import {
  Card,
  Col,
  DatePicker,
  Divider,
  Form,
  Input,
  InputNumber,
  Radio,
  Row,
  Select,
} from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";

function HistoricProjet({ open, onCancel, onSuccess }) {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const handleSubmit = (values) => {};
  const handleAdd = () => {
    const currentdata = form.getFieldValue("avancements") || [];
    const avancement = form.getFieldValue("avancement");

    if (avancement) {
      const key = Date.now();
      form.setFieldsValue({
        avancements: [
          ...currentdata,
          {
            key: key,
            avancement: avancement,
          },
        ],
      });
      form.resetFields(["avancement"]);
    } else {
      message.error(t("Merci de bien vouloir remplir tous les champs."));
    }
  };

  return (
    <ModalForm
      title={t("Avancement du projet")}
      width="60%"
      form={form}
      open={open}
      modalProps={{
        style: { top: 20 },
      }}
      onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.save"),
          resetText: t("common.actions.cancel"),
        },
      }}
    >
      <Card className="mt-2">
        <Row gutter={16} style={{ marginBottom: "10px" }}>
          <Col span={20}></Col>
          <Col span={1}></Col>
        </Row>
        <Form.List name="avancements">
          {(fields, { remove }) => (
            <>
              {fields.map((field, index) => (
                <Row gutter={16} align="middle" key={field.key}>
                  <Col span={2}></Col>
                  <Col span={20}>
                    <Form.Item name={[index, "avancement"]} rules={[]}>
                      <Input.TextArea allowClear={true} />
                    </Form.Item>
                  </Col>

                  <Col span={1}>
                    <Form.Item>
                      <MinusCircleOutlined
                        style={{
                          color: "red",
                          fontSize: "18px",
                          cursor: "pointer",
                        }}
                        onClick={() => remove(index)}
                      />
                    </Form.Item>
                  </Col>
                </Row>
              ))}
            </>
          )}
        </Form.List>

        <Row gutter={16} align="middle" style={{ marginBottom: "5px" }}>
          <Col span={2}></Col>

          <Col span={20}>
            <Form.Item name={`avancement`} rules={[]}>
              <Input.TextArea allowClear={true} />
            </Form.Item>
          </Col>

          <Col span={1}>
            <Form.Item>
              <PlusCircleOutlined
                style={{
                  color: "green",
                  fontSize: "18px",
                  cursor: "pointer",
                }}
                onClick={() => handleAdd()}
              />
            </Form.Item>
          </Col>
        </Row>
      </Card>
    </ModalForm>
  );
}
export default HistoricProjet;
