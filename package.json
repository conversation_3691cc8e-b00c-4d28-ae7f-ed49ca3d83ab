{"name": "my-react-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/charts": "^2.2.6", "@ant-design/icons": "^5.5.2", "@ant-design/plots": "^1.2.6", "@ant-design/pro-components": "^2.8.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fullcalendar/core": "^6.1.17", "@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/list": "^6.1.17", "@fullcalendar/react": "^6.1.17", "@fullcalendar/resource-timeline": "^6.1.17", "@fullcalendar/timegrid": "^6.1.17", "@mui/icons-material": "^6.4.4", "@mui/material": "^6.4.4", "@reduxjs/toolkit": "^2.5.1", "@tinymce/tinymce-react": "^5.1.1", "antd": "^5.22.2", "apexcharts": "^4.4.0", "axios": "^1.7.8", "chart.js": "^4.4.8", "dayjs": "^1.11.13", "i18next": "^24.0.2", "i18next-browser-languagedetector": "^8.0.0", "i18next-http-backend": "^3.0.1", "lucide-react": "^0.473.0", "moment": "^2.30.1", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-calendar": "^5.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-i18next": "^15.1.2", "react-quill": "^2.0.0", "react-redux": "^9.1.2", "react-router-dom": "^7.0.1", "react-to-print": "^3.0.5", "recharts": "^2.15.1", "redux": "^5.0.1", "redux-thunk": "^3.1.0", "sonner": "^1.7.2", "uuid": "^11.0.5", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.13.0", "@types/node": "^22.13.1", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.3", "autoprefixer": "^10.4.20", "eslint": "^9.13.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^15.11.0", "less": "^4.2.2", "postcss": "^8.4.49", "tailwindcss": "^3.4.15", "vite": "^5.4.10"}, "engines": {"node": "^20.0.0 || ^22.0.0"}}