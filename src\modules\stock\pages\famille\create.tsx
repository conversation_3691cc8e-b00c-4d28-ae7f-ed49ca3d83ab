import { ModalForm } from "@ant-design/pro-components";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { Form } from "antd";
import FamilleForm from "../../components/FamilleForm";

function CreateFamilleForm({ open, setOpen, tableRef, tvas,}){
    const {t} = useTranslation();
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
  
  
    return(
    <ModalForm
      title={t("familles.add")}
      open={open}
    //   onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
        setOpen();
        }
      }}
      form={form}
      initialValues={{allow_negative_stock:1}}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.save"),
          resetText: t("common.actions.cancel"),
        },
      }}
    >
      <FamilleForm tvas={tvas}/>
    </ModalForm>
    )
}
export default CreateFamilleForm;