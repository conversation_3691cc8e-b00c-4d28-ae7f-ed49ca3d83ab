import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";

export const showLoader = createAsyncThunk("loader/show", () => {});
export const hideLoader = createAsyncThunk("loader/hide", () => {});

const loadingSlice = createSlice({
  name: "loader",
  initialState: {
    isLoading: false,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(showLoader.fulfilled, (state) => {
        state.isLoading = true;
      })
      .addCase(hideLoader.fulfilled, (state) => {
        state.isLoading = false;
      })
  },
});

export default loadingSlice.reducer;
