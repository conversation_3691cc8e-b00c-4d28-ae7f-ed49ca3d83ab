import React, { useState } from 'react';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import { Badge, Modal, Button, Form, Input, Select, DatePicker, message } from 'antd';
import { getAllCalendarItems, updateEvent, createEvent } from './utils/data';
import { formatDateTime } from './utils/helpers';
import dayjs from 'dayjs';
import 'dayjs/locale/fr';

dayjs.locale('fr');

const { Option } = Select;
const { RangePicker } = DatePicker;

export default function CalendarView() {
  const [events, setEvents] = useState(() => getAllCalendarItems());
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [form] = Form.useForm();

  const eventContent = ({ event }) => (
    <div className="fc-event-content">
      <Badge color={event.extendedProps.color} />
      <span className="fc-event-title">{event.title}</span>
      <div className="fc-event-time">
        {dayjs(event.start).format('HH:mm')}
      </div>
    </div>
  );

  const handleCreate = (values) => {
    const newEvent = {
      ...values,
      id: Math.max(...events.map(e => e.id)) + 1,
      color: '#1890ff',
      status: 'planned'
    };
    createEvent(newEvent);
    setEvents(getAllCalendarItems());
    setCreateModalVisible(false);
    message.success('Événement créé avec succès');
  };

  const handleEdit = (values) => {
    const updatedEvent = {
      ...selectedEvent,
      ...values,
      id: selectedEvent.id
    };
    updateEvent(updatedEvent);
    setEvents(getAllCalendarItems());
    setEditModalVisible(false);
    setSelectedEvent(null);
    message.success('Événement mis à jour avec succès');
  };

  const handleDateSelect = (selectInfo) => {
    form.setFieldsValue({
      start: selectInfo.start,
      end: selectInfo.end
    });
    setCreateModalVisible(true);
  };

  return (
    <div className="calendar-container">
      <div style={{ marginBottom: 16 }}>
        <Button 
          type="primary" 
          onClick={() => setCreateModalVisible(true)}
        >
          Créer un événement
        </Button>
      </div>

      <FullCalendar
        plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
        initialView="dayGridMonth"
        headerToolbar={{
          left: 'prev,next today',
          center: 'title',
          right: 'dayGridMonth,timeGridWeek,timeGridDay'
        }}
        events={events}
        eventContent={eventContent}
        eventClick={(info) => {
          setSelectedEvent(info.event);
          form.setFieldsValue({
            title: info.event.title,
            start: dayjs(info.event.start),
            end: dayjs(info.event.end),
            status: info.event.extendedProps.status
          });
        }}
        selectable={true}
        select={handleDateSelect}
        editable={true}
        droppable={true}
        locale="fr"
        height="auto"
      />

      {/* Create Event Modal */}
      <Modal
        title="Créer un nouvel événement"
        visible={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        onOk={() => form.submit()}
      >
        <Form form={form} onFinish={handleCreate} layout="vertical">
          <Form.Item name="title" label="Titre" rules={[{ required: true }]}>
            <Input />
          </Form.Item>

          <Form.Item name="time" label="Période" rules={[{ required: true }]}>
            <RangePicker showTime format="YYYY-MM-DD HH:mm" />
          </Form.Item>

          <Form.Item name="status" label="Statut" initialValue="planned">
            <Select>
              <Option value="planned">Planifié</Option>
              <Option value="in-progress">En cours</Option>
              <Option value="completed">Terminé</Option>
            </Select>
          </Form.Item>

          <Form.Item name="location" label="Lieu">
            <Input />
          </Form.Item>
        </Form>
      </Modal>

      {/* Edit Event Modal */}
      <Modal
        title="Modifier l'événement"
        visible={!!selectedEvent}
        onCancel={() => setSelectedEvent(null)}
        footer={[
          <Button key="delete" danger onClick={() => {
            setSelectedEvent(null);
          }}>
            Supprimer
          </Button>,
          <Button key="submit" type="primary" onClick={() => form.submit()}>
            Enregistrer
          </Button>
        ]}
      >
        <Form form={form} onFinish={handleEdit} layout="vertical">
          <Form.Item name="title" label="Titre" rules={[{ required: true }]}>
            <Input />
          </Form.Item>

          <Form.Item name="time" label="Période" rules={[{ required: true }]}>
            <RangePicker showTime format="YYYY-MM-DD HH:mm" />
          </Form.Item>

          <Form.Item name="status" label="Statut">
            <Select>
              <Option value="planned">Planifié</Option>
              <Option value="in-progress">En cours</Option>
              <Option value="completed">Terminé</Option>
            </Select>
          </Form.Item>

          <Form.Item name="location" label="Lieu">
            <Input />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
}