import { ProForm, ProFormText, ProFormSelect, ProFormDatePicker, ProFormTextArea } from "@ant-design/pro-form";
import { message, Button, Space } from "antd";
import dayjs from "dayjs";
import "./CreateActivity.css"; // Fichier CSS pour le positionnement des boutons

const CreateActivity = ({ onCancel, setDataSource }) => {
  const taskTypes = [
    { label: "Appel", value: "call" },
    { label: "Réunion", value: "meeting" },
    { label: "Email", value: "email" },
    { label: "Note", value: "note" },
    { label: "À faire", value: "todo" },
  ];

  const handleSubmit = async (values) => {
    try {
      const newKey = Date.now();
      const formattedStartDate = values.startDate ? dayjs(values.startDate).format("DD/MM/YYYY HH:mm") : null;
      const formattedDueDate = values.dueDate ? dayjs(values.dueDate).format("DD/MM/YYYY HH:mm") : null;
      
      const newActivity = {
        key: newKey,
        type: values.type,
        title: values.title,
        description: values.description,
        startDate: formattedStartDate,
        dueDate: formattedDueDate,
        relatedTo: values.relatedTo,
        owners: values.owners || [],
        status: "planned",
        notes: values.notes || "",
      };

      const updatedData = [...datas, newActivity];
      setDataSource(updatedData);
      message.success("Tâche créée avec succès");
      
      if (values.createFollowUp) {
        // Logique pour rediriger vers la création d'une nouvelle tâche
      } else {
        onCancel();
      }
    } catch (error) {
      console.error(error);
      message.error("Erreur lors de la création de la tâche");
    }
  };

  return (
    <div className="task-creation-form">
      <ProForm
        onFinish={handleSubmit}
        submitter={{
          render: (_, doms) => (
            <div className="form-footer-actions">
              <Space>
                <Button key="cancel" onClick={onCancel}>
                  Annuler
                </Button>
                <Button 
                  key="completeAndFollowUp" 
                  type="primary" 
                  onClick={() => {
                    _.form?.setFieldsValue({ createFollowUp: true });
                    _.form?.submit();
                  }}
                >
                  Enregistrer et créer un suivi
                </Button>
                <Button key="complete" type="primary" onClick={() => _.form?.submit()}>
                  Enregistrer
                </Button>
              </Space>
            </div>
          ),
        }}
      >
        <ProFormSelect
          name="type"
          label="Type de tâche"
          options={taskTypes}
          rules={[{ required: true, message: "Veuillez sélectionner un type de tâche" }]}
          placeholder="Sélectionnez un type"
        />

        <ProFormText
          name="title"
          label="Titre"
          placeholder="Entrez le titre de la tâche"
          rules={[{ required: true, message: "Veuillez saisir un titre" }]}
        />

        <ProFormDatePicker
          name="startDate"
          label="Date de début"
          showTime
          format="DD/MM/YYYY HH:mm"
          placeholder="Sélectionnez la date de début"
        />

        <ProFormDatePicker
          name="dueDate"
          label="Date d'échéance"
          showTime
          format="DD/MM/YYYY HH:mm"
          placeholder="Sélectionnez la date d'échéance"
        />

        <ProFormTextArea
          name="description"
          label="Description"
          placeholder="Décrivez la tâche"
        />

        <ProFormText
          name="relatedTo"
          label="Lié à"
          placeholder="Recherchez une entreprise, contact ou lead"
        />

        <ProFormTextArea
          name="notes"
          label="Notes"
          placeholder="Ajoutez des notes supplémentaires"
        />

        <ProFormText name="createFollowUp" hidden />
      </ProForm>
    </div>
  );
};

export default CreateActivity;