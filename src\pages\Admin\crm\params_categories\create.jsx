import React from 'react';
import { Modal, Form, Input, Select, Button, message } from 'antd';

const { Option } = Select;

const CreateCategoryModal = ({ visible, onClose, onSuccess }) => {
  const [form] = Form.useForm();

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      message.success('Catégorie créée avec succès !');
      onSuccess();
      form.resetFields();
      onClose();
    } catch (error) {
      console.error('Erreur:', error);
    }
  };

  return (
    <Modal
      title="Créer une nouvelle catégorie"
      visible={visible}
      onCancel={onClose}
      footer={null}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
      >
        <Form.Item
          name="name"
          label="Nom"
          rules={[{ required: true, message: 'Ce champ est obligatoire' }]}
        >
          <Input placeholder="Ex: Clients" />
        </Form.Item>
        <Form.Item
          name="type"
          label="Type"
          rules={[{ required: true, message: 'Sélectionnez un type' }]}
        >
          <Select placeholder="Sélectionnez un type">
            <Option value="email">Email</Option>
            <Option value="document">Document</Option>
          </Select>
        </Form.Item>
        <Form.Item>
          <div style={{ textAlign: 'right' }}>
            <Button style={{ marginRight: 8 }} onClick={onClose}>
              Annuler
            </Button>
            <Button type="primary" htmlType="submit">
              Enregistrer
            </Button>
          </div>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default CreateCategoryModal;