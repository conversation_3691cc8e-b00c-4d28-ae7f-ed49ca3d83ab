import React, { useState } from 'react';
import { Form, Input, Button, Select, DatePicker, Checkbox, Row, Col, Collapse, Divider } from 'antd';
import { DeleteOutlined } from '@ant-design/icons';

const { Option } = Select;
const { TextArea } = Input;

const CompetenceForm = ({ onCreate }) => {
  const [form] = Form.useForm();
  const [positions, setPositions] = useState([{ id: Date.now() }]);
  const [activeKey, setActiveKey] = useState([]);

  const handleFinish = (values) => {
    const formattedValues = {
      ...values,
      positionHistory: positions.map((pos, index) => ({
        startDate: values[`startDate_${index}`],
        endDate: values[`endDate_${index}`],
        company: values[`company_${index}`],
        position: values[`position_${index}`]
      }))
    };
    onCreate(formattedValues);
    form.resetFields();
    setPositions([{ id: Date.now() }]);
  };

  const addPosition = () => {
    const newPosition = { id: Date.now() };
    setPositions([...positions, newPosition]);
    setActiveKey([newPosition.id.toString()]);
  };

  const removePosition = (idToRemove) => {
    setPositions(positions.filter(pos => pos.id !== idToRemove));
  };


  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleFinish}
    >
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item label="Prénom" name="firstName">
            <Input placeholder="Prénom de la compétence tunisienne" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Nom" name="lastName" rules={[{ required: true }]}>
            <Input placeholder="Nom de la compétence tunisienne" />
          </Form.Item>
        </Col>
      </Row>

      <Form.Item label="Adresse" name="address" rules={[{ required: true }]}>
        <Input placeholder="Adresse de la compétence tunisienne" />
      </Form.Item>

      <Row gutter={16}>
        <Col span={8}>
          <Form.Item label="Téléphone" name="phone" rules={[{ required: true }]}>
            <Input placeholder="Numéro de téléphone" />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item label="Fax" name="fax" rules={[{ required: true }]}>
            <Input placeholder="Numéro de fax" />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item label="Email" name="email" rules={[{ required: true, type: 'email' }]}>
            <Input placeholder="Adresse email" />
          </Form.Item>
        </Col>
      </Row>

      <Form.Item label="Âge" name="age" rules={[{ required: true }]}>
        <Input placeholder="Âge approximatif" type="number" />
      </Form.Item>

      <Form.Item label="Initiateur" name="initiator" rules={[{ required: true }]}>
        <Select placeholder="Sélectionner l'initiateur">
          <Option value="representative">Représentant à l'étranger</Option>
          <Option value="sector_manager">Cadre sectoriel</Option>
        </Select>
      </Form.Item>

      <Form.Item label="Date de contact" name="contactDate" rules={[{ required: true }]}>
        <DatePicker style={{ width: '100%' }} />
      </Form.Item>

      <Form.Item label="Poste actuel" name="currentPosition">
        <Input placeholder="Poste occupé actuellement" />
      </Form.Item>

      <Form.Item label="Diplôme" name="diploma">
        <Input placeholder="Diplôme le plus important" />
      </Form.Item>

      <Form.Item label="Société/Institution" name="company">
        <Input placeholder="Raison sociale ou nom de l'institution" />
      </Form.Item>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item label="Pays" name="country" rules={[{ required: true }]}>
            <Select placeholder="Sélectionner le pays">
              <Option value="france">France</Option>
              <Option value="germany">Allemagne</Option>
              <Option value="canada">Canada</Option>
              {/* Add more countries as needed */}
            </Select>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Lieu" name="location">
            <Input placeholder="Ville ou région" />
          </Form.Item>
        </Col>
      </Row>

      <Form.Item label="Secteur" name="sector" rules={[{ required: true }]}>
        <Select placeholder="Sélectionner le secteur">
          <Option value="tic">TIC</Option>
          <Option value="health">Santé</Option>
          <Option value="education">Éducation</Option>
          <Option value="engineering">Ingénierie</Option>
          {/* Add more sectors as needed */}
        </Select>
      </Form.Item>

      <Divider>Historique des postes occupés</Divider>
      
      <div style={{ display: 'flex', justifyContent: 'flex-end', marginBottom: 16 }}>
        <Button onClick={addPosition} type="primary">+</Button>
      </div>

      {positions.map((position, index) => (
        <div key={position.id} style={{ display: 'flex', alignItems: 'center', gap: '10px', width: '100%' }}>
          <Collapse
            style={{ flex: 1, marginBottom: '16px' }}
            activeKey={activeKey}
            onChange={(keys) => setActiveKey(keys)}
            items={[
              {
                key: position.id.toString(),
                label: `Poste ${index + 1}`,
                children: (
                  <>
                    <Row gutter={16}>
                      <Col span={12}>
                        <Form.Item
                          name={`startDate_${index}`}
                          label="Date début"
                          rules={[{ required: true }]}
                        >
                          <DatePicker style={{ width: '100%' }} />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          name={`endDate_${index}`}
                          label="Date fin"
                        >
                          <DatePicker style={{ width: '100%' }} />
                        </Form.Item>
                      </Col>
                    </Row>
                    <Form.Item
                      name={`position_${index}`}
                      label="Poste"
                      rules={[{ required: true }]}
                    >
                      <Input placeholder="Poste occupé" />
                    </Form.Item>
                    <Form.Item
                      name={`company_${index}`}
                      label="Société/Institution"
                      valuePropName="checked"
                    >
                      <Checkbox>Société/Institution</Checkbox>
                    </Form.Item>
                  </>
                )
              }
            ]}
          />
          {index !== 0 && (
            <Button
              onClick={() => removePosition(position.id)}
              danger
              icon={<DeleteOutlined />}
            />
          )}
        </div>
      ))}

      <Form.Item>
        <div style={{ textAlign: 'right' }}>
          <Button type="primary" htmlType="submit">
            Enregistrer
          </Button>
        </div>
      </Form.Item>
    </Form>
  );
};


export default CompetenceForm;