import React from 'react';
import { Descriptions, Tag } from 'antd';
import moment from 'moment';

const InteractionView = ({ interaction }) => {
  const formatDate = (date) => moment(date).format('DD/MM/YYYY');

  return (
    <Descriptions title="Détails de l'Interaction" bordered column={1}>
      <Descriptions.Item label="Type">
        <Tag color="blue">{interaction.type}</Tag>
      </Descriptions.Item>
      <Descriptions.Item label="Date">{formatDate(interaction.date)}</Descriptions.Item>
      <Descriptions.Item label="Profil Diaspora">{interaction.diasporaProfile}</Descriptions.Item>
      <Descriptions.Item label="Objet">{interaction.object}</Descriptions.Item>
      <Descriptions.Item label="Notes">{interaction.notes}</Descriptions.Item>
      <Descriptions.Item label="Prochaines étapes">{interaction.nextSteps}</Descriptions.Item>
      <Descriptions.Item label="Créé le">{formatDate(interaction.createdAt)}</Descriptions.Item>
      <Descriptions.Item label="Mis à jour le">{formatDate(interaction.updatedAt)}</Descriptions.Item>
    </Descriptions>
  );
};

export default InteractionView;