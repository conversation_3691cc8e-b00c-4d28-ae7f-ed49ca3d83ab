import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { logout } from "@features/auth/authSlice";
import { default as api } from "@/apis/axiosInterceptor";

export const addPermissionToRole: any = createAsyncThunk(
  "role",
  async (data, thunkAPI) => {
    try {
      let url = `/permissions/addPermissionToRole`;
      const resp = await api.post(url,data);
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 401) {
          thunkAPI.dispatch(logout());
        }else  if (status === 403) {
          window.location.href = '/unauthorized';
        }
        return thunkAPI.rejectWithValue(data.message);
      }  else {
        // Erreur lors de la configuration de la requête
        return thunkAPI.rejectWithValue("Une erreur s'est produite.");
      }
    }
  }
);

export const getPermissions: any = createAsyncThunk(
  "permission",
  async (data, thunkAPI) => {
    try {
      let url = `/permissions/all`;
      const resp = await api.get(url);
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 401) {
          thunkAPI.dispatch(logout());
        }else  if (status === 403) {
          window.location.href = '/unauthorized';
        }
        return thunkAPI.rejectWithValue(data.message);
      }  else {
        // Erreur lors de la configuration de la requête
        return thunkAPI.rejectWithValue("Une erreur s'est produite.");
      }
    }
  }
);

export const addPermission: any = createAsyncThunk(
  "permission",
  async (data, thunkAPI) => {
    try {
      let url = `/permissions`;
      const resp = await api.post(url,data);
      // update state to add new permission
      thunkAPI.dispatch(getPermissions());
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 401) {
          thunkAPI.dispatch(logout());
        }else  if (status === 403) {
          window.location.href = '/unauthorized';
        }
        return thunkAPI.rejectWithValue(data.message);
      }  else {
        // Erreur lors de la configuration de la requête
        return thunkAPI.rejectWithValue("Une erreur s'est produite.");
      }
    }
  }
);

export const updatePermission: any = createAsyncThunk(
  "permission",
  async (data:{id: number, name:string, groupe:string}, thunkAPI) => {
    try {
      let url = `/permissions/${data.id}`;
      const resp = await api.put(url, data);
      // update state to update permission
      thunkAPI.dispatch(getPermissions());
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 401) {
          thunkAPI.dispatch(logout());
        }else  if (status === 403) {
          window.location.href = '/unauthorized';
        }
        return thunkAPI.rejectWithValue(data.message);
      }  else {
        // Erreur lors de la configuration de la requête
        return thunkAPI.rejectWithValue("Une erreur s'est produite.");
      }
    }
  }
);

export const deletePermission: any = createAsyncThunk(
  "permission",
  async (data:{id: number}, thunkAPI) => {
    try {
      let url = `/permissions/${data.id}`;
      const resp = await api.delete(url);
      thunkAPI.dispatch(getPermissions());
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 401) {
          thunkAPI.dispatch(logout());
        }else  if (status === 403) {
          window.location.href = '/unauthorized';
        }
        return thunkAPI.rejectWithValue(data.message);
      }  else {
        // Erreur lors de la configuration de la requête
        return thunkAPI.rejectWithValue("Une erreur s'est produite.");
      }
    }
  }
);

const permissionSlice = createSlice({
  name: "permission",
  initialState: {
    loading: false,
    error: null,
    data: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getPermissions.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.data = null;
      })
      .addCase(getPermissions.fulfilled, (state, action) => {
        state.data = action.payload.data;
        state.loading = false;
        state.error = null;
      })
      .addCase(getPermissions.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        state.data = null;
      })
  },
});


export default permissionSlice.reducer;
