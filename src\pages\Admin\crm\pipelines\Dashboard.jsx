import React, { useState, useEffect } from "react";
import { Row, Col, Card, Statistic, Typography, Progress } from "antd";
import { 
  UserOutlined, 
  DollarCircleOutlined, 
  ProfileOutlined, 
  FundOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  CalendarOutlined,
  TeamOutlined
} from "@ant-design/icons";
import {datas} from "./datadash";
import { TaskTable } from "./ProgressBarSales";
import OpportunityAftercare from "./TrackingAfterCare";
import RevenueChart from "./RevenuMentionel";
import StatOOportunites from "./StatOpportunites";

const { Title, Text } = Typography;

const Dashboard = () => {
  const [stats, setStats] = useState({});
  const [leads, setLeads] = useState([]);
  const [opportunities, setOpportunities] = useState([]);
  const [tasks, setTasks] = useState([]);
  const [revenueData, setRevenueData] = useState([]);

  useEffect(() => {
    setStats(datas.kpi);
    setLeads(datas.leads);
    setOpportunities(datas.opportunities);
    setTasks(datas.tasks);
    setRevenueData(datas.revenue_per_month);
  }, []);

  const kpiCards = [
    {
      title: "Clients",
      value: stats?.total_clients,
      icon: <UserOutlined className="kpi-icon" />,
      color: "#1890ff",
      bgColor: "rgba(24, 144, 255, 0.1)"
    },
    {
      title: "Prospects",
      value: stats?.total_prospects,
      icon: <TeamOutlined className="kpi-icon" />,
      color: "#13c2c2",
      bgColor: "rgba(19, 194, 194, 0.1)"
    },
    {
      title: "Opportunités",
      value: stats?.total_opportunities,
      icon: <FundOutlined className="kpi-icon" />,
      color: "#722ed1",
      bgColor: "rgba(114, 46, 209, 0.1)"
    },
    {
      title: "Revenu Total",
      value: `$${stats?.total_revenue}`,
      icon: <DollarCircleOutlined className="kpi-icon" />,
      color: "#52c41a",
      bgColor: "rgba(82, 196, 26, 0.1)"
    },
  ];

  return (
    <div className="dashboard-container">
      {/* Top Section */}
      <div className="dashboard-top-section-wrapper">
        <div className="dashboard-top-section-bg"></div>
        <div className="dashboard-top-section-content">
          {/* Header Section */}
          <div className="dashboard-header">
            <h1 level={2} style={{ marginBottom: 0 }}>Tableau de Bord CRM</h1>
          </div>
          {/* KPI Cards Section */}
          <Row gutter={[16, 16]} className="kpi-row">
            {kpiCards.map((card, index) => (
              <Col xs={24} sm={12} md={6} key={index}>
                <Card 
                  className="kpi-card"
                  style={{ backgroundColor: "#fff" }}
                  bordered={false}
                >
                  <div className="kpi-content">
                    <div className="kpi-icon-container" style={{ backgroundColor: card.bgColor, color: card.color }}>
                      {card.icon}
                    </div>
                    <div className="kpi-text">
                      <h4 className="kpi-title">{card.title}</h4>
                      <Statistic 
                        value={card.value} 
                        className="kpi-value"
                        valueStyle={{ color: card.color, fontSize: '24px' }}
                      />
                    </div>
                  </div>
                </Card>
              </Col>
            ))}
          </Row>
        </div>
      </div>
      {/* Bottom Section */}
      <Row gutter={[16, 16]} className="section-row">
        <Col xs={24} lg={24}>
          <Card 
            title="Statistiques des Opportunités" 
            className="dashboard-card"
          >
            <StatOOportunites />
          </Card>
        </Col>
      </Row>

      {/* Charts Section */}
      <Row gutter={[16, 16]} className="section-row">
        <Col xs={24} lg={12}>
          <Card 
            title="Suivi des Opportunités" 
            className="dashboard-card"
            extra={<Text type="secondary">30 derniers jours</Text>}
          >
            <OpportunityAftercare />
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          <Card 
            title="Revenus Mensuels" 
            className="dashboard-card"
            extra={<Text type="secondary">2023</Text>}
          >
            <RevenueChart />
          </Card>
        </Col>
      </Row>

      {/* Bottom Section */}
      <Row gutter={[16, 16]} className="section-row">
        <Col xs={24} lg={24}>
          <Card 
            title="Tâches en Cours" 
            className="dashboard-card"
          >
            <TaskTable />
            <div className="task-summary">
              <div className="task-summary-item">
                <Text strong>Terminées:</Text>
                <Text strong style={{ color: '#52c41a' }}>24</Text>
              </div>
              <div className="task-summary-item">
                <Text strong>En retard:</Text>
                <Text strong style={{ color: '#f5222d' }}>3</Text>
              </div>
              <div className="task-summary-item">
                <Text strong>En cours:</Text>
                <Text strong style={{ color: '#1890ff' }}>12</Text>
              </div>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;