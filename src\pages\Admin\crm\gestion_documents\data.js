import dayjs from 'dayjs';

export const documents = [
  {
    id: 1,
    name: "Client FAQ",
    summary: "Sales call questions",
    category: "Marketing",
    format: "txt",
    file: "client-faq.txt",
    createdAt: dayjs().subtract(2, 'day').toISOString(),
    updatedAt: dayjs().subtract(1, 'day').toISOString()
  },
  {
    id: 2,
    name: "Employee Contract",
    summary: "Contract for new employees",
    category: "Legal",
    format: "pdf",
    file: "employee-contract.pdf",
    createdAt: dayjs().subtract(5, 'day').toISOString(),
    updatedAt: dayjs().subtract(3, 'day').toISOString()
  },
];

export const categories = ["Marketing", "Legal", "Finance", "Technical"];
export const formats = ["txt", "pdf", "csv", "xlsx", "docx"];