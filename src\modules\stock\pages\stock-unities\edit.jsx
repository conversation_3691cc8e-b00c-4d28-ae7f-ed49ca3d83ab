import { useTranslation } from "react-i18next";
import StockUnitiesForm from "../../components/StockUnitiesForm";
import { ModalForm } from "@ant-design/pro-components";
import { useState } from "react";

const EditStockUnityModal = ({ open, onCancel, stockUnity }) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (values) => {
    setLoading(true);
    try {
      const submissionData = {
        ...values,
      };
      await new Promise((resolve) => setTimeout(resolve, 2000));

      message.success("Emplacement ajouté avec succès !");
    } catch (error) {
      console.log(error);
      message.error("Erreur lors de l'ajout de l'emplacement !");
    } finally {
      setLoading(false);
      onCancel();
    }
  };

  return (
    <ModalForm
      width={800}
      title={t("stock-unities.edit")}
      open={open}
      onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.edit"),
          resetText: t("common.actions.cancel"),
        },
        submitButtonProps: {
          loading,
        },
      }}
      modalProps={{
        destroyOnClose: true,
      }}
    >
      <StockUnitiesForm disabled={false} stockUnity={stockUnity} />
    </ModalForm>
  );
};

export default EditStockUnityModal;
