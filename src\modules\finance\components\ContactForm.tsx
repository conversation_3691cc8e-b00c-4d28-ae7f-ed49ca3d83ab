import { MinusCircleOutlined, PlusOutlined } from "@ant-design/icons";
import { Input, Form, Row, Col, Select, Button, Space } from "antd";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { citiesProvincesJSON } from "./citiesProvinces";

const ContactForm = ({ disabled, contacts }) => {
  const { t } = useTranslation();
  const [countries, setCountries] = useState([]);
  const [form] = Form.useForm();
  const [cities, setCities] = useState([]);
  const [selectedCountry, setSelectedCountry] = useState("");

  useEffect(() => {
    if (contacts) {
      form.setFieldsValue(contacts);
    }
  }, [contacts]);

  // useEffect(() => {
  //   fetch("https://restcountries.com/v3.1/all?fields=name")
  //     .then((response) => response.json())
  //     .then((data) => {
  //       setCountries(
  //         data.map((c, index) => ({ value: index, label: c.name.common }))
  //       );
  //     })
  //     .catch((error) => {
  //       console.error("Error fetching countries:", error);
  //     });
  // }, []);

  const filterOption = (input, option) =>
    (option?.label ?? "").toLowerCase().includes(input.toLowerCase());

  const filterSort = (optionA, optionB) =>
    (optionA?.label ?? "")
      .toLowerCase()
      .localeCompare((optionB?.label ?? "").toLowerCase());

  useEffect(() => {
    const countrySet = new Set(citiesProvincesJSON.map((item) => item.country));

    const countryList = Array.from(countrySet)
      .map((country) => ({ value: country, label: country }))
      .sort((a, b) => a.label.localeCompare(b.label));

    setCountries(countryList);
  }, []);

  const handleCountryChange = (value) => {
    setSelectedCountry(value);

    const filteredCities = citiesProvincesJSON
      .filter((item) => item.country === value)
      .map((item) => item.city);

    setCities(filteredCities);
  };

  return (
    <>
      <Form.List name="contacts">
        {(fields, { add, remove }) => (
          <>
            {fields.map((field) => (
              <div
                key={field.key}
                style={{ border: "1px dashed #23537C", borderRadius: "6px" }}
                className="px-4 mt-3 mb-6"
              >
                <h3
                  style={{
                    textAlign: "start",
                    color: "#23537C",
                    marginBottom: "3px",
                    marginTop: "3px",
                    padding: "3px",
                  }}
                >
                  {`Contact ${field.key + 1}`}
                </h3>
                <Space
                  style={{
                    display: "flex",
                    justifyContent: "space-evenly",
                  }}
                  align="baseline"
                >
                  <Row gutter={16}>
                    <Col md={8} xs={24}>
                      <Form.Item
                        name={[field.name, "firstName"]}
                        label={t("vente.contact.firstName")}
                        validateStatus={""}
                        help={""}
                      >
                        <Input
                          placeholder={t("vente.contact.firstName_placeholder")}
                        />
                      </Form.Item>
                    </Col>
                    <Col md={8} xs={24}>
                      <Form.Item
                        name={[field.name, "lastName"]}
                        label={t("vente.contact.lastName")}
                        validateStatus={""}
                        help={""}
                      >
                        <Input
                          placeholder={t("vente.contact.lastName_placeholder")}
                        />
                      </Form.Item>
                    </Col>
                    <Col md={8} xs={24}>
                      <Form.Item
                        name={[field.name, "civilite"]}
                        label={t("vente.contact.civilite")}
                        rules={[
                          {
                            required: true,
                            message: "Veuillez sélectionner une civilité",
                          },
                        ]}
                      >
                        <Select
                          allowClear
                          placeholder={t("vente.contact.civilite_placeholder")}
                          options={[
                            { value: "M.", label: "M." },
                            { value: "Mme", label: "Mme" },
                          ]}
                          showSearch
                          filterOption={filterOption}
                          filterSort={filterSort}
                        ></Select>
                      </Form.Item>
                    </Col>
                    <Col md={8} xs={24}>
                      <Form.Item
                        name={[field.name, "email"]}
                        label={t("vente.contact.email")}
                        validateStatus={""}
                        help={""}
                      >
                        <Input
                          placeholder={t("vente.contact.email_placeholder")}
                        />
                      </Form.Item>
                    </Col>
                    <Col md={8} xs={24}>
                      <Form.Item
                        name={[field.name, "poste"]}
                        label={t("vente.contact.position")}
                        validateStatus={""}
                        help={""}
                      >
                        <Input
                          placeholder={t("vente.contact.position_placeholder")}
                        />
                      </Form.Item>
                    </Col>
                    <Col md={8} xs={24}>
                      <Form.Item
                        name={[field.name, "paysId"]}
                        label={t("vente.contact.pays")}
                        rules={[
                          {
                            required: true,
                            message: "Veuillez séléctionner le pays",
                          },
                        ]}
                      >
                        <Select
                          onChange={handleCountryChange}
                          value={selectedCountry}
                          placeholder={t("vente.contact.pays_placeholder")}
                          options={countries}
                          allowClear
                          showSearch
                        />
                      </Form.Item>
                    </Col>
                    <Col md={8} xs={24}>
                      <Form.Item
                        name={[field.name, "gouvernoratId"]}
                        label={t("vente.contact.gouvernorat")}
                      >
                        <Select
                          disabled={!selectedCountry}
                          placeholder={t(
                            "vente.contact.gouvernorat_placeholder"
                          )}
                          options={cities
                            ?.map((city) => ({ value: city, label: city }))
                            .filter(Boolean)}
                          allowClear
                          showSearch
                        />
                      </Form.Item>
                    </Col>
                    <Col md={8} xs={24}>
                      <Form.Item
                        name={[field.name, "delegationId"]}
                        label={t("vente.contact.delegation")}
                      >
                        <Input
                          placeholder={t(
                            "vente.contact.delegation_placeholder"
                          )}
                        />
                      </Form.Item>
                    </Col>
                    <Col md={8} xs={24}>
                      <Form.Item
                        name={[field.name, "adresse"]}
                        label={t("vente.contact.addresse")}
                        validateStatus={""}
                        help={""}
                      >
                        <Input
                          placeholder={t("vente.contact.addresse_placeholder")}
                        />
                      </Form.Item>
                    </Col>
                    <Col md={8} xs={24}>
                      <Form.Item
                        name={[field.name, "codePostal"]}
                        label={t("vente.contact.code_postal")}
                        validateStatus={""}
                        help={""}
                      >
                        <Input
                          placeholder={t(
                            "vente.contact.code_postal_placeholder"
                          )}
                        />
                      </Form.Item>
                    </Col>
                    <Col md={8} xs={24}>
                      <Form.Item
                        name={[field.name, "phone"]}
                        label={t("vente.contact.phone")}
                        validateStatus={""}
                        help={""}
                      >
                        <Input
                          placeholder={t("vente.contact.phone_placeholder")}
                        />
                      </Form.Item>
                    </Col>
                    <Col md={8} xs={24}>
                      <Form.Item
                        name={[field.name, "phoneFix"]}
                        label={t("vente.contact.phone_fix")}
                        validateStatus={""}
                        help={""}
                      >
                        <Input
                          placeholder={t("vente.contact.phone_fix_placeholder")}
                        />
                      </Form.Item>
                    </Col>
                    <Col md={8} xs={24}>
                      <Form.Item
                        name={[field.name, "fax"]}
                        label={t("vente.contact.fax")}
                        validateStatus={""}
                        help={""}
                      >
                        <Input
                          placeholder={t("vente.contact.fax_placeholder")}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Button
                    type="link"
                    disabled={disabled}
                    onClick={() => remove(field.name)}
                  >
                    <MinusCircleOutlined
                      style={{ fontSize: "20px", color: "#23537C" }}
                    />
                  </Button>
                </Space>
              </div>
            ))}
            <Form.Item>
              <Button
                type="dashed"
                onClick={() => add()}
                block
                icon={<PlusOutlined style={{ fontSize: "20px" }} />}
              >
                {t("vente.contact.add_contact")}
              </Button>
            </Form.Item>
          </>
        )}
      </Form.List>
    </>
  );
};
export default ContactForm;
