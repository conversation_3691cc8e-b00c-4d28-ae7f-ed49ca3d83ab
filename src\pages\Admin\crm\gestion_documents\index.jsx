import React, { useState, useRef, useEffect, useMemo } from 'react';
import {
  Button,
  Modal,
  Card,
  Popconfirm,
  Space,
  Tooltip,
  Tag,
  DatePicker,
  Input,
  message,
  Upload,
} from 'antd';
import { ProTable } from '@ant-design/pro-components';
import {
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  DownloadOutlined,
  PlusOutlined,
  HistoryOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';
import { documents } from './data';
import DocumentCreate from './create';
import DocumentEdit from './edit';
import DocumentView from './view';
import History from './History';
import { DeleteForeverOutlined, OutboundOutlined, OutboundRounded } from '@mui/icons-material';
import { ArchiveIcon } from 'lucide-react';

const { RangePicker } = DatePicker;

const DocumentsPage = () => {
  const [data, setData] = useState(documents);
  const [deletedArchivedData, setDeletedArchivedData] = useState([]); // New state for deleted/archived documents
  const [selectedDoc, setSelectedDoc] = useState(null);
  const [modalType, setModalType] = useState(null);
  const [dateRange, setDateRange] = useState([null, null]);

  const tableRef = useRef();
  const formatDate = (date) => dayjs(date).format('DD/MM/YYYY');
  const currentUser = 'Current User'; // Replace with actual user data from your auth system

  const addHistoryEntry = (docId, action, dataSource = data, setDataSource = setData) => {
    setDataSource((prevData) =>
      prevData.map((doc) =>
        doc.id === docId
          ? {
              ...doc,
              history: [
                ...(doc.history || []),
                {
                  id: (doc.history?.length || 0) + 1,
                  action,
                  date: dayjs().toISOString(),
                  person: currentUser,
                },
              ],
            }
          : doc
      )
    );
  };

  const handleCreate = (newDoc) => {
    const docWithId = {
      ...newDoc,
      id: Math.max(...data.map((d) => d.id), 0) + 1,
      createdAt: dayjs().toISOString(),
      updatedAt: dayjs().toISOString(),
      history: [
        {
          id: 1,
          action: 'Created',
          date: dayjs().toISOString(),
          person: currentUser,
        },
      ],
    };
    setData([...data, docWithId]);
    setModalType(null);
    message.success('Document created successfully!');
  };

  const handleUpdate = (updatedDoc) => {
    setData(
      data.map((doc) =>
        doc.id === updatedDoc.id
          ? { ...updatedDoc, updatedAt: dayjs().toISOString() }
          : doc
      )
    );
    addHistoryEntry(updatedDoc.id, 'Edited');
    setModalType(null);
    message.success('Document updated successfully!');
  };

  const handleDelete = (id) => {
    const docToDelete = data.find((doc) => doc.id === id);
    if (docToDelete) {
      addHistoryEntry(id, 'Deleted');
      setDeletedArchivedData((prev) => [...prev, { ...docToDelete, status: 'Deleted' }]);
      setData(data.filter((doc) => doc.id !== id));
      message.success('Document deleted successfully!');
    }
  };

  const handleArchive = (id) => {
    const docToArchive = data.find((doc) => doc.id === id);
    if (docToArchive) {
      addHistoryEntry(id, 'Archived');
      setDeletedArchivedData((prev) => [...prev, { ...docToArchive, status: 'Archived' }]);
      setData(data.filter((doc) => doc.id !== id));
      message.success('Document archived successfully!');
    }
  };

  const handleView = (record) => {
    addHistoryEntry(record.id, 'Viewed');
    setSelectedDoc(record);
    setModalType('view');
  };

  const handleDownload = (document) => {
    try {
      if (typeof window === 'undefined') {
        message.error('Le téléchargement est seulement disponible dans le navigateur');
        return;
      }

      const formatToExtension = {
        PDF: 'pdf',
        DOCX: 'docx',
        XLSX: 'xlsx',
        PPTX: 'pptx',
        TXT: 'txt',
      };
      const formatUpper = document.format?.toUpperCase();
      const extension = formatToExtension[formatUpper] || 'bin';
      const filename = `${document.name.replace(/\s+/g, '_')}.${extension}`;

      const link = document.createElement('a');
      link.href = `/files/${document.file}`;
      link.download = filename;

      const formatToMimeType = {
        PDF: 'application/pdf',
        DOCX: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        XLSX: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        PPTX: 'application/vnd.openxmlformats-officedocumentationml.presentation',
        TXT: 'text/plain',
      };

      if (formatToMimeType[document.format]) {
        link.type = formatToMimeType[document.format];
      }

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      addHistoryEntry(document.id, 'Downloaded');
      message.success(`Téléchargement de ${filename} commencé`);
    } catch (error) {
      console.error('Erreur lors du téléchargement:', error);
      message.error('Erreur lors du téléchargement du document');
    }
  };

  const handleImport = (file) => {
    try {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const importedData = JSON.parse(e.target.result);
          if (!Array.isArray(importedData)) {
            message.error('Invalid JSON format: Expected an array of documents');
            return;
          }

          const validDocuments = importedData.filter((doc) => {
            if (!doc.id || !doc.name || !doc.createdAt || !doc.updatedAt) {
              console.warn('Invalid document:', doc);
              return false;
            }
            return true;
          });

          const existingIds = new Set([...data.map((d) => d.id), ...deletedArchivedData.map((d) => d.id)]);
          const newDocuments = validDocuments.map((doc) => {
            let newId = doc.id;
            while (existingIds.has(newId)) {
              newId = Math.max(...[...data, ...deletedArchivedData].map((d) => d.id), 0) + 1;
            }
            return { ...doc, id: newId };
          });

          setData((prevData) => {
            const updatedData = [...prevData, ...newDocuments];
            newDocuments.forEach((doc) => {
              addHistoryEntry(doc.id, 'Imported', updatedData, setData);
            });
            return updatedData;
          });

          message.success(`${newDocuments.length} documents imported successfully!`);
        } catch (error) {
          console.error('Error parsing JSON:', error);
          message.error('Failed to parse JSON file');
        }
      };
      reader.readAsText(file);
    } catch (error) {
      console.error('Error reading file:', error);
      message.error('Failed to read the file');
    }
    return false;
  };

  const handleShowDeletedArchivedHistory = () => {
    setModalType('deletedArchivedHistory');
  };

  useEffect(() => {
    const validateData = () => {
      const invalidCategories = data.filter((item) => !item.category);
      if (invalidCategories.length) {
        console.warn('Documents without category:', invalidCategories);
        setData((prevData) =>
          prevData.map((item) =>
            item.category ? item : { ...item, category: 'Uncategorized' }
          )
        );
      }
    };
    validateData();
  }, [data]);

  useEffect(() => {
    tableRef.current?.reload();
  }, [data]);

  const categoryValueEnum = useMemo(
    () =>
      data.reduce((acc, item) => {
        if (item.category) acc[item.category] = { text: item.category };
        return acc;
      }, {}),
    [data]
  );

  const formatValueEnum = useMemo(
    () =>
      data.reduce((acc, item) => {
        if (item.format) acc[item.format] = { text: item.format };
        return acc;
      }, {}),
    [data]
  );

  const columns = [
    {
      title: 'Nom',
      dataIndex: 'name',
      key: 'name',
      sorter: (a, b) => a.name.localeCompare(b.name),
      render: (text) => <span style={{ fontWeight: 500 }}>{text}</span>,
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm }) => (
        <div style={{ padding: 8 }}>
          <Input
            placeholder="Search name"
            value={selectedKeys[0]}
            onChange={(e) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={confirm}
            style={{ width: 188, marginBottom: 8, display: 'block' }}
          />
          <Button type="primary" onClick={confirm} size="small" style={{ width: 90 }}>
            Search
          </Button>
        </div>
      ),
    },
    {
      title: 'Description',
      dataIndex: 'summary',
      key: 'summary',
      ellipsis: true,
      search: false,
      render: (text) => <Tooltip title={text}><span>{text}</span></Tooltip>,
    },
    {
      title: 'Catégorie',
      dataIndex: 'category',
      key: 'category',
      valueType: 'select',
      render: (category) => (category ? <Tag color="blue">{category}</Tag> : '-'),
      valueEnum: categoryValueEnum,
    },
    {
      title: 'Format',
      dataIndex: 'format',
      key: 'format',
      valueType: 'select',
      render: (format) => (format ? <Tag color="geekblue">{format}</Tag> : '-'),
      valueEnum: formatValueEnum,
    },
    {
      title: 'Date de création',
      dataIndex: 'createdAt',
      key: 'createdAt',
      valueType: 'dateRange',
      render: (_, record) => (record.createdAt ? dayjs(record.createdAt).format('YYYY-MM-DD') : '-'),
      search: {
        transform: (value) => {
          if (!value || value.length !== 2) return {};
          return {
            startTime: dayjs(value[0]).startOf('day').format(),
            endTime: dayjs(value[1]).endOf('day').format(),
          };
        },
      },
    },
    {
      title: 'Actions',
      key: 'actions',
      align: 'center',
      width: 280,
      fixed: 'right',
      sorter: false,
      search: false,
      render: (_, record) => (
        <Space size="small" align="center">
          <Tooltip title={`Download ${record.format || 'document'}`}>
            <Button
              type="link"
              size="small"
              icon={<DownloadOutlined />}
              onClick={() => handleDownload(record)}
              style={{ color: '#1890ff' }}
            />
          </Tooltip>
          <Tooltip title="View document details">
            <Button
              type="link"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleView(record)}
              style={{ color: '#13c2c2' }}
            />
          </Tooltip>
          <Tooltip title="Edit document">
            <Button
              type="link"
              size="small"
              icon={<EditOutlined />}
              onClick={() => {
                setSelectedDoc(record);
                setModalType('edit');
                addHistoryEntry(record.id, 'Edited');
              }}
              style={{ color: '#fa8c16' }}
            />
          </Tooltip>
          <Tooltip title="Delete document">
            <Popconfirm
              title="Are you sure to delete this document?"
              onConfirm={() => handleDelete(record.id)}
              okText="Yes"
              cancelText="No"
              placement="left"
            >
              <Button
                type="link"
                size="small"
                icon={<DeleteOutlined />}
                style={{ color: '#f5222d' }}
              />
            </Popconfirm>
          </Tooltip>
          <Tooltip title="Archive document">
            <Popconfirm
              title="Are you sure to archive this document?"
              onConfirm={() => handleArchive(record.id)}
              okText="Yes"
              cancelText="No"
              placement="left"
            >
              <Button
                type="link"
                size="small"
                icon={<HistoryOutlined />}
                style={{ color: '#eb2f96' }}
              />
            </Popconfirm>
          </Tooltip>
          <Tooltip title="View action history">
            <Button
              type="link"
              size="small"
              icon={<HistoryOutlined />}
              onClick={() => {
                setSelectedDoc(record);
                setModalType('history');
              }}
              style={{ color: '#722ed1' }}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const deletedArchivedColumns = [
    {
      title: 'Nom',
      dataIndex: 'name',
      key: 'name',
      render: (text) => <span style={{ fontWeight: 500 }}>{text}</span>,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={status === 'Deleted' ? 'red' : 'volcano'}>{status}</Tag>
      ),
    },
    {
      title: 'Action',
      dataIndex: 'history',
      key: 'action',
      render: (history) => {
        const lastAction = history?.slice(-1)[0];
        return lastAction ? (
          <Tag color={lastAction.action === 'Deleted' ? 'red' : 'volcano'}>
            {lastAction.action}
          </Tag>
        ) : (
          '-'
        );
      },
    },
    {
      title: 'Date',
      dataIndex: 'history',
      key: 'date',
      render: (history) => {
        const lastAction = history?.slice(-1)[0];
        return lastAction ? dayjs(lastAction.date).format('YYYY-MM-DD HH:mm:ss') : '-';
      },
      sorter: (a, b) => {
        const dateA = a.history?.slice(-1)[0]?.date;
        const dateB = b.history?.slice(-1)[0]?.date;
        return dateA && dateB ? dayjs(dateA).unix() - dayjs(dateB).unix() : 0;
      },
    },
    {
      title: 'Person',
      dataIndex: 'history',
      key: 'person',
      render: (history) => {
        const lastAction = history?.slice(-1)[0];
        return lastAction?.person || '-';
      },
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Button
          type="link"
          size="small"
          icon={<HistoryOutlined />}
          onClick={() => {
            setSelectedDoc(record);
            setModalType('history');
          }}
          style={{ color: '#722ed1' }}
        >
          View Full History
        </Button>
      ),
    },
  ];

  return (
    <Card
      title="Librairie des documents"
      bordered={false}
      extra={
        <Space>
          <Button type="primary" icon={<PlusOutlined />} onClick={() => setModalType('create')}>
            Ajouter
          </Button>
          <Upload accept=".json" showUploadList={false} beforeUpload={handleImport}>
            <Button icon={<UploadOutlined />}>Importer</Button>
          </Upload>
          <Button icon={<HistoryOutlined />} onClick={handleShowDeletedArchivedHistory}>
            Historique Supprimés/Archivés
          </Button>
        </Space>
      }
      bodyStyle={{ padding: '24px 0' }}
    >
      <ProTable
        actionRef={tableRef}
        columns={columns}
        rowKey="id"
        request={async (filter = {}, sort = {}) => {
          try {
            let filteredData = [...data];

            console.log('Active filters:', {
              category: filter?.category,
              format: filter?.format,
              startTime: filter?.startTime,
              endTime: filter?.endTime,
              name: filter?.name,
              initialDataCount: data.length,
              filteredDataCount: filteredData.length,
            });

            if (filter?.category !== undefined) {
              filteredData = filteredData.filter((item) =>
                item.category && item.category === filter.category
              );
            }

            if (filter?.format !== undefined) {
              filteredData = filteredData.filter((item) =>
                item.format && filter.format.includes(item.format)
              );
            }

            if (filter.startTime !== undefined || filter.endTime !== undefined) {
              filteredData = filteredData.filter((item) => {
                if (!item.createdAt) return false;
                const date = dayjs(item.createdAt);
                return (
                  (filter.startTime === undefined || date.isAfter(dayjs(filter.startTime))) &&
                  (filter.endTime === undefined || date.isBefore(dayjs(filter.endTime)))
                );
              });
            }

            if (filter.name !== undefined) {
              const searchTerm = filter.name.toLowerCase();
              filteredData = filteredData.filter((item) =>
                item.name?.toLowerCase().includes(searchTerm)
              );
            }

            if (sort.field === 'name' && sort.order) {
              filteredData.sort((a, b) =>
                sort.order === 'ascend'
                  ? (a.name || '').localeCompare(b.name || '')
                  : (b.name || '').localeCompare(a.name || '')
              );
            }

            return {
              data: filteredData,
              success: true,
              total: filteredData.length,
            };
          } catch (error) {
            console.error('Filter error:', error);
            return {
              data: [],
              success: false,
              total: 0,
              error: error.message,
            };
          }
        }}
        options={{
          reload: () => setData(documents),
          density: true,
          setting: true,
          fullScreen: true,
        }}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: 10,
          pageSizeOptions: ['10', '20', '50'],
          showTotal: (total) => `Total ${total} documents`,
        }}
        scroll={{ x: 'max-content' }}
        sticky
      />

      <Modal
        title="Ajouter un nouveau document"
        open={modalType === 'create'}
        onCancel={() => setModalType(null)}
        footer={null}
        width={800}
        destroyOnClose
      >
        <DocumentCreate onCreate={handleCreate} />
      </Modal>

      <Modal
        title="Modifier le document"
        open={modalType === 'edit'}
        onCancel={() => setModalType(null)}
        footer={null}
        width={800}
        destroyOnClose
      >
        <DocumentEdit initialValues={selectedDoc} onUpdate={handleUpdate} />
      </Modal>

      <Modal
        title="Détails du document"
        open={modalType === 'view'}
        onCancel={() => setModalType(null)}
        footer={null}
        width={800}
        destroyOnClose
      >
        <DocumentView document={selectedDoc} />
      </Modal>

      <Modal
        title={`Historique des actions - ${selectedDoc?.name}`}
        open={modalType === 'history'}
        onCancel={() => setModalType(null)}
        footer={null}
        width={800}
        destroyOnClose
      >
        <History history={selectedDoc?.history || []} />
      </Modal>

      <Modal
        title="Historique des documents supprimés et archivés"
        open={modalType === 'deletedArchivedHistory'}
        onCancel={() => setModalType(null)}
        footer={null}
        width={1000}
        destroyOnClose
      >
        <ProTable
          columns={deletedArchivedColumns}
          dataSource={deletedArchivedData}
          rowKey="id"
          pagination={{ pageSize: 5 }}
          search={false}
          options={false}
          size="small"
        />
      </Modal>
    </Card>
  );
};

export default DocumentsPage;