import React, { useState } from 'react';
import { Form, Input, But<PERSON>, <PERSON>, <PERSON><PERSON>icker, <PERSON><PERSON><PERSON>, Row, Col, Divider } from 'antd';
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';

const { Option } = Select;
const { TextArea } = Input;

const InteractionCreate = ({ onCreate, profileId }) => {
  const [form] = Form.useForm();
  const [interactions, setInteractions] = useState([{ id: Date.now() }]);
  const [activeKey, setActiveKey] = useState([]);

  const addInteraction = () => {
    const newInteraction = { id: Date.now() };
    setInteractions([...interactions, newInteraction]);
    setActiveKey([newInteraction.id.toString()]);
  };

  const removeInteraction = (idToRemove) => {
    setInteractions(interactions.filter(int => int.id !== idToRemove));
  };

  const handleFinish = (values) => {
    const formattedInteractions = interactions.map((interaction) => ({
      type: values[`type_${interaction.id}`],
      date: values[`date_${interaction.id}`] ? values[`date_${interaction.id}`].format('YYYY-MM-DD') : null,
      diasporaProfile: values[`diasporaProfile_${interaction.id}`],
      object: values[`object_${interaction.id}`],
      notes: values[`notes_${interaction.id}`],
      nextSteps: values[`nextSteps_${interaction.id}`],
      profileId,
    }));
    onCreate(formattedInteractions);
    form.resetFields();
    setInteractions([{ id: Date.now() }]);
    setActiveKey([]);
  };

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleFinish}
    >
      <Divider>Interactions</Divider>

      <div style={{ display: 'flex', justifyContent: 'flex-end', marginBottom: 16 }}>
        <Button onClick={addInteraction} icon={<PlusOutlined />} type="primary">
        </Button>
      </div>

      {interactions.map((interaction, index) => {
        // Get current form values for rendering the label
        const formValues = form.getFieldsValue();
        const objectValue = formValues[`object_${interaction.id}`];

        return (
          
           
          <div key={interaction.id} style={{ display: 'flex', alignItems: 'flex-start', gap: '10px', width: '100%', marginBottom: 16 }}>
            
            
            
            <Collapse
              style={{ flex: 1 }}
              activeKey={activeKey}
              onChange={(keys) => setActiveKey(keys)}
              items={[
                {
                  key: interaction.id.toString(),
                  label: objectValue || `Interaction ${index + 1}`,
                  children: (
                    <>
                      <Form.Item
                        name={`type_${interaction.id}`}
                        label="Type"
                        rules={[{ required: true, message: 'Veuillez sélectionner un type' }]}
                      >
                        <Select placeholder="Sélectionner un type">
                          <Option value="Événement">Événement</Option>
                          <Option value="Discussion">Discussion</Option>
                          <Option value="Engagement">Engagement</Option>
                        </Select>
                      </Form.Item>

                      <Form.Item
                        name={`date_${interaction.id}`}
                        label="Date"
                        rules={[{ required: true, message: 'Veuillez sélectionner une date' }]}
                      >
                        <DatePicker style={{ width: '100%' }} format="YYYY-MM-DD" />
                      </Form.Item>

                      <Form.Item
                        name={`diasporaProfile_${interaction.id}`}
                        label="Profil Diaspora"
                        rules={[{ required: true, message: 'Veuillez entrer le profil diaspora' }]}
                      >
                        <Input placeholder="Nom du profil diaspora" />
                      </Form.Item>

                      <Form.Item
                        name={`object_${interaction.id}`}
                        label="Objet"
                        rules={[{ required: true, message: 'Veuillez entrer un objet' }]}
                      >
                        <Input placeholder="Objet de l'interaction" />
                      </Form.Item>

                      <Form.Item
                        name={`notes_${interaction.id}`}
                        label="Notes"
                      >
                        <TextArea rows={3} placeholder="Notes sur l'interaction" />
                      </Form.Item>

                      <Form.Item
                        name={`nextSteps_${interaction.id}`}
                        label="Prochaines Étapes"
                      >
                        <TextArea rows={2} placeholder="Prochaines étapes à suivre" />
                      </Form.Item>
                    </>
                  ),
                },
              ]}
            />
            <Button
              onClick={() => removeInteraction(interaction.id)}
              danger
              icon={<DeleteOutlined />}
              style={{ marginTop: 16 }}
            />
          </div>
        );
      })}

      <Form.Item>
        <div style={{ textAlign: 'right' }}>
          <Button type="primary" htmlType="submit">
            Enregistrer
          </Button>
        </div>
      </Form.Item>
    </Form>
  );
};

export default InteractionCreate;