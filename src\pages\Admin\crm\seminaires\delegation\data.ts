import dayjs from "dayjs";

const medias = [
  {
    id: 1,
    pays_id: 1,
    type_id: 1,
    delegation: "Région xxxxx",
    responsable : "resposable Fipa",
    propose_par : "Test test" ,
    date_visite: dayjs("2025-01-10"),
  },
];
const pays = [
  { id: 1, nom: "France" },
  { id: 2, nom: "Italie" },
  { id: 3, nom: "Allemagne" },
  { id: 4, nom: "Espagne" },
  { id: 5, nom: "États-Unis" },
  { id: 6, nom: "Canada" },
  { id: 7, nom: "Algérie" },
  { id: 8, nom: "Maroc" },
  { id: 9, nom: "Libye" },
  { id: 10, nom: "Turquie" },
  { id: 11, nom: "<PERSON><PERSON>" },
  { id: 12, nom: "Émirats arabes unis" },
  { id: 13, nom: "Qatar" },
  { id: 14, nom: "Égypte" },
  { id: 15, nom: "Chine" },
  { id: 16, nom: "Japon" },
  { id: 17, nom: "Corée du Sud" },
  { id: 18, nom: "Belgique" },
  { id: 19, nom: "Suisse" },
  { id: 20, nom: "Russie" },
];

const organisateurs =[
   { id: 1, nom: "IFEMA" },
] ;
const types =[
   { id: 1, type : "type 1" },
] ;
export { medias, pays ,organisateurs , types};
