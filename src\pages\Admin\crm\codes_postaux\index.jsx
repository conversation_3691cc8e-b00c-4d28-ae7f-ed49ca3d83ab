import { useState, useRef } from "react";
import { ProTable } from "@ant-design/pro-components";
import { <PERSON>ton, Popconfirm, Space, Tooltip, Typography, Card, Modal } from "antd";
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import postalCodes from "./datas";
import CreatePostalCode from "./create";
import EditPostalCode from "./edit";
import ViewPostalCode from "./view";

function PostalCodes() {
  const { t } = useTranslation();
  const tableRef = useRef();

  const [dataSource, setDataSource] = useState(postalCodes);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [viewModalVisible, setViewModalVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState(null);
  const [viewingRecord, setViewingRecord] = useState(null);

  const handleCancel = () => {
    setViewModalVisible(false);
  };

  const handleEdit = (record) => {
    setEditingRecord(record);
    setEditModalVisible(true);
  };

  const columns = [
    { title: "Code Postal", dataIndex: "code", key: "code" },
    { title: "Ville", dataIndex: "city", key: "city" },
    { title: "Gouvernorat", dataIndex: "governorate", key: "governorate" },
    {
      title: "Actions",
      key: "action",
      align: "center",
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title="Voir">
            <Button type="link" icon={<EyeOutlined />} onClick={() => {
              setViewingRecord(record);
              setViewModalVisible(true);
            }} />
          </Tooltip>
          <Tooltip title="Modifier">
            <Button type="link" style={{ color: "#f5b041" }} icon={<EditOutlined />} onClick={() => handleEdit(record)} />
          </Tooltip>
          <Tooltip title="Supprimer">
            <Popconfirm title="Confirmer la suppression ?" onConfirm={() => {
              const newDataSource = dataSource.filter(item => item.key !== record.key);
              setDataSource(newDataSource);
            }}>
              <Button type="link" style={{ color: "#ec7063" }} icon={<DeleteOutlined />} />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <Card
      bordered
      title={<Typography.Title level={4}>Liste des Codes Postaux</Typography.Title>}
      extra={
        <Button type="primary" icon={<PlusOutlined />} onClick={() => setCreateModalVisible(true)}>
          Ajouter
        </Button>
      }
    >
      <ProTable
        actionRef={tableRef}
        columns={columns}
        dataSource={dataSource}
        rowKey="key"
        pagination={{ showSizeChanger: true, defaultPageSize: 5 }}
      />
      {/* Create Modal */}
      <Modal
        title="Ajouter un Code Postal"
        visible={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        footer={null}
        width={1000}
      >
        <CreatePostalCode
          onCancel={() => setCreateModalVisible(false)}
          setDataSource={setDataSource}
        />
      </Modal>

      {/* Edit Modal */}
      <Modal
        title="Modifier un Code Postal"
        visible={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        footer={null}
        width={800}
      >
        <EditPostalCode
          record={editingRecord}
          onCancel={() => setEditModalVisible(false)}
          setDataSource={setDataSource}
        />
      </Modal>

      {/* View Modal */}
      <Modal
        title="Détails du Code Postal"
        visible={viewModalVisible}
        onCancel={handleCancel}
        footer={null}
        width={800}
      >
        <ViewPostalCode
          record={viewingRecord}
          onCancel={() => setViewModalVisible(false)}
        />
      </Modal>
    </Card>
  );
}

export default PostalCodes;
