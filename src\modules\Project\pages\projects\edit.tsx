import React, { useEffect, useState } from "react";
import {
  Input,
  Form,
  Row,
  Col,
  Select,
  DatePicker,
  InputNumber,
  Switch,
} from "antd";
import dayjs from "dayjs";
import { useDispatch } from "react-redux";
import { IClient, IEntreprise, IDepartement, IProject } from "../interfaces";
import { ModalForm } from "@ant-design/pro-components";
import { useTranslation } from "react-i18next";
import { updateProject } from "../../features/projects/projectSlice";
const { Option } = Select;
const Edit: React.FC<{
  visible: boolean;
  setVisible: React.Dispatch<React.SetStateAction<boolean>>;
  tableRef: any;
  entreprises: IEntreprise[];
  departements: IDepartement[];
  clients: IClient[];
  projects: IProject[];
  messageApi: any;
  project: IProject;
}> = ({
  visible,
  setVisible,
  tableRef,
  entreprises,
  departements,
  clients,
  projects,
  messageApi,
  project,
}) => {
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [entreprise, setEntreprise] = useState<number>(null);
  const [departement, setDepartement] = useState<number>(null);
  const [projetParent, setProjetParent] = useState<IProject>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [contractMaintenanceEnabled, setContractMaintenanceEnabled] =
    useState<boolean>(false);

  //select search and sort
  const filterOption = (input, option) =>
    (option?.label ?? "").toLowerCase().includes(input.toLowerCase());
  const filterSort = (optionA, optionB) =>
    (optionA?.label ?? "")
      .toLowerCase()
      .localeCompare((optionB?.label ?? "").toLowerCase());

  const onClose = (open) => {
    if (!open) {
      setVisible(false);
      setDepartement(null);
      setEntreprise(null);
      setProjetParent(null);
    }
  };

  // Handle input errors messages
  const handleInputErrors = (error) => {
    const apiErrors = error.errors;
    const fields: any = Object.entries(apiErrors).map(
      ([fieldName, messages]) => ({
        name: fieldName,
        errors: messages,
      })
    );
    form.setFields(fields);
  };

  const handleSubmit = async (values) => {
    setIsLoading(true);
    messageApi.open({
      key: "updatable",
      type: "loading",
      content: t("common.messages.loading"),
    });
    dispatch(
      updateProject({
        id: project.id,
        ...values,
        date_ordre_service: values.date_ordre_service
          ? dayjs(values.date_ordre_service).format("YYYY-MM-DD")
          : null,
        signature_contrat: values.signature_contrat
          ? dayjs(values.signature_contrat).format("YYYY-MM-DD")
          : null,
        reception_definitive: values.reception_definitive
          ? dayjs(values.reception_definitive).format("YYYY-MM-DD")
          : null,
        reception_provisoire: values.reception_provisoire
          ? dayjs(values.reception_provisoire).format("YYYY-MM-DD")
          : null,
        contract_maintenance: values.contract_maintenance
          ? dayjs(values.contract_maintenance).format("YYYY-MM-DD")
          : null,
      })
    )
      .unwrap()
      .then((originalPromiseResult) => {
        messageApi.open({
          key: "updatable",
          type: "success",
          content: t("common.messages.success"),
          duration: 2,
        });
        tableRef.current.reload();
        setIsLoading(false);
        onClose(false);
      })
      .catch((error) => {
        handleInputErrors(error);
        messageApi.open({
          key: "updatable",
          type: "error",
          content: t("common.messages.error"),
          duration: 2,
        });
        setIsLoading(false);
      });
  };

  useEffect(() => {
    setDepartement(project?.department_id);
    setEntreprise(project?.enterprise_id);
    setProjetParent(project?.project_id);
    form.setFieldsValue({
      ...project,
      date_ordre_service:
        project?.date_ordre_service &&
        dayjs(project.date_ordre_service, "YYYY-MM-DD"),
      duree_exec: project?.duree_exec,
      lien_partage: project?.lien_partage,
      signature_contrat:
        project?.signature_contrat &&
        dayjs(project.signature_contrat, "YYYY-MM-DD"),
      reception_definitive:
        project?.reception_definitive &&
        dayjs(project.reception_definitive, "YYYY-MM-DD"),
      reception_provisoire:
        project?.reception_provisoire &&
        dayjs(project.reception_provisoire, "YYYY-MM-DD"),
      contract_maintenance:
        project?.contract_maintenance &&
        dayjs(project.contract_maintenance, "YYYY-MM-DD"),
      lettre_participation: !!project?.lettre_participation,
      lettre_bonne_execution: !!project?.lettre_bonne_execution,
    });
    setContractMaintenanceEnabled(project?.contract_maintenance_option);
  }, [project]);
  return (
    <ModalForm
      title={t("projects.edit.title") + " " + project?.designation}
      width={750}
      onOpenChange={onClose}
      open={visible}
      layout="vertical"
      form={form}
      onFinish={handleSubmit}
      onValuesChange={(changedValues) => {
        const fieldName = Object.keys(changedValues)[0];
        form.setFields([{ name: fieldName, errors: [] }]);

        // Surveille le changement du switch
        if (fieldName === "contract_maintenance_option") {
          const checked = changedValues[fieldName];
          setContractMaintenanceEnabled(checked);
          if (!checked) {
            form.setFieldsValue({ contract_maintenance: null });
          }
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.save"),
          resetText: t("common.actions.cancel"),
        },
      }}
      loading={isLoading}
    >
      <Row gutter={16}>
        <Col xs={24} sm={12} md={12} lg={12} xl={12} xxl={12}>
          <Form.Item
            name="enterprise_id"
            label={t("projects.columns.enterprise")}
            rules={[
              {
                required: true,
                message: t("projects.messages.enterprise_required"),
              },
            ]}
          >
            <Select
              placeholder={t("projects.placeholders.select_enterprise")}
              showSearch
              filterOption={filterOption}
              filterSort={filterSort}
              value={entreprise}
              onSelect={(e) => {
                setEntreprise(e);
                form.setFieldsValue({
                  department_id: null,
                  project_id: null,
                });
                setDepartement(null);
                setProjetParent(null);
              }}
            >
              {entreprises?.map((item) => (
                <Option key={item.id} value={item.id} label={item.designation}>
                  {item.designation}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={12} lg={12} xl={12} xxl={12}>
          <Form.Item
            name="department_id"
            label={t("projects.columns.department")}
            rules={[
              {
                required: true,
                message: t("projects.messages.department_required"),
              },
            ]}
          >
            <Select
              placeholder={t("projects.placeholders.select_department")}
              showSearch
              filterOption={filterOption}
              filterSort={filterSort}
              disabled={!entreprise}
              value={departement}
              onSelect={(e) => {
                setDepartement(e);
                setProjetParent(null);
                form.setFieldsValue({
                  project_id: null,
                });
              }}
            >
              {departements?.map(
                (item) =>
                  item.enterprise_id === entreprise && (
                    <Option
                      key={item.id}
                      value={item.id}
                      label={item.designation}
                    >
                      {item.designation}
                    </Option>
                  )
              )}
            </Select>
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={12} lg={12} xl={12} xxl={12}>
          <Form.Item
            name="tier_id"
            label={t("projects.columns.client")}
            rules={[
              {
                required: true,
                message: t("projects.messages.client_required"),
              },
            ]}
          >
            <Select
              placeholder={t("projects.placeholders.select_client")}
              showSearch
              filterOption={filterOption}
              filterSort={filterSort}
            >
              {clients?.map((item) => (
                <Option key={item.id} value={item.id} label={item.designation}>
                  {item.designation}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={12} lg={12} xl={12} xxl={12}>
          <Form.Item
            name="project_id"
            label={t("projects.columns.parent_project")}
          >
            <Select
              placeholder={t("projects.placeholders.select_parent_project")}
              showSearch
              allowClear
              filterOption={filterOption}
              filterSort={filterSort}
              disabled={!departement}
              value={projetParent}
            >
              {projects?.map(
                (item) =>
                  item.department_id === departement && (
                    <Option
                      key={item.id}
                      value={item.id}
                      label={item.designation}
                    >
                      {item.designation}
                    </Option>
                  )
              )}
            </Select>
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={12} lg={12} xl={12} xxl={12}>
          <Form.Item
            name="designation"
            label={t("projects.columns.project_name")}
            rules={[
              {
                required: true,
                message: t("projects.messages.project_name_required"),
              },
            ]}
          >
            <Input
              placeholder={t("projects.placeholders.enter_project_name")}
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={12} lg={12} xl={12} xxl={12}>
          <Form.Item
            name="reference"
            label={t("projects.columns.tender_number")}
          >
            <Input
              placeholder={t("projects.placeholders.enter_tender_number")}
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={12} lg={12} xl={12} xxl={12}>
          <Form.Item
            name="date_ordre_service"
            label={t("projects.columns.service_order_date")}
          >
            <DatePicker
              style={{ width: "100%" }}
              format={"YYYY-MM-DD"}
              allowClear
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={12} lg={12} xl={12} xxl={12}>
          <Form.Item
            name="duree_exec"
            label={t("projects.columns.execution_duration")}
          >
            <InputNumber
              style={{ width: "100%" }}
              placeholder={t("projects.placeholders.enter_execution_duration")}
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={12} lg={12} xl={12} xxl={12}>
          <Form.Item
            name="lien_partage"
            label={t("projects.columns.share_link")}
          >
            <Input placeholder={t("projects.placeholders.enter_share_link")} />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={12} lg={12} xl={12} xxl={12}>
          <Form.Item
            name="signature_contrat"
            label={t("projects.columns.contract_signature")}
          >
            <DatePicker
              style={{ width: "100%" }}
              format={"YYYY-MM-DD"}
              allowClear
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={12} lg={12} xl={12} xxl={12}>
          <Form.Item
            name="reception_provisoire"
            label={t("projects.columns.provisional_reception")}
          >
            <DatePicker
              style={{ width: "100%" }}
              format="YYYY-MM-DD"
              allowClear
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={12} lg={12} xl={12} xxl={12}>
          <Form.Item
            name="duree_exec_prov"
            label={t("projects.columns.execution_provisional_duration")}
          >
            <InputNumber
              style={{ width: "100%" }}
              placeholder={t(
                "projects.placeholders.enter_provisional_execution_duration"
              )}
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={12} lg={12} xl={12} xxl={12}>
          <Form.Item
            name="reception_definitive"
            label={t("projects.columns.final_reception")}
          >
            <DatePicker
              style={{ width: "100%" }}
              format="YYYY-MM-DD"
              allowClear
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={12} lg={12} xl={12} xxl={12}>
          <Form.Item
            name="warranty_period"
            label={t("projects.columns.warranty_duration")}
          >
            <InputNumber
              style={{ width: "100%" }}
              placeholder={t("projects.placeholders.enter_warranty_duration")}
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={12} lg={12} xl={12} xxl={12}>
          <Form.Item
            name="contract_maintenance_option"
            label={t("projects.columns.maintenance_contract_option")}
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={12} lg={12} xl={12} xxl={12}>
          <Form.Item
            name="contract_maintenance"
            label={t("projects.columns.maintenance_contract")}
          >
            <DatePicker
              style={{ width: "100%" }}
              format={"YYYY-MM-DD"}
              allowClear
              disabled={!contractMaintenanceEnabled}
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={12} lg={12} xl={12} xxl={12}>
          <Form.Item
            name="lettre_participation"
            label={t("projects.columns.participation_letter")}
          >
            <Select placeholder={t("common.placeholders.select")} allowClear>
              <Option key={0} value={false} label={t("common.no")}>
                {t("common.no")}
              </Option>
              <Option key={1} value={true} label={t("common.yes")}>
                {t("common.yes")}
              </Option>
            </Select>
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={12} lg={12} xl={12} xxl={12}>
          <Form.Item
            name="lettre_bonne_execution"
            label={t("projects.columns.good_execution_letter")}
          >
            <Select placeholder={t("common.placeholders.select")} allowClear>
              <Option key={0} value={false} label={t("common.no")}>
                {t("common.no")}
              </Option>
              <Option key={1} value={true} label={t("common.yes")}>
                {t("common.yes")}
              </Option>
            </Select>
          </Form.Item>
        </Col>
      </Row>
    </ModalForm>
  );
};

export default Edit;
