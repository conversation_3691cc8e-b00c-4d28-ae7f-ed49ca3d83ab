import { useTranslation } from "react-i18next";
import { ModalForm } from "@ant-design/pro-components";
import { useState } from "react";
import { message, Steps, Button } from "antd";
import TierInfoGeneralForm from "@src/modules/finance/components/TierInfoGeneralForm";
import ContactForm from "@src/modules/finance/components/ContactForm";

const EditFournisseurModal = ({ open, onCancel, fournisseur }) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);

  const steps = [
    {
      title: t("common.steps.general_info"),
      content: <TierInfoGeneralForm disabled={false} tier={fournisseur} />,
    },
    {
      title: t("common.steps.contacts_info"),
      content: <ContactForm disabled={false} contacts={fournisseur}/>,
    },
  ];

  const percent = ((currentStep + 1) / steps.length) * 100;

  const handleSubmit = async (values) => {
    setLoading(true);
    try {
      await new Promise((resolve) => setTimeout(resolve, 2000));
      message.success("Fournisseur modifié avec succès !");
    } catch (error) {
      console.log(error);
      message.error("Error lors de la modification du fournisseur !");
    } finally {
      setLoading(false);
      onCancel();
    }
  };

  return (
    <ModalForm
      width={900}
      title={t("achat.fournisseur.edit")}
      open={open}
      onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
          setCurrentStep(0)
        }
      }}
      submitter={false}
      modalProps={{
        destroyOnClose: true,
      }}
    >
      <div className="p-4">
        <Steps
          current={currentStep}
          percent={percent}
          labelPlacement="vertical"
          items={steps}
        />
        <div>{steps[currentStep].content}</div>
        <div style={{ display: "flex", justifyContent: "space-between" }}>
          <Button
            disabled={currentStep === 0}
            onClick={() => setCurrentStep(currentStep - 1)}
          >
            {t("common.actions.previous")}
          </Button>
          {currentStep < steps.length - 1 ? (
            <Button
              type="primary"
              onClick={() => setCurrentStep(currentStep + 1)}
            >
              {t("common.actions.next")}
            </Button>
          ) : (
            <Button type="primary" loading={loading} onClick={handleSubmit}>
              {t("common.actions.save")}
            </Button>
          )}
        </div>
      </div>
    </ModalForm>
  );
};

export default EditFournisseurModal;
