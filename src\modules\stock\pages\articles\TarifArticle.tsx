import { Col, Form, Input, Radio, Row, Select } from "antd";

import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";


const TarifArticle : React.FC<{
  tvas: any;
  resetFields: boolean;
  data: any;
  setTarifValues: React.Dispatch<React.SetStateAction<any>>;
  disabled: boolean;
}>
= ({ tvas, resetFields, data, setTarifValues,  disabled})  => {
  const {t} = useTranslation();
  const [form] = Form.useForm();
  const [prixRevient, setPrixRevient] = useState('');
  const [prixAchat, setPrixAchat] = useState(data?.purchase_price||'');
  const [frais, setFrais] = useState<string>(data?.frais||'');
  const [fraisArgent, setFraisArgent] = useState<string>('');
  const [marge, setMarge] = useState<string>(data?.marge ||'');
  const [margeArgent, setMargeArgent] = useState<string>('');
  const [prixHT, setPrixHT] = useState('');
  const [tva, setTva] = useState('');
  const [tvaValeur, setTvaValeur] = useState('');
  const [prixTTC, setPrixTTC] = useState('');
  const [fraisType, setFraisType] = useState<number>(data?.frais_type || 1);
  const [margeType, setMargeType] = useState<number>(data?.marge_type||1);
  // const [localTarifValues, setLocalTarifValues] = useState({
  //   prix_ht : data?.prix_ht, prix_ttc : data?.prix_ttc, prix_revient : data?.prix_revient
  // });
  const filterOption = (input, option) =>
    (option?.label ?? "").toLowerCase().includes(input.toLowerCase());
  const filterSort = (optionA, optionB) =>
    (optionA?.label ?? "")
    .toLowerCase()
    .localeCompare((optionB?.label ?? "").toLowerCase());

  const updatePrixAchat = (e) => {
    setPrixAchat(e.target.value);
  };
  const updateFrais = (e) => {
    setFrais(e.target.value);
  };
  const updateMarge = (e) => {
    setMarge(e.target.value);
  }
  const updateTVA = (e) => {        
    tvas.map((item) => {
      if(item.value == e)
      {
        setTva(item?.valeur);
      }
    })
  }
  useEffect(() => {
    if (resetFields) {
      setPrixAchat('');
      setFrais('');
      setMarge('');
      setPrixRevient('0');
      setPrixHT('0');
      setTva('0');
      setPrixTTC('0');
      form.resetFields(); // Reset Ant Design Form fields if any
    }
  }, [resetFields]);

  useEffect(() => {
    const updatedPrixAchat = parseFloat(prixAchat) || 0;
    const updatedFrais = fraisType === 2 ? (parseFloat(frais) * updatedPrixAchat / 100) || 0 : parseFloat(frais) || 0 ;
    const updatedTva = parseFloat(tva) || 0;
    const value = updatedPrixAchat + updatedFrais;
    const updatedMarge = margeType === 2 ? (parseFloat(marge)* value / 100) || 0 : parseFloat(marge) || 0 ;
    const updatedPrixHT = value + updatedMarge;
    const updatedTvaValeur = updatedPrixHT *(updatedTva/100);
    const updatedPrixTTC = (updatedPrixHT  + updatedTvaValeur).toFixed(3);
    setTvaValeur(updatedTvaValeur.toFixed(3).toString())
    setFraisArgent(updatedFrais.toFixed(3).toString())
    setMargeArgent(updatedMarge.toFixed(3).toString())
    setPrixRevient(value.toFixed(3).toString());
    setPrixHT(updatedPrixHT.toFixed(3).toString());
    setPrixTTC(updatedPrixTTC.toString());
    setTarifValues({prix_ht : updatedPrixHT, prix_ttc : updatedPrixTTC, 
      frais : frais, prix_revient : value, marge : marge}); 
    console.log(123365489655789) 
    console.log(prixAchat)
  }, [prixAchat,fraisType,margeType, frais,marge,tva]);
    
  return(<> 
    <Row gutter={6} >
      <Col md={12} xs={24} >
        <Form.Item name="purchase_price" label={t('articles.fields.prix_achat')}  > 
          <Input placeholder="Veuillez entrer le prix unitaire" type="number" min={0} value={prixAchat}    
            onChange={updatePrixAchat}   disabled={disabled}
          />
        </Form.Item>
      </Col>
      <Col  span={24} >
        <Row gutter={6}>
          <Col md={12} xs={24} >
            <Form.Item name="frais_type" label={t('articles.fields.frais_type')}  help={false}> 
              <Radio.Group
                onChange={(e)=>setFraisType(e.target.value)}
                disabled={disabled}
                options={[
                  {value:1, label:t('articles.money')},
                  {value:2, label:t('articles.percentage')},
                ]}
              />
            </Form.Item>
          </Col>
          {fraisType === 2 ?  
            <>
              <Col md={6} xs={12} >                
                <Form.Item   label={t('articles.fields.frais_percent')} >
                  <Input name="frais" placeholder="Veuillez entrer le pourcentage" type="number" min={0} value={frais}
                    onChange={updateFrais}         disabled={disabled}                 
                  />
                </Form.Item>
              </Col> 
              <Col md={6} xs={12} >                
                <Form.Item label={t('articles.fields.frais_argent')} >
                  <Input name="fraisArgent"  value={fraisArgent} readOnly   disabled={disabled}  />
                </Form.Item>
              </Col> 
            </> : 
            <Col md={12} xs={24} >                
              <Form.Item  label={t('articles.fields.frais_argent')}   >
                <Input name="frais" placeholder={t('articles.fields.frais_argent')} type="number" min={0} value={frais}
                  onChange={updateFrais}         disabled={disabled}                 
                />
              </Form.Item>
            </Col>
          }
        </Row>
      </Col>    
      <Col md={12} xs={24} >
        <Form.Item label={t('articles.fields.prix_revient')}   >
            <Input name="prix_revient"  value={prixRevient} readOnly  disabled={disabled}/>
        </Form.Item>
      </Col>
      <Col  span={24} >
        <Row gutter={6}>
          <Col md={12} xs={24} >
            <Form.Item name="marge_type" label={t('articles.fields.marge_type')} help={false}> 
              <Radio.Group
                onChange={(e)=>setMargeType(e.target.value)}
                disabled={disabled}
                options={[
                  {value:1, label:t('articles.money')},
                  {value:2, label:t('articles.percentage')},
                ]}
              />
            </Form.Item>
          </Col>
          {margeType === 2 ?  
            <>
              <Col md={6} xs={12} >                
                <Form.Item  label={t('articles.fields.marge_percent')} >
                  <Input name="marge"  type="number" min={0}   placeholder="Veuillez entrer le marge" value={marge} 
                    onChange={updateMarge}  disabled={disabled} 
                  />
                </Form.Item>
              </Col> 
              <Col md={6} xs={12} >                
                <Form.Item  label={t('articles.fields.marge_argent')}   >
                  <Input name="margeArgent"  value={margeArgent} readOnly   disabled={disabled} />
                </Form.Item>
              </Col> 
            </> : 
            <Col md={12} xs={24} >                
              <Form.Item  label={t('articles.fields.marge_argent')}   >
                <Input name="marge"  type="number" min={0} placeholder="Veuillez entrer le marge" value={marge} 
                  onChange={updateMarge}  disabled={disabled} 
                />
              </Form.Item>
            </Col>
          }
        </Row>
      </Col> 
      <Col md={12} xs={24}>  
        <Form.Item label={t('articles.fields.prix_ht')} >
          <Input name="prix_ht" value={prixHT} readOnly  disabled={disabled}/>
        </Form.Item>              
      </Col>
      <Col md={6} xs={12} >
        <Form.Item name="tva_id" label={t('articles.fields.taux_tva')} >
          <Select placeholder="Sélectionnez le taux de tva"
            filterOption={filterOption}
            filterSort={filterSort}
            allowClear
            showSearch
            onClear={()=>{setTva('0');}}
            options={tvas}
            onChange={updateTVA}
            disabled={disabled}
          />
        </Form.Item>
      </Col>
      <Col md={6} xs={12} >
        <Form.Item  label={t('articles.fields.tva_argent')} >
          <Input
            value={tvaValeur}
            readOnly
           
            disabled={disabled}
          />
        </Form.Item>
      </Col>
      <Col md={12} xs={24} >
        <Form.Item label={t('articles.fields.prix_ttc')}>
          <Input name="prix_ttc" value={prixTTC} readOnly  disabled={disabled}/>
        </Form.Item>      
      </Col>
    </Row>  
  </>)
}
export default TarifArticle;