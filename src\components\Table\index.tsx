// src/components/Loader.js
import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { fetchData } from '../../redux/actions/postActions';
import { Table } from "antd";
import Loader from '../Loader';

const TableExample = () => {
    const dispatch = useDispatch()
   const  isLoading = useSelector(state => state.loading.isLoading) 
    const columns = [
        {
          title: 'User Id',
          dataIndex: 'userId',
          key: 'userId',
        },
        {
          title: 'Titre',
          dataIndex: 'title',
          key: 'title',
        }
      ];
    const posts = useSelector((state)=> state.post.posts) // fetch data from store
    useEffect(()=>{
            dispatch(fetchData()) // API call 
    },[dispatch])
  return (
    <div className='w-full relative'>
      <h3 className='font-medium text-2xl my-4'>Liste des posts</h3>
     
      <Table dataSource={posts} columns={columns} className='w-full' />
    </div>
    
  );
};

export default TableExample;