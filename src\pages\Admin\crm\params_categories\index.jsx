import React, { useState, useRef, useEffect } from "react";
import { ProTable } from "@ant-design/pro-components";
import { Button, Popconfirm, Tag, Space, Input, Tooltip, Typography, Card, Modal, message, Form, Select } from "antd";
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined, SearchOutlined, HistoryOutlined } from "@ant-design/icons";
import dayjs from 'dayjs';

// Mock data
const initialCategories = [
  {
    id: 1,
    name: 'Contracts',
    type: 'Document',
    history: [
      { id: 1, action: 'Created', date: '2025-01-10T08:00:00Z', person: 'Admin' },
    ],
  },
  {
    id: 2,
    name: 'Reports',
    type: 'Document',
    history: [
      { id: 1, action: 'Created', date: '2025-01-10T08:00:00Z', person: 'Admin' },
    ],
  },
];

// Create Modal Component
const CreateModal = ({ visible, onCancel, onSuccess }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      onSuccess(values);
      form.resetFields();
    } catch (error) {
      console.error('Error:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Form form={form} layout="vertical">
      <Form.Item 
        name="name" 
        label="Nom" 
        rules={[{ required: true, message: 'Ce champ est obligatoire' }]}
      >
        <Input placeholder="Ex: Clients" />
      </Form.Item>
      <Form.Item 
        name="type" 
        label="Type" 
        rules={[{ required: true, message: 'Sélectionnez un type' }]}
      >
        <Select placeholder="Sélectionnez un type">
          <Option value="Document">Document</Option>
          <Option value="Email">Email</Option>
        </Select>
      </Form.Item>
      <div style={{ textAlign: 'right', marginTop: 16 }}>
        <Button style={{ marginRight: 8 }} onClick={onCancel}>
          Annuler
        </Button>
        <Button type="primary" loading={loading} onClick={handleSubmit}>
          Enregistrer
        </Button>
      </div>
    </Form>
  );
};

// Edit Modal Component
const EditModal = ({ record, onCancel, onSuccess }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (record) {
      form.setFieldsValue({
        name: record.name,
        type: record.type
      });
    }
  }, [record, form]);

  const handleSubmit = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      onSuccess({ ...record, ...values });
    } catch (error) {
      console.error('Error:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Form form={form} layout="vertical">
      <Form.Item 
        name="name" 
        label="Nom" 
        rules={[{ required: true, message: 'Ce champ est obligatoire' }]}
      >
        <Input />
      </Form.Item>
      <Form.Item 
        name="type" 
        label="Type" 
        rules={[{ required: true, message: 'Sélectionnez un type' }]}
      >
        <Select>
          <Option value="Document">Document</Option>
          <Option value="Email">Email</Option>
        </Select>
      </Form.Item>
      <div style={{ textAlign: 'right', marginTop: 16 }}>
        <Button style={{ marginRight: 8 }} onClick={onCancel}>
          Annuler
        </Button>
        <Button type="primary" loading={loading} onClick={handleSubmit}>
          Enregistrer
        </Button>
      </div>
    </Form>
  );
};

// Main Category Page Component
const CategoryPage = () => {
  const actionRef = useRef();
  const [data, setData] = useState(initialCategories);
  const [deletedData, setDeletedData] = useState([]);
  const [createVisible, setCreateVisible] = useState(false);
  const [editVisible, setEditVisible] = useState(false);
  const [viewVisible, setViewVisible] = useState(false);
  const [historyVisible, setHistoryVisible] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState(null);
  const [searchText, setSearchText] = useState('');
  const [loading, setLoading] = useState(false);

  const currentUser = 'Current User';

  // Filter data based on search text
  const filteredData = data.filter(item =>
    item.name.toLowerCase().includes(searchText.toLowerCase()) ||
    item.type.toLowerCase().includes(searchText.toLowerCase())
  );

  const addHistoryEntry = (categoryId, action) => {
    setData(prevData =>
      prevData.map(category =>
        category.id === categoryId
          ? {
              ...category,
              history: [
                ...(category.history || []),
                {
                  id: (category.history?.length || 0) + 1,
                  action,
                  date: dayjs().toISOString(),
                  person: currentUser,
                },
              ],
            }
          : category
      )
    );
  };

  const handleCreate = (newCategory) => {
    setLoading(true);
    try {
      const categoryWithId = {
        ...newCategory,
        id: Math.max(...data.map(c => c.id), 0) + 1,
        history: [
          {
            id: 1,
            action: 'Created',
            date: dayjs().toISOString(),
            person: currentUser,
          },
        ],
      };
      setData([...data, categoryWithId]);
      message.success('Catégorie créée avec succès');
      setCreateVisible(false);
      actionRef.current?.reload();
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (updatedCategory) => {
    setLoading(true);
    try {
      setData(data.map(category => 
        category.id === updatedCategory.id ? updatedCategory : category
      ));
      addHistoryEntry(updatedCategory.id, 'Edited');
      message.success('Catégorie modifiée avec succès');
      setEditVisible(false);
      actionRef.current?.reload();
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = (id) => {
    setLoading(true);
    try {
      const categoryToDelete = data.find(category => category.id === id);
      if (categoryToDelete) {
        addHistoryEntry(id, 'Deleted');
        setDeletedData(prev => [...prev, { ...categoryToDelete, status: 'Deleted' }]);
        setData(data.filter(category => category.id !== id));
        message.success('Catégorie supprimée avec succès');
        actionRef.current?.reload();
      }
    } finally {
      setLoading(false);
    }
  };

  const columns = [
    {
      title: 'Nom',
      dataIndex: 'name',
      key: 'name',
      sorter: (a, b) => a.name.localeCompare(b.name),
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      render: type => <Tag color={type === 'Email' ? 'blue' : 'green'}>{type}</Tag>,
    },
    {
      title: 'Actions',
      key: 'actions',
      align: 'center',
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title="Voir">
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => {
                setSelectedRecord(record);
                setViewVisible(true);
                addHistoryEntry(record.id, 'Viewed');
              }}
            />
          </Tooltip>
          <Tooltip title="Modifier">
            <Button
              type="link"
              style={{ color: "#f5b041" }}
              icon={<EditOutlined />}
              onClick={() => {
                setSelectedRecord(record);
                setEditVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title="Supprimer">
            <Popconfirm
              title="Êtes-vous sûr de vouloir supprimer cette catégorie ?"
              onConfirm={() => handleDelete(record.id)}
            >
              <Button type="link" danger icon={<DeleteOutlined />} />
            </Popconfirm>
          </Tooltip>
          <Tooltip title="Historique">
            <Button
              type="link"
              icon={<HistoryOutlined />}
              onClick={() => {
                setSelectedRecord(record);
                setHistoryVisible(true);
              }}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <Card
      bordered={false}
      title={<Typography.Title level={4}>Gestion des Catégories</Typography.Title>}
      extra={
        <Space>
          <Input
            placeholder="Rechercher"
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={e => setSearchText(e.target.value)}
            style={{ width: 200 }}
          />
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setCreateVisible(true)}
          >
            Ajouter
          </Button>
        </Space>
      }
    >
      <ProTable
        actionRef={actionRef}
        loading={loading}
        columns={columns}
        dataSource={searchText ? filteredData : data}
        rowKey="id"
        search={false}
        options={false}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
        }}
      />

      {/* Create Modal */}
      <Modal
        title="Nouvelle Catégorie"
        open={createVisible}
        onCancel={() => setCreateVisible(false)}
        footer={null}
        destroyOnClose
      >
        <CreateModal
          onCancel={() => setCreateVisible(false)}
          onSuccess={handleCreate}
        />
      </Modal>

      {/* Edit Modal */}
      <Modal
        title="Modifier Catégorie"
        open={editVisible}
        onCancel={() => setEditVisible(false)}
        footer={null}
        destroyOnClose
      >
        <EditModal
          record={selectedRecord}
          onCancel={() => setEditVisible(false)}
          onSuccess={handleEdit}
        />
      </Modal>

      {/* View Modal */}
      <Modal
        title={`Détails: ${selectedRecord?.name || ''}`}
        open={viewVisible}
        onCancel={() => setViewVisible(false)}
        footer={null}
      >
        {selectedRecord && (
          <div>
            <p><strong>Nom:</strong> {selectedRecord.name}</p>
            <p><strong>Type:</strong> {selectedRecord.type}</p>
            <p><strong>Créé le:</strong> {dayjs(selectedRecord.createdAt).format('LLL')}</p>
          </div>
        )}
      </Modal>

      {/* History Modal */}
      <Modal
        title={`Historique: ${selectedRecord?.name || 'Général'}`}
        open={historyVisible}
        onCancel={() => {
          setHistoryVisible(false);
          setSelectedRecord(null);
        }}
        footer={null}
        width={800}
      >
        {selectedRecord ? (
          <div>
            {selectedRecord.history?.map(item => (
              <div key={item.id} style={{ marginBottom: 16 }}>
                <p><strong>Action:</strong> {item.action}</p>
                <p><strong>Date:</strong> {dayjs(item.date).format('LLL')}</p>
                <p><strong>Par:</strong> {item.person}</p>
                <hr />
              </div>
            ))}
          </div>
        ) : (
          <div>
            <h4>Catégories supprimées</h4>
            {deletedData.map(item => (
              <div key={item.id} style={{ marginBottom: 16 }}>
                <p><strong>Nom:</strong> {item.name}</p>
                <p><strong>Dernière action:</strong> {item.history?.slice(-1)[0]?.action}</p>
                <hr />
              </div>
            ))}
          </div>
        )}
      </Modal>
    </Card>
  );
};

export default CategoryPage;