// src/redux/actions/dataActions.js
import axiosInstance from '../../apis/axiosInterceptor';
import { FETCH_POSTS_FAILURE, FETCH_POSTS_START, FETCH_POSTS_SUCCESS } from '../constants/postConstants';
import { hideLoader, showLoader } from './loadingActions';

  
  export const fetchDataStart = () => ({
    type: FETCH_POSTS_START,
  });
  
  export const fetchDataSuccess = (data) => ({
    type: FETCH_POSTS_SUCCESS,
    payload: data,
  });
  
  export const fetchDataFailure = (error) => ({
    type: FETCH_POSTS_FAILURE,
    payload: error,
  });
  
  export const fetchData = () => async (dispatch) => {
    dispatch(fetchDataStart());
    dispatch(showLoader())
    try {
      
      const response = await axiosInstance.get('/posts');
      console.log(response);
      
     
      dispatch(fetchDataSuccess(response.data));
      dispatch(hideLoader())
    } catch (error) {
      dispatch(fetchDataFailure(error.message));
      dispatch(hideLoader())
    }
  };
  