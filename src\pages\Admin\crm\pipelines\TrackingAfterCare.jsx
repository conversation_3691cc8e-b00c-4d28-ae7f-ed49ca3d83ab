import { useState } from "react";
import { ProFormSelect } from "@ant-design/pro-form";
import { <PERSON>Chart, Line, CartesianGrid, ResponsiveContainer, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from "recharts";
import { Card } from "antd";

const opportunities = [
  { label: "Opportunity A", value: "opportunityA" },
  { label: "Opportunity B", value: "opportunityB" },
  { label: "Opportunity C", value: "opportunityC" },
];

const aftercareData = {
  opportunityA: [
    { day: "Mon", followUps: 5 },
    { day: "Tue", followUps: 8 },
    { day: "Wed", followUps: 6 },
    { day: "Thu", followUps: 10 },
    { day: "Fri", followUps: 12 },
  ],
  opportunityB: [
    { day: "Mon", followUps: 3 },
    { day: "Tue", followUps: 7 },
    { day: "Wed", followUps: 9 },
    { day: "Thu", followUps: 4 },
    { day: "Fri", followUps: 11 },
  ],
  opportunityC: [
    { day: "Mon", followUps: 2 },
    { day: "Tue", followUps: 5 },
    { day: "Wed", followUps: 7 },
    { day: "Thu", followUps: 6 },
    { day: "Fri", followUps: 8 },
  ],
};

const OpportunityAftercare = () => {
  const [selectedOpportunity, setSelectedOpportunity] = useState("opportunityA");

  return (
    <Card title="Sélectionner une opportunité et voir le suivi">
      <ProFormSelect
        name="opportunity"
        label="Opportunité"
        options={opportunities}
        placeholder="Sélectionnez une opportunité"
        initialValue="opportunityA" 
        fieldProps={{ onChange: (value) => setSelectedOpportunity(value) }}
      />
      {selectedOpportunity && (
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={aftercareData[selectedOpportunity]}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="day" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Line type="monotone" dataKey="followUps" stroke="#52c41a" strokeWidth={2} />
          </LineChart>
        </ResponsiveContainer>
      )}
    </Card>
  );
};

export default OpportunityAftercare;
