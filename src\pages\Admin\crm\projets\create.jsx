import { ModalForm } from "@ant-design/pro-components";
import { Card,
  Col,
  DatePicker,
  Divider,
  Form,
  Input,
  InputNumber,
  Radio,
  Row,
  Select,
 
} from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";

function CreateProjet({ open, onCancel, onSuccess }) {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const handleSubmit = (values) => {};


  return (
    <ModalForm
      title={t("Ajouter un projet")}
      width="60%"
      form={form}
      open={open}
      modalProps={{
        style: { top: 20 },
      }}
      onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.save"),
          resetText: t("common.actions.cancel"),
        },
      }}
    >
      <Divider>Informations générale</Divider>
      <Card className="mt-2">
        <Row gutter={16}>
          <Col md={8} xs={24}>
            <Form.Item name={"responsable"} label={t("Responsable")}>
              <Select />
            </Form.Item>
          </Col>
          <Col md={4} xs={24}>
            <Form.Item name={"dg_fipa"} label={t(" ")}>
              <Radio>Important</Radio>
            </Form.Item>
          </Col>
          <Col md={4} xs={24}>
            <Form.Item name={"dg_fipa"} label={t(" ")}>
              <Radio>Inclure</Radio>
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item name={"dg_fipa"} label={t("Date MAJ")}>
              <DatePicker />
            </Form.Item>
          </Col>

          <Col md={8} xs={24}>
            <Form.Item name={"dg_fipa"} label={t(" ")}>
              <Radio>Idée de projet</Radio>
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item name={"dg_fipa"} label={t(" ")}>
              <Radio>Porjet en cours de réalisation</Radio>
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item name={"dg_fipa"} label={t(" ")}>
              <Radio> Projet entré en production </Radio>
            </Form.Item>
          </Col>

          <Col md={8} xs={24}>
            <Form.Item name={"dg_fipa"} label={t("Date catégotie IP")}>
              <DatePicker></DatePicker>
            </Form.Item>
          </Col>
        </Row>
      </Card>
      <Divider>Coordonnées en Tunisie</Divider>
      <Card className="mt-2">
        <Row gutter={16}>
          <Col md={24} xs={24}>
            <Form.Item name={""} label={t("RS en Tunisie")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item name={""} label={t("Contact")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name={""} label={t("Tél.")}>
              <DatePicker />
            </Form.Item>
          </Col>

          <Col md={12} xs={24}>
            <Form.Item name={""} label={t("Fax")}>
              <DatePicker />
            </Form.Item>
          </Col>
          <Col md={16} xs={24}>
            <Form.Item name={""} label={t("Adr en Tunisie")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={4} xs={24}>
            <Form.Item name={""} label={"CP"}>
              <Input />
            </Form.Item>
          </Col>

          <Col md={4} xs={24}>
            <Form.Item name={""} label={t("Ville")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name={""} label={t("Partenaire tunisien")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name={""} label={t("E-mail")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={24} xs={24}>
            <Form.Item name={""} label={t("Site web en Tunisie")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item
              name={""}
              label={t("Gouvernorat")}
              style={{
                display: "inline-block",
                width: "100%",
                margin: "0 8px 8px",
              }}
            >
              <Select />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name={""} label={t("Secteur")}>
              <Select />
            </Form.Item>
          </Col>
          <Col md={24} xs={24}>
            <Form.Item name={""} label={t("ZI")}>
              <Input></Input>
            </Form.Item>
          </Col>
        </Row>
      </Card>
      <Divider>Coordonnées à l'étranger</Divider>
      <Card className="mt-2">
        <Row gutter={16}>
          <Col md={24} xs={24}>
            <Form.Item
              name={""}
              label={t("RS Etranger")}
            >
              <Input />
            </Form.Item>
          </Col>
          <Col md={24} xs={24}>
            <Form.Item name={""} label={t("Adr à l'étranger")}>
              <Input.TextArea rows={3} />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item
              name={""}
              label={t("Tél./GSM")}
            >
              <Select />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item
              name={""}
              label={t("Fax")}
            >
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item
              name={""}
              label={t("Maison Mère")}
            >
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item
              name={""}
              label={t("E-mail")}
            >
              <Input />
            </Form.Item>
          </Col>
          <Col md={24} xs={24}>
            <Form.Item
              name={""}
              label={t("Site web à l'étranger")}
            >
              <Input />
            </Form.Item>
          </Col>
        
        </Row>
      </Card>
      <Divider>Origine du contact</Divider>
      <Card className="mt-2">
        <Row gutter={16}>
          <Col md={24} xs={24}>
            <Form.Item name={""} label={t("Action promotionnelle qui a généré le contact")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item name={""} label={t("Date du contact")}>
                <DatePicker />
            </Form.Item>
          </Col>

          <Col md={8} xs={24}>
            <Form.Item name={""} label={t("Cadre FIPA qui l'a contacté")}>
              <Input />
            </Form.Item>
          </Col>
           <Col md={8} xs={24}>
            <Form.Item name={""} label={t("Date 1ère visite")}>
              <DatePicker />
            </Form.Item>
          </Col>
            <Col md={12} xs={24}>
            <Form.Item name={""} label={t("Initiateur")}>
              <Select />
            </Form.Item>
          </Col>
           <Col md={12} xs={24}>
            <Form.Item name={""} label={t("Observation")}>
              <Input />
            </Form.Item>
          </Col>
             <Col md={8} xs={24}>
            <Form.Item name={""} label={t("Nature")}>
              <Select />
            </Form.Item>
          </Col>
             <Col md={8} xs={24}>
            <Form.Item name={""} label={t("Marché")}>
              <Select />
            </Form.Item>
          </Col>
           <Col md={8} xs={24}>
            <Form.Item name={""} label={t("Nationalité")}>
              <Select />
            </Form.Item>
          </Col>
           <Col md={16} xs={24}>
            <Form.Item name={""} label={t("Activité")}>
              <Input />
            </Form.Item>
          </Col>
            <Col md={8} xs={24}>
            <Form.Item name={""} label={t("%PE")}>
              <InputNumber />
            </Form.Item>
          </Col>
           <Col md={8} xs={24}>
            <Form.Item name={""} label={t("Invest. Prev.")}>
              <InputNumber />
            </Form.Item>
          </Col>
           <Col md={8} xs={24}>
            <Form.Item name={""} label={t("Empl. Prev.")}>
              <InputNumber />
            </Form.Item>
          </Col>
            <Col md={8} xs={24}>
            </Col>
          <Col md={8} xs={24}>
            <Form.Item name={""} label={t(" ")}>
            <Radio> Contacté en Tunisie par cadre FIPA </Radio>
            </Form.Item>
          </Col>
          <Col md={16} xs={24}>
            <Form.Item name={""} label={t("Occasion du contact")}>
              <Input.TextArea />
            </Form.Item>
          </Col>
        
        </Row>
      </Card>
 
    </ModalForm>
  );
}
export default CreateProjet;
