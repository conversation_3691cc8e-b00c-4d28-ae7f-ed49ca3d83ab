import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { default as api } from "@/apis/axiosInterceptor";

export const getProjectSteps: any = createAsyncThunk(
    "projectSteps/fetch",
    async (project_id, thunkAPI) => {
        try {
            let url = `/project-step?project_id=${project_id}`;
            const resp = await api.get(url);
            return resp.data;
        } catch (error) {
            return thunkAPI.rejectWithValue("something went wrong");
        }
    }
);

export const createProjectStep: any = createAsyncThunk(
    "projectSteps/create",
    async (data, thunkAPI) => {
        try {
            let url = `/project-step`;
            const resp = await api.post(url, data);
            return resp.data;
        } catch (error: any) {
            return thunkAPI.rejectWithValue(
                error.response?.data || "An error occurred."
            );
        }
    }
);

export const updateProjectStep: any = createAsyncThunk(
    "projectSteps/update",
    async (data: any, thunkAPI) => {
        try {
            let url = `/project-step/${data.id}`;
            const resp = await api.put(url, data);
            return resp.data;
        } catch (error: any) {
            return thunkAPI.rejectWithValue(
                error.response?.data || "An error occurred."
            );
        }
    }
);

export const deleteProjectStep: any = createAsyncThunk(
    "projectSteps/delete",
    async (id, thunkAPI) => {
        try {
            let url = `/project-step/${id}`;
            const resp = await api.delete(url);
            return resp.data;
        } catch (error: any) {
            return thunkAPI.rejectWithValue(
                error.response?.data || "An error occurred."
            );
        }
    }
);

export const closeProjectStep: any = createAsyncThunk(
    "project",
    async (data, thunkAPI) => {
        try {
            let url = `/project-step/close-step/${data['id']}`;
            const resp = await api.put(url, data);
            return resp.data;
        } catch (error: any) {
            return thunkAPI.rejectWithValue(
                error.response?.data || "An error occurred."
            );
        }
    }
);


