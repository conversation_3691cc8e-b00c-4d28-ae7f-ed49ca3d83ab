import { ModalForm } from "@ant-design/pro-components";
import {
  <PERSON><PERSON>,
  <PERSON>,
  Col,
  DatePicker,
  Form,
  Input,
  InputNumber,
  Popconfirm,
  Radio,
  Row,
  Select,
  Table,
} from "antd";

import TextArea from "antd/es/input/TextArea.js";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { companies, responsables } from "./data";

function EditDemandeForm({ open, onCancel, record }) {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const dateFormat = "YYYY-MM-DD";
  const [form] = Form.useForm();
  useEffect(() => {
    if (open) {
      form.setFieldsValue(record);
    }
  }, [open]);

  return (
    <ModalForm
      title={t("Modifier une demande")}
      form={form}
      open={open}
 
      modalProps={{
        style: { top: 20 },
      }}
      //   onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.edit"),
          resetText: t("common.actions.cancel"),
        },
      }}
    >
      <Card className="mt-5">
      <Row gutter={4} className="mt-2">
      <Col className="gutter-row" span={24}>
            <Form.Item
              name="entreprise_id"
              label={t("Entreprise")}
            
            >
             <Select placeholder="Sélectionnez une entreprise">
                {companies.map((type) => (
                  <Select.Option key={type.id} value={type.id}>
                    {type.nom}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col className="gutter-row" span={24}>
            <Form.Item
              name="date_demande"
              label={t("Date demande")}
            >
             <DatePicker style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Col className="gutter-row" span={24}>
            <Form.Item
              name="responsable_id"
              label={t("Reponsable")}
            //  rules={[{ required: true, message: "Ce champ est obligatoire" }]}
            >
             <Select >
                {responsables.map((type) => (
                  <Select.Option key={type.id} value={type.id}>
                    {type.nom}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
  
          <Col className="gutter-row" span={24}>
            <Form.Item
              name="description"
              label="Description"
            //  rules={[{ required: true, message: "Ce champ est obligatoire" }]}
            >
              <TextArea rows={4} placeholder="" />
            </Form.Item>
          </Col>
        </Row>
      </Card>
    </ModalForm>
  );
}
export default EditDemandeForm;
