import { createAsyncThunk } from "@reduxjs/toolkit";
import { default as api } from "@/apis/axiosInterceptor";

export const createProjectStepProgress: any = createAsyncThunk(
    "projectStepTimelines/create",
    async (data, thunkAPI) => {
        try {
            let url = `/project-step-timeline`;
            const resp = await api.post(url, data);
            return resp.data;
        } catch (error: any) {
            return thunkAPI.rejectWithValue(
                error.response?.data || "An error occurred."
            );
        }
    }
);


