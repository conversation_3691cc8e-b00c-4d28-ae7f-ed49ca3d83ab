import { useState, useRef, useEffect } from "react";
import { ProTable } from "@ant-design/pro-components";
import {
  Button,
  Popconfirm,
  Space,
  Input,
  Tooltip,
  Typography,
  Card,

  Form,
  Tag,
} from "antd";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  SearchOutlined,
  InteractionOutlined,
} from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import {
  group_pays
} from "./data";
import CreateGroupPaysForm from "./create";
import ShowGroupPaysForm from "./view";
import EditGroupPaysForm from "./edit";






function GroupPays() {
  const tableRef = useRef();
  const { t } = useTranslation();
  const [tableParams, setTableParams] = useState({}); // Paramètres de recherche et de filtrage
  const [isLoading, setIsLoading] = useState(false); // Indique si les données sont en cours de chargement
  const [data, setData] = useState(group_pays); // Données affichées dans la table
  const [total, setTotal] = useState(data.length); // Nombre total de lignes affichées
  const [pageNumber, setPageNumber] = useState(1); // Numéro de page actuelle
  const [dataSource, setDataSource] = useState([]);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [viewModalVisible, setViewModalVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState(null);
  const [viewingRecord, setViewingRecord] = useState(null);

  const [form] = Form.useForm();
  // Déclenchement de la recherche avec un délai (700ms)
  const handleSearch = (e) => {
    const searchValue = e.target.value; // Récupération de la valeur de recherche
    setTableParams({ ...tableParams, search: searchValue }); // Mise à jour des paramètres
  };


  const columns = [
  
    {
      title: t("Code"),
      dataIndex: "code",
      key: "code",
      ellipsis: true,
      order: 1,
      render: (text) => {
        return (
          <Typography.Text ellipsis={{ tooltip: text }}>{text}</Typography.Text>
        );
      },
    },  
   
        {
      title: t("Libelle"),
      dataIndex: "libelle",
      key: "libelle",
      ellipsis: true,
      order: 1,
      render: (text) => {
        return (
          <Typography.Text ellipsis={{ tooltip: text }}>{text}</Typography.Text>
        );
      },
    },

    {
      title: t("common.actions.title"),
      key: "action",
      align: "center",
      sorter: false,
      search: false,
      render: (_, record) => (
        <Space size="auto">
          <Tooltip title={t("common.actions.view")}>
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => {
                setViewingRecord(record);
                setViewModalVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title={t("common.actions.edit")}>
            <Button
              type="link"
              style={{ color: "#f5b041" }}
              icon={<EditOutlined />}
              onClick={() => {
                setEditingRecord(record);
                setEditModalVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title={t("common.actions.delete")}>
            <Popconfirm
              title={t("common.messages.confirm_delete")}
              onConfirm={() => {
                const newDataSource = dataSource.filter(
                  (item) => item.key !== record.key
                );
                setDataSource(newDataSource);
              }}
            >
              <Button
                type="link"
                style={{ color: "#ec7063" }}
                icon={<DeleteOutlined />}
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];
 


  const onFinish = (values) => {
    setTansferLoading(true);
    setTimeout(() => {
      setTansferLoading(false);
      setIsModalOpen(false);
    }, [1000]);
  };

  return (
    <>
      <Card
        title={
          <Typography.Title level={4}>{t("Liste des groupes")}</Typography.Title>
        }
        extra={
          <Space>
            {/* Champ de recherche */}
            <Input
              size="large"
              placeholder={t("common.search")}
              suffix={<SearchOutlined style={{ color: "#bfbfbf" }} />}
              //    onChange={debouncedOnChange}
            />
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setCreateModalVisible(true)}
            >
              {t("Ajouter")}
            </Button>
          </Space>
        }
      >
        <ProTable
          actionRef={tableRef} // Référence pour accéder aux actions
          search={{
            searchText: t("common.filter"),
            labelWidth: "auto",
            defaultCollapsed: false,
          }}
          loading={isLoading}
          columns={columns}
          dataSource={data}
          rowKey="id"
          params={tableParams}
          scroll={{ x: "max-content" }}
          options={false}
          request={async (params) => {
            setIsLoading(true); // Démarrage du chargement
            console.log(params);
            setTimeout(() => {
              // Simule une requête serveur
              setIsLoading(false);
            }, 500);
            return {
              // Retourne les données
              data: data,
              success: true,
            };
          }}
          pagination={{
            showSizeChanger: true, // Permet de modifier la taille des pages
            defaultPageSize: 3, // Taille de page par défaut
            total: total, // Nombre total de lignes
            onChange: (page) => setPageNumber(page), // Fonction de changement de page
          }}
        />
       {createModalVisible && (
          <CreateGroupPaysForm
            open={createModalVisible}
            onCancel={() => setCreateModalVisible(false)}
            onSuccess={(newDomain) => {
              setDataSource([...dataSource, newDomain]);
              setCreateModalVisible(false);
            }}
          />
        )}
         {viewModalVisible && (
                  <ShowGroupPaysForm
                    open={viewModalVisible}
                    record={viewingRecord}
                    onCancel={() => {
                      setViewModalVisible(false);
                      setViewingRecord(null);
                    }}
                  />
                )}
         {editModalVisible && (
                  <EditGroupPaysForm
                    open={editModalVisible}
                    record={editingRecord}
                    onCancel={() => {
                      setEditModalVisible(false);
                      setEditingRecord(null);
                    }}
                  />
                )}
              
      </Card>
 
    </>
  );
}
export default GroupPays;
