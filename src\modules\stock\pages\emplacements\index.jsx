import { useState, useRef, useEffect } from "react";
import { ProTable } from "@ant-design/pro-components";
import {
  Button,
  Popconfirm,
  Space,
  Input,
  Tooltip,
  Typography,
  Card,
} from "antd";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { emplacements } from "./data";
import useDebounce from "@hooks/useDebounce";
import AddLocationModal from "./create";
import ShowLocationModal from "./view";
import EditLocationModal from "./edit";

function Emplacements() {
  const { t } = useTranslation();
  const tableRef = useRef();
  const [dataSource, setDataSource] = useState(emplacements);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [location, setLocation] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [tableParams, setTableParams] = useState([]);
  const [total, setTotal] = useState(dataSource.length);
  const [filteredData, setFilteredData] = useState(dataSource);
  const [pageNumber, setPageNumber] = useState(1);
  const [showEditModal, setShowEditModal] = useState(false);

  const handleSearchData = (params) => {
    const filtered = dataSource.filter((item) => {
      return (
        (!params.code ||
          item.code?.toLowerCase().includes(params.code.toLowerCase())) &&
        (!params.libelle ||
          item.libelle?.toLowerCase().includes(params.libelle.toLowerCase())) &&
        (!params.depot ||
          item.depot?.toLowerCase().includes(params.depot.toLowerCase()))
      );
    });

    setFilteredData(filtered); // Update table with filtered results
  };

  const columns = [
    {
      title: t("emplacements.fields.code"),
      dataIndex: "code",
      key: "code",
      filters: dataSource.map((item) => ({
        text: item.code,
        value: item.code,
      })),
      onFilter: (value, record) => record.code === value,
      render: (text, record) => {
        return (
          <Typography.Text
            ellipsis={{ tooltip: text }}
            style={{ maxWidth: "auto" }}
          >
            {text}
          </Typography.Text>
        );
      },
    },
    {
      title: t("emplacements.fields.libelle"),
      dataIndex: "libelle",
      key: "libelle",
    },
    {
      title: t("emplacements.fields.depot"),
      dataIndex: "depot_id",
      key: "depot",
      filters: dataSource.map((item) => ({
        text: item.depot,
        value: item.depot,
      })),
      onFilter: (value, record) => record.depot === value,
    },
    {
      title: t("common.actions.title"),
      key: "action",
      align: "center",
      sorter: false,
      search: false,
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title={t("common.actions.view")}>
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => {
                console.log(record);

                setLocation(record);
                setShowViewModal(true);
              }}
            />
          </Tooltip>
          <Tooltip title={t("common.actions.edit")}>
            <Button
              type="link"
              style={{ color: "#f5b041" }}
              icon={<EditOutlined />}
              onClick={() => {
                setShowEditModal(true);
                setLocation(record);
              }}
            />
          </Tooltip>
          <Tooltip title={t("common.actions.delete")}>
            <Popconfirm
              title={t("common.messages.confirm_delete")}
              onConfirm={() => {
                setIsLoading(true);
                const newDataSource = dataSource.filter(
                  (item) => item.key !== record.key
                );
                setDataSource(newDataSource);
                setFilteredData(newDataSource);
                setIsLoading(false);
              }}
            >
              <Button
                type="link"
                style={{ color: "#ec7063" }}
                icon={<DeleteOutlined />}
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  const handleSearch = (e) => {
    const searchValue = e.target.value;
    setTableParams({ ...tableParams, search: searchValue });
  };

  const debouncedOnChange = useDebounce(handleSearch, 700);

  useEffect(() => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  }, [dataSource, filteredData]);

  return (
    <Card
      bordered
      title={
        <Typography.Title level={4}>{t("emplacements.list")}</Typography.Title>
      }
      extra={
        <Space>
          <Input
            size="large"
            placeholder={t("common.search")}
            suffix={<SearchOutlined style={{ color: "#bfbfbf" }} />}
            onChange={debouncedOnChange}
          />
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setShowAddModal(true)}
          >
            {t("emplacements.add")}
          </Button>
        </Space>
      }
    >
      <ProTable
        actionRef={tableRef}
        loading={isLoading}
        columns={columns}
        dataSource={filteredData}
        rowKey="key"
        search={{
          searchText: t("common.filter"),
          labelWidth: "auto",
          defaultCollapsed: false,
        }}
        options={false}
        params={tableParams}
        scroll={{ x: "max-content" }}
        onSubmit={handleSearchData}
        onReset={() => handleSearchData({})}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: 5,
          total: total,
          onChange: (page) => setPageNumber(page),
        }}
      />
      <AddLocationModal
        open={showAddModal}
        onCancel={() => setShowAddModal(false)}
      />
      <ShowLocationModal
        open={showViewModal}
        onCancel={() => {
          setShowViewModal(false);
          setLocation(null);
        }}
        location={location}
      />
      <EditLocationModal
        open={showEditModal}
        onCancel={() => {
          setShowEditModal(false);
          setLocation(null);
        }}
        location={location}
      />
    </Card>
  );
}

export default Emplacements;
