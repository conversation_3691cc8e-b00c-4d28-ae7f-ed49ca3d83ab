import { Form, Input, Select, <PERSON><PERSON>icker, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Checkbox } from "antd";
import contacts from "./datas";
import { useState } from "react";


const CreateContact = ({ onCancel, setDataSource }) => {
  const [listContacts, setListContacts] = useState([]);
  const [selectedContact, setSelectedContact] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [form] = Form.useForm();

  const handleSubmit = async (values) => {
    try {
      const newKey = Date.now().toString();
      const newContact = {
        key: newKey,
        ...values,
      };

      const updatedData = [...contacts, newContact];
      setDataSource(updatedData);

      message.success("Contact créé avec succès!");
      onCancel();
    } catch (error) {
      console.error(error);
      message.error("Erreur lors de la création du contact");
    }
  };

  const options = [
    { label: "Entreprise A", value: "Entreprise A" },
    { label: "Entreprise B", value: "Entreprise B" },
    { label: "Entreprise C", value: "Entreprise C" },
  ];

  const getEntrepriseContacts = (id) => {
    const newContacts = [
      {
        key: Date.now() + 1, 
        nom: "Nom Contact " + (id + 1),
        prenom: "Prenom Contact " + (id + 1),
        email: "Email Contact " + (id + 1),
        telephone: "TEL Contact " + (id + 1),
      },
      {
        key: Date.now() + 2,
        nom: "Nom Contact " + (id + 2),
        prenom: "Prenom Contact " + (id + 2),
        email: "Email Contact " + (id + 2),
        telephone: "TEL Contact " + (id + 2),
      },
      {
        key: Date.now() + 3,
        nom: "Nom Contact " + (id + 3),
        prenom: "Prenom Contact " + (id + 3),
        email: "Email Contact " + (id + 3),
        telephone: "TEL Contact " + (id + 3),
      },
  
    ];
    setListContacts(newContacts);
  };

  const openModal = (contact) => {
    setSelectedContact(contact);
    console.log(selectedContact);
    setIsEditing(false);
    form.setFieldsValue(contact);
    setIsModalOpen(true);
  };

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = () => {
    form.validateFields().then((values) => {
      console.log('values')
      setSelectedContact((prev) => ({ ...prev, ...values }));
      setIsEditing(false);
      message.success("Modifications enregistrées !");
    });
  };

  return (
    <>
    
    <Form form={form} layout="vertical" >
      <Divider>Informations Générales</Divider>
      <Form.Item label="Date Arrivée" name="dateArrivee">
        <DatePicker style={{ width: "100%" }} />
      </Form.Item>
      <Form.Item name="demandeSatisfaite" valuePropName="checked">
        <Checkbox>Demande satisfaite</Checkbox>
      </Form.Item>
      <Form.Item label="Date Réponse" name="dateReponse">
        <DatePicker style={{ width: "100%" }} />
      </Form.Item>
      <Form.Item label="R.S. / Promoteur" name="promoteur">
        <Input placeholder="Saisir la raison sociale ou le nom du promoteur" />
      </Form.Item>
      <Form.Item label="Adresse" name="adresse">
        <Input placeholder="Saisir l'adresse du demandeur" />
      </Form.Item>
      <Form.Item label="Pays" name="pays">
        <Select>
          <Option value="tunisie">Tunisie</Option>
          <Option value="france">France</Option>
        </Select>
      </Form.Item>
      <Form.Item label="Secteur" name="secteur">
        <Select>
          <Option value="industrie">Industrie</Option>
          <Option value="services">Services</Option>
        </Select>
      </Form.Item>
      <Form.Item label="Email / Site web" name="emailSiteWeb">
        <Input placeholder="Saisir l'email et/ou le site web du demandeur" />
      </Form.Item>
      <Form.Item label="Superficie Terrain" name="superficieTerrain">
        <Input placeholder="Saisir la superficie du terrain sollicitée" />
      </Form.Item>
      <Form.Item label="Superficie Local" name="superficieLocal">
        <Input placeholder="Saisir la superficie du local sollicitée" />
      </Form.Item>
      <Form.Item label="Gouvernorats" name="gouvernorats">
        <Select mode="multiple" placeholder="Choisir jusqu'à 4 gouvernorats">
          <Option value="tunis">Tunis</Option>
          <Option value="sousse">Sousse</Option>
          <Option value="sfax">Sfax</Option>
          <Option value="nabeul">Nabeul</Option>
        </Select>
      </Form.Item>
      <Form.Item label="Cadre FIPA" name="cadreFipa">
        <Select>
          <Option value="cadre1">Cadre 1</Option>
          <Option value="cadre2">Cadre 2</Option>
        </Select>
      </Form.Item>
      <Form.Item label="Source demande" name="sourceDemande">
        <Select>
          <Option value="mdci">MDCI</Option>
          <Option value="investisseur">Investisseur</Option>
          <Option value="direction">Direction</Option>
          <Option value="unite">Unité</Option>
        </Select>
      </Form.Item>
      <Form.Item label="Observation demande" name="observationDemande">
        <Input.TextArea placeholder="Indiquer une observation concernant la demande" rows={3} />
      </Form.Item>
      <Form.Item label="Observation réponse" name="observationReponse">
        <Input.TextArea placeholder="Indiquer une observation concernant la réponse" rows={3} />
      </Form.Item>
      
      <Divider />
      <Form.Item style={{ textAlign: 'right' }}>
        <Button type="primary" htmlType="submit">Enregister</Button>
      </Form.Item>
    </Form>
    </>
  );
};

export default CreateContact;
