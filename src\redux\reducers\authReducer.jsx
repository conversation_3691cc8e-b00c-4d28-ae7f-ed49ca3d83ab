// src/redux/reducers/dataReducer.js

import { FETCH_LOGIN_FAILURE, FETCH_LOGIN_START, FETCH_LOGIN_SUCCESS, FETCH_REGISTER_FAILURE, FETCH_REGISTER_START, FETCH_REGISTER_SUCCESS  } from "../constants/authConstants";

  
  const initialState = {
    user: null,
    token : null,
    error: null,
    loggedIn : false,
  };
  
  const authReducer = (state = initialState, action) => {
    switch (action.type) {
      case FETCH_LOGIN_START:
        return { ...state, isLoading: true, error: null };
      case FETCH_LOGIN_SUCCESS:
        return { ...state, isLoading: false,loggedIn : true, user: action.payload.user , token: action.payload.token};
      case FETCH_LOGIN_FAILURE:
        return { ...state, isLoading: false, error: action.payload };
      case FETCH_REGISTER_START:
            return { ...state, isLoading: true, error: null };
      case FETCH_REGISTER_SUCCESS:
            return { ...state, isLoading: false, user: action.payload , token: action.payload};
       case FETCH_REGISTER_FAILURE:
            return { ...state, isLoading: false, error: action.payload };
      default:
        return state;
    }
  };
  
  export default authReducer;
  