
import { ModalForm } from "@ant-design/pro-components";
import {  Card, Col, DatePicker, Form, Input, Row, Select, Upload } from "antd";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

function ResultatForm({ open, onCancel, record }) {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const props = {
    name: "file",
     accept :".pdf" ,
    maxCount :1,
    action: "https://660d2bd96ddfa2943b33731c.mockapi.io/api/upload",
    headers: {
      authorization: "authorization-text",
    },
    onChange(info) {
      if (info.file.status !== "uploading") {
        console.log(info.file, info.fileList);
      }
      if (info.file.status === "done") {
        message.success(`${info.file.name} file uploaded successfully`);
      } else if (info.file.status === "error") {
        message.error(`${info.file.name} file upload failed.`);
      }
    },
  };
  return (
    <ModalForm
      title={t("Evaluation de l’action et reconduction")}
      form={form}
      open={open}
  
      modalProps={{
        style: { top: 20 },
      }}
      //   onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.save"),
          resetText: t("common.actions.cancel"),
        },
      }}
    >
          <Card className="mt-2">
        <Row gutter={16}>
          <Col md={12} xs={24}>
            <Form.Item
              name={""}
              label={t("Evaluation")}
            >
              <Select />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item
              name={""}
              label={t("Reconduction")}
            >
              <Select />
            </Form.Item>
          </Col>
          <Col md={24} xs={24}>
            <Form.Item
              name={""}
              label={t("Commentaires spécifiques")}
            >
              <Input.TextArea />
            </Form.Item>
          </Col>
        </Row>
      </Card>

    </ModalForm>
  );
}
export default ResultatForm;
