import {
  <PERSON>,
  <PERSON>,
  <PERSON>,
  But<PERSON>,
  Descriptions,
  Avatar,
  Space,
  Form,
  Input,
  Tag,
  Typography,
} from "antd";

import { EyeTwoTone, EyeInvisibleOutlined } from "@ant-design/icons";
import React, { useEffect, useState } from "react";

import profilavatar from "../../../assets/images/face.jpg";
import { useTranslation } from "react-i18next";

function Profile() {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [buttonLoading, setButtonLoading] = useState(false);
  const [organization, setOrganization] = useState("");
  const items = [
    {
      key: "1",
      label: t("profile.user_fields.username"),
      children: "<PERSON><PERSON> ben foulen",
    },
    {
      key: "2",
      label: t("profile.user_fields.email"),
      children: "<EMAIL>",
    },
    {
      key: "3",
      label: t("profile.user_fields.organization"),
      children: "Organisation",
    },
    {
      key: "4",
      label: t("profile.user_fields.roles"),
      children: (
        <>
          {" "}
          <Tag>Admin</Tag> <Tag>Manager</Tag>{" "}
        </>
      ),
    },
  ];
  const handleSubmit = (values) => {
    setButtonLoading(true);
  };

  useEffect(() => {}, []);

  return (
    <>
      <div className="sign-up-header"></div>
      <Card
        className="card-profile-head py-3"
        styles={{ body: { display: "none" } }}
        title={
          <Space>
            <Avatar size={74} shape="square" src={profilavatar} />
            <div className="avatar-info">
              <h4 className="font-semibold m-0">Flen ben foulen</h4>
              <span className="text-muted"><EMAIL></span>
            </div>
          </Space>
        }
      ></Card>

      <Row gutter={[24, 24]}>
        <Col md={12} xs={24} style={{ backgroundColor: "transparent" }}>
          <Card bordered={true}>
            <Typography.Title level={4}>
              {t("profile.personal_information_title")}
            </Typography.Title>
            <br/>
            <Descriptions
              className="profile-descriptions"
              items={items}
              column={{ md: 1, lg: 1, xxl: 1, sm: 1, xl: 1, xs: 1 }}
            />
          </Card>
        </Col>
        <Col md={12} xs={24} style={{ backgroundColor: "transparent" }}>
          <Card bordered={true}>
            <Typography.Title level={4}>
              {t("profile.change_password_title")}
            </Typography.Title>
            <br/>
            <Form
              name="basic"
              layout="vertical"
              form={form}
              initialValues={{ remember: true }}
              onFinish={handleSubmit}
            >
              <Form.Item
                name="current_password"
                label={t("profile.form.current_password_label")}
                rules={[
                  {
                    required: true,
                    message: t("profile.form.validation.current_password_required"),
                  },
                ]}
              >
                <Input.Password
                  iconRender={(visible) =>
                    visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
                  }
                />
              </Form.Item>
              <Form.Item
                name="new_password"
                label={t("profile.form.new_password_label")}
                rules={[
                  {
                    required: true,
                    message: t("profile.form.validation.new_password_required"),
                  },
                  {
                    min: 8,
                    message: t("profile.form.validation.new_password_min_length"),
                  },
                  {
                    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[+-.!@\$%\^&\*()])/,
                    message: t("profile.form.validation.new_password_pattern"),
                  },
                ]}
              >
                <Input.Password
                  iconRender={(visible) =>
                    visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
                  }
                />
              </Form.Item>
              <Form.Item
                name="new_password_confirmed"
                label={t("profile.form.confirm_password_label")}
                rules={[
                  {
                    required: true,
                    message: t("profile.form.validation.confirm_password_required"),
                  },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!value || getFieldValue("new_password") === value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(
                        new Error(
                          t("profile.form.validation.confirm_password_mismatch")
                        )
                      );
                    },
                  }),
                ]}
              >
                <Input.Password
                  iconRender={(visible) =>
                    visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
                  }
                />
              </Form.Item>
              <Form.Item>
                <Button
                  style={{ width: "100%" }}
                  type="primary"
                  htmlType="submit"
                  loading={buttonLoading}
                >
                  {t("profile.form.submit_button")}
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </Col>
      </Row>
    </>
  );
}

export default Profile;
