import {
  Check<PERSON><PERSON>cleOutlined,
  Check<PERSON><PERSON>cleTwoTone,
  PlusCircleFilled,
  UploadOutlined,
} from "@ant-design/icons";
import { ModalForm } from "@ant-design/pro-components";
import {
  Button,
  Card,
  Checkbox,
  Col,
  DatePicker,
  Divider,
  Form,
  Input,
  message,
  Modal,
  Popover,
  Radio,
  Row,
  Select,
  Steps,
  Upload,
} from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { organismes, pays, secteurs } from "./data";

function CreateVisite({ open, onCancel, onSuccess ,show  }) {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const handleSubmit = (values) => {};
  const [showMotifModal, setShowMotifModal] = useState(false);
  const props = {
    name: "file",
    accept: ".pdf",
    maxCount: 1,
    action: "https://660d2bd96ddfa2943b33731c.mockapi.io/api/upload",
    headers: {
      authorization: "authorization-text",
    },
    onChange(info) {
      if (info.file.status !== "uploading") {
        console.log(info.file, info.fileList);
      }
      if (info.file.status === "done") {
        message.success(`${info.file.name} file uploaded successfully`);
      } else if (info.file.status === "error") {
        message.error(`${info.file.name} file upload failed.`);
      }
    },
  };
  return (
    <ModalForm
      title={t("Ajouter une visite")}
      form={form}
      open={open}
      modalProps={{
        style: { top: 20 },
      }}
      onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.save"),
          resetText: t("common.actions.cancel"),
        },
      }}
      
    >
      <Card className="mt-2">
        <Row gutter={16}>
         
          <Col md={12} xs={24}>
            <Form.Item name={""} label={t("")}>
              <Checkbox disabled={show}> Encadré avec programme </Checkbox>
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name={""} label={t("")}>
              <Checkbox disabled={show} > Entreprise importante </Checkbox>
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name={"organisme_id"} label={t("Initiateur")}>
              <Select disabled={show} options={organismes.map((item) => ({
                  value: item.id,
                  label: item.nom,
                }))} />
            </Form.Item>
          </Col>
          <Col md={6} xs={24}>
            <Form.Item name={"nb_visite"} label={t("Nombre de visites")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={6} xs={24}>
           <Form.Item name={"date_contact"} label={t("Date du contact")}>
              <DatePicker />
            </Form.Item>
          </Col>
          <Col md={24} xs={24}>
            <Form.Item name={"raison_social"} label={t("Raison sociale")}>
              <Input />
            </Form.Item>
          </Col>
           <Col md={12} xs={24}>
            <Form.Item name={"responsable"} label={t("Responsable")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name={"fonction"} label={t("Fonction")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name={"pays_id"} label={t("Nationalité")}>
              <Select  options={pays.map((item) => ({
                  value: item.id,
                  label: item.nom,
                }))}/>
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name={"secteur_id"} label={t("Secteur")}>
              <Select options={secteurs.map((item) => ({
                  value: item.id,
                  label: item.secteur,
                }))} />
            </Form.Item>
          </Col>
          <Col md={24} xs={24}>
            <Form.Item name={"activite"} label={t("Activité")}>
              <Input.TextArea />
            </Form.Item>
          </Col>
          <Col md={24} xs={24}>
            <Form.Item name={"adresse"} label={t("Adresse")}>
              <Input />
            </Form.Item>
          </Col>
           <Col md={12} xs={24}>
            <Form.Item name={"tel"} label={t("Téléphone")}>
              <Input />
            </Form.Item>
          </Col>
           <Col md={12} xs={24}>
            <Form.Item name={"fax"} label={t("Fax")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name={"email"} label={t("E-mail")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name={"site_web"} label={t("Site web")}>
              <Input />
            </Form.Item>
          </Col>
        </Row>
    
      </Card>
    </ModalForm>
  );
}
export default CreateVisite;
