// datas.js
export const tasks = [
  {
    key: 1,
    type: "call",
    title: "<PERSON><PERSON><PERSON> le client Dupont",
    description: "<PERSON>pel pour confirmation de commande",
    startDate: "01/06/2025 14:00",
    dueDate: "01/06/2025 15:00",
    relatedTo: "<PERSON><PERSON>",
    owners: ["<PERSON>"],
    status: "completed",
    notes: "Client a confirmé sa commande, à préparer pour livraison",
  },
  {
    key: 2,
    type: "meeting",
    title: "Réunion équipe commerciale",
    description: "Point sur les objectifs du trimestre",
    startDate: "03/06/2025 09:30",
    dueDate: "03/06/2025 11:00",
    relatedTo: "Équipe Commerciale",
    owners: ["<PERSON>", "<PERSON>"],
    status: "planned",
    notes: "Préparer les slides de performance",
  },
  {
    key: 3,
    type: "email",
    title: "Envoyer proposition commerciale",
    description: "Proposition pour le projet X",
    startDate: "28/05/2025 10:00",
    dueDate: "30/05/2025 18:00",
    relatedTo: "Société ABC",
    owners: ["<PERSON><PERSON>"],
    status: "overdue",
    notes: "Relancer le client après envoi",
  },
  {
    key: 4,
    type: "todo",
    title: "Mettre à jour la base CRM",
    description: "Vérifier les coordonnées des clients",
    startDate: "04/06/2025 10:00",
    dueDate: "04/06/2025 12:00",
    relatedTo: "Base de données",
    owners: ["Thomas Leroy"],
    status: "planned",
    notes: "",
  },
  {
    key: 5,
    type: "note",
    title: "Notes sur le nouveau prospect",
    description: "Informations à retenir",
    startDate: "02/06/2025 16:00",
    dueDate: "02/06/2025 16:30",
    relatedTo: "Prospect XYZ",
    owners: ["Sophie Bernard"],
    status: "completed",
    notes: "Intéressé par nos solutions premium",
  },
];