import React from 'react';
import { Form, Input, Button, Select, DatePicker } from 'antd';
import dayjs from 'dayjs';

const { TextArea } = Input;

const InteractionEdit = ({ initialValues, onUpdate }) => {
  const [form] = Form.useForm();

  const handleFinish = (values) => {
    onUpdate({
      ...initialValues,
      ...values,
      date: values.date.format('YYYY-MM-DD'),
    });
  };

  return (
    <Form layout="vertical" form={form} initialValues={{
      ...initialValues,
      date: dayjs(initialValues.date)
    }} onFinish={handleFinish}>
      <Form.Item name="type" label="Type" rules={[{ required: true }]}> 
        <Select>
          <Select.Option value="Événement">Événement</Select.Option>
          <Select.Option value="Discussion">Discussion</Select.Option>
          <Select.Option value="Engagement">Engagement</Select.Option>
        </Select>
      </Form.Item>

      <Form.Item name="date" label="Date" rules={[{ required: true }]}> 
        <DatePicker style={{ width: '100%' }} />
      </Form.Item>

      <Form.Item name="diasporaProfile" label="Profil Diaspora" rules={[{ required: true }]}> 
        <Input />
      </Form.Item>

      <Form.Item name="object" label="Objet" rules={[{ required: true }]}> 
        <Input />
      </Form.Item>

      <Form.Item name="notes" label="Notes"> 
        <TextArea rows={3} />
      </Form.Item>

      <Form.Item name="nextSteps" label="Prochaines Étapes"> 
        <TextArea rows={2} />
      </Form.Item>

      <Form.Item>
        <Button type="primary" htmlType="submit">Modifier</Button>
      </Form.Item>
    </Form>
  );
};

export default InteractionEdit;

