import dayjs from "dayjs";

const companies = [
  {
    id: 1,
    code_fipa: "100173TF01",
    code_douane: "100173T",
    code_ins: "",
    cod_api: "",
    code_cnss: "",
    matricule_fiscal: "0447037S",
    raison_social: "TechNova Solutions",
    date_mise_a_jour: dayjs("2025-01-10"),
    category_id: 1, // Industrie
    secteur_id: 1,
    activite: "Autres",
    promoteur: "xxxxxxxx",
    responsable: "xxxxxxxx",
    adresse_siege: "adresse siege",
    adresse_usine: "adresse usine",
    code_postal: 2027,
    gouvernorat_id: 1,
    delegation_id: 2,
    tel: 20202020,
    nationalite_id: 1,
    date_maj: dayjs("2025-01-20"),
    produits: "Produits électroniques",
    investissement: 30000,
    inv_reel: 53850,
    effectif_total: 10,
    date_entree_production: dayjs("2020-01-20"),
  },
  {
    id: 2,
    code_fipa: "200274AG02",
    code_douane: "200274A",
    code_ins: "",
    cod_api: "",
    code_cnss: "",
    matricule_fiscal: "0558021A",
    raison_social: "AgriGrow",
    date_mise_a_jour: dayjs("2025-02-15"),
    category_id: 2, // Agriculture
    secteur_id: 3,
    activite: "Production céréalière",
    promoteur: "YYYYYYYY",
    responsable: "YYYYYYYY",
    adresse_siege: "Ferme centrale, région nord",
    adresse_usine: "",
    code_postal: 3045,
    gouvernorat_id: 3,
    delegation_id: 5,
    tel: 30303030,
    nationalite_id: 1,
    date_maj: dayjs("2025-02-25"),
    produits: "Blé, Orge, Maïs",
    investissement: 150000,
    inv_reel: 180000,
    effectif_total: 50,
    date_entree_production: dayjs("2018-07-10"),
  },
  {
    id: 3,
    code_fipa: "300375TC03",
    code_douane: "300375T",
    code_ins: "",
    cod_api: "",
    code_cnss: "",
    matricule_fiscal: "0669032T",
    raison_social: "EcoEnergy",
    date_mise_a_jour: dayjs("2025-03-05"),
    category_id: 11, // Énergie & Environnement
    secteur_id: 2,
    activite: "Énergies renouvelables",
    promoteur: "ZZZZZZZZ",
    responsable: "ZZZZZZZZ",
    adresse_siege: "Zone industrielle, ville X",
    adresse_usine: "Parc solaire, région sud",
    code_postal: 4090,
    gouvernorat_id: 5,
    delegation_id: 8,
    tel: 50505050,
    nationalite_id: 1,
    date_maj: dayjs("2025-03-10"),
    produits: "Panneaux solaires, Éoliennes",
    investissement: 500000,
    inv_reel: 600000,
    effectif_total: 200,
    date_entree_production: dayjs("2016-05-15"),
  },
  {
    id: 4,
    code_fipa: "400476TE04",
    code_douane: "400476T",
    code_ins: "",
    cod_api: "",
    code_cnss: "",
    matricule_fiscal: "0779043E",
    raison_social: "MediCare Pharma",
    date_mise_a_jour: dayjs("2025-04-12"),
    category_id: 6, // Santé
    secteur_id: 1,
    activite: "Fabrication de médicaments",
    promoteur: "AAAAAAAA",
    responsable: "AAAAAAAA",
    adresse_siege: "Quartier médical, ville Y",
    adresse_usine: "Laboratoire central, ville Y",
    code_postal: 6001,
    gouvernorat_id: 7,
    delegation_id: 9,
    tel: 60606060,
    nationalite_id: 1,
    date_maj: dayjs("2025-04-15"),
    produits: "Antibiotiques, Vaccins",
    investissement: 800000,
    inv_reel: 920000,
    effectif_total: 500,
    date_entree_production: dayjs("2014-09-20"),
  },
];
const activites = [
  {
    id: 1,
    code : "000000" ,
    libelle : "Secteur des industries diverses" 
  } ,
  {
    id: 2,
   code: "000001",
    libelle: "Secteur de la santé"
  },
  {
    id: 3,
   code: "000002",
    libelle: "Secteur de la technologie"
  },
  {
    id: 4,
   code: "000003",
    libelle: "Secteur de l'agriculture"
  },
  {
    id: 5,
   code: "000004",
    libelle: "Secteur de l'éducation"
  },
  {
    id: 6,
   code: "000005",
    libelle: "Secteur de la finance"
  },
  {
    id: 7,
   code: "000006",
    libelle: "Secteur du commerce"
  },
  {
    id: 8,
   code: "000007",
    libelle: "Secteur de la construction"
  },
  {
    id: 9,
   code: "000008",
    libelle: "Secteur des transports"
  },
  {
    id: 10,
   code: "000009",
    libelle: "Secteur des énergies renouvelables"
  }
];

  const secteurs = [
    { id: 1, secteur: "Industries diverses" },
    { id: 2, secteur: "Technologie" },
    { id: 3, secteur: "Services" },
    { id: 4, secteur: "Tourisme" },
    { id: 5, secteur: "Agriculture" },
    { id: 6, secteur: "Energie" },

  ];
  const categories = [
{id : 1 , category : "Industrie"} ,
{id : 2 , category : "Agriculture"} ,
{ id: 3, category: "Technologie" },
{ id: 4, category: "Commerce" },
{ id: 5, category: "Finance" },
{ id: 6, category: "Santé" }
  ] 
  const  gouvernorats= [
      { id: 1, nom: "Tunis" },
      { id: 2, nom: "Ariana" },
      { id: 3, nom: "Ben Arous" },
      { id: 4, nom: "Manouba" },
      { id: 5, nom: "Nabeul" },
      { id: 6, nom: "Zaghouan" },
      { id: 7, nom: "Bizerte" },
      { id: 8, nom: "Beja" },
      { id: 9, nom: "Jendouba" },
      { id: 10, nom: "Le Kef" },
      { id: 11, nom: "Siliana" },
      { id: 12, nom: "Kairouan" },
      { id: 13, nom: "Kasserine" },
      { id: 14, nom: "Sidi Bouzid" },
      { id: 15, nom: "Sousse" },
      { id: 16, nom: "Monastir" },
      { id: 17, nom: "Mahdia" },
      { id: 18, nom: "Sfax" },
      { id: 19, nom: "Gafsa" },
      { id: 20, nom: "Tozeur" },
      { id: 21, nom: "Kebili" },
      { id: 22, nom: "Gabes" },
      { id: 23, nom: "Medenine" },
      { id: 24, nom: "Tataouine" }
    ]
 
    const  delegations = [
        { id: 1, nom: "Bab Bhar", "gouvernorat_id": 1 },
        { id: 2, nom: "Bab Souika", "gouvernorat_id": 1 },
        { id: 3, nom: "Carthage", "gouvernorat_id": 1 },
        { id: 4, nom: "Cité El Khadra", "gouvernorat_id": 1 },
        { id: 5, nom: "Djebel Jelloud", "gouvernorat_id": 1 },
        { id: 6, nom: "El Kabaria", "gouvernorat_id": 1 },
        { id: 7, nom: "El Menzah", "gouvernorat_id": 1 },
        { id: 8, nom: "El Omrane", "gouvernorat_id": 1 },
        { id: 9, nom: "El Omrane supérieur", "gouvernorat_id": 1 },
        { id: 10, nom: "El Ouardia", "gouvernorat_id": 1 },
        { id: 11, nom: "Ettahrir", "gouvernorat_id": 1 },
        { id: 12, nom: "Ezzouhour", "gouvernorat_id": 1 },
        { id: 13, nom: "La Goulette", "gouvernorat_id": 1 },
        { id: 14, nom: "La Marsa", "gouvernorat_id": 1 },
        { id: 15, nom: "Le Bardo", "gouvernorat_id": 1 },
        { id: 16, nom: "Le Kram", "gouvernorat_id": 1 },
        { id: 17, nom: "Séjoumi", "gouvernorat_id": 1 },
        { id: 18, nom: "Sidi El Béchir", "gouvernorat_id": 1 },
        { id: 19, nom: "Sidi Hassine", "gouvernorat_id": 1 },
    
        { id: 20, nom: "Ariana Ville", "gouvernorat_id": 2 },
        { id: 21, nom: "Ettadhamen", "gouvernorat_id": 2 },
        { id: 22, nom: "Kalaât el-Andalous", "gouvernorat_id": 2 },
        { id: 23, nom: "Mnihla", "gouvernorat_id": 2 },
        { id: 24, nom: "Raoued", "gouvernorat_id": 2 },
        { id: 25, nom: "Sidi Thabet", "gouvernorat_id": 2 },
        { id: 26, nom: "La Soukra", "gouvernorat_id": 2 },
    
        { id: 27, nom: "Ben Arous", "gouvernorat_id": 3 },
        { id: 28, nom: "Bou Mhel el-Bassatine", "gouvernorat_id": 3 },
        { id: 29, nom: "El Mourouj", "gouvernorat_id": 3 },
        { id: 30, nom: "Fouchana", "gouvernorat_id": 3 },
        { id: 31, nom: "Hammam Chott", "gouvernorat_id": 3 },
        { id: 32, nom: "Hammam Lif", "gouvernorat_id": 3 },
        { id: 33, nom: "Mohamedia-Fouchana", "gouvernorat_id": 3 },
        { id: 34, nom: "Mornag", "gouvernorat_id": 3 },
        { id: 35, nom: "Radès", "gouvernorat_id": 3 },
    
        { id: 36, nom: "Manouba", "gouvernorat_id": 4 },
        { id: 37, nom: "Douar Hicher", "gouvernorat_id": 4 },
        { id: 38, nom: "Tebourba", "gouvernorat_id": 4 },
        { id: 39, nom: "Mornaguia", "gouvernorat_id": 4 },
        { id: 40, nom: "Oued Ellil", "gouvernorat_id": 4 },
    
        { id: 41, nom: "Nabeul", "gouvernorat_id": 5 },
        { id: 42, nom: "Hammamet", "gouvernorat_id": 5 },
        { id: 43, nom: "Korba", "gouvernorat_id": 5 },
        { id: 44, nom: "Kelibia", "gouvernorat_id": 5 },
        { id: 45, nom: "Menzel Temime", "gouvernorat_id": 5 },
    
        { id: 46, nom: "Bizerte", "gouvernorat_id": 7 },
        { id: 47, nom: "Menzel Bourguiba", "gouvernorat_id": 7 },
        { id: 48, nom: "Ras Jebel", "gouvernorat_id": 7 },
        { id: 49, nom: "Metline", "gouvernorat_id": 7 },
        { id: 50, nom: "Ghar El Melh", "gouvernorat_id": 7 },
    
        { id: 51, nom: "Sousse", "gouvernorat_id": 15 },
        { id: 52, nom: "Msaken", "gouvernorat_id": 15 },
        { id: 53, nom: "Akouda", "gouvernorat_id": 15 },
        { id: 54, nom: "Hammam Sousse", "gouvernorat_id": 15 },
    
        { id: 55, nom: "Sfax", "gouvernorat_id": 18 },
        { id: 56, nom: "Sakiet Ezzit", "gouvernorat_id": 18 },
        { id: 57, nom: "Sakiet Eddaier", "gouvernorat_id": 18 },
        { id: 58, nom: "El Amra", "gouvernorat_id": 18 },
    
        { id: 59, nom: "Gafsa", "gouvernorat_id": 19 },
        { id: 60, nom: "Métlaoui", "gouvernorat_id": 19 },
        { id: 61, nom: "Redeyef", "gouvernorat_id": 19 },
    
        { id: 62, nom: "Tozeur", "gouvernorat_id": 20 },
        { id: 63, nom: "Nefta", "gouvernorat_id": 20 },
    
        { id: 64, nom: "Tataouine", "gouvernorat_id": 24 },
        { id: 65, nom: "Bir Lahmar", "gouvernorat_id": 24 }
      ]
    const nationalites =[
      { id: 1, nom: "France" },
      { id: 2, nom: "Italie" },
      { id: 3, nom: "Allemagne" },
      { id: 4, nom: "Espagne" },
      { id: 5, nom: "États-Unis" },
      { id: 6, nom: "Canada" },
      { id: 7, nom: "Algérie" },
      { id: 8, nom: "Maroc" },
      { id: 9, nom: "Libye" },
      { id: 10, nom: "Turquie" },
      { id: 11, nom: "Arabie Saoudite" },
      { id: 12, nom: "Émirats arabes unis" },
      { id: 13, nom: "Qatar" },
      { id: 14, nom: "Égypte" },
      { id: 15, nom: "Chine" },
      { id: 16, nom: "Japon" },
      { id: 17, nom: "Corée du Sud" },
      { id: 18, nom: "Belgique" },
      { id: 19, nom: "Suisse" },
      { id: 20, nom: "Russie" }
    ]
export {companies , secteurs , gouvernorats , delegations ,nationalites  ,categories ,activites }