import {Card, Col, Row, Typography} from "antd";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import {
  UserOutlined,
  StockOutlined, MoneyCollectOutlined,
} from "@ant-design/icons";

const Home = () => {
  const {t} = useTranslation();
  return (
    <Card
      bordered={true}
      title={
        <Typography.Title style={{textWrap: "wrap"}} level={4}>
          {t('accueil.dashboard.title')}
        </Typography.Title>
      }
    >
      <br/>
      <br/>
    </Card>
  )
};

export default Home;
