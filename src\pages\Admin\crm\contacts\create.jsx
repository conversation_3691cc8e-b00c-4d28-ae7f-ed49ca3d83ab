import { ProForm, ProFormSelect, ProFormText } from "@ant-design/pro-form";
import { Card, Col, Divider, message, Row, Modal, Button, Typography, Tabs, Form, Input } from "antd";
import contacts from "./datas";
import { useState } from "react";

const { Title } = Typography;

const CreateContact = ({ onCancel, setDataSource }) => {
  const [listContacts, setListContacts] = useState([]);
  const [selectedContact, setSelectedContact] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [form] = Form.useForm();

  const handleSubmit = async (values) => {
    try {
      const newKey = Date.now().toString();
      const newContact = {
        key: newKey,
        ...values,
      };

      const updatedData = [...contacts, newContact];
      setDataSource(updatedData);

      message.success("Contact créé avec succès!");
      onCancel();
    } catch (error) {
      console.error(error);
      message.error("Erreur lors de la création du contact");
    }
  };

  const options = [
    { label: "Entreprise A", value: "Entreprise A" },
    { label: "Entreprise B", value: "Entreprise B" },
    { label: "Entreprise C", value: "Entreprise C" },
  ];

  const getEntrepriseContacts = (id) => {
    const newContacts = [
      {
        key: Date.now() + 1, 
        nom: "Nom Contact " + (id + 1),
        prenom: "Prenom Contact " + (id + 1),
        email: "Email Contact " + (id + 1),
        telephone: "TEL Contact " + (id + 1),
      },
      {
        key: Date.now() + 2,
        nom: "Nom Contact " + (id + 2),
        prenom: "Prenom Contact " + (id + 2),
        email: "Email Contact " + (id + 2),
        telephone: "TEL Contact " + (id + 2),
      },
      {
        key: Date.now() + 3,
        nom: "Nom Contact " + (id + 3),
        prenom: "Prenom Contact " + (id + 3),
        email: "Email Contact " + (id + 3),
        telephone: "TEL Contact " + (id + 3),
      },
  
    ];
    setListContacts(newContacts);
  };

  const openModal = (contact) => {
    setSelectedContact(contact);
    console.log(selectedContact);
    setIsEditing(false);
    form.setFieldsValue(contact);
    setIsModalOpen(true);
  };

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = () => {
    form.validateFields().then((values) => {
      console.log('values')
      setSelectedContact((prev) => ({ ...prev, ...values }));
      setIsEditing(false);
      message.success("Modifications enregistrées !");
    });
  };

  return (
    <>
      <ProForm onFinish={handleSubmit}>
        <Divider>Informations Personnelles</Divider>
        <ProFormText name="prenom" label="Prénom" placeholder="Saisissez le prénom" rules={[{ required: true }]} />
        <ProFormText name="nom" label="Nom" placeholder="Saisissez le nom" rules={[{ required: true }]} />
        <ProFormText name="email" label="Email" placeholder="Saisissez l'email" rules={[{ required: true, type: "email" }]} />
        <ProFormText name="telephone" label="Téléphone" placeholder="Saisissez le téléphone" rules={[{ required: true }]} />

        <Divider>Informations Professionnelles</Divider>
        <ProFormText name="titre" label="Titre" placeholder="Saisissez le titre" rules={[{ required: true }]} />
        <ProFormSelect
          name="entreprise"
          label="Raison Sociale"
          placeholder="Sélectionnez une entreprise"
          options={options}
          onChange={(value) => getEntrepriseContacts(value)}
          rules={[{ required: true }]}
        />

        {listContacts.length !== 0 && (
          <Row gutter={[16, 16]} style={{ backgroundColor: "#edf2fb", padding: "15px" }}>
            {listContacts.map((contact) => (
              <Col key={contact.key} xs={24} sm={12} md={8} lg={6}>
                <Card hoverable onClick={() => openModal(contact)}>
                  <p><strong>Nom:</strong> {contact.nom}</p>
                </Card>
              </Col>
            ))}
          </Row>
        )}

        <ProFormText name="adresse" label="Adresse" placeholder="Saisissez l'adresse" rules={[{ required: true }]} />
        <ProFormText name="siteWeb" label="Site Web" placeholder="Saisissez l'URL du site web" rules={[{ required: true }]} />
        <ProFormText name="secteurActivite" label="Secteur d'activité" placeholder="Saisissez le secteur" rules={[{ required: true }]} />
        <ProFormText name="sourceContact" label="Source du contact" placeholder="Saisissez la source" rules={[{ required: true }]} />
        <ProFormText name="statutContact" label="Statut du contact" placeholder="Saisissez le statut" rules={[{ required: true }]} />
        <ProFormText name="proprietaire" label="Propriétaire" placeholder="Saisissez le propriétaire" rules={[{ required: true }]} />
      </ProForm>

      {/* MODAL */}
      {selectedContact && (
        <Modal
          title={selectedContact.value}
          open={isModalOpen}
          onCancel={() => setIsModalOpen(false)}
          footer={[
            <Button key="close" onClick={() => setIsModalOpen(false)}>
              Fermer
            </Button>,
            isEditing ? (
              <Button key="save" type="primary" onClick={handleSave}>
                Enregistrer
              </Button>
            ) : (
              <Button key="edit" type="primary" onClick={handleEdit}>
                Modifier
              </Button>
            ),
          ]}
          width={700}
        >
          <Tabs defaultActiveKey="1">
            <Tabs.TabPane tab="Détails" key="1">
              <Form form={form} layout="vertical" initialValues={selectedContact} disabled={!isEditing}>
                <Form.Item name="nom" label="Nom" rules={[{ required: true }]}>
                  <Input />
                </Form.Item>
                <Form.Item name="email" label="Email" rules={[{ required: true, type: "email" }]}>
                  <Input />
                </Form.Item>
                <Form.Item name="telephone" label="Téléphone" rules={[{ required: true }]}>
                  <Input />
                </Form.Item>
                <Form.Item name="entreprise" label="Entreprise">
                  <Input disabled />
                </Form.Item>
              </Form>
            </Tabs.TabPane>
            <Tabs.TabPane tab="Tâches" key="2">Gestion des tâches...</Tabs.TabPane>
            <Tabs.TabPane tab="Campagnes" key="3">Campagnes marketing...</Tabs.TabPane>
            <Tabs.TabPane tab="Notes" key="4">Notes et commentaires...</Tabs.TabPane>
          </Tabs>
        </Modal>
      )}
    </>
  );
};

export default CreateContact;
