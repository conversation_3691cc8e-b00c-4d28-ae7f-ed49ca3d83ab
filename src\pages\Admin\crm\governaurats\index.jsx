import { useState, useRef } from "react";
import { ProTable } from "@ant-design/pro-components";
import { Button, Popconfirm, Space, Input, Tooltip, Typography, Card, Modal } from "antd";
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined, SearchOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import gouvernorats from "./datas"; // Data for the gouvernorats
import CreateGouvernorat from "./create"; // Import create modal component
import EditGouvernorat from "./edit"; // Import edit modal component
import ViewGouvernorat from "./view"; // Import view modal component

function Gouvernorats() {
  const { t } = useTranslation();
  const tableRef = useRef();

  const [dataSource, setDataSource] = useState(gouvernorats); // Initialize with gouvernorats data
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [viewModalVisible, setViewModalVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState(null);
  const [viewingRecord, setViewingRecord] = useState(null);

  const handleCancel = () => {
    setViewModalVisible(false);
  };

  const handleEdit = (record) => {
    setEditingRecord(record);
    setEditModalVisible(true);
  };

  const columns = [
    { title: "Code Gouvernorat", dataIndex: "code", key: "code" },
    { title: "Libellé", dataIndex: "capital", key: "capital" },
    {
      title: "Actions",
      key: "action",
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title="Voir">
            <Button type="link" icon={<EyeOutlined />} onClick={() => {
              setViewingRecord(record);
              setViewModalVisible(true);
            }} />
          </Tooltip>
          <Tooltip title="Modifier">
            <Button type="link" style={{ color: "#f5b041" }} icon={<EditOutlined />} onClick={() => handleEdit(record)} />
          </Tooltip>
          <Tooltip title="Supprimer">
            <Popconfirm title="Confirmer la suppression ?" onConfirm={() => {
              const newDataSource = dataSource.filter(item => item.key !== record.key);
              setDataSource(newDataSource);
            }}>
              <Button type="link" style={{ color: "#ec7063" }} icon={<DeleteOutlined />} />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <Card
      bordered
      title={<Typography.Title level={4}>Liste des Gouvernorats</Typography.Title>}
      extra={
        <Space>
          <Input size="large" placeholder="Rechercher" suffix={<SearchOutlined style={{ color: "#bfbfbf" }} />} />
          <Button type="primary" icon={<PlusOutlined />} onClick={() => setCreateModalVisible(true)}>
            Ajouter
          </Button>
        </Space>
      }
    >
      <ProTable
        actionRef={tableRef}
        columns={columns}
        dataSource={dataSource}
        rowKey="key"
        search={false}
        pagination={{ showSizeChanger: true, defaultPageSize: 5 }}
      />
      
      {/* Create Modal */}
      <Modal
        title="Ajouter un Gouvernorat"
        visible={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        footer={null}
      >
        <CreateGouvernorat
          onCancel={() => setCreateModalVisible(false)}
          setDataSource={setDataSource}
        />
      </Modal>

      {/* Edit Modal */}
      <Modal
        title="Modifier un Gouvernorat"
        visible={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        footer={null}
      >
        <EditGouvernorat
          record={editingRecord}
          onCancel={() => setEditModalVisible(false)}
          setDataSource={setDataSource}
        />
      </Modal>

      {/* View Modal */}
      <Modal
        title="Détails du Gouvernorat"
        visible={viewModalVisible}
        onCancel={handleCancel}
        footer={null}
      >
        <ViewGouvernorat
          record={viewingRecord}
          onCancel={() => setViewModalVisible(false)}
        />
      </Modal>
    </Card>
  );
}

export default Gouvernorats;
