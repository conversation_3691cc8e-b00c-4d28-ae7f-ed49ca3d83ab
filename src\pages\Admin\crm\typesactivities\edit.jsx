import { useEffect, useState } from "react";
import { ProForm, ProFormText, ProFormDatePicker } from "@ant-design/pro-form";
import { message, Spin } from "antd";
import dayjs from "dayjs";
import { useTranslation } from "react-i18next";
import { datas } from "./datas";

const EditActivity = ({ record, onCancel, setDataSource }) => {
  const { t } = useTranslation();
  const [activity, setActivity] = useState(null);
  const [form] = ProForm.useForm();

  useEffect(() => {
    if (record) {
      setActivity(record);
      form.resetFields();
    } else {
      message.error(t("crm.activities.edit.messages.not_found"));
      onCancel();
    }
  }, [record, form, onCancel, t]);

  const handleSubmit = async (values) => {
    try {
      const formattedPlanifier = dayjs(values.planifier).format("DD/MM/YYYY");

      const updatedData = datas.map((item) =>
        item.key === record.key ? { ...item, ...values, planifier: formattedPlanifier } : item
      );

      setDataSource(updatedData);

      message.success(t("crm.activities.edit.messages.success"));
      onCancel();
    } catch (error) {
      console.log(error);
      message.error(t("crm.activities.edit.messages.error"));
    }
  };

  if (!activity) {
    return <Spin size="large" />;
  }

  return (
    <>
      <h2>{t("crm.activities.edit.title")}</h2>
      <ProForm
        form={form}
        onFinish={handleSubmit}
        initialValues={{
          ...record,
          planifier: dayjs(record.planifier, "DD/MM/YYYY"),
        }}
      >
        <ProFormText
          name="nom"
          label={t("crm.activities.edit.fields.name")}
          placeholder={t("crm.activities.edit.placeholders.name")}
          rules={[{ required: true }]}
        />
        <ProFormText
          name="resume"
          label={t("crm.activities.edit.fields.resume")}
          placeholder={t("crm.activities.edit.placeholders.resume")}
          rules={[{ required: true }]}
        />
        <ProFormDatePicker
          name="planifier"
          label={t("crm.activities.edit.fields.planifier")}
          rules={[{ required: true }]}
        />
      </ProForm>
    </>
  );
};

export default EditActivity;
