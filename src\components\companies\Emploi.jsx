import {
  FileOutlined,
  MinusCircleOutlined,
  PlusCircleOutlined,
  UploadOutlined,
} from "@ant-design/icons";
import { Col, DatePicker, Form, Input, message, Row, Select } from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { categories } from "../../pages/companies/data";

const Emploi = ({ form, view }) => {
  const { t } = useTranslation();

  const handleAdd = () => {
    const currentdata = form.getFieldValue("emplois") || [];

    const year = form.getFieldValue("year");
    const category = form.getFieldValue("category");
    const effectif = form.getFieldValue("effectif");
    const sexe = form.getFieldValue("sexe");
    const salaire = form.getFieldValue("salaire");
    const date_info = form.getFieldValue("date_info");
    const ce = form.getFieldValue("ce");

    if (year && category && effectif && sexe && salaire && date_info && ce) {
      const key = Date.now();
      form.setFieldsValue({
        emplois: [
          ...currentdata,
          {
            key: key,
            year: year,
            category: category,
            effectif: effectif,
            sexe: sexe,
            salaire: salaire,
            date_info: date_info,
            ce: ce,
          },
        ],
      });
      form.resetFields([
        "year",
        "category",
        "effectif",
        "sexe",
        "salaire",
        "date_info",
        "ce",
      ]);
    } else {
      message.error(t("Merci de bien vouloir remplir tous les champs."));
    }
  };
  return (
    <>
      <Row gutter={16} style={{ marginBottom: "10px" }}>
        <Col span={3}>Année</Col>
        <Col span={5}>Catégorie Socio-prof.</Col>
        <Col span={3}>Effectifs</Col>
        <Col span={3}>Sexe</Col>
        <Col span={3}>Salaires</Col>
        <Col span={4}>Date info</Col>
        <Col span={2}>C/E</Col>
        <Col span={1}></Col>
      </Row>
      <Form.List name="emplois">
        {(fields, { remove }) => (
          <>
            {fields.map((field, index) => (
              <Row gutter={16} align="middle" key={field.key}>
                <Col span={3}>
                  <Form.Item  name={[index, "year"]} rules={[]}>
                    <Input allowClear={true} />
                  </Form.Item>
                </Col>
                <Col span={5}>
                  <Form.Item name={[index,`category`]} rules={[]}>
                    <Select
                      allowClear
                      options={categories.map((item) => ({
                        value: item.id,
                        label: item.category,
                      }))}
                    />
                  </Form.Item>

                 
                </Col>
                <Col span={3}>
                  <Form.Item name={[index,`effectif`]} rules={[]}>
                    <Input allowClear={true} />
                  </Form.Item>
                </Col>
                <Col span={3}>
                  <Form.Item name={[index,`sexe`]} rules={[]}>
                    <Select allowClear={true} options= {[{value : 1 , label : "Femme" } , {value : 2 , label : "Homme" }]} />
                  </Form.Item>
                </Col>
                <Col span={3}>
                  <Form.Item name={[index,`salaire`]} rules={[]}>
                    <Input allowClear={true} />
                  </Form.Item>
                </Col>
                <Col span={4}>
                  <Form.Item name={[index,`date_info`]} rules={[]}>
                    <DatePicker allowClear={true} />
                  </Form.Item>
                </Col>
                <Col span={2}>
                  <Form.Item name={[index,`ce`]} rules={[]}>
                  <Select allowClear={true} options= {[{value : 1 , label : "C" } , {value : 2 , label : "E" } , {value : 2 , label : "R" }]} />
                  </Form.Item>
                </Col>
                <Col span={1}>
                  {!view && (
                    <Form.Item>
                      <MinusCircleOutlined
                        style={{
                          color: "red",
                          fontSize: "18px",
                          cursor: "pointer",
                        }}
                        onClick={() => remove(index)}
                      />
                    </Form.Item>
                  )}
                </Col>
              </Row>
            ))}
          </>
        )}
      </Form.List>

      {!view && (
        <Row gutter={16} align="middle" style={{ marginBottom: "5px" }}>
          <Col span={3}>
            <Form.Item name={`year`} rules={[]}>
              <Input allowClear={true} />
            </Form.Item>
          </Col>
          <Col span={5}>
            <Form.Item name={`category`} rules={[]}>
            <Select
                      allowClear
                      options={categories.map((item) => ({
                        value: item.id,
                        label: item.category,
                      }))}
                    />
            </Form.Item>
          </Col>
          <Col span={3}>
            <Form.Item name={`effectif`} rules={[]}>
              <Input allowClear={true} />
            </Form.Item>
          </Col>
          <Col span={3}>
            <Form.Item name={`sexe`} rules={[]}>
              <Select allowClear={true} options= {[{value : 1 , label : "Femme" } , {value : 2 , label : "Homme" }]}/>
            </Form.Item>
          </Col>
          <Col span={3}>
            <Form.Item name={`salaire`} rules={[]}>
              <Input allowClear={true} />
            </Form.Item>
          </Col>
          <Col span={4}>
            <Form.Item name={`date_info`} rules={[]}>
              <DatePicker allowClear={true} />
            </Form.Item>
          </Col>
          <Col span={2}>
            <Form.Item name={`ce`} rules={[]}>
              <Select allowClear={true} options= {[{value : 1 , label : "C" } , {value : 2 , label : "E" } , {value : 2 , label : "R" }]} />
            </Form.Item>
          </Col>
          <Col span={1}>
            <Form.Item>
              <PlusCircleOutlined
                style={{
                  color: "green",
                  fontSize: "18px",
                  cursor: "pointer",
                }}
                onClick={() => handleAdd()}
              />
            </Form.Item>
          </Col>
        </Row>
      )}
    </>
  );
};

export default Emploi;
