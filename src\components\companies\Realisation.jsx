import {
  FileOutlined,
  MinusCircleOutlined,
  PlusCircleOutlined,
  UploadOutlined,
} from "@ant-design/icons";
import {
  Col,
  DatePicker,
  Form,
  Input,
  message,
  Row,
  Select,
  Upload,
} from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";

const Realisation = ({ form, view }) => {
  const { t } = useTranslation();

  const handleAdd = () => {
    const currentdata = form.getFieldValue("realisations") || [];

    const year = form.getFieldValue("year");
    const poste = form.getFieldValue("poste");
    const deflateur = form.getFieldValue("deflateur");
    const montant = form.getFieldValue("montant");
    const montant_reel = form.getFieldValue("montant_reel");
    const date_info = form.getFieldValue("date_info");
    const ce = form.getFieldValue("ce");

    if (year && poste && deflateur && montant_reel && montant && date_info && ce) {
      const key = Date.now();
      form.setFieldsValue({
        realisations: [
          ...currentdata,
          {
            key: key,
            year: year,
            poste: poste,
            deflateur: deflateur,
            montant_reel: montant_reel,
            montant: montant,
            date_info: date_info,
            ce: ce,
          },
        ],
      });
      form.resetFields([
        "year",
        "poste",
        "deflateur",
        "montant",
        "montant_reel",
        "date_info",
        "ce",
      ]);
    } else {
      message.error(t("Merci de bien vouloir remplir tous les champs."));
    }
  };
  return (
    <>
      <Row gutter={16} style={{ marginBottom: "10px" }}>
        <Col span={3}>Année</Col>
        <Col span={5}>Poste de schema</Col>
        <Col span={3}>% Def</Col>
        <Col span={3}>Montant</Col>
        <Col span={3}>M.Réel</Col>
        <Col span={4}>Date info</Col>
        <Col span={2}>C/E</Col>
        <Col span={1}></Col>
      </Row>
      <Form.List name="realisations">
        {(fields, { remove }) => (
          <>
            {fields.map((field, index) => (
              <Row gutter={16} align="middle" key={field.key}>
                <Col span={3}>
                  <Form.Item name={[index, "year"]} rules={[]}>
                    <Input allowClear={true} />
                  </Form.Item>
                </Col>
                <Col span={5}>
                  <Form.Item name={[index, `poste`]} rules={[]}>
                    <Select
                      allowClear
                      options={[
                        { value: 1, label: "Capital social" },
                        { value: 2, label: "Equipement importé" }
                      ]}
                    />
                  </Form.Item>
                </Col>
                <Col span={3}>
                  <Form.Item name={[index, `deflateur`]} rules={[]}>
                    <Input allowClear={true} />
                  </Form.Item>
                </Col>
                <Col span={3}>
                  <Form.Item name={[index, `montant`]} rules={[]}>
                  <Input allowClear={true} />
                  </Form.Item>
                </Col>
                <Col span={3}>
                  <Form.Item name={[index, `montant_reel`]} rules={[]}>
                    <Input allowClear={true} />
                  </Form.Item>
                </Col>
                <Col span={4}>
                  <Form.Item name={[index, `date_info`]} rules={[]}>
                    <DatePicker allowClear={true} />
                  </Form.Item>
                </Col>
                <Col span={2}>
                  <Form.Item name={[index, `ce`]} rules={[]}>
                    <Select
                      allowClear={true}
                      options={[
                        { value: 1, label: "C" },
                        { value: 2, label: "E" },
                        { value: 2, label: "R" }
                      ]}
                    />
                  </Form.Item>
                </Col>
                <Col span={1}>
                  {!view && (
                    <Form.Item>
                      <MinusCircleOutlined
                        style={{
                          color: "red",
                          fontSize: "18px",
                          cursor: "pointer",
                        }}
                        onClick={() => remove(index)}
                      />
                    </Form.Item>
                  )}
                </Col>
              </Row>
            ))}
          </>
        )}
      </Form.List>

      {!view && (
        <Row gutter={16} align="middle" style={{ marginBottom: "5px" }}>
          <Col span={3}>
            <Form.Item name={`year`} rules={[]}>
              <Input allowClear={true} />
            </Form.Item>
          </Col>
          <Col span={5}>
            <Form.Item name={`poste`} rules={[]}>
            <Select
                      allowClear
                      options={[
                        { value: 1, label: "Capital social" },
                        { value: 2, label: "Equipement importé" }
                      ]}
                    />
            </Form.Item>
          </Col>
          <Col span={3}>
            <Form.Item name={`deflateur`} rules={[]}>
              <Input allowClear={true} />
            </Form.Item>
          </Col>
          <Col span={3}>
            <Form.Item name={`montant`} rules={[]}>
              <Input allowClear={true} />
            </Form.Item>
          </Col>
          <Col span={3}>
            <Form.Item name={`montant_reel`} rules={[]}>
              <Input allowClear={true} />
            </Form.Item>
          </Col>
          <Col span={4}>
            <Form.Item name={`date_info`} rules={[]}>
              <DatePicker allowClear={true} />
            </Form.Item>
          </Col>
          <Col span={2}>
            <Form.Item name={`ce`} rules={[]}>
              <Select allowClear={true} options= {[{value : 1 , label : "C" } , {value : 2 , label : "E" } , {value : 2 , label : "R" }]}  />
            </Form.Item>
          </Col>
          <Col span={1}>
            <Form.Item>
              <PlusCircleOutlined
                style={{
                  color: "green",
                  fontSize: "18px",
                  cursor: "pointer",
                }}
                onClick={() => handleAdd()}
              />
            </Form.Item>
          </Col>
        </Row>
      )}
    </>
  );
};

export default Realisation;
