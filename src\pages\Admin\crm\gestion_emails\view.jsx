import React from 'react';
import { Descriptions, Tag, Space } from 'antd';

const EmailTemplateView = ({ template }) => {
  return (
    <div>
      <Descriptions bordered column={1}>
        <Descriptions.Item label="Subject">{template.subject}</Descriptions.Item>
        <Descriptions.Item label="Category">
          <Tag color="blue">{template.category}</Tag>
        </Descriptions.Item>
        <Descriptions.Item label="Variables">
          <Space>
            {template.variables.map(variable => (
              <Tag key={variable}>{`{{${variable}}}`}</Tag>
            ))}
          </Space>
        </Descriptions.Item>
        <Descriptions.Item label="Created At">{template.createdAt}</Descriptions.Item>
        <Descriptions.Item label="Last Updated">{template.updatedAt}</Descriptions.Item>
      </Descriptions>

      <div style={{ marginTop: 16, padding: 16, border: '1px solid #f0f0f0' }}>
        <h3>Template Content</h3>
        <div dangerouslySetInnerHTML={{ __html: template.content }} />
      </div>
    </div>
  );
};

export default EmailTemplateView;