import React, { useState, useRef } from 'react';
import { <PERSON><PERSON>, Modal, Card, Tag, Popconfirm, Space, Tooltip } from 'antd';
import { ProTable } from '@ant-design/pro-components';
import { EyeOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { mockInteractions, interactionFields } from './data';
import InteractionCreate from './create';
import InteractionEdit from './edit';
import InteractionView from './view';

const InteractionsIndex = () => {
  const [interactions, setInteractions] = useState(mockInteractions);
  const [selectedInteraction, setSelectedInteraction] = useState(null);
  const [modalType, setModalType] = useState(null);
  const tableRef = useRef();

  const handleCreate = (newInteraction) => {
    setInteractions([...interactions, { ...newInteraction, id: interactions.length + 1 }]);
    setModalType(null);
  };

  const handleUpdate = (updatedInteraction) => {
    setInteractions(interactions.map(i => i.id === updatedInteraction.id ? updatedInteraction : i));
    setModalType(null);
  };

  const handleDelete = (id) => {
    setInteractions(interactions.filter(i => i.id !== id));
  };

  const columns = [
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      filters: [...new Set(interactions.map(i => i.type))].map(val => ({ text: val, value: val })),
      onFilter: (value, record) => record.type === value,
    },
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
    },
    {
      title: 'Profil Diaspora',
      dataIndex: 'diasporaProfile',
      key: 'diasporaProfile',
    },
    {
      title: 'Objet',
      dataIndex: 'object',
      key: 'object',
    },
    {
      title: 'Actions',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title="Voir">
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => {
                setSelectedInteraction(record);
                setModalType('view');
              }}
            />
          </Tooltip>
          <Tooltip title="Modifier">
            <Button
              type="link"
              style={{ color: '#f5b041' }}
              icon={<EditOutlined />}
              onClick={() => {
                setSelectedInteraction(record);
                setModalType('edit');
              }}
            />
          </Tooltip>
          <Tooltip title="Supprimer">
            <Popconfirm
              title="Confirmer la suppression ?"
              onConfirm={() => handleDelete(record.id)}
            >
              <Button
                type="link"
                style={{ color: '#ec7063' }}
                icon={<DeleteOutlined />}
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <Card
      title="Gestion des Interactions"
      extra={<Button type="primary" onClick={() => setModalType('create')}>Ajouter une Interaction</Button>}
    >
      <ProTable
        actionRef={tableRef}
        columns={columns}
        dataSource={interactions}
        rowKey="id"
        search={false}
        options={{ reload: () => {}, density: true, setting: true }}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: 5,
          total: interactions.length,
          showTotal: (total) => `Total ${total} interactions`,
        }}
        scroll={{ x: 'max-content' }}
      />

      <Modal
        title="Ajouter une nouvelle interaction"
        open={modalType === 'create'}
        onCancel={() => setModalType(null)}
        footer={null}
      >
        <InteractionCreate onCreate={handleCreate} />
      </Modal>

      <Modal
        title="Modifier l'interaction"
        open={modalType === 'edit'}
        onCancel={() => setModalType(null)}
        footer={null}
      >
        <InteractionEdit initialValues={selectedInteraction} onUpdate={handleUpdate} />
      </Modal>

      <Modal
       
        open={modalType === 'view'}
        onCancel={() => setModalType(null)}
        footer={null}
      >
        <InteractionView interaction={selectedInteraction} />
      </Modal>
    </Card>
  );
};

export default InteractionsIndex;
