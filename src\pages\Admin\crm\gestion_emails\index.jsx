import React, { useState, useRef, useEffect, useMemo } from 'react';
import {
  Button,
  Modal,
  Card,
  Popconfirm,
  Space,
  Tooltip,
  Tag,
  Input,
  message,
  Descriptions,
} from 'antd';
import { ProTable } from '@ant-design/pro-components';
import {
  InfoCircleOutlined,
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  PlusOutlined,
  CodeOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';
import templatesData from './data.json';
import EmailTemplateCreate from './create';
import EmailTemplateEdit from './edit';
import EmailTemplateView from './view';
import VariableInfo from './VariableInfo';

const ListeTemplatesEmail = () => {
  const [data, setData] = useState(templatesData.templates);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [modalType, setModalType] = useState(null);
  const [previewHtml, setPreviewHtml] = useState('');
  const tableRef = useRef();

  const handleCreate = (newTemplate) => {
    const templateWithId = {
      ...newTemplate,
      id: data.length + 1,
      createdAt: dayjs().toISOString(),
      updatedAt: dayjs().toISOString(),
    };
    setData([...data, templateWithId]);
    setModalType(null);
    message.success('Template créé avec succès !');
  };

  const handleUpdate = (updatedTemplate) => {
    setData(
      data.map((template) =>
        template.id === updatedTemplate.id
          ? { ...updatedTemplate, updatedAt: dayjs().toISOString() }
          : template
      )
    );
    setModalType(null);
    message.success('Template mis à jour avec succès !');
  };

  const handleDelete = (id) => {
    setData(data.filter((template) => template.id !== id));
    message.success('Template supprimé avec succès !');
  };

  const handlePreview = (template) => {
    const demoValues = {
      name: "Jean Dupont",
      email: "<EMAIL>",
      resetLink: "https://example.com/reset-password?token=abc123"
      
    };
    
    let previewContent = template.content;
    template.variables.forEach(varName => {
      previewContent = previewContent.replace(
        new RegExp(`\\{\\{${varName}\\}\\}`, 'g'), 
        `<span style="color: #1890ff; font-weight: bold;">${demoValues[varName] || `[${varName}]`}</span>`
      );
    });
    console.log('Preview content:',demoValues);
    
    setPreviewHtml(previewContent);
    setSelectedTemplate(template);
    setModalType('preview');
  };

  useEffect(() => {
    tableRef.current?.reload();
  }, [data]);

  // Valeur mémoïsée pour les catégories
  const categoryValueEnum = useMemo(
    () =>
      data.reduce((acc, item) => {
        if (item.category) acc[item.category] = { text: item.category };
        return acc;
      }, {}),
    [data]
  );

  const columns = [
    {
      title: 'Nom',
      dataIndex: 'name',
      key: 'name',
      sorter: (a, b) => a.name.localeCompare(b.name),
      render: (text) => <span style={{ fontWeight: 500 }}>{text}</span>,
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm }) => (
        <div style={{ padding: 8 }}>
          <Input
            placeholder="Rechercher par nom"
            value={selectedKeys[0]}
            onChange={(e) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={confirm}
            style={{ width: 188, marginBottom: 8, display: 'block' }}
          />
          <Button type="primary" onClick={confirm} size="small" style={{ width: 90 }}>
            Rechercher
          </Button>
        </div>
      ),
    },
    {
      title: 'Sujet',
      dataIndex: 'subject',
      key: 'subject',
      ellipsis: true,
      search: false,
      render: (text) => <Tooltip title={text}><span>{text}</span></Tooltip>,
    },
    {
      title: 'Catégorie',
      dataIndex: 'category',
      key: 'category',
      valueType: 'select',
      render: (category) => <Tag color="blue">{category}</Tag>,
      valueEnum: categoryValueEnum,
    },
    {
      title: 'Variables',
      dataIndex: 'variables',
      key: 'variables',
      search: false,
      render: (variables) => (
        <Space size="small">
          {variables.map((variable) => (
            <Tooltip 
              key={variable} 
              title={
                variable === 'name' ? "Nom complet" :
                variable === 'email' ? "Adresse email" :
                variable === 'resetLink' ? "Lien de réinitialisation" :
                "Variable"
              }
            >
              <Tag>{`{{${variable}}}`}</Tag>
            </Tooltip>
          ))}
        </Space>
      ),
    },
    {
      title: 'Dernière modification',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      valueType: 'date',
      render: (_, record) => dayjs(record.updatedAt).format('YYYY-MM-DD'),
      sorter: (a, b) => dayjs(a.updatedAt).unix() - dayjs(b.updatedAt).unix(),
    },
    {
      title: 'Actions',
      key: 'actions',
      align: 'center',
      width: 220,
      fixed: 'right',
      sorter: false,
      search: false,
      render: (_, record) => (
        <Space size="small" align="center">
          <Tooltip title="Voir les détails du template">
            <Button
              type="link"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => {
                setSelectedTemplate(record);
                setModalType('view');
              }}
              style={{ color: '#13c2c2' }}
            />
          </Tooltip>
          <Tooltip title="Prévisualiser le template">
            <Button
              type="link"
              size="small"
              icon={<CodeOutlined />}
              onClick={() => handlePreview(record)}
              style={{ color: '#722ed1' }}
            />
          </Tooltip>
          <Tooltip title="Modifier le template">
            <Button
              type="link"
              size="small"
              icon={<EditOutlined />}
              onClick={() => {
                setSelectedTemplate(record);
                setModalType('edit');
              }}
              style={{ color: '#fa8c16' }}
            />
          </Tooltip>
          <Tooltip title="Supprimer le template">
            <Popconfirm
              title="Êtes-vous sûr de vouloir supprimer ce template ?"
              onConfirm={() => handleDelete(record.id)}
              okText="Oui"
              cancelText="Non"
              placement="left"
            >
              <Button
                type="link"
                size="small"
                icon={<DeleteOutlined />}
                style={{ color: '#f5222d' }}
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <Card
      title="Modèles d'emails"
      bordered={false}
      extra={
        <Button 
          type="primary" 
          icon={<PlusOutlined />} 
          onClick={() => setModalType('create')}
        >
          Nouveau modèle
        </Button>
      }
      bodyStyle={{ padding: '24px 0' }}
    >
      <ProTable
         actionRef={tableRef}
        columns={columns}
        rowKey="id"
          request={async (filter = {}, sort = {}) => {
          try {
            let filteredData = [...data];
        
            console.log('Active filters:', {
              category: filter?.category,
              startTime: filter?.startTime,
            });
        
            if (filter?.category !== undefined) {
              console.log('Applying category filter');
              filteredData = filteredData.filter(item => 
                item.category && item.category === filter.category
              );
            }
        
        
            if (filter.startTime !== undefined || filter.endTime !== undefined) {
              console.log('Applying date filter');
              filteredData = filteredData.filter(item => {
                if (!item.createdAt) return false;
                const date = dayjs(item.createdAt);
                return (
                  (filter.startTime === undefined || date.isAfter(dayjs(filter.startTime))) &&
                  (filter.endTime === undefined || date.isBefore(dayjs(filter.endTime)))
                );
              });
            }
        
            if (filter.name !== undefined) {
              console.log('Applying name filter');
              const searchTerm = filter.name.toLowerCase();
              filteredData = filteredData.filter(item =>
                item.name?.toLowerCase().includes(searchTerm)
              );
            }
        
            if (sort.field === 'name' && sort.order) {
              console.log('Applying name sorting');
              filteredData.sort((a, b) =>
                sort.order === 'ascend'
                  ? (a.name || '').localeCompare(b.name || '')
                  : (b.name || '').localeCompare(a.name || '')
              );
            }
        
            console.log('Filter result:', {
              initialCount: data.length,
              filteredCount: filteredData.length,
              sample: filteredData.slice(0, 3)
            });
        
            return {
              data: filteredData,
              success: true,
              total: filteredData.length,
            };
          } catch (error) {
            console.error('Filter error:', error);
            return { 
              data: [], 
              success: false, 
              total: 0,
              error: error.message 
            };
          }
        }}
        options={{
          reload: () => setData(templatesData.templates),
          density: true,
          setting: true,
          fullScreen: true,
        }}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: 10,
          pageSizeOptions: ['10', '20', '50'],
          showTotal: (total) => `Total de ${total} modèles`,
        }}
        scroll={{ x: 'max-content' }}
        sticky
      />

      {/* Modals */}
      <Modal
        title="Créer un nouveau modèle d'email"
        open={modalType === 'create'}
        onCancel={() => setModalType(null)}
        footer={null}
        width={800}
        destroyOnClose
      >
        <EmailTemplateCreate onCreate={handleCreate} />
      </Modal>

      <Modal
        title="Modifier le modèle d'email"
        open={modalType === 'edit'}
        onCancel={() => setModalType(null)}
        footer={null}
        width={800}
        destroyOnClose
      >
        {selectedTemplate && (
          <EmailTemplateEdit 
            template={selectedTemplate} 
            onUpdate={handleUpdate} 
          />
        )}
      </Modal>

      <Modal
        title={selectedTemplate?.name || 'Détails du modèle'}
        open={modalType === 'view'}
        onCancel={() => setModalType(null)}
        footer={null}
        width={800}
        destroyOnClose
      >
        {selectedTemplate && (
          <>
            <Descriptions bordered column={1}>
              <Descriptions.Item label="Sujet">{selectedTemplate.subject}</Descriptions.Item>
              <Descriptions.Item label="Catégorie">
                <Tag color="blue">{selectedTemplate.category}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Contenu">
                <div 
                  style={{ 
                    border: '1px solid #f0f0f0',
                    padding: '16px',
                    borderRadius: '4px',
                    marginTop: '8px'
                  }}
                >
                  {selectedTemplate.content}
                </div>
              </Descriptions.Item>
            </Descriptions>
            <VariableInfo variables={selectedTemplate.variables} />
          </>
        )}
      </Modal>

      <Modal
        title={`Prévisualisation: ${selectedTemplate?.name || ''}`}
        open={modalType === 'preview'}
        onCancel={() => setModalType(null)}
        footer={null}
        width="80%"
        style={{ top: 20 }}
        destroyOnClose
      >
        <div style={{ marginBottom: 16 }}>
          <Tag color="blue">Sujet:</Tag> {selectedTemplate?.subject}
        </div>
        <div 
          dangerouslySetInnerHTML={{ __html: previewHtml }} 
          style={{ 
            border: '1px solid #f0f0f0',
            padding: '20px',
            borderRadius: '4px',
            minHeight: '300px',
            backgroundColor: '#fff'
          }}
        />
        {selectedTemplate && (
          <VariableInfo variables={selectedTemplate.variables} />
        )}
        <div style={{ marginTop: 16, color: '#666', fontSize: 12 }}>
          <InfoCircleOutlined /> Les valeurs en bleu sont des exemples qui seront remplacées par les valeurs réelles.
        </div>
      </Modal>
    </Card>
  );
};

export default ListeTemplatesEmail;