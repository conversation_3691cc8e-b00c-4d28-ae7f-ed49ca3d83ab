import { Button, Col, Form, Input, Row, Select } from "antd"
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

const InforGenerale = ({familles, sous_familles, unites, fournisseurs, setIsBien,disabled})=>{
    const {t} = useTranslation();
    const [sousFamilles, setSousFamilles] = useState(sous_familles ? sous_familles : []);
    const handleTypeChange = (value:string) => {
        setIsBien(value === 'bien');
    }
    return (<>
        
        <Row gutter={6}>
            <Col md={12} xs={24} >
                <Form.Item name="code" label={t('articles.fields.code')}
                    rules={[{required: true, message: "Veuillez entrer un code"}]}
                >
                    <Input placeholder="Veuillez entrer un code" disabled={disabled}/>
                </Form.Item>
            </Col>
            <Col md={12} xs={24} >
                <Form.Item name="designation" label={t('articles.fields.designation')}
                    rules={[{required: true, message: "Veuillez entrer une désignation"}]}
                >
                    <Input placeholder="Veuillez entrer une désignation" disabled={disabled}/>
                </Form.Item>
            </Col>
            <Col md={12} xs={24} >
                <Form.Item name="type" label={t('articles.fields.type_article')}
                    rules={[{required: true, message: "Sélectionner le type d'article"}]}
                >
                    <Select placeholder="Sélectionner le type de l'article"
                        options={[
                            {value : 'bien', label : 'Bien'},
                            {value : 'service', label : 'Service'},
                        ]}
                        allowClear
                        onChange={handleTypeChange}    
                        disabled={disabled}            
                    />
                </Form.Item>
            </Col>
            <Col md={12} xs={24} >
                <Form.Item name="status" label={t('articles.fields.status')}
                    rules={[{required: true, message: "Sélectionnez un status"}]}
                >
                    <Select placeholder="Sélectionnez un status" 
                        options={[
                            {value : 1, label : 'Actif'},
                            {value : 2, label : 'En sommeil'},
                        ]}
                        allowClear
                        disabled={disabled}
                    />
                </Form.Item>
            </Col>
        
            <Col md={12} xs={24} >
                <Form.Item name="category_id"
                    label={t('articles.fields.famille')}
                >
                    <Select placeholder="Sélectionnez  une famille"
                        options={familles}
                        allowClear
                        disabled={disabled}
                    />
                </Form.Item>
            </Col>
            <Col md={12} xs={24} >
                <Form.Item name="sub_category_id" 
                    label={t('articles.fields.sous_famille')}
                >
                    <Select placeholder="Sélectionnez une sous-famille"
                        options={sousFamilles}
                        disabled={disabled}
                        allowClear
                    />
                </Form.Item>
            </Col>
            <Col md={12} xs={24} >
                <Form.Item name="purchase_unit_id" 
                    label={t('articles.fields.unite_achat')}
                >
                    <Select placeholder="Sélectionnez l'unité d'achat" 
                        options={unites}
                        disabled={disabled}
                        allowClear
                    />
                </Form.Item>
            </Col>
            <Col md={12} xs={24} >
                <Form.Item name="stock_unit_id" 
                label={t('articles.fields.unite_stock')}
                >
                    <Select placeholder="Sélectionnez l'unité de stock"
                        options={unites}
                        disabled={disabled}
                        allowClear
                    />
                </Form.Item>
            </Col>
            
            <Col md={12} xs={24} >
                <Form.Item name="sale_unit_id" 
                    label={t('articles.fields.unite_vente')}
                >
                    <Select placeholder="Sélectionnez l'unité de vente"
                        options={unites}
                        disabled={disabled}
                        allowClear
                    />
                </Form.Item>
            </Col>
            <Col md={12} xs={24} >
                <Form.Item name="supplier_id" 
                    label={t('articles.fields.fournisseur')}                 
                >
                    <Select placeholder="Sélectionnez le fournisseur"
                        options={fournisseurs}
                        disabled={disabled}
                        allowClear
                    />
                </Form.Item>
            </Col>
        </Row>
        <Form.Item name="description" label={t('articles.fields.description')}
        >
            <Input.TextArea placeholder="Veuillez entrer une Description" disabled={disabled}/>
        </Form.Item>
    
    </>)
}

export default InforGenerale