import { v4 as uuidv4 } from "uuid";

// import "" from "../../../../assets/Recrutement/inde."

export const CandidatesData = [
  {
    id: "1",
    task: "Organisation du séminaire TIC",
    assigned_To: "Faris",
    assignee: "Layla",
    Status: "À faire",
    priority: "Haute",
    due_Date: "15-Mars-2025",
    sector: "tic",
    objective: "Explorer les opportunités d'innovation dans le secteur TIC",
    speakers: "Experts en IA et cybersécurité",
    workshops: "Ateliers sur l'IA et la blockchain",
    documents: ["presentation.pdf", "agenda.docx"],
    logistics: {
      travel: "Vols pris en charge par l'organisateur",
      hotel: "Réservations à l'Hôtel Radisson",
      transport: "Navettes disponibles",
      welcome: "Accueil VIP pour les conférenciers",
    },
  },
  {
    id: "2",
    task: "Préparation du forum sur l'énergie",
    assigned_To: "<PERSON>",
    assignee: "<PERSON>",
    Status: "En cours",
    priority: "Moyenne",
    due_Date: "10-Avril-2025",
    sector: "energie",
    objective: "Discussion sur les énergies renouvelables",
    speakers: "Ministres et experts en énergie",
    workshops: "Sessions sur l'hydrogène vert",
    documents: ["brochure.pdf"],
    logistics: {
      travel: "Billets de train fournis",
      hotel: "Intercontinental Hotel",
      transport: "Voitures de fonction",
      welcome: "Réception au hall principal",
    },
  },
];

export const columnsFromBackend = {
  [uuidv4()]: {
    title: "Planifié",
    items: CandidatesData.filter((item) => item.Status === "À faire"),
  },
  [uuidv4()]: {
    title: "En cours",
    items: CandidatesData.filter((item) => item.Status === "En cours"),
  },
  [uuidv4()]: {
    title: "Terminé",
    items: CandidatesData.filter((item) => item.Status === "Test"),
  },
};
