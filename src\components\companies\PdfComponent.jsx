import React from "react";
import fipaLogo from "./fipa.png"


const styles = {
    container: {
      fontFamily: 'Arial, sans-serif',
      padding: '20px',
      backgroundColor: '#fff',
    },
    header: {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: '20px',
    },
    logoContainer: {
      flex: 1,
    },
    logo: {
      width: '100px', // Adjust based on your logo size
      height: 'auto',
    },
    titleContainer: {
      flex: 2,
      textAlign: 'right',
    },
    headerTitle: {
      fontSize: '24px',
      fontWeight: 'bold',
    },
    body: {
      textAlign: 'center',
    },
    bodyTitle: {
      fontSize: '28px',
      fontWeight: 'bold',
      marginBottom: '20px',
    },
    bodyContent: {
      fontSize: '16px',
      lineHeight: '1.6',
    },
  };

const PdfComponent = React.forwardRef(({ record }, ref) => {

  const companyData = {
    identification: {
      codeDouane: '0420029F01',
      dateEP: '10/05/1980',
      situation: 'Autres',
      marche: 'MARCHE LOCAL',
      formeJuridique: 'Société à responsabilité limitée',
      groupe: '',
    },
    coordonnees: {
      responsable: 'M.GIRVES',
      siege: 'RTE DE FOUCHANA KM 8-2082 FOUCHANA',
      usine: 'RTE DE FOUCHANA KM 8-2082 FOUCHANA',
      tel: '71.245.716',
      fax: '',
    },
    activite: {
      secteur: 'Agro-alimentaire',
      activite: 'AUTRES INDUSTRIES AGRICOLES ET ALIMENTAIRES',
      production: '',
    }
  };
  return (
    <div ref={ref} style={styles.container}>
        <div style={styles.header}>
        <div style={styles.logoContainer}>
          <img src={fipaLogo} alt="Logo" style={styles.logo} />
        </div>
        <div style={styles.titleContainer}>
          <h1 style={styles.headerTitle}>FIPA</h1>
        </div>
      </div>

      <main>
      <div style={{ textAlign: 'center', marginBottom: '20px' }}>
          <h1 style={{ margin: 0 }}>F.I.P.A - Utilitateur</h1>
          <h2 style={{ margin: '5px 0' }}>FIPA</h2>
          <h3 style={{ margin: '10px 0', color: '#555' }}># LA REINE DU JOUGAR</h3>
        </div>

        {/* Identification Section */}
        <Section title="Identification de l'Entreprise">
          <DataRow label="Code Douane" value={companyData.identification.codeDouane} />
          <DataRow label="Date E.P." value={companyData.identification.dateEP} />
          <DataRow label="Situation" value={companyData.identification.situation} />
          <DataRow label="Marché" value={companyData.identification.marche} />
          <DataRow label="Forme juridique" value={companyData.identification.formeJuridique} />
          <DataRow label="Groupe" value={companyData.identification.groupe} />
        </Section>

        {/* Coordonnées Section */}
        <Section title="Coordonnées de l'Entreprise">
          <DataRow label="Responsable" value={companyData.coordonnees.responsable} />
          <DataRow label="Adresse Siège" value={companyData.coordonnees.siege} />
          <DataRow label="Adresse Usine" value={companyData.coordonnees.usine} />
          <DataRow label="Tél Siège" value={companyData.coordonnees.tel} />
          <DataRow label="Fax Siège" value={companyData.coordonnees.fax} />
        </Section>

        {/* Activité Section */}
        <Section title="Activité de l'Entreprise">
          <DataRow label="Secteur" value={companyData.activite.secteur} />
          <DataRow label="Activite" value={companyData.activite.activite} />
          <DataRow label="Production" value={companyData.activite.production} />
        </Section>

          <section>


            <h3>Remarques : societé en cessation d'activité</h3>
          </section>
        </main>

        {/* Footer (optional) */}
        <footer
          style={{
            marginTop: "20px",
            borderTop: "1px solid #ddd",
            paddingTop: "10px",
            textAlign: "center",
            fontSize: "12px",
            color: "#666",
          }}
        >
          <p>  {new Date().getFullYear()} FIPA</p>
        </footer>

    </div>
  );
});
const Section = ({ title, children }) => (
  <div style={{ 
    margin: '15px 0',
    padding: '10px 0',
    borderBottom: '1px solid #ddd'
  }}>
    <h4 style={{ 
      margin: '0 0 10px 0',
      fontSize: '16px',
      fontWeight: 'bold',
      color: '#333'
    }}>{title}</h4>
    {children}
  </div>
);

// Reusable Data Row Component
const DataRow = ({ label, value }) => (
  <div style={{
    display: 'grid',
    gridTemplateColumns: '150px 1fr',
    margin: '5px 0',
    alignItems: 'center'
  }}>
    <span style={{ fontWeight: '600', color: '#666' }}>{label}</span>
    <span style={{ paddingLeft: '10px' }}>{value || '-'}</span>
  </div>
);
export default PdfComponent;
