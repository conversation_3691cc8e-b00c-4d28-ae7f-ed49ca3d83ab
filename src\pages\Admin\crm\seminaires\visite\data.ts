import dayjs from "dayjs";

const visites = [
  {
    id: 1,
    num_visite : 1235,
    nb_visite : 6 ,
    raison_social: "Raison Sociale",
    pays_id: 1,
    type_id: 1,
    region : "nom de la région ",
    fonction : "D.G ",
    responsable  : "<PERSON> DAUSSE" ,
    secteur_id : 1  ,
    organisme_id : 2 ,
    date_contact: dayjs("2025-01-10"),
    date_fin: dayjs("2025-01-10"),
  },
];
const pays = [
  { id: 1, nom: "France" },
  { id: 2, nom: "Italie" },
  { id: 3, nom: "Allemagne" },
  { id: 4, nom: "Espagne" },
  { id: 5, nom: "États-Unis" },
  { id: 6, nom: "Canada" },
  { id: 7, nom: "Algérie" },
  { id: 8, nom: "Maroc" },
  { id: 9, nom: "Libye" },
  { id: 10, nom: "Turquie" },
  { id: 11, nom: "<PERSON><PERSON>" },
  { id: 12, nom: "Émirats arabes unis" },
  { id: 13, nom: "Qatar" },
  { id: 14, nom: "Égypte" },
  { id: 15, nom: "Chine" },
  { id: 16, nom: "Japon" },
  { id: 17, nom: "Corée du Sud" },
  { id: 18, nom: "Belgique" },
  { id: 19, nom: "Suisse" },
  { id: 20, nom: "Russie" },
];

const responsables =[
   { id: 1, nom: "Monsieur DAUSSE" },
] ;
const types =[
   { id: 1, type : "Une visite en Tunisie encadrée" },
   { id: 2, type : "Une visite en Tunisie non encadrée par le siège" },
   { id: 3, type : "Une visite spontanée" },
] ;

const organismes = [
 {id : 1 , nom : "FIPA Paris"} ,
 {id : 2 , nom : "FIPA Bruxelles"} ,
 {id : 3 , nom : "FIPA Londres"} ,
 {id : 4 , nom : "FIPA Milan"} ,
 {id : 5 , nom : "FIPA Madrid"} ,
 {id : 6 , nom : "FIPA Cologne"} ,
 {id : 7 , nom : "Chambre de Commerce"} ,
 {id : 8 , nom : "Partenaire tunisien"} ,
 {id : 9 , nom : "Ambassade"} 
]  ;

  const secteurs = [
    { id: 1, secteur: "Industries diverses" },
    { id: 2, secteur: "Technologie" },
    { id: 3, secteur: "Services" },
    { id: 4, secteur: "Tourisme" },
    { id: 5, secteur: "Agriculture" },
    { id: 6, secteur: "Energie" },

  ];
export { visites, pays ,responsables , types , organismes , secteurs};
