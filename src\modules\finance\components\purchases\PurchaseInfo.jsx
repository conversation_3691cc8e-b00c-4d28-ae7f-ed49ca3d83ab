import { Card, Col, DatePicker, Form, Input, Row, Select } from "antd";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import {
  entreprises,
  departements,
  clients
} from "../../pages/sales/purchases/data";

const PurchaseInfo = ({ form ,record , open }) => {
  const { t } = useTranslation();
  const dateFormat = "YYYY-MM-DD";
  const [entrepriseId, setEntrepriseId] = useState(null);
  const [departementId, setDepartementId] = useState(null);
  const [tierId, setTierId] = useState(null);
  const showTierData = (e) => {
    const tier = clients.find((t) => t.id == e);
    form.setFieldsValue({
      email: tier?.email,
      adresse: tier?.adresse,
      phone: tier?.phone,
      matricule_fiscale: tier?.matricule_fiscale,
    });
  };

  const resetTierData = () => {
    form.setFieldsValue({
      email: null,
      adresse: null,
      phone: null,
      matricule_fiscale: null,
    });
  };

    useEffect(() => {
    if (open) {
      form.setFieldsValue(record);
      setEntrepriseId(record?.entreprise_id);
      setDepartementId(record?.departement_id);
      setTierId(record?.tier_id);
      showTierData(record?.tier_id);
    }
  }, [open]);
  return (
    <Row className="mt-5" gutter={[12, 24]}>
      <Col span={12}>
        <Card title={t("sales.general_info")}>
          <Form.Item
            name={"num_cmd"}
            label={t("cmd_cols.num_cmd")}
            style={{
              display: "inline-block",
              width: "45%",
              margin: "0 8px 8px",
            }}
          >
            <Input disabled={true} />
          </Form.Item>
          <Form.Item
            name={"date_cmd"}
            label={
              <span>
                {t("cmd_cols.date_cmd")} <span style={{ color: "red" }}>*</span>
              </span>
            }
            style={{
              display: "inline-block",
              width: "45%",
              margin: "0 8px 8px",
            }}
            rules={[
              { required: true, message: t("sales_message.required_field") },
            ]}
          >
            <DatePicker
              format={dateFormat}
              style={{ width: "100%" }}
              //  disabled={show}
            />
          </Form.Item>
          <Form.Item
            name={"entreprise_id"}
            label={
              <span>
                {t("sales.entreprise")}{" "}
                <span style={{ color: "red" }}>*</span>
              </span>
            }
            style={{
              display: "inline-block",
              width: "45%",
              margin: "0 8px 8px",
            }}
            rules={[
              {
                required: true,
                message: t("sales_message.required_field"),
              },
            ]}
          >
            <Select
              showSearch
              allowClear
              placeholder={t("sales.entreprise")}
              options={entreprises.map((item) => ({
                value: item.id,
                label: item.nom,
              }))}
              value={entrepriseId}
              onSelect={(e) => {
                setEntrepriseId(e);
              }}
              onClear={() => {
                setEntrepriseId(null);
                setDepartementId(null);
                setTierId(null);
              }}
              //  disabled={show}
            />
          </Form.Item>
          <Form.Item
            name="departement_id"
            label={
              <span>
                {t("sales.departement")}{" "}
                <span style={{ color: "red" }}>*</span>
              </span>
            }
            style={{
              display: "inline-block",
              width: "45%",
              margin: "0 8px 8px",
            }}
            rules={[
              {
                required: true,
                message: t("sales_message.required_field"),
              },
            ]}
          >
            <Select
              showSearch
              allowClear
              placeholder={t("sales.departement")}
              options={departements
                .filter((dept) => dept.entreprise_id === entrepriseId)
                .map((item) => ({ value: item.id, label: item.nom }))}
              value={departementId}
              onSelect={(e) => {
                setDepartementId(e);
              }}
              onClear={() => {
                setDepartementId(null);
                setTierId(null);
              }}
            />
          </Form.Item>
        </Card>
      </Col>
      <Col span={12}>
        <Card title={t("sales.client_info")}>
          <Form.Item
            name={"tier_id"}
            label={
              <span>
                {t("sales.client")} <span style={{ color: "red" }}>*</span>
              </span>
            }
            style={{
              display: "inline-block",
              width: "45%",
              margin: "0 8px 8px",
            }}
            rules={[
              { required: true, message: t("sales_message.required_field") },
            ]}
          >
            <Select
              showSearch
              allowClear
              placeholder={t("sales.client")}
              value={tierId}
              options={clients
                .filter(
                  (tier) =>
                    tier.entreprise_id === entrepriseId &&
                    tier.departement_id === departementId
                )
                .map((item) => ({ value: item.id, label: item.nom }))}
              onSelect={(e) => {
                setTierId(e);
                showTierData(e);
              }}
              onClear={() => {
                resetTierData();
              }}
            />
          </Form.Item>
          <Form.Item
            name={"matricule_fiscale"}
            label={t("sales.matricule")}
            style={{
              display: "inline-block",
              width: "45%",
              margin: "0 8px 8px",
            }}
          >
            <Input autoFocus />
          </Form.Item>

          <Form.Item
            name="email"
            label={t("sales.email")}
            style={{
              display: "inline-block",
              width: "45%",
              margin: "0 8px 8px",
            }}
          >
            <Input autoFocus />
          </Form.Item>
          <Form.Item
            name="phone"
            label={t("sales.tel")}
            style={{
              display: "inline-block",
              width: "45%",
              margin: "0 8px 8px",
            }}
          >
            <Input autoFocus />
          </Form.Item>
          <Form.Item
            name="adresse"
            label={t("sales.address")}
            style={{
              display: "inline-block",
              width: "94%",
              margin: "0 8px 8px",
            }}
          >
            <Input autoFocus />
          </Form.Item>
        </Card>
      </Col>
    </Row>
  );
};
export default PurchaseInfo;
