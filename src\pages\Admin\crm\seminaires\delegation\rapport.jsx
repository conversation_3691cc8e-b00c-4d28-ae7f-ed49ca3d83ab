import { ModalForm } from "@ant-design/pro-components";
import { Card, Col, DatePicker, Form, Input, Row } from "antd";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

function RapportForm({ open, onCancel, record }) {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  return (
    <ModalForm
      title={t("Rapport de visite ")}
      form={form}
      open={open}
      width="70%"
      modalProps={{
        style: { top: 20 },
      }}
      //   onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.save"),
          resetText: t("common.actions.cancel"),
        },
      }}
    >
      <Card className="mt-2">
        <Row>
          <Input.TextArea rows={10} />
        </Row>
      </Card>
    </ModalForm>
  );
}
export default RapportForm;
