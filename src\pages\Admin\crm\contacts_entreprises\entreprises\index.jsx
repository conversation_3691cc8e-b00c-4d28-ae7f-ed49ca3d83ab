import { useState, useRef, useEffect } from "react";
import { ProTable } from "@ant-design/pro-components";
import {
  Button,
  Popconfirm,
  Space,
  Input,
  Tooltip,
  Typography,
  Card,
  Form,
  Tag,
  Upload,
} from "antd";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  SearchOutlined,
  InteractionOutlined,
  PrinterOutlined,
  ProjectOutlined,
  ContactsOutlined,
} from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { pays, entreprises, secteurs } from "./data";
import CreateCompany from "./create";

import * as XLSX from "xlsx";

import { useReactToPrint } from "react-to-print";
import EditCompany from "./edit";
import ShowCompany from "./view";
import CreateSecondCompany from "./createsecond";
import Interactions from "./interaction";
import ProjectListPanel from "./projects";
import CompanyContacts from "./contact";

function Company() {
  const tableRef = useRef();
  const { t } = useTranslation();
  const [tableParams, setTableParams] = useState({}); // Paramètres de recherche et de filtrage
  const [isLoading, setIsLoading] = useState(false); // Indique si les données sont en cours de chargement
  const [data, setData] = useState(entreprises); // Données affichées dans la table
  const [total, setTotal] = useState(data.length); // Nombre total de lignes affichées
  const [pageNumber, setPageNumber] = useState(1); // Numéro de page actuelle
  const [dataSource, setDataSource] = useState([]);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [viewModalVisible, setViewModalVisible] = useState(false);
  const [interModalVisible, setInterModalVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState(null);
  const [viewingRecord, setViewingRecord] = useState(null);
  const [interctingRecord, setInterctingRecord] = useState(null);
  const [projetModalVisible, setProjetModalVisible] = useState(false);
  const [projetRecord, setProjetRecord] = useState(null);
  const [contactModalVisible, setContactModalVisible] = useState(false);
  const [contactRecord, setContactRecord] = useState(null);
  const [form] = Form.useForm();
  // Déclenchement de la recherche avec un délai (700ms)
  const handleSearch = (e) => {
    const searchValue = e.target.value; // Récupération de la valeur de recherche
    setTableParams({ ...tableParams, search: searchValue }); // Mise à jour des paramètres
  };
  const [activeFilters, setActiveFilters] = useState({});
  const columns = [
    {
      title: t("Raison Sociale"),
      dataIndex: "raison_social",
      key: "raison_social",
      ellipsis: true,
      order: 1,
      render: (text) => {
        return (
          <Typography.Text ellipsis={{ tooltip: text }}>{text}</Typography.Text>
        );
      },
    },
    {
      title: t("Pays"),
      dataIndex: "pays_id",
      key: "pays_id",
      ellipsis: true,
      valueType: "select",
      valueEnum: () =>
        Object.fromEntries(
          pays.map((value) => [value.id, { text: value.nom }])
        ),
      order: 1,
      render: (text) => {
        return (
          <Typography.Text ellipsis={{ tooltip: text }}>{text}</Typography.Text>
        );
      },
    },

    {
      title: t("Secteur"),
      dataIndex: "secteur_id",
      key: "secteur_id",
      ellipsis: true,
      valueType: "select",
      valueEnum: () =>
        Object.fromEntries(
          secteurs.map((value) => [value.id, { text: value.secteur }])
        ),
      order: 1,
      render: (text) => {
        return (
          <Typography.Text ellipsis={{ tooltip: text }}>{text}</Typography.Text>
        );
      },
    },

    {
      title: t("Chiffres d'affaires"),
      dataIndex: "chiffre_affaire",
      key: "chiffre_affaire",
      ellipsis: true,
      search: false,
      order: 1,
      render: (text) => {
        return (
          <Typography.Text ellipsis={{ tooltip: text }}>{text}</Typography.Text>
        );
      },
    },
    {
      title: t("Actions"),
      key: "action",
      align: "center",
      sorter: false,
      search: false,
      render: (_, record) => (
        <Space size="auto">
          <Tooltip title={t("common.actions.view")}>
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => {
                setViewingRecord(record);
                setViewModalVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title={t("common.actions.edit")}>
            <Button
              type="link"
              style={{ color: "#f5b041" }}
              icon={<EditOutlined />}
              onClick={() => {
                setEditingRecord(record);
                setEditModalVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title={t("Intercations")}>
            <Button
              type="link"
              style={{ color: "#197823" }}
              icon={<InteractionOutlined />}
              onClick={() => {
                setInterctingRecord(record);
                setInterModalVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title={t("Projets")}>
            <Button
              type="link"
              style={{ color: "#e6be10" }}
              icon={<ProjectOutlined />}
              onClick={() => {
                setProjetRecord(record);
                setProjetModalVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title={t("Contacts")}>
            <Button
              type="link"
              style={{ color: "#354a44" }}
              icon={<ContactsOutlined />}
              onClick={() => {
                setContactRecord(record);
                setContactModalVisible(true);
              }}
            ></Button>
          </Tooltip>
          <Tooltip title={t("Imprimer la Fiche Entreprise")}>
            <Button
              type="link"
              style={{ color: "#fa021f" }}
              icon={<PrinterOutlined />}
              onClick={() => {
                handleSetRefAndPrint(record);
              }}
            />
          </Tooltip>

          <Tooltip title={t("common.actions.delete")}>
            <Popconfirm
              title={t("common.messages.confirm_delete")}
              onConfirm={() => {
                const newDataSource = dataSource.filter(
                  (item) => item.key !== record.key
                );
                setDataSource(newDataSource);
              }}
            >
              <Button
                type="link"
                style={{ color: "#ec7063" }}
                icon={<DeleteOutlined />}
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  const onFinish = (values) => {
    setTansferLoading(true);
    setTimeout(() => {
      setTansferLoading(false);
      setIsModalOpen(false);
    }, [1000]);
  };
  const [isUpload, setIsUpload] = useState(false);
  const [importContact, setImportContact] = useState([]);
  const handleFileUpload = (file) => {
    const reader = new FileReader();
    setIsUpload(true);
    reader.onload = (e) => {
      const arrayBuffer = e.target.result;
      const workbook = XLSX.read(arrayBuffer, { type: "array" });
      const firstSheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[firstSheetName];
      const jsonData = XLSX.utils.sheet_to_json(worksheet, {
        header: 1,
        raw: false,
        dateNF: "yyyy-mm-dd hh:mm:ss",
      });

      const headers = jsonData[1];

      const contacts = jsonData
        .slice(2)
        .filter((row) => {
          return row.length > 0;
        })
        .map((row, rowIndex) => {
          if (row.length > 0) {
            return element;
          }
        });

      contacts.forEach((row, index) => {
        return { ...row, key: index };
      });

      setImportContact(contacts);
      console.log(importContact);
    };
    reader.readAsArrayBuffer(file);
    setTimeout(() => {
      setIsUpload(false);
    }, 2000);
    return false;
  };

  var contentToPrintRef = useRef(null);
  const componentRefs = useRef({});
  // Set up the print function for each row
  const handlePrint = useReactToPrint({
    documentTitle: "Title",
    contentRef: contentToPrintRef,
  });

  // Set the ref and trigger print for a row
  const handleSetRefAndPrint = (record) => {
    // Check if the record has a valid key before setting the ref
    if (record.id) {
      contentToPrintRef = componentRefs.current[record.id];
      handlePrint();
    }
  };

  const handleDownload = (filters) => {
    // Prepare data for export
    const dataToExport = contacts.map((contact) => {
      const entreprise = entreprises.find(
        (ent) => ent.id === contact.entreprise_id
      );
      const fonction = fonctions.find(
        (func) => func.id === contact.fonction_id
      );
      const secteur = secteurs.find((sec) => sec.id === contact.secteur_id);
      const type = type_contact.find(
        (type) => type.id === contact.type_contact_id
      );

      return {
        Numéro: contact.id,
        Contact: contact.contact,
        Entreprise: entreprise ? entreprise.raison_social : "",
        Fonction: fonction ? fonction.nom : "",
        Secteur: secteur ? secteur.nom : "",
        Type: type ? type.type : "",
      };
    });

    // Create worksheet and workbook
    const worksheet = XLSX.utils.json_to_sheet(dataToExport);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Contacts");

    // Export to Excel file
    XLSX.writeFile(workbook, "contacts.xlsx");
  };
  return (
    <>
      <Card
        title={
          <Typography.Title level={4}>
            {t("Entreprises Cibles")}
          </Typography.Title>
        }
        extra={
          <Space>
            {/* Champ de recherche */}
            <Input
              size="large"
              placeholder={t("Rechercher")}
              suffix={<SearchOutlined style={{ color: "#bfbfbf" }} />}
              //    onChange={debouncedOnChange}
            />

            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setCreateModalVisible(true)}
            >
              {t("Ajouter")}
            </Button>
          </Space>
        }
      >
        <ProTable
          actionRef={tableRef} // Référence pour accéder aux actions
          search={{
            searchText: t("Filtrer"),
            labelWidth: "auto",
            defaultCollapsed: false,
          }}
          loading={isLoading}
          columns={columns}
          dataSource={data}
          rowKey="id"
          params={tableParams}
          scroll={{ x: "max-content" }}
          options={false}
          request={async (params) => {
            setIsLoading(true); // Démarrage du chargement
            setActiveFilters(params);
            setTimeout(() => {
              // Simule une requête serveur
              setIsLoading(false);
            }, 500);
            return {
              // Retourne les données
              data: data,
              success: true,
            };
          }}
          pagination={{
            showSizeChanger: true, // Permet de modifier la taille des pages
            defaultPageSize: 3, // Taille de page par défaut
            total: total, // Nombre total de lignes
            onChange: (page) => setPageNumber(page), // Fonction de changement de page
          }}
        />
        {createModalVisible && (
          <>
            {/* <CreateCompany
            open={createModalVisible}
            onCancel={() => setCreateModalVisible(false)}
            onSuccess={(newDomain) => {
              setDataSource([...dataSource, newDomain]);
              setCreateModalVisible(false);
            }}
          /> */}
            <CreateSecondCompany
              open={createModalVisible}
              onCancel={() => setCreateModalVisible(false)}
              onSuccess={(newDomain) => {
                setDataSource([...dataSource, newDomain]);
                setCreateModalVisible(false);
              }}
            />
          </>
        )}
        {editModalVisible && (
          <EditCompany
            open={editModalVisible}
            record={editingRecord}
            onCancel={() => {
              setEditModalVisible(false);
              setEditingRecord(null);
            }}
          />
        )}
        {viewModalVisible && (
          <ShowCompany
            open={viewModalVisible}
            record={viewingRecord}
            onCancel={() => {
              setViewModalVisible(false);
              setViewingRecord(null);
            }}
          />
        )}
        {interModalVisible && (
          <Interactions
            open={interModalVisible}
            record={interctingRecord}
            onCancel={() => {
              setInterctingRecord(null);
              setInterModalVisible(false);
            }}
          />
        )}
        {projetModalVisible && (
          <ProjectListPanel
            open={projetModalVisible}
            record={projetRecord}
            onCancel={() => {
              setProjetModalVisible(false);
              setProjetRecord(null);
            }}
          />
        )}
        {contactModalVisible && (
          <CompanyContacts
            open={contactModalVisible}
            record={contactRecord}
            onCancel={() => {
              setContactModalVisible(false);
              setContactRecord(null);
            }}
          />
        )}
      </Card>
    </>
  );
}
export default Company;
