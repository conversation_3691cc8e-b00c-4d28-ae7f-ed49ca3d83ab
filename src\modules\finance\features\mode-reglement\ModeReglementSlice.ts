import {createAsyncThunk, createSlice} from "@reduxjs/toolkit";
import { default as api } from "@/apis/axiosInterceptor";

export const getModeReglement: any = createAsyncThunk(
  "get/ModeReglement",
  async (data: { pageNumber, code, libelle, }, _thunkAPI) => {
    try {
      let url = `/modeReglements`;
      if (data.pageNumber) {
        url += `?page=${data.pageNumber}`;
      }
      if (data.code) {
        url += `&code=${data.code}`;
      }
      if (data.libelle) {
        url += `&libelle=${data.libelle}`;
      }
      const resp = await api.get(url);
      return resp.data;
    } catch (error: any) {
      console.log(error);
    }
  }
);

export const addModeReglement: any = createAsyncThunk(
  "create/modeReglement",
  async (data: any, _thunkAPI) => {
    try {
      let url = `/modeReglements`;
      const resp = await api.post(url, data);
      _thunkAPI.dispatch(getModeReglement())
      return resp.data;
    } catch (error: any) {
      console.log(error);
    }
  }
);

export const updateModeReglement: any = createAsyncThunk(
  "update/modeReglement",
  async (data: any, _thunkAPI) => {
    try {
      let url = `/modeReglements/${data.id}`;
      const resp = await api.patch(url, data);
      _thunkAPI.dispatch(getModeReglement())
      return resp.data;
    } catch (error: any) {
      console.log(error);
    }
  }
)
export const deleteModeReglement: any = createAsyncThunk(
  "delete/modeReglement",
  async (id: any, _thunkAPI) => {
    try {
      let url = `/modeReglements/${id}`;
      const resp = await api.delete(url);
      _thunkAPI.dispatch(getModeReglement())

      return resp.data;
    } catch (error: any) {
      console.log(error);
    }
  }
);

export const getMoyenPaiement: any = createAsyncThunk(
  "get/moyenPaiement",
  async (_, _thunkAPI) => {
    try {
      let url = `/moyenPaiements`;
      const resp = await api.get(url);
      return resp.data;
    } catch (error: any) {
      console.log(error);
    }
  }
)

export const modeReglementSlice = createSlice({
  name: 'modeReglement',
  initialState: {
    data: [],
  },
  reducers: {},
  extraReducers: builder => {
    builder.addCase(getModeReglement.fulfilled, (state, { payload }) => {
      state.data = payload;
    })
  }
})

export default modeReglementSlice.reducer

