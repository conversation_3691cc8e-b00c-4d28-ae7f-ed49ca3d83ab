import { Col, Form, Input, Row, Select } from "antd";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";

const EmplacementForm = ({ disabled, location }) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();

  useEffect(() => {
    if (location) {
      form.setFieldsValue(location);
    }
  }, [location]);

  return (
    <Form layout="vertical" disabled={disabled} form={form}>
      <Row gutter={4} className="mt-2">
        <Col className="gutter-row" span={24}>
          <Form.Item
            name="code"
            label={t("emplacements.fields.code")}
            rules={[
              {
                required: true,
                message: "emplacements.validations.required_code",
              },
            ]}
          >
            <Input placeholder={t("emplacements.fields.code")} />
          </Form.Item>
        </Col>
        <Col className="gutter-row" span={24}>
          <Form.Item
            name="libelle"
            label={t("emplacements.fields.libelle")}
            rules={[
              {
                required: true,
                message: "emplacements.validations.required_libelle",
              },
            ]}
          >
            <Input placeholder={t("emplacements.fields.libelle")} />
          </Form.Item>
        </Col>
        <Col className="gutter-row" span={24}>
          <Form.Item
            name="depot"
            label={t("emplacements.fields.depot")}
            rules={[
              {
                required: true,
                message: "emplacements.validations.required_depot",
              },
            ]}
          >
            <Select
              required
              placeholder={t("emplacements.fields.depot")}
              allowClear
              options={[
                { label: "Dépôt Logistique", value: "Dépôt Logistique" },
                { label: "Dépôt Restauration", value: "Dépôt Restauration" },
                { label: "Dépôt Fournitures", value: "Dépôt Fournitures" },
                { label: "Dépôt Principal", value: "Dépôt Principal" },
                {
                  label: "Dépôt Tac-Tic Ghazela",
                  value: "Dépôt Tac-Tic Ghazela",
                },
              ]}
            />
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );
};

export default EmplacementForm;
