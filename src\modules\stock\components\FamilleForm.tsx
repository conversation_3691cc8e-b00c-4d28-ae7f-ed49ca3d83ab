import { Checkbox, Col, Form, Input, Row, Select } from "antd";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";

const FamilleForm = ({tvas, }) => {
  const { t } = useTranslation();
  const filterOption = (input, option) =>
    (option?.label ?? "").toLowerCase().includes(input.toLowerCase());
  const filterSort = (optionA, optionB) =>
    (optionA?.label ?? "")
    .toLowerCase()
    .localeCompare((optionB?.label ?? "").toLowerCase());
    

  return (
    <Row gutter={8}>
    <Col md={12} xs={24}>
      <Form.Item
        name="code"
        label={t("familles.fields.code")}
        rules={[{ required: true, message: t("common.validation.required") }]}
      >
        <Input placeholder={t("familles.fields.code")} />
      </Form.Item>
    </Col>
    <Col md={12} xs={24}>
      <Form.Item
        name="libelle"
        label={t("familles.fields.libelle")}
        rules={[{ required: true, message: t("common.validation.required") }]}
      >
        <Input placeholder={t("familles.fields.libelle")} />
      </Form.Item>
    </Col>
    <Col md={12} xs={24}>
      <Form.Item name="tva_id" 
        label={t("familles.fields.tva")}
        // rules={[{ required: true, message: t("common.validation.required") }]}
      >
        <Select
            placeholder={t("familles.fields.tva")}
            filterOption={filterOption}
            filterSort={filterSort}
            allowClear
            showSearch
            options={tvas}
          />
      </Form.Item>
    </Col>
    <Col md={12} xs={24}>
      <Form.Item name="remise" 
        label={t("familles.fields.remise")}
        // rules={[{ required: true, message: t("common.validation.required") }]}
      >
        <Input type="number" placeholder={t("familles.fields.remise")} />
      </Form.Item>
    </Col>
   
    <Col md={12} xs={24}>
      <Form.Item name="allow_negative_stock" valuePropName="checked" >
        <Checkbox >{t("familles.fields.allow_negative_stock")}</Checkbox>
      </Form.Item>
    </Col>
  </Row>
  );
};

export default FamilleForm;
