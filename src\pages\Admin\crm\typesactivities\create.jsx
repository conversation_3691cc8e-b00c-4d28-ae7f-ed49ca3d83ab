import { ProForm, ProFormText, ProFormDatePicker, ProFormTextArea } from "@ant-design/pro-form";
import { message } from "antd";
import dayjs from "dayjs";
import { useTranslation } from "react-i18next";
import { datas } from "./datas";

const CreateActivity = ({ onCancel, setDataSource }) => {
  const { t } = useTranslation();

  const handleSubmit = async (values) => {
    try {
      const newKey = Date.now();
      const formattedPlanifier = dayjs(values.planifier).format("DD/MM/YYYY");
      const newActivity = {
        key: newKey,
        nom: values.nom,
        resume: values.resume,
        planifier: formattedPlanifier,
      };
      const updatedData = [...datas, newActivity];
      setDataSource(updatedData);
      message.success(t("crm.activities.create.messages.success"));
      onCancel();
    } catch (error) {
      console.log(error);
      message.error(t("crm.activities.create.messages.error"));
    }
  };

  return (
    <>
      <h2>{t("crm.activities.create.title")}</h2>
      <ProForm onFinish={handleSubmit}>
        <ProFormText
          name="nom"
          label={"Nom"}
          placeholder={"Nom"}
          rules={[{ required: true }]}
        />
        <ProFormTextArea
          name="resume"
          label={"Resumé"}
          placeholder={"Résumé"}
          rules={[{ required: true }]}
        />
        {/* <ProFormDatePicker
          name="planifier"
          label={t("crm.activities.create.fields.planifier")}
          rules={[{ required: true }]}
        /> */}
      </ProForm>
    </>
  );
};

export default CreateActivity;
