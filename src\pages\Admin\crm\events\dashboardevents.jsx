import React, { useState } from 'react';
import { Tabs, Row, Col, Statistic, Card, Modal, Button, Progress, Alert, Calendar, Badge } from 'antd';
import dayjs from 'dayjs';
import { Line } from 'react-chartjs-2';
import { Chart, CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend } from 'chart.js';
import { eventsData } from './datas';

// Register Chart.js components
Chart.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend);

const { TabPane } = Tabs;

const DashboardEvents = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [value, setValue] = useState(() => dayjs());
  const [selectedValue, setSelectedValue] = useState(() => dayjs());

  // Count events by status
  const countByStatus = eventsData.reduce((acc, event) => {
    acc[event.status] = (acc[event.status] || 0) + 1;
    return acc;
  }, {});

  const totalEvents = eventsData.length;

  // Data for Line Chart
  const lineChartData = {
    labels: ['Planifié', 'En cours', 'Terminé'],
    datasets: [
      {
        label: 'Nombre d\'Événements',
        data: [countByStatus['Planifié'] || 0, countByStatus['En cours'] || 0, countByStatus['Terminé'] || 0],
        borderColor: 'rgba(75,192,192,1)',
        backgroundColor: 'rgba(75,192,192,0.2)',
        fill: true,
      },
    ],
  };

  // Handle date selection
  const onSelect = (newValue) => {
    setValue(newValue);
    setSelectedValue(newValue);
    const event = eventsData.find(event => event.date === newValue.format('YYYY-MM-DD'));

    console.log(newValue.format('YYYY-MM-DD'));
    if (event) {
      setSelectedEvent(event);
      setIsModalOpen(true);
    }
  };

  const onPanelChange = (newValue) => {
    setValue(newValue);
  };

  return (
    <div style={{ padding: '20px' }}>
      <h1>Tableau de Bord des Événements</h1>

      <Tabs defaultActiveKey="1">
        {/* Statistiques Tab */}
        <TabPane tab="Statistiques" key="1">
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <Card>
                <Line data={lineChartData} />
              </Card>
            </Col>
            <Col span={8}>
              <Statistic title="Événements Planifiés" value={countByStatus['Planifié'] || 0} />
              <Progress percent={((countByStatus['Planifié'] || 0) / totalEvents) * 100} status="active" />
            </Col>
            <Col span={8}>
              <Statistic title="Événements en Cours" value={countByStatus['En cours'] || 0} />
              <Progress percent={((countByStatus['En cours'] || 0) / totalEvents) * 100} status="active" />
            </Col>
            <Col span={8}>
              <Statistic title="Événements Terminés" value={countByStatus['Terminé'] || 0} />
              <Progress percent={((countByStatus['Terminé'] || 0) / totalEvents) * 100} status="success" />
            </Col>
          </Row>
        </TabPane>

        {/* Calendar Tab */}
        <TabPane tab="Calendrier" key="2">
          {/* <Alert message={`Vous avez sélectionné la date : ${selectedValue?.format('YYYY-MM-DD')}`} /> */}
          <Card style={{ marginBottom: 20 }}>
            <Calendar value={value} onSelect={onSelect} onPanelChange={onPanelChange} />
          </Card>

          {/* Modal for Event Details */}
          <Modal
            title="Détails de l'Événement"
            open={isModalOpen}
            onCancel={() => setIsModalOpen(false)}
            footer={[<Button key="close" onClick={() => setIsModalOpen(false)}>Fermer</Button>]}
          >
            {selectedEvent ? (
              <div>
                <h3>{selectedEvent.title}</h3>
                <p><strong>Secteur:</strong> {selectedEvent.sector}</p>
                <p><strong>Pays:</strong> {selectedEvent.country}</p>
                <p><strong>Date:</strong> {selectedEvent.date}</p>
                <p><strong>Statut:</strong> {selectedEvent.status}</p>
              </div>
            ) : (
              <p>Aucun événement sélectionné.</p>
            )}
          </Modal>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default DashboardEvents;
