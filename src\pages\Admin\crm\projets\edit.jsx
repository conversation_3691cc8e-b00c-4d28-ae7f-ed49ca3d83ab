import { ModalForm } from "@ant-design/pro-components";
import {
  Card,
  Col,
  DatePicker,
  Divider,
  Form,
  Input,
  InputNumber ,
  Radio,
  Row,
  Select 
} from "antd";

import TextArea from "antd/es/input/TextArea.js";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

function EditProjet({ open, onCancel, record, show }) {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [steps, setSteps] = useState(record.initialSteps);
  const [form] = Form.useForm();
  useEffect(() => {
    if (open) {
      form.setFieldsValue(record);
    }
  }, [open]);

  return (
    <ModalForm
      title={
        show ? t("Afficher le projet") : t("Modifier le projet")
      }
      form={form}
      open={open}
      width="60%"
      modalProps={{
        style: { top: 20 },
      }}
      //   onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={ show ? {
         submitButtonProps: { hidden: true },
        searchConfig: {
        
          resetText: t("common.actions.cancel"),
        },
      } : { searchConfig: {
          submitText: t("common.actions.edit"),
          resetText: t("common.actions.cancel"),
        }}}
    >
      <Divider>Informations générale</Divider>
           <Card className="mt-2">
             <Row gutter={16}>
               <Col md={8} xs={24}>
                 <Form.Item name={"responsable"} label={t("Responsable")}>
                   <Select disabled={show} />
                 </Form.Item>
               </Col>
               <Col md={4} xs={24}>
                 <Form.Item name={""} label={t(" ")}>
                   <Radio disabled={show}  >Important</Radio>
                 </Form.Item>
               </Col>
               <Col md={4} xs={24}>
                 <Form.Item name={""} label={t(" ")}>
                   <Radio disabled={show}  >Inclure</Radio>
                 </Form.Item>
               </Col>
               <Col md={8} xs={24}>
                 <Form.Item name={"date_maj"} label={t("Date MAJ")}>
                   <DatePicker disabled={show}  />
                 </Form.Item>
               </Col>
     
               <Col md={8} xs={24}>
                 <Form.Item name={""} label={t(" ")}>
                   <Radio disabled={show}  >Idée de projet</Radio>
                 </Form.Item>
               </Col>
               <Col md={8} xs={24}>
                 <Form.Item name={""} label={t(" ")}>
                   <Radio disabled={show} >Porjet en cours de réalisation</Radio>
                 </Form.Item>
               </Col>
               <Col md={8} xs={24}>
                 <Form.Item name={""} label={t(" ")}>
                   <Radio disabled={show}  > Projet entré en production </Radio>
                 </Form.Item>
               </Col>
     
               <Col md={8} xs={24}>
                 <Form.Item name={"date_category"} label={t("Date catégotie IP")}>
                   <DatePicker disabled={show}  ></DatePicker>
                 </Form.Item>
               </Col>
             </Row>
           </Card>
           <Divider>Coordonnées en Tunisie</Divider>
           <Card className="mt-2">
             <Row gutter={16}>
               <Col md={24} xs={24}>
                 <Form.Item name={""} label={t("RS en Tunisie")}>
                   <Input disabled={show} />
                 </Form.Item>
               </Col>
               <Col md={8} xs={24}>
                 <Form.Item name={""} label={t("Contact")}>
                   <Input disabled={show} />
                 </Form.Item>
               </Col>
               <Col md={12} xs={24}>
                 <Form.Item name={""} label={t("Tél.")}>
                   <DatePicker disabled={show} />
                 </Form.Item>
               </Col>
     
               <Col md={12} xs={24}>
                 <Form.Item name={""} label={t("Fax")}>
                   <DatePicker disabled={show} />
                 </Form.Item>
               </Col>
               <Col md={16} xs={24}>
                 <Form.Item name={""} label={t("Adr en Tunisie")}>
                   <Input disabled={show} />
                 </Form.Item>
               </Col>
               <Col md={4} xs={24}>
                 <Form.Item name={""} label={"CP"}>
                   <Input disabled={show} />
                 </Form.Item>
               </Col>
     
               <Col md={4} xs={24}>
                 <Form.Item name={""} label={t("Ville")}>
                   <Input disabled={show} />
                 </Form.Item>
               </Col>
               <Col md={12} xs={24}>
                 <Form.Item name={""} label={t("Partenaire tunisien")}>
                   <Input disabled={show} />
                 </Form.Item>
               </Col>
               <Col md={12} xs={24}>
                 <Form.Item name={""} label={t("E-mail")}>
                   <Input disabled={show} />
                 </Form.Item>
               </Col>
               <Col md={24} xs={24}>
                 <Form.Item name={""} label={t("Site web en Tunisie")}>
                   <Input disabled={show} />
                 </Form.Item>
               </Col>
               <Col md={12} xs={24}>
                 <Form.Item
                   name={""}
                   label={t("Gouvernorat")}
                   style={{
                     display: "inline-block",
                     width: "100%",
                     margin: "0 8px 8px",
                   }}
                 >
                   <Select disabled={show} />
                 </Form.Item>
               </Col>
               <Col md={12} xs={24}>
                 <Form.Item name={""} label={t("Secteur")}>
                   <Select disabled={show}/>
                 </Form.Item>
               </Col>
               <Col md={24} xs={24}>
                 <Form.Item name={""} label={t("ZI")}>
                   <Input disabled={show} />
                 </Form.Item>
               </Col>
             </Row>
           </Card>
           <Divider>Coordonnées à l'étranger</Divider>
           <Card className="mt-2">
             <Row gutter={16}>
               <Col md={24} xs={24}>
                 <Form.Item
                   name={""}
                   label={t("RS Etranger")}
                 >
                   <Input disabled={show} />
                 </Form.Item>
               </Col>
               <Col md={24} xs={24}>
                 <Form.Item name={""} label={t("Adr à l'étranger")}>
                   <Input.TextArea disabled={show} rows={3} />
                 </Form.Item>
               </Col>
               <Col md={12} xs={24}>
                 <Form.Item
                   name={""}
                   label={t("Tél./GSM")}
                 >
                   <Select disabled={show} />
                 </Form.Item>
               </Col>
               <Col md={12} xs={24}>
                 <Form.Item
                   name={""}
                   label={t("Fax")}
                 >
                   <Input disabled={show} />
                 </Form.Item>
               </Col>
               <Col md={12} xs={24}>
                 <Form.Item
                   name={""}
                   label={t("Maison Mère")}
                 >
                   <Input disabled={show} />
                 </Form.Item>
               </Col>
               <Col md={12} xs={24}>
                 <Form.Item
                   name={""}
                   label={t("E-mail")}
                 >
                   <Input disabled={show} />
                 </Form.Item>
               </Col>
               <Col md={24} xs={24}>
                 <Form.Item
                   name={""}
                   label={t("Site web à l'étranger")}
                 >
                   <Input disabled={show} />
                 </Form.Item>
               </Col>
             
             </Row>
           </Card>
           <Divider>Origine du contact</Divider>
           <Card className="mt-2">
             <Row gutter={16}>
               <Col md={24} xs={24}>
                 <Form.Item name={""} label={t("Action promotionnelle qui a généré le contact")}>
                   <Input disabled={show} />
                 </Form.Item>
               </Col>
               <Col md={8} xs={24}>
                 <Form.Item name={""} label={t("Date du contact")}>
                     <DatePicker disabled={show} />
                 </Form.Item>
               </Col>
     
               <Col md={8} xs={24}>
                 <Form.Item name={""} label={t("Cadre FIPA qui l'a contacté")}>
                   <Input disabled={show} />
                 </Form.Item>
               </Col>
                <Col md={8} xs={24}>
                 <Form.Item name={""} label={t("Date 1ère visite")}>
                   <DatePicker disabled={show} />
                 </Form.Item>
               </Col>
                 <Col md={12} xs={24}>
                 <Form.Item name={""} label={t("Initiateur")}>
                   <Select disabled={show} />
                 </Form.Item>
               </Col>
                <Col md={12} xs={24}>
                 <Form.Item name={""} label={t("Observation")}>
                   <Input disabled={show} />
                 </Form.Item>
               </Col>
                  <Col md={8} xs={24}>
                 <Form.Item name={""} label={t("Nature")}>
                   <Select disabled={show} />
                 </Form.Item>
               </Col>
                  <Col md={8} xs={24}>
                 <Form.Item name={""} label={t("Marché")}>
                   <Select disabled={show}/>
                 </Form.Item>
               </Col>
                <Col md={8} xs={24}>
                 <Form.Item name={""} label={t("Nationalité")}>
                   <Select disabled={show} />
                 </Form.Item>
               </Col>
                <Col md={16} xs={24}>
                 <Form.Item name={""} label={t("Activité")}>
                   <Input disabled={show} />
                 </Form.Item>
               </Col>
                 <Col md={8} xs={24}>
                 <Form.Item name={""} label={t("%PE")}>
                   <InputNumber disabled={show} />
                 </Form.Item>
               </Col>
                <Col md={8} xs={24}>
                 <Form.Item name={""} label={t("Invest. Prev.")}>
                   <InputNumber disabled={show} />
                 </Form.Item>
               </Col>
                <Col md={8} xs={24}>
                 <Form.Item name={""} label={t("Empl. Prev.")}>
                   <InputNumber disabled={show} />
                 </Form.Item>
               </Col>
                 <Col md={8} xs={24}>
                 </Col>
               <Col md={8} xs={24}>
                 <Form.Item name={""} label={t(" ")}>
                 <Radio disabled={show} > Contacté en Tunisie par cadre FIPA </Radio>
                 </Form.Item>
               </Col>
               <Col md={16} xs={24}>
                 <Form.Item name={""} label={t("Occasion du contact")}>
                   <Input.TextArea disabled={show} />
                 </Form.Item>
               </Col>
             
             </Row>
           </Card>
    </ModalForm>
  );
}
export default EditProjet;
