import { ModalForm } from "@ant-design/pro-components";
import { Card, Checkbox, Col, DatePicker, Form, Input, Radio, Row, message } from "antd";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { entreprises } from "./data";
import { MinusCircleOutlined, PlusCircleOutlined } from "@ant-design/icons";

function TrackContactForm({ view, open, onCancel, record }) {
  const { t } = useTranslation();
  const handleAdd = () => {
    const currentData = form.getFieldValue("trackItems") || [];

    const date_suivi = form.getFieldValue("date_suivi");
    const action = form.getFieldValue("action");
    const date_relance = form.getFieldValue("date_relance");
    const responsable = form.getFieldValue("responsable");
  
 
    if (date_suivi && action &&  responsable && date_relance ) {

      const key = Date.now();
      form.setFieldsValue({
        trackItems: [
          ...currentData,
          {
            key: key,
            date_suivi: date_suivi,
            action: action,
            date_relance: date_relance,
            responsable: responsable ,
            suivi_done : true 
          },
        ],
      });
     form.resetFields(["date_suivi" ,"action" ,"date_relance" ,"responsable"]);
    } else {
      message.error(t("Merci de bien vouloir remplir tous les champs."));
    }
  };
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  useEffect(() => {
    if (open) {
      form.setFieldsValue(record);
    }
  }, [open]);
  const [value, setValue] = useState(null);

 
  return (
    <ModalForm
      title={t("Suivi du contact intéressant")}
      form={form}
      width="70%"
      open={open}
      modalProps={{
        style: { top: 20 },
      }}
      //   onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.save"),
          resetText: t("common.actions.cancel"),
        },
      }}
    >
      <Card className="mt-5">
        <Row gutter={16} style={{ marginBottom: "10px" }}>
          <Col span={4}>Date suivi</Col>
          <Col span={9}>Action de suivi</Col>
          <Col span={4}>Date relance</Col>
          <Col span={4}>Responsable suivi</Col>
          <Col span={3}>Suivi effectué</Col>
          <Col span={1}></Col>
        </Row>
        <Form.List name="trackItems">
          {(fields, { remove }) => (
            <>
              {fields.map((field, index) => (
                <Row gutter={16} align="middle" key={field.key}>
                  <Col span={4}>
                    <Form.Item name={[index, "date_suivi"]} rules={[]}>
                      <DatePicker allowClear={true} disabled={view} />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item name={[index, "action"]} rules={[]}>
                      <Input allowClear={true} disabled={view} />
                    </Form.Item>
                  </Col>
                  <Col span={4}>
                    <Form.Item name={[index, "date_relance"]} rules={[]}>
                      <DatePicker allowClear={true} disabled={view} />
                    </Form.Item>
                  </Col>
                  <Col span={4}>
                    <Form.Item name={[index, "responsable"]} rules={[]}>
                      <Input allowClear={true} disabled={view} />
                    </Form.Item>
                  </Col>
                  <Col span={3}>
                    <Form.Item
                     name={[index, "suivi_done"]}   
                      //  label={" "}
                      style={{
                        display: "inline-block",
                        marginLeft: "40px",
                      }}
                    >
<Checkbox onChange={(e) => setValue(e.target.checked)}/>
                    </Form.Item>
                  </Col>
                  <Col span={1}>
                    {!view && (
                      <Form.Item>
                        <MinusCircleOutlined
                          style={{
                            color: "red",
                            fontSize: "18px",
                            cursor: "pointer",
                          }}
                          onClick={() => remove(index)}
                        />
                      </Form.Item>
                    )}
                  </Col>
                </Row>
              ))}
            </>
          )}
        </Form.List>

        {!view && (
          <Row gutter={16} align="middle" style={{ marginBottom: "5px" }}>
            <Col span={4}>
              <Form.Item name={`date_suivi`} rules={[]}>
                <DatePicker allowClear={true} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name={`action`} rules={[]}>
                <Input allowClear={true} />
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item name={`date_relance`} rules={[]}>
                <DatePicker allowClear={true} />
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item name={`responsable`} rules={[]}>
                <Input allowClear={true} />
              </Form.Item>
            </Col>
            <Col span={3}>
              <Form.Item
                name={"suivi_done"}
                style={{
                  display: "inline-block",
                  marginLeft: "40px",
                }}
              >
                 <Checkbox onChange={(e) => setValue(e.target.checked)}/>
               
              </Form.Item>
            </Col>
            <Col span={1}>
              <Form.Item>
                <PlusCircleOutlined
                  style={{
                    color: "green",
                    fontSize: "18px",
                    cursor: "pointer",
                  }}
                  onClick={() => handleAdd()}
                />
              </Form.Item>
            </Col>
          </Row>
        )}
      </Card>
    </ModalForm>
  );
}
export default TrackContactForm;
