{"emplacements": {"title": "Emplacements", "list": "Liste des emplacements", "add": "Ajouter un emplacement", "edit": "Modifier l'emplacement", "view": "Détails de l'emplacement", "fields": {"code": "Code", "libelle": "<PERSON><PERSON><PERSON>", "depot": "Depot"}}, "stock-unities": {"title": "Stock Unités", "list": "Liste des stock unités", "add": "Ajouter un stock unité", "edit": "Modifier le stock unité", "view": "Détails de stock unité", "fields": {"libelle": "<PERSON><PERSON><PERSON>", "symbole": "Symbole", "parentUnity": "Unité parente", "coefficient": "Coefficient"}}, "tvas": {"title": "TVA", "list": "Liste des TVA", "add": "Ajouter TVA", "edit": "Modifier TVA", "view": "Détails TVA", "fields": {"default": "<PERSON><PERSON> <PERSON><PERSON>", "type": "Type", "code": "Code", "designation": "Designation", "valeur": "<PERSON><PERSON>"}, "type": {"achat": "<PERSON><PERSON><PERSON>", "vente": "Vente", "achat_et_vente": "Achat et Vente"}, "default": {"non-default": "Pas par défaut"}}, "moyens-paiement": {"title": "Moyens de paiement", "list": "Liste des moyens de paiement", "add": "Ajouter un moyen de paiement", "edit": "Modifier le moyen de paiement", "view": "<PERSON><PERSON><PERSON> de moyen de paiement", "fields": {"code": "Code", "libelle": "<PERSON><PERSON><PERSON>"}}, "vente": {"dashboard": {"title": "Vente dashboard"}, "info_general": {"type": "Type", "code": "Code", "raison_sociale": "Raison sociale", "lastName": "Nom de famille", "firstName": "Prénom", "poste": "Poste", "civilite": "Civilité", "email": "<PERSON><PERSON><PERSON> email", "phone": "Téléphone", "entreprise": "Entreprise", "departement": "Departement", "adresse_1": "Adresse 1", "adresse_2": "Adresse 2", "pays": "Pays", "code_postal": "Code postal", "matricule_fiscale": "Matricule fiscale", "assujetti_tva": "Assujetti TVA", "code_placeholder": "Code sera généré automatiquement", "raison_sociale_placeholder": "Entrer la raison sociale", "lastName_placeholder": "Entrer le nom de famille", "firstName_placeholder": "En<PERSON>r le prénom", "poste_placeholder": "Entrer le poste", "civilite_placeholder": "Séléctionner la civilité", "email_placeholder": "Entrer l'adresse email", "phone_placeholder": "Entrer le numéro de téléphone", "entreprise_placeholder": "Séléctionner l'entreprise", "departement_placeholder": "Séléctionner le departement", "adresse_1_placeholder": "Entrer l'adresse 1", "adresse_2_placeholder": "Entrer l'adresse 2", "pays_placeholder": "Séléctionner le pays", "code_postal_placeholder": "Entrer le code postal", "matricule_fiscale_placeholder": "Entrer la matricule fiscale", "assujetti_tva_placeholder": "Assujetti TVA"}, "client": {"list": "Liste des clients", "add": "Ajouter un client", "edit": "Modifier client", "view": "Détails du client"}, "prospect": {"list": "Liste des prospects", "add": "Ajouter un prospect", "edit": "Modifier prospect", "view": "<PERSON><PERSON><PERSON> du prospect"}, "contact": {"add_contact": "Ajouter un contact", "firstName": "Prénom", "lastName": "Nom de famille", "civilite": "Civilité", "email": "<PERSON><PERSON><PERSON> email", "position": "Poste", "pays": "Pays", "gouvernorat": "Governorat", "delegation": "Délégation", "addresse": "<PERSON><PERSON><PERSON>", "code_postal": "Code postal", "phone": "Numéro de téléphone", "phone_fix": "Numéro de téléphone fixe", "fax": "Numéro de fax", "firstName_placeholder": "En<PERSON>r le prénom", "lastName_placeholder": "Entrer le nom de famille", "civilite_placeholder": "Séléctionner la civilité", "email_placeholder": "Entrer l'adresse email", "position_placeholder": "Entrer le poste", "pays_placeholder": "Séléctionner le pays", "gouvernorat_placeholder": "Séléctionner le gouvernorat", "delegation_placeholder": "Entrer la délégation", "addresse_placeholder": "En<PERSON>r l'adresse", "code_postal_placeholder": "Entrer le code postal", "phone_placeholder": "Entrer le numéro de téléphone", "phone_fix_placeholder": "Entrer numéro téléphone fixe", "fax_placeholder": "Entrer le numéro de fax"}}, "achat": {"dashboard": {"title": "Achat dashboard"}, "fournisseur": {"list": "Liste des fournisseurs", "add": "Ajouter un fournisseur", "edit": "Modifier fournisseur", "view": "<PERSON><PERSON><PERSON> du fournisseur"}}, "common": {"search": "Rechercher...", "filter": "<PERSON><PERSON><PERSON>", "actions": {"title": "Actions", "view": "Voir", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "save": "Enregistrer", "cancel": "Annuler", "back": "Retour", "upload": "Télécharger", "download": "Télécharger", "confirm": "Confirmer", "previous": "Précédent", "next": "Suivant"}, "steps": {"general_info": "Informations générales", "contacts_info": "Informations de(s) contact(s)"}, "status": {"active": "Actif", "inactive": "Inactif", "non_active": "Non active", "draft": "Brouillon", "published": "<PERSON><PERSON><PERSON>"}, "validation": {"required": "Ce champ est requis", "invalid_date": "Date invalide", "positive_number": "Le nombre doit être positif", "min_length": "Doit contenir au moins {min} caractères", "max_length": "Ne doit pas dépasser {max} caractères", "invalid_format": "Format invalide"}, "messages": {"confirm_delete": "Êtes-vous sûr de vouloir supprimer?", "error_load": "Erreur lors du chargement des données", "error_save": "Erreur lors de l'enregistrement"}}}