// src/pages/activities/datas.js
export const scoringData = [
  {
    key: 'sector',
    label: "Secteur d’activité",
    conditions: [
      { value: "TIC", points: 20 },
      { value: "Industrie", points: 15 },
      { value: "Tourisme", points: 10 },
      { value: "Autres", points: 5 },
    ],
  },
  {
    key: 'location',
    label: "Localisation géographique",
    conditions: [
      { value: ["Europe", "Amérique du Nord"], points: 15 },
      { value: ["Moyen-Orient", "Afrique du Nord"], points: 10 },
      { value: "Autres", points: 5 },
    ],
  },
  {
    key: 'projectSize',
    label: "Taille du projet",
    conditions: [
      { value: "Grand", points: 20 },
      { value: "Moyen", points: 15 },
      { value: "Petit", points: 5 },
    ],
  },
  {
    key: 'investmentType',
    label: "Type d’investissement",
    conditions: [
      { value: "Direct", points: 15 },
      { value: "Partenariat", points: 10 },
      { value: "Autre", points: 5 },
    ],
  },
  {
    key: 'potential',
    label: "Potentiel économique",
    conditions: [
      { field: "jobCreation", condition: ">50", points: 20 },
      { field: "jobCreation", condition: ">=10 && <=50", points: 10 },
      { field: "techTransfer", condition: "true", points: 15 },
      { field: "investmentAmount", condition: ">1000000", points: 15 },
      { field: "infrastructureImpact", condition: "Élevé", points: 10 },
    ],
  },
  {
    key: 'engagement',
    label: "Engagement du lead",
    conditions: [
      { field: "interactions", condition: "includes:meeting", points: 10 },
      { field: "interactions", condition: "includes:email_response", points: 5 },
      { field: "interactions", condition: "includes:document_submission", points: 15 },
    ],
  },
  {
    key: 'maxScore',
    label: "Score maximum",
    conditions: [
      { value: 100, points: null },
    ],
  },
];