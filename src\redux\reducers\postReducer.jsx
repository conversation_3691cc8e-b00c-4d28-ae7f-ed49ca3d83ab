// src/redux/reducers/dataReducer.js

import { FETCH_POSTS_FAILURE, FETCH_POSTS_START, FETCH_POSTS_SUCCESS } from "../constants/postConstants";

  
  const initialState = {
    posts: [],
    error: null,
  };
  
  const postReducer = (state = initialState, action) => {
    switch (action.type) {
      case FETCH_POSTS_START:
        return { ...state, isLoading: true, error: null };
      case FETCH_POSTS_SUCCESS:
        return { ...state, isLoading: false, posts: action.payload };
      case FETCH_POSTS_FAILURE:
        return { ...state, isLoading: false, error: action.payload };
      default:
        return state;
    }
  };
  
  export default postReducer;
  