import { Col, Form, Input, Row, Select } from "antd";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";

const StockUnitiesForm = ({ disabled, stockUnity }) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();

  useEffect(() => {
    if (stockUnity) {
      form.setFieldsValue(stockUnity);
    }
  }, [stockUnity]);

  return (
    <Form layout="vertical" disabled={disabled} form={form}>
      <Row gutter={4} className="mt-2">
        <Col className="gutter-row" span={24}>
          <Form.Item
            name="libelle"
            label={t("stock-unities.fields.libelle")}
            rules={[
              {
                required: true,
                message: "stock-unities.validations.required_libelle",
              },
            ]}
          >
            <Input placeholder={t("stock-unities.fields.libelle")} />
          </Form.Item>
        </Col>
        <Col className="gutter-row" span={24}>
          <Form.Item
            name="symbole"
            label={t("stock-unities.fields.symbole")}
            rules={[
              {
                required: true,
                message: "stock-unities.validations.required_symbole",
              },
            ]}
          >
            <Input placeholder={t("stock-unities.fields.symbole")} />
          </Form.Item>
        </Col>
        <Col className="gutter-row" span={24}>
          <Form.Item
            name="parentUnity"
            label={t("stock-unities.fields.parentUnity")}
            rules={[
              {
                required: true,
                message: "stock-unities.validations.required_parentUnity",
              },
            ]}
          >
            <Select
              required
              placeholder={t("stock-unities.fields.parentUnity")}
              allowClear
              options={[
                { label: "Mètre", value: "Mètre" },
                { label: "Unité(s)", value: "Unité(s)" },
              ]}
            />
          </Form.Item>
        </Col>
        <Col className="gutter-row" span={24}>
          <Form.Item
            name="coefficient"
            label={t("stock-unities.fields.coefficient")}
            rules={[
              {
                required: true,
                message: "stock-unities.validations.required_coefficient",
              },
            ]}
          >
            <Input placeholder={t("stock-unities.fields.coefficient")} />
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );
};

export default StockUnitiesForm;
