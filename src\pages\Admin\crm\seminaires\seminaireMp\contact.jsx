import {
  DeleteOutlined,
  Minus<PERSON>ircleOutlined,
  PlusCircleOutlined,
} from "@ant-design/icons";
import { ModalForm } from "@ant-design/pro-components";
import {
  <PERSON><PERSON>,
  Card,
  Col,
  Collapse,
  DatePicker,
  Divider,
  Form,
  Input,
  message,
  Row,
  Select,
  Table,
} from "antd";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

function ContactMpForm({ open, onCancel }) {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [activeKey, setActiveKey] = useState(["1"]);
  const handleAdd = () => {
    const presents = form.getFieldValue("presents") || [];
    const key = Date.now();
    form.setFieldsValue({
      presents: [
        ...presents,
        {
          key: key,
        },
      ],
    });
 
  };
  const handleSubmit = async (values) => {
    try {
      const newKey = Date.now();
      const newPlanActivity = {
        key: new<PERSON><PERSON>,
        name: values.name,
        description: values.description,
        activities: activities.map((activity, index) => ({
          nom: values[`nom_${index}`],
          resume: values[`resume_${index}`],
          planifier: values[`planifier_${index}`],
        })),
      };
      setDataSource((prevData) => [...prevData, newPlanActivity]);
      message.success("Plan d'activité créé avec succès!");
      onCancel();
    } catch (error) {
      console.log(error);
      message.error("Erreur lors de la création");
    }
  };

  return (
    <ModalForm
      title={t("Liste des contacts")}
      form={form}
      open={open}
      width="55%"
      modalProps={{
        style: { top: 20 },
      }}
      onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.save"),
          resetText: t("common.actions.cancel"),
        },
      }}
    >
    
      <Card className="mt-2">
     
      
      </Card>
    </ModalForm>
  );
}
export default ContactMpForm;
