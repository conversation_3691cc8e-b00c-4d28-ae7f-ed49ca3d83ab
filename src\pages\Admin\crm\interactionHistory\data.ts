import dayjs from "dayjs";

const intercations = [
  {
    id: 1,
    entreprise_id: 1,
    date_intercation: dayjs("2025-01-10"),
    description: "Follow-up call with client.",
    type_intercation_id: 1,
  },
  {
    id: 2,
    entreprise_id: 2,
    date_intercation: dayjs("2025-02-15"),
    description: "Sent project proposal via email.",
    type_intercation_id: 2,
  },
  {
    id: 3,
    entreprise_id: 3,
    date_intercation: dayjs("2025-03-03"),
    description: "Initial meeting scheduled with team.",
    type_intercation_id: 3,
  },
  {
    id: 4,
    entreprise_id: 4,
    date_intercation: dayjs("2025-03-20"),
    description: "Exchanged messages for quick clarifications.",
    type_intercation_id: 4,
  },
  {
    id: 5,
    entreprise_id: 1,
    date_intercation: dayjs("2025-04-01"),
    description: "Follow-up call regarding pending invoice.",
    type_intercation_id: 1,
  },
];

const companies = [
  {
    id: 1,
    nom: "TechNova Solutions",
  },
  {
    id: 2,
    nom: "AgriGrow",
  },
  {
    id: 3,
    nom: "EcoEnergy",
  },
  {
    id: 4,
    nom: "MediCare Pharma",
  },
];
const secteurs = [
  { id: 1, secteur: "Industries diverses" },
  { id: 2, secteur: "Technologie" },
  { id: 3, secteur: "Services" },
  { id: 4, secteur: "Tourisme" },
  { id: 5, secteur: "Agriculture" },
  { id: 6, secteur: "Energie" },
];
const categories = [
  { id: 1, category: "Industrie" },
  { id: 2, category: "Agriculture" },
  { id: 3, category: "Technologie" },
  { id: 4, category: "Commerce" },
  { id: 5, category: "Finance" },
  { id: 6, category: "Santé" },
];
const gouvernorats = [
  { id: 1, nom: "Tunis" },
  { id: 2, nom: "Ariana" },
  { id: 3, nom: "Ben Arous" },
  { id: 4, nom: "Manouba" },
  { id: 5, nom: "Nabeul" },
  { id: 6, nom: "Zaghouan" },
  { id: 7, nom: "Bizerte" },
  { id: 8, nom: "Beja" },
  { id: 9, nom: "Jendouba" },
  { id: 10, nom: "Le Kef" },
  { id: 11, nom: "Siliana" },
  { id: 12, nom: "Kairouan" },
  { id: 13, nom: "Kasserine" },
  { id: 14, nom: "Sidi Bouzid" },
  { id: 15, nom: "Sousse" },
  { id: 16, nom: "Monastir" },
  { id: 17, nom: "Mahdia" },
  { id: 18, nom: "Sfax" },
  { id: 19, nom: "Gafsa" },
  { id: 20, nom: "Tozeur" },
  { id: 21, nom: "Kebili" },
  { id: 22, nom: "Gabes" },
  { id: 23, nom: "Medenine" },
  { id: 24, nom: "Tataouine" },
];

const delegations = [
  { id: 1, nom: "Bab Bhar", gouvernorat_id: 1 },
  { id: 2, nom: "Bab Souika", gouvernorat_id: 1 },
  { id: 3, nom: "Carthage", gouvernorat_id: 1 },
  { id: 4, nom: "Cité El Khadra", gouvernorat_id: 1 },
  { id: 5, nom: "Djebel Jelloud", gouvernorat_id: 1 },
  { id: 6, nom: "El Kabaria", gouvernorat_id: 1 },
  { id: 7, nom: "El Menzah", gouvernorat_id: 1 },
  { id: 8, nom: "El Omrane", gouvernorat_id: 1 },
  { id: 9, nom: "El Omrane supérieur", gouvernorat_id: 1 },
  { id: 10, nom: "El Ouardia", gouvernorat_id: 1 },
  { id: 11, nom: "Ettahrir", gouvernorat_id: 1 },
  { id: 12, nom: "Ezzouhour", gouvernorat_id: 1 },
  { id: 13, nom: "La Goulette", gouvernorat_id: 1 },
  { id: 14, nom: "La Marsa", gouvernorat_id: 1 },
  { id: 15, nom: "Le Bardo", gouvernorat_id: 1 },
  { id: 16, nom: "Le Kram", gouvernorat_id: 1 },
  { id: 17, nom: "Séjoumi", gouvernorat_id: 1 },
  { id: 18, nom: "Sidi El Béchir", gouvernorat_id: 1 },
  { id: 19, nom: "Sidi Hassine", gouvernorat_id: 1 },

  { id: 20, nom: "Ariana Ville", gouvernorat_id: 2 },
  { id: 21, nom: "Ettadhamen", gouvernorat_id: 2 },
  { id: 22, nom: "Kalaât el-Andalous", gouvernorat_id: 2 },
  { id: 23, nom: "Mnihla", gouvernorat_id: 2 },
  { id: 24, nom: "Raoued", gouvernorat_id: 2 },
  { id: 25, nom: "Sidi Thabet", gouvernorat_id: 2 },
  { id: 26, nom: "La Soukra", gouvernorat_id: 2 },

  { id: 27, nom: "Ben Arous", gouvernorat_id: 3 },
  { id: 28, nom: "Bou Mhel el-Bassatine", gouvernorat_id: 3 },
  { id: 29, nom: "El Mourouj", gouvernorat_id: 3 },
  { id: 30, nom: "Fouchana", gouvernorat_id: 3 },
  { id: 31, nom: "Hammam Chott", gouvernorat_id: 3 },
  { id: 32, nom: "Hammam Lif", gouvernorat_id: 3 },
  { id: 33, nom: "Mohamedia-Fouchana", gouvernorat_id: 3 },
  { id: 34, nom: "Mornag", gouvernorat_id: 3 },
  { id: 35, nom: "Radès", gouvernorat_id: 3 },

  { id: 36, nom: "Manouba", gouvernorat_id: 4 },
  { id: 37, nom: "Douar Hicher", gouvernorat_id: 4 },
  { id: 38, nom: "Tebourba", gouvernorat_id: 4 },
  { id: 39, nom: "Mornaguia", gouvernorat_id: 4 },
  { id: 40, nom: "Oued Ellil", gouvernorat_id: 4 },

  { id: 41, nom: "Nabeul", gouvernorat_id: 5 },
  { id: 42, nom: "Hammamet", gouvernorat_id: 5 },
  { id: 43, nom: "Korba", gouvernorat_id: 5 },
  { id: 44, nom: "Kelibia", gouvernorat_id: 5 },
  { id: 45, nom: "Menzel Temime", gouvernorat_id: 5 },

  { id: 46, nom: "Bizerte", gouvernorat_id: 7 },
  { id: 47, nom: "Menzel Bourguiba", gouvernorat_id: 7 },
  { id: 48, nom: "Ras Jebel", gouvernorat_id: 7 },
  { id: 49, nom: "Metline", gouvernorat_id: 7 },
  { id: 50, nom: "Ghar El Melh", gouvernorat_id: 7 },

  { id: 51, nom: "Sousse", gouvernorat_id: 15 },
  { id: 52, nom: "Msaken", gouvernorat_id: 15 },
  { id: 53, nom: "Akouda", gouvernorat_id: 15 },
  { id: 54, nom: "Hammam Sousse", gouvernorat_id: 15 },

  { id: 55, nom: "Sfax", gouvernorat_id: 18 },
  { id: 56, nom: "Sakiet Ezzit", gouvernorat_id: 18 },
  { id: 57, nom: "Sakiet Eddaier", gouvernorat_id: 18 },
  { id: 58, nom: "El Amra", gouvernorat_id: 18 },

  { id: 59, nom: "Gafsa", gouvernorat_id: 19 },
  { id: 60, nom: "Métlaoui", gouvernorat_id: 19 },
  { id: 61, nom: "Redeyef", gouvernorat_id: 19 },

  { id: 62, nom: "Tozeur", gouvernorat_id: 20 },
  { id: 63, nom: "Nefta", gouvernorat_id: 20 },

  { id: 64, nom: "Tataouine", gouvernorat_id: 24 },
  { id: 65, nom: "Bir Lahmar", gouvernorat_id: 24 },
];
const nationalites = [
  { id: 1, nom: "France" },
  { id: 2, nom: "Italie" },
  { id: 3, nom: "Allemagne" },
  { id: 4, nom: "Espagne" },
  { id: 5, nom: "États-Unis" },
  { id: 6, nom: "Canada" },
  { id: 7, nom: "Algérie" },
  { id: 8, nom: "Maroc" },
  { id: 9, nom: "Libye" },
  { id: 10, nom: "Turquie" },
  { id: 11, nom: "Arabie Saoudite" },
  { id: 12, nom: "Émirats arabes unis" },
  { id: 13, nom: "Qatar" },
  { id: 14, nom: "Égypte" },
  { id: 15, nom: "Chine" },
  { id: 16, nom: "Japon" },
  { id: 17, nom: "Corée du Sud" },
  { id: 18, nom: "Belgique" },
  { id: 19, nom: "Suisse" },
  { id: 20, nom: "Russie" },
];
const intercationTypes = [
  {id : 1, nom: "Réunion", color: "blue" },
  {id : 2,   nom: "Assistance", color: "green" },
  {id :3 , nom: "Intervention", color: "red" },
];
export {
  companies,
  intercations,
  secteurs,
  gouvernorats,
  delegations,
  nationalites,
  intercationTypes,
};
