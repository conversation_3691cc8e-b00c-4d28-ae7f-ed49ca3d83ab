import { useState, useRef, useEffect } from "react";
import { ProTable } from "@ant-design/pro-components";
import {
  Button,
  Popconfirm,
  Space,
  Input,
  Tooltip,
  Typography,
  Card,

  Form,
  Tag,
} from "antd";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  SearchOutlined,
  InteractionOutlined,
} from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import {
  companies , demandes ,responsables
} from "./data";

import CreateDemandeForm from "./create";
import ActionDemandeForm from "./action";
import EditDemandeForm from "./edit";
import ShowDemandeForm from "./view";




function DemandeIncident() {
  const tableRef = useRef();
  const { t } = useTranslation();
  const [tableParams, setTableParams] = useState({}); // Paramètres de recherche et de filtrage
  const [isLoading, setIsLoading] = useState(false); // Indique si les données sont en cours de chargement
  const [data, setData] = useState(demandes); // Données affichées dans la table
  const [total, setTotal] = useState(data.length); // Nombre total de lignes affichées
  const [pageNumber, setPageNumber] = useState(1); // Numéro de page actuelle
  const [dataSource, setDataSource] = useState([]);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [viewModalVisible, setViewModalVisible] = useState(false);
  const [actionModalVisible, setActionModalVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState(null);
  const [viewingRecord, setViewingRecord] = useState(null);
  const [actionRecord, setActionRecord] = useState(null);
  const [loadingDuplicate, setLoadingDuplicate] = useState({
    status: false,
    id: 0,
  });
  const [form] = Form.useForm();
  // Déclenchement de la recherche avec un délai (700ms)
  const handleSearch = (e) => {
    const searchValue = e.target.value; // Récupération de la valeur de recherche
    setTableParams({ ...tableParams, search: searchValue }); // Mise à jour des paramètres
  };

  //  const debouncedOnChange = useDebounce(handleSearch, 700);

  const formatter = new Intl.NumberFormat("fr-TN", {
    style: "currency",
    currency: "TND",
    minimumFractionDigits: 3,
  });


  const columns = [
    {
      title: t("Entreprise"),
      dataIndex: "entreprise_id",
      key: "entreprise_id",
      ellipsis: true,
      valueType: "select",
      valueEnum: () =>
        Object.fromEntries(
          companies.map((value) => [value.id, { text: value.nom }])
        ),
      order: 1,
      render: (text) => {
        return (
          <Typography.Text ellipsis={{ tooltip: text }}>{text}</Typography.Text>
        );
      },
    },  
   
        {
      title: t("Date demande"),
      dataIndex: "date_demande",
      key: "date_demande",
      ellipsis: true,
      valueType: "date",
      order: 1,
      render: (text) => {
        return (
          <Typography.Text ellipsis={{ tooltip: text }}>{text}</Typography.Text>
        );
      },
    },
    {
      title: t("Responsbale"),
      dataIndex: "responsable_id",
      key: "responsable_id",
      ellipsis: true,
      valueType: "select",
      valueEnum: () =>
        Object.fromEntries(
          responsables.map((value) => [value.id, { text: value.nom }])
        ),
      order: 1,
      render: (text) => {
        return (
          <Typography.Text ellipsis={{ tooltip: text }}>{text}</Typography.Text>
        );
      },
    },  
   
    {
      title: t("Actions"),
      key: "action",
      align: "center",
      sorter: false,
      search: false,
      render: (_, record) => (
        <Space size="auto">
          <Tooltip title={t("common.actions.view")}>
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => {
                setViewingRecord(record);
                setViewModalVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title={t("common.actions.edit")}>
            <Button
              type="link"
              style={{ color: "#f5b041" }}
              icon={<EditOutlined />}
              onClick={() => {
                setEditingRecord(record);
                setEditModalVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title={t("Actions correctives")}>
            <Button
              type="link"
              style={{ color: "#197823" }}
              icon={ <InteractionOutlined />}
              onClick={() => {
                setActionRecord(record);
                setActionModalVisible(true);
              }}
            />
          </Tooltip>
         

          <Tooltip title={t("common.actions.delete")}>
            <Popconfirm
              title={t("common.messages.confirm_delete")}
              onConfirm={() => {
                const newDataSource = dataSource.filter(
                  (item) => item.key !== record.key
                );
                setDataSource(newDataSource);
              }}
            >
              <Button
                type="link"
                style={{ color: "#ec7063" }}
                icon={<DeleteOutlined />}
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];
 


  const onFinish = (values) => {
    setTansferLoading(true);
    setTimeout(() => {
      setTansferLoading(false);
      setIsModalOpen(false);
    }, [1000]);
  };

  return (
    <>
      <Card
        title={
          <Typography.Title level={4}>{t("Demandes et Incidents")}</Typography.Title>
        }
        extra={
          <Space>
            {/* Champ de recherche */}
            <Input
              size="large"
              placeholder={t("Rechercher")}
              suffix={<SearchOutlined style={{ color: "#bfbfbf" }} />}
              //    onChange={debouncedOnChange}
            />
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setCreateModalVisible(true)}
            >
              {t("Ajouter")}
            </Button>
          </Space>
        }
      >
        <ProTable
          actionRef={tableRef} // Référence pour accéder aux actions
          search={{
            searchText: t("Filtrer"),
            labelWidth: "auto",
            defaultCollapsed: false,
          }}
          loading={isLoading}
          columns={columns}
          dataSource={data}
          rowKey="id"
          params={tableParams}
          scroll={{ x: "max-content" }}
          options={false}
          request={async (params) => {
            setIsLoading(true); 
            console.log(params);
            setTimeout(() => {
              
              setIsLoading(false);
            }, 500);
            return {
              
              data: data,
              success: true,
            };
          }}
          pagination={{
            showSizeChanger: true, 
            defaultPageSize: 3, 
            total: total, 
            onChange: (page) => setPageNumber(page), // Fonction de changement de page
          }}
        />
       {createModalVisible && (
          <CreateDemandeForm
            open={createModalVisible}
            onCancel={() => setCreateModalVisible(false)}
            onSuccess={(newDomain) => {
              setDataSource([...dataSource, newDomain]);
              setCreateModalVisible(false);
            }}
          />
        )}
         {viewModalVisible && (
                  <ShowDemandeForm
                    open={viewModalVisible}
                    record={viewingRecord}
                    onCancel={() => {
                      setViewModalVisible(false);
                      setViewingRecord(null);
                    }}
                  />
                )}
         {editModalVisible && (
                  <EditDemandeForm
                    open={editModalVisible}
                    record={editingRecord}
                    onCancel={() => {
                      setEditModalVisible(false);
                      setEditingRecord(null);
                    }}
                  />
                )}
                {actionModalVisible && (
                  <ActionDemandeForm
                    open={actionModalVisible}
                    record={actionRecord}
                    onCancel={() => {
                      setActionModalVisible(false);
                      setActionRecord(null);
                    }}
                  />
                )}
               
      </Card>
 
    </>
  );
}
export default DemandeIncident;
