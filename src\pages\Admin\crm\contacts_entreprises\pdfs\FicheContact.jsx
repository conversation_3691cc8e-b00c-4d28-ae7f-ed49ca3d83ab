import React from "react";
import { Card, Typography, Row, Col, Table } from "antd";

const { Title, Text } = Typography;
import fipaLogo from "./fipa.png";


const FicheContact =React.forwardRef((props, ref) => {
  const followUpColumns = [
    { title: 'Suivi effectué', dataIndex: 'followup', key: 'followup' },
    { title: 'Date', dataIndex: 'date', key: 'date', width: 100 },
    { title: 'Responsable Suivi', dataIndex: 'responsible', key: 'responsible', width: 150 },
  ];

  const followUpData = [
    {
      followup: '22001/2013 Contact téléphonique ce jour avec Mr. LIONEL.',
      date: '30/03/2013',
      responsible: '',
    },
  ];

  return (
    <div ref={ref} style={{ padding: 20 }}>
      <Card
        bordered={false}
        style={{
          maxWidth: 800,
          margin: '0 auto',
          fontFamily: 'Arial, sans-serif',
        }}
        bodyStyle={{ padding: 24 }}
      >
        {/* Header Section */}
        <div style={{ textAlign: 'center', marginBottom: 24 }}>
          <Title level={2} style={{ color: '#003366', marginBottom: 0 }}>
            FICHE DE CONTACT
          </Title>
          <Title level={4} style={{ color: '#666666', marginTop: 8 }}>
            MICROMORA 2012
          </Title>
          <Title level={1} style={{ 
            color: '#003366', 
            margin: '16px 0',
            fontSize: '28px',
            fontWeight: 'bold'
          }}>
            B & B TECHNICS
          </Title>
        </div>

        {/* Contact Details */}
        <Row gutter={24} style={{ marginBottom: 16 }}>
          <Col span={12}>
            <div style={{ marginBottom: 12 }}>
              <Text strong>Cadre FIPA</Text><br/>
              <Text>Date du contact: 24/09/2012</Text>
            </div>
            
            <div style={{ marginBottom: 12 }}>
              <Text strong>Personne contactée</Text><br/>
              <Text>Monsieur Bernard LIONEL</Text>
            </div>
            
            <div style={{ marginBottom: 12 }}>
              <Text strong>Nationalité</Text><br/>
              <Text>Lieu rencontre</Text>
            </div>
          </Col>

          <Col span={12}>
            <div style={{ marginBottom: 12 }}>
              <Text strong>Adresse</Text><br/>
              <Text>avenue du Technicum 42 - 2400 LE LOCLE -</Text>
            </div>
            
            <div style={{ marginBottom: 12 }}>
              <Text strong>Téléphone</Text><br/>
              <Text>00 41 32 933 59 10</Text>
            </div>
            
            <div style={{ marginBottom: 12 }}>
              <Text strong>Email / Site Web</Text><br/>
              <Text><EMAIL></Text>
            </div>
          </Col>
        </Row>

        {/* Activity Section */}
        <div style={{ margin: '16px 0' }}>
          <Text strong style={{ display: 'block', marginBottom: 8 }}>Secteur</Text>
          <Text>Mécanique, métallique et métallurgique</Text>
        </div>
        
        <div style={{ margin: '16px 0' }}>
          <Text strong style={{ display: 'block', marginBottom: 8 }}>Activité</Text>
          <Text>Polissage de bracelets en acier inox sans nickel</Text>
        </div>

        {/* Interview Content */}
        <div style={{ margin: '16px 0' }}>
          <Text strong style={{ display: 'block', marginBottom: 8 }}>Contenu entretien</Text>
          <ul style={{ marginLeft: 24, paddingLeft: 0 }}>
            <li>Environnement de l'investissement en Tunisie</li>
            <li>Etude de création d'une unité de polissage en partenariat avec la société RATEL</li>
          </ul>
        </div>

        {/* Follow-up Section */}
        <div style={{ marginTop: 24 }}>
          <Title level={5} style={{ 
            color: '#ff0000', 
            marginBottom: 16,
            fontSize: '14px',
            fontWeight: 'bold'
          }}>
            Fact basé entreprise
          </Title>
          <Table
            columns={followUpColumns}
            dataSource={followUpData}
            pagination={false}
            bordered
            size="small"
            showHeader={true}
            rowKey="date"
            style={{ fontSize: '12px' }}
          />
        </div>

        {/* Footer */}
        <div style={{ 
          marginTop: 24,
          borderTop: '1px solid #ddd',
          paddingTop: 12,
          textAlign: 'center',
          fontSize: '12px',
          color: '#666'
        }}>
          <Text>{new Date().getFullYear()} FIPA</Text>
        </div>
      </Card>
    </div>
  );
});

export default FicheContact;