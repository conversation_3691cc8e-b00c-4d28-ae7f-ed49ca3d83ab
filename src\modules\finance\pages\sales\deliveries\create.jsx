import { ModalForm } from "@ant-design/pro-components";
import DeliveryItem from "@src/modules/finance/components/delivery/DeliveryItem";
import {  Card, Col, DatePicker, Form, Input, InputNumber, Row, Select } from "antd";
import { entreprises , departements , clients , depots } from "./data";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";


function CreateBonForm({ open, onCancel, onSuccess }){
    const {t} = useTranslation();
    const [loading, setLoading] = useState(false); 
    const [form] = Form.useForm();
    const dateFormat = "YYYY-MM-DD";
    const [entrepriseId, setEntrepriseId] = useState(null);
    const [departementId, setDepartementId] = useState(null);
    const [tierId, setTierId] = useState(null);
    const [depotId, setDepotId] = useState(null);
    const showTierData = (e) => {
      const tier = clients.find((t) => t.id == e);
      form.setFieldsValue({
        email: tier?.email,
        adresse: tier?.adresse,
        phone: tier?.phone,
        matricule_fiscale: tier?.matricule_fiscale,
      });
    };
  
    const resetTierData = () => {
      form.setFieldsValue({
        email: null,
        adresse: null,
        phone: null,
        matricule_fiscale: null,
      });
    };
     

    const handleSubmit = (values) => {
      console.log(values);
    }
  
    return(
    <ModalForm
      title={t("bon.add")}
      width ='90%'
      form={form}
      open={open}
      modalProps={{
        style: { top: 30 }, 
      }}
      onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.save"),
          resetText: t("common.actions.cancel"),
        },
      }}
    >
       <Row className="mt-0" gutter={[12, 24]}>
            <Col span={12}>
              <Card title= {t('sales.general_info')} >
                <Form.Item
                  name={"num_bon"}
                  label={t('bon_cols.num_bon')}
                  style={{
                    display: "inline-block",
                    width: "45%",
                    margin: "0 8px 8px",
                  }}
                >
                  <Input disabled={true} />
                </Form.Item>
             
                <Form.Item
                  name={"entreprise_id"}
                  label={
                    <span>
                      {t('sales.entreprise')} <span style={{ color: "red" }}>*</span>
                    </span>
                  }
                  style={{
                    display: "inline-block",
                    width: "45%",
                    margin: "0 8px 8px",
                  }}
                  rules={[
                    {
                      required: true,
                      message:  t('devis_message.required_field')   , 
                    },
                  ]}
                >
                  <Select  
                    allowClear
                    placeholder= {t('sales.entreprise')}
                    options={entreprises.map((item) => ({ value: item.id, label: item.nom }))}  
                    value={entrepriseId}   
                    onSelect={(e) => {
                     setEntrepriseId(e);
                    }}
                    onClear={() => {
                    setEntrepriseId(null);
                    setDepartementId(null);
                    setTierId(null);
                    }}         
                  />
                </Form.Item>
                <Form.Item
                  name="departement_id"
                  label={
                    <span>
                       {t('sales.departement')}  <span style={{ color: "red" }}>*</span>
                    </span>
                  }
                  style={{
                    display: "inline-block",
                    width: "45%",
                    margin: "0 8px 8px",
                  }}
                  rules={[
                    {
                      required: true,
                      message:  t('devis_message.required_field')   , 
                    },
                  ]}
                >
                  <Select
                    allowClear
                    placeholder= {t('sales.departement')}  
                    value={departementId}   
                    onSelect={(e) => {setDepartementId(e);}}  
                    onClear={() => { setDepartementId(null); }}  
                    options={departements.filter( (dept) => dept.entreprise_id === entrepriseId).map((item) => ({ value: item.id, label: item.nom }))}                 
                  />
                </Form.Item>
                <Form.Item
                  name={"date_bon"}
                  label={
                    <span>
                   {t('bon_cols.date_bon')} <span style={{ color: "red" }}>*</span>
                    </span>
                  }
                  style={{
                    display: "inline-block",
                    width: "45%",
                    margin: "0 8px 8px",
                  }}
                  rules={[
                    { required: true,  message:  t('devis_message.required_field') } 
                  ]}
                >
                  <DatePicker
                    format={dateFormat}
                    style={{ width: "100%" }}
                    placeholder ={t('bon_cols.date_bon')}
                  />
                </Form.Item>
                <Form.Item
                  name={"depot_id"}
                  label={
                    <span>
                      {t('sales.depot')} <span style={{ color: "red" }}>*</span>
                    </span>
                  }
                  style={{
                    display: "inline-block",
                    width: "45%",
                    margin: "0 8px 8px",
                  }}
                  rules={[
                    {
                      required: true,
                      message:  t('devis_message.required_field')   , 
                    },
                  ]}
                >
                  <Select  
                    allowClear
                    placeholder= {t('sales.depot')}
                    options={depots.map((item) => ({ value: item.id, label: item.nom }))}  
                    value={entrepriseId}   
                    onSelect={(e) => {
                     setDepotId(e);
                    }}
                    onClear={() => {
                      setDepotId(null);
                    }}         
                  />
                </Form.Item>
              </Card>
            </Col>
            <Col span={12}>
              <Card title= {t('sales.client_info')}  >
                <Form.Item
                  name={"tier_id"}
                  label={
                    <span>
                     {t('sales.client')} <span style={{ color: "red" }}>*</span>
                    </span>
                  }
                  style={{
                    display: "inline-block",
                    width: "45%",
                    margin: "0 8px 8px",
                  }}
                  rules={[
                    { required: true, message: t('devis_message.required_field') },
                  ]}
                >
                  <Select
                    allowClear
                    placeholder={t('sales.client')}
                    value={tierId}   
                    onSelect={(e) => {setTierId(e);showTierData(e);}}  
                    onClear={() => { setTierId(null);resetTierData() }} 
                    options={clients.filter(tier => tier.entreprise_id === entrepriseId &&  tier.departement_id === departementId).map((item) => ({ value: item.id, label: item.nom }))}  
                  />
                </Form.Item>
                <Form.Item
                  name={"matricule_fiscale"}
                  label={t('sales.matricule')}
                  style={{
                    display: "inline-block",
                    width: "45%",
                    margin: "0 8px 8px",
                  }}
                >
                  <Input autoFocus placeholder={t('sales.matricule')} />
                 </Form.Item>

                <Form.Item
                  name="email"
                  label={t('sales.email')}
                  style={{
                    display: "inline-block",
                    width: "45%",
                    margin: "0 8px 8px",
                  }}
                >
                  <Input autoFocus placeholder={t('sales.email')} />
                </Form.Item>
                <Form.Item
                  name="phone"
                  label={t('sales.tel')}
                  style={{
                    display: "inline-block",
                    width: "45%",
                    margin: "0 8px 8px",
                  }}
                >
                  <Input autoFocus  placeholder={t('sales.tel')}/>
                </Form.Item>
                <Form.Item
                  name="adresse"
                  label={t('sales.address')}
                  style={{
                    display: "inline-block",
                    width: "94%",
                    margin: "0 8px 8px",
                  }}
                >
                  <Input autoFocus placeholder={t('sales.address')} />
                </Form.Item>
              </Card>
            </Col>

           
            <Col span={24}></Col>
            
       </Row>
    <DeliveryItem form={form} open={open} depot={depotId} />
   
   
    </ModalForm>
    )
}
export default CreateBonForm;