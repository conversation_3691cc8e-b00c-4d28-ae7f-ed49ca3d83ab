import React from "react";
import { useTranslation } from "react-i18next";
import { EyeInvisibleOutlined, EyeTwoTone } from "@ant-design/icons";
import logo_signIn from '../../../assets/images/logo_erp.png';
import {
    Layout,
    Button,
    Card,
    Form,
    Input,
    Row,
    Col
} from "antd";
import { Link } from "react-router-dom";

const { Header, Content } = Layout;


export default function SignIn() {
    const {t} = useTranslation();
    const handleLogin = (values) => {
    };

    const onFinish = (values) => {
        handleLogin(values);
    };

    const onFinishFailed = (errorInfo) => {
        console.log("Failed:", errorInfo);
    };

    return (
        <div style={{ height: "93vh", position: 'relative' }}>
            <div className="layout-default ant-layout layout-sign-up h-full" style={{margin: 0}}>
                <Header>
                </Header>

                <Content className="p-0">
                    <div className="sign-up-header"></div>

                    <Card
                        className="card-signup header-solid h-full ant-card "
                        title=""
                        bordered="false"
                    >

                        <Form
                            name="basic"
                            initialValues={{ remember: true }}
                            onFinish={onFinish}
                            onFinishFailed={onFinishFailed}
                            className="row-col"
                            layout="vertical"
                        >

                            <div style={{
                                display: 'flex',
                                justifyContent: 'center',
                                alignItems: 'center',
                                marginBottom: '20px',
                            }}>
                                <img src={logo_signIn} alt="" />
                            </div>
                            <div style={{
                                display: 'flex',
                                justifyContent: 'center',
                                alignItems: 'center',
                                marginBottom: '30px'
                            }}>
                                <h5> {t('auth_login')} </h5>
                            </div>
                            <Form.Item
                                name="email"
                                label={t('auth_email.label')}
                                rules={[
                                    {
                                        type: 'email',
                                        message: t('auth_email.type'),
                                    },
                                    { 
                                        required: true,
                                        message: t('auth_email.required')
                                    },
                                ]}
                            >
                                <Input placeholder="<EMAIL>" />
                            </Form.Item>
                            <Form.Item
                                name="password" label={t('auth_password.label')}
                                rules={[
                                    {
                                        required: true,
                                        message:t ('auth_password.required')
                                    },
                                    {
                                      min: 8,
                                      message: t("profile.form.validation.new_password_min_length"),
                                    },
                                    {
                                      pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[+-.!@\$%\^&\*()])/,
                                      message: t("profile.form.validation.new_password_pattern"),
                                    },
                                ]}
                            >
                                <Input.Password
                                    placeholder="*******"
                                    autoComplete="On"
                                    iconRender={(visible) =>
                                        visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
                                    }
                                />
                            </Form.Item>
                            <Row style={{ marginBottom : "20px" }}>
                                <Col span={12}>
                                    <Link
                                        to={"/distress-beacon/forgot-password"}
                                        className="text-muted font-semibold"
                                    >
                                        {t('auth_forgot_password')}
                                    </Link>
                                </Col>
                                <Col span={12} style={{ textAlign: "end" }}>
                                    <Link 
                                        to={"/distress-beacon/register"}
                                        className="font-semibold text-end text-muted"
                                    >
                                        {t('auth_create_acount')}
                                    </Link>
                                </Col>
                            </Row>
                            <Form.Item style={{marginBottom: "10px"}}>
                                <Button
                                    style={{ width: "100%" }}
                                    type="primary"
                                    htmlType="submit"
                                >
                                    {t('auth_login')}
                                </Button>
                            </Form.Item>
                        </Form>
                    </Card>
                </Content>
                <div>
                    <div style={{ padding: '8px', display: 'flex', justifyContent: 'center' }}>
                        © {new Date().getFullYear()}, Powered by &nbsp;
                        <a href="#" className="font-weight-bold" target="_blank" style={{ textDecoration: 'none', fontWeight: 700, color: '#000' }}>
                            TAC-TIC
                        </a>
                    </div>
                </div>
            </div>

        </div>
    );
}
