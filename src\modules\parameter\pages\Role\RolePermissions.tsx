import React, { useEffect, useState } from "react";
import {Button, Space, Card, Col, Row, message, Checkbox, Badge} from "antd";
import { LeftOutlined, RightOutlined } from "@ant-design/icons";
import PermissionsTree from "./PermissionsTree";
import { useDispatch } from "react-redux";
import {addPermissionToRole} from "@/modules/parameter/features/permissions/permissionSlice";
export interface IPermission {
  key: React.Key;
  title: string;
  id: number;
  children?: {
    related_permission?: {
      groupe?: string;
      id?: number;
      label?: string;
    }[];
    id:number; key: React.Key; title: string; children?: {}[] }[];
}

const RolePermissions = ({ role, permissions }) => {
  const dispatch = useDispatch();
  const [messageApi, contextHolder] = message.useMessage();
  const [buttonLoading, setButtonLoading] = useState<boolean>(false);
  const [listAllowedPermissions, setLisitAllowedPermissions] = useState<
    IPermission[]
  >(JSON.parse(JSON.stringify(role.permissions)));
  const [listPermissions, setLisitPermissions] = useState<IPermission[]>();
  const [checkList, setCheckList] = useState<string[]>([]);
  const [checkList2, setCheckList2] = useState<string[]>([]);
  const [addRelativeCheck, setAddRelativeCheck] = useState<boolean>(true);
  const [removeRelativeCheck, setRemoveRelativeCheck] = useState<boolean>(true);

  const findChildByKey = (key: string) => {
    for (const item of listPermissions) {
      if (item.children) {
        const child = item.children.find((child) => child.key == key);
        if (child) {
          return child;
        }
      }
    }
    return null;
  };

  const ChangeListPermissions = (
    allowed = JSON.parse(JSON.stringify(listAllowedPermissions))
  ) => {
    setLisitPermissions(
      permissions.map((item) => {
        let x = [];
        let tab = allowed.find((item2) => item2.key === item.key)?.children;
        item.children.map((item1) => {
          if (!tab?.find((y) => y.key === item1.key)) {
            x.push(item1);
          }
        });
        return {
          title: item.title,
          key: item.key,
          icon:  item.icon,
          children: x,
        };
      })
    );
  };
  const addPermission = () => {
    (async () => {
      checkList.map((item) => {
        if (item.indexOf("-") !== -1) {
          const relativePermissions = findChildByKey(item)?.related_permission;
          console.log(item);
          console.log(relativePermissions);
          if ( addRelativeCheck && relativePermissions.length > 0 ) {
            // ajouter les permissions relatifs si la case est coché
            relativePermissions?.map((item2) => {
              const k = item2?.groupe + "-" + item2?.id;
              setLisitAllowedPermissions(
                listAllowedPermissions.map((x) => {
                  if (
                    item.indexOf(x.key.toString()) === 0 &&
                    x.children.filter((y) => y.key === item).length === 0
                  ) {
                    x.children.push(findChildByKey(item));
                  }
                  if (
                    k.indexOf(x.key.toString()) === 0 &&
                    x.children.filter((y) => y.key === k).length === 0
                  ) {
                    x.children.push(findChildByKey(k));
                  }
                  return x;
                })
              );
            });
          }
          else {
            setLisitAllowedPermissions(
              listAllowedPermissions.map((x) => {
                if (
                  item.indexOf(x.key.toString()) === 0 &&
                  x.children.filter((y) => y.key === item).length === 0
                ) {
                  x.children.push(findChildByKey(item));
                }
                return x;
              })
            );
          }
        }
      });
    })().then(() => {
      ChangeListPermissions();
      setCheckList([]);
    }).catch((reason) => {
      console.log(reason)
    });
  };
  const removePermission = () => {

    (async () => {
      checkList2.map((item) => {
        if (item.length > 2) {
          if (removeRelativeCheck) {
            // supprime les permissions relatifs si la case est coché

            let relativePermissions = [];

            for (const i of listAllowedPermissions) {
              if (i.children) {
                const child = i.children.find((child) => child.key == item);
                if (child) {
                  relativePermissions = child?.related_permission;
                  break;
                }
              }
            }

            relativePermissions?.map((item2) => {
              const k = item2?.groupe + "-" + item2?.id;
              setLisitAllowedPermissions(
                listAllowedPermissions.map((x) => {
                  if (
                    item.indexOf(x.key.toString()) === 0 &&
                    x.children.filter((y) => y.key === item).length !== 0
                  ) {
                    x.children.splice(
                      x.children.indexOf(x.children.filter((y) => y.key === item)[0]),
                      1
                    );
                  }
                  if (
                    k.indexOf(x.key.toString()) === 0 &&
                    x.children.filter((y) => y.key === k).length !== 0
                  ) {
                    x.children.splice(
                      x.children.indexOf(x.children.filter((y) => y.key === k)[0]),
                      1
                    );
                  }
                  return x;
                })
              );
            });
          }
          else {
            setLisitAllowedPermissions(
              listAllowedPermissions.map((x) => {
                if (
                  item.indexOf(x.key.toString()) === 0 &&
                  x.children.filter((y) => y.key === item).length !== 0
                ) {
                  x.children.splice(
                    x.children.indexOf(x.children.filter((y) => y.key === item)[0]),
                    1
                  );
                }
                return x;
              })
            );
          }
        }
      });
    })().then(() => {
      ChangeListPermissions();
      setCheckList2([]);
    }).catch((reason) => {
      console.log(reason)
    });
  };

  const handleUpdate = () => {
    setButtonLoading(true)
    messageApi.open({
      key:'updatable',
      type: 'loading',
      content: 'Loading...',
    });
    let newList=[];
    listAllowedPermissions.map((item) => {
      item.children.map((perm)=>{
        newList.push(perm.id)
      })
    })
    dispatch(addPermissionToRole({
      role_id:role.id,
      permissions:newList
    }))
    .unwrap()
    .then((originalPromiseResult) => {
      messageApi.open({
        key:'updatable',
        type: 'success',
        content: 'Success ',
        duration: 2,
      });
      setButtonLoading(false)
    })
    .catch((rejectedValueOrSerializedError) => {
      messageApi.open({
        key:'updatable',
        type: 'error',
        content: 'Error',
        duration: 2,
      });
      setButtonLoading(false)
      console.log(rejectedValueOrSerializedError);
      return [];
    });
  };
  useEffect(() => {
    ChangeListPermissions();
  }, []);
  return (
    <Row gutter={[12, 12]}>
      {contextHolder}
      <Col span={24}>
        <Row>
          <Col span={11}>
            <Card
              className="h-full"
              title={
                <div style={{ fontSize: "14px" }}>
                  <Row gutter={10}>
                    <Col >
                      Liste des permissions
                    </Col>
                    <Col> / </Col>
                    <Col >
                      {/*checkbox pour ajouter les permissions relatifs */}
                      <Checkbox
                        className="mx-2"
                        checked={addRelativeCheck}
                        onChange={(e) => {
                          setAddRelativeCheck(e.target.checked);
                        }}
                      />
                      <span>
                        Épingler les relatifs
                      </span>
                    </Col>
                  </Row>
                </div>
              }
            >
              <PermissionsTree
                data={listPermissions}
                setCheckList={setCheckList}
                checkList={checkList}
              />
            </Card>
          </Col>
          <Col span={2} className="grid place-items-center">
            <Space direction="vertical">
              <Button
                type="primary"
                disabled={checkList.length === 0}
                onClick={addPermission}
              >
                <RightOutlined />
              </Button>
              <Button
                disabled={checkList2.length === 0}
                onClick={removePermission}
              >
                <LeftOutlined />
              </Button>
            </Space>
          </Col>
          <Col span={11}>
            <Card
              className="h-full"
              title={
                <div style={{ fontSize: "14px" }}>
                  <Row gutter={10}>
                    <Col >
                      Les permissions accordées
                    </Col>
                    <Col> / </Col>
                    <Col >
                      {/*checkbox pour supprimer les permissions relatifs */}
                      <Checkbox
                        className="mx-2"
                        checked={removeRelativeCheck}
                        onChange={(e) => {
                          setRemoveRelativeCheck(e.target.checked);
                        }}
                      />
                      <span>
                        Désépingler les relatifs
                      </span>
                    </Col>
                  </Row>
                </div>
              }
            >
              <PermissionsTree
                data={listAllowedPermissions}
                setCheckList={setCheckList2}
                checkList={checkList2}
              />
            </Card>
          </Col>
        </Row>
      </Col>
      <Col span={24} className="text-right ">
        <Space>
          <Button
            onClick={() => {
              setLisitAllowedPermissions(
                JSON.parse(JSON.stringify(role.permissions))
              );
              ChangeListPermissions(
                JSON.parse(JSON.stringify(role.permissions))
              );
            }}
          >
            Annuler
          </Button>
          <Button
            htmlType="submit"
            type="primary"
            onClick={handleUpdate}
            loading={buttonLoading}
          >
            Mettre à jour
          </Button>
        </Space>
      </Col>
    </Row>
  );
};

export default RolePermissions;
