import React from "react";
import { Descriptions, Divider, <PERSON><PERSON> } from "antd";
import {
  FileTextOutlined,
  AppstoreOutlined,
  SettingOutlined,
  PaperClipOutlined,
} from "@ant-design/icons";
import { datas } from "./datas";

const ViewLead = ({ onCancel }) => {
  const lead = datas[0]; // "New factory" lead

  return (
    <div className="satusActivies">
      <Divider>
        <FileTextOutlined /> Détails de lead
      </Divider>
      <Descriptions bordered column={1}>
        <Descriptions.Item label="Titre de lead">{lead.leadName}</Descriptions.Item>
        <Descriptions.Item label="Entreprise">{lead.enterprise}</Descriptions.Item>
        <Descriptions.Item label="Campagne">{lead.campaign}</Descriptions.Item>
        <Descriptions.Item label="Détails du stade">{lead.stageDetails}</Descriptions.Item>
      </Descriptions>

      <Divider>
        <AppstoreOutlined /> Informations sur le lead
      </Divider>
      <Descriptions bordered column={1}>
        <Descriptions.Item label="Projet confidentiel">{lead.confidential ? "Yes" : "No"}</Descriptions.Item>
        <Descriptions.Item label="Type d'investissement prévu">{lead.investmentType}</Descriptions.Item>
        <Descriptions.Item label="Plan d'investissement">{lead.investmentPlan}</Descriptions.Item>
        <Descriptions.Item label="Montant d'investissement prévu">{lead.investmentAmount}</Descriptions.Item>
        <Descriptions.Item label="Nombre d'emplois directs prévus">{lead.jobsDirect}</Descriptions.Item>
        <Descriptions.Item label="Nombre d'emplois indirects">{lead.jobsIndirect}</Descriptions.Item>
        <Descriptions.Item label="Salaire moyen pour les emplois directs">{lead.salary || "N/A"}</Descriptions.Item>
        <Descriptions.Item label="Lieu d'implantation prévu">{lead.locations.join(", ")}</Descriptions.Item>
        <Descriptions.Item label="Besoin en biens fonciers">{lead.landNeed || "N/A"}</Descriptions.Item>
        <Descriptions.Item label="Offre terrain et locaux envoyée">{lead.landOffer || "N/A"}</Descriptions.Item>
        <Descriptions.Item label="Origine de l'opportunité (détails)">{lead.opportunityOrigin}</Descriptions.Item>
        <Descriptions.Item label="Origine de l'opportunité (Internet URL)">{lead.opportunityUrl || "N/A"}</Descriptions.Item>
        <Descriptions.Item label="Agence">{lead.agency || "N/A"}</Descriptions.Item>
        <Descriptions.Item label="Coût">{lead.cost || "N/A"}</Descriptions.Item>
        <Descriptions.Item label="Date de la décision d'investissement">{lead.decisionDate}</Descriptions.Item>
        <Descriptions.Item label="Date d'annonce d'investissement">{lead.announcementDate || "N/A"}</Descriptions.Item>
      </Descriptions>

      <Divider>
        <AppstoreOutlined /> Type de projet
      </Divider>
      <Descriptions bordered column={1}>
        <Descriptions.Item label="Type de projet">{lead.projectType}</Descriptions.Item>
        <Descriptions.Item label="Produit/Service">{lead.productService}</Descriptions.Item>
      </Descriptions>

      <Divider>
        <AppstoreOutlined /> Contacts
      </Divider>
      <Descriptions bordered column={1}>
        <Descriptions.Item label="Contact libre">{lead.companyOrContact}</Descriptions.Item>
      </Descriptions>

      <Divider>
        <SettingOutlined /> Suivi (Follow up)
      </Divider>
      <Descriptions bordered column={1}>
        <Descriptions.Item label="Type de lead">{lead.type}</Descriptions.Item>
        <Descriptions.Item label="Date d'échéance">{lead.dueDate}</Descriptions.Item>
        <Descriptions.Item label="Assigné à">{lead.assignedTo}</Descriptions.Item>
      </Descriptions>

      <Divider>
        <PaperClipOutlined /> Autres détails
      </Divider>
      <Descriptions bordered column={1}>
        <Descriptions.Item label="Rappel">{lead.reminder || "N/A"}</Descriptions.Item>
        <Descriptions.Item label="Notes supplémentaires">{lead.notes || "N/A"}</Descriptions.Item>
      </Descriptions>

      <Button onClick={onCancel} style={{ marginTop: "20px" }}>Close</Button>
    </div>
  );
};

export default ViewLead;