// TalentAnalysisDashboard.js
import React from 'react';
import { Card, Row, Col, Statistic, Tooltip, Progress } from 'antd';
import { Bar, Pie } from '@ant-design/charts';
import { talentProfiles } from './data'; // Importer les profils de talents

const TalentAnalysisDashboard = () => {
  // Graphique à barres pour afficher les investissements par talent
  const investmentData = talentProfiles.map(talent => ({
    name: talent.name,
    investment: talent.investment,
  }));

  const investmentConfig = {
    data: investmentData,
    xField: 'name',
    yField: 'investment',
    seriesField: 'name',
    label: {
      position: 'middle',
      style: { fill: '#fff', opacity: 0.6 },
    },
  };

  // Graphique en secteurs pour afficher la répartition des talents par secteur
  const sectorData = talentProfiles.map(talent => ({
    type: talent.sector,
    value: 1, // On compte juste le nombre de talents par secteur
  }));

  const sectorConfig = {
    appendPadding: 10,
    data: sectorData,
    angleField: 'value',
    colorField: 'type',
    radius: 0.8,
    label: {
      type: 'spider',
      formatter: (datum) => `${datum.type}: ${datum.value} talents`,
    },
    interactions: [{ type: 'element-active' }],
  };

  // Graphique en barres pour afficher l'impact des talents
  const impactData = talentProfiles.map(talent => ({
    name: talent.name,
    impact: talent.impact,
  }));

  const impactConfig = {
    data: impactData,
    xField: 'name',
    yField: 'impact',
    seriesField: 'name',
    label: {
      position: 'middle',
      style: { fill: '#fff', opacity: 0.6 },
    },
  };

  return (
    <div style={{ padding: 24 }}>
      <Row gutter={16}>
        <Col span={8}>
          <Card title="Impact des Talents" bordered={false}>
            {talentProfiles.map(talent => (
              <div key={talent.id}>
                <Statistic
                  title={talent.name}
                  value={talent.impact}
                  suffix="/ 100"
                  precision={0}
                  valueStyle={{ color: '#3f8600' }}
                />
                <Progress percent={talent.impact} status="active" />
              </div>
            ))}
          </Card>
        </Col>

        <Col span={16}>
          <Card title="Investissements par Talent" bordered={false}>
            <Bar {...investmentConfig} />
          </Card>
        </Col>
      </Row>

      <Row gutter={16} style={{ marginTop: 24 }}>
        <Col span={12}>
          <Card title="Répartition des Talents par Secteur" bordered={false}>
            <Pie {...sectorConfig} />
          </Card>
        </Col>

        <Col span={12}>
          <Card title="Impact des Talents sur les Projets" bordered={false}>
            <Bar {...impactConfig} />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default TalentAnalysisDashboard;
