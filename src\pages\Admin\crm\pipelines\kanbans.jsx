import React, { useState } from "react";
import styled from "@emotion/styled";
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";
import { 
  Grid, 
  Box, 
  Typography, 
  Chip, 
  Avatar, 
  Menu, 
  MenuItem, 
  Button,
  Select,
  FormControl,
  InputLabel,
  Badge
} from "@mui/material";
import Divider from "@mui/material/Divider";
import FilterAltIcon from "@mui/icons-material/FilterAlt";
import { datas } from "./datas";
import { calculateLeadScore } from "../../../../modules/crm/utils/scoring";

const Container = styled("div")(() => ({
  display: "flex",
  flexDirection: "column",
  padding: "20px",
  backgroundColor: "#f5f7fa",
}));

const FiltersContainer = styled("div")(() => ({
  display: "flex",
  gap: "16px",
  padding: "16px",
  backgroundColor: "white",
  borderRadius: "8px",
  marginBottom: "20px",
  boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
}));

const TaskList = styled("div")(({ isDraggingOver }) => ({
  minHeight: "100px",
  display: "flex",
  flexDirection: "column",
  background: isDraggingOver ? "#e0e5f0" : "#d7dce8",
  minWidth: "340px",
  borderRadius: "8px",
  padding: "15px",
  marginRight: "20px",
  boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
  transition: "background 0.2s ease",
}));

const TaskColumnStyles = styled("div")(() => ({
  display: "flex",
  overflowX: "auto",
  paddingBottom: "20px",
  minHeight: "80vh",
}));

const Title = styled("span")(() => ({
  fontWeight: "bold",
  color: "#333333",
  fontSize: "16px",
}));

const DealCard = styled("div")(({ isDragging }) => ({
  backgroundColor: isDragging ? "#e3f2fd" : "white",
  borderRadius: "6px",
  padding: "16px",
  margin: "8px 0",
  boxShadow: isDragging 
    ? "0 4px 8px rgba(0,0,0,0.2)" 
    : "0 1px 3px rgba(0,0,0,0.1)",
  transition: "all 0.3s ease",
  position: "relative",
  "&:hover": {
    boxShadow: "0 2px 6px rgba(0,0,0,0.15)",
  },
}));

const ScoreBadge = styled("div")(({ score }) => ({
  position: "absolute",
  top: "-10px",
  right: "-10px",
  width: "30px",
  height: "30px",
  borderRadius: "50%",
  backgroundColor: score > 75 ? "#4caf50" : score > 50 ? "#ffc107" : "#f44336",
  color: "white",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  fontWeight: "bold",
  fontSize: "12px",
  boxShadow: "0 2px 4px rgba(0,0,0,0.2)",
}));

const DealHeader = styled("div")({
  display: "flex",
  justifyContent: "space-between",
  alignItems: "flex-start",
  marginBottom: "8px",
});

const DealAmount = styled(Typography)(() => ({
  fontWeight: "bold",
  color: "#2e7d32",
  fontSize: "14px",
}));

const DealInfo = styled(Typography)(() => ({
  color: "#666",
  fontSize: "12px",
  marginTop: "4px",
}));

const DealActivity = styled("div")(() => ({
  display: "flex",
  alignItems: "center",
  marginTop: "8px",
  fontSize: "12px",
  color: "#999",
}));

const LocationChip = styled(Chip)({
  marginRight: "4px",
  marginBottom: "4px",
  fontSize: "10px",
  height: "20px",
});

const getInitialColumns = () => {
  const stages = {
    "pre-pipeline": { title: "PRÉ-PIPELINE", items: [] },
    "outreach": { title: "OUTREACH", items: [] },
    "lead": { title: "LEAD", items: [] },
    "prospect": { title: "PROSPECT", items: [] },
    "negotiation": { title: "NEGOCIATION", items: [] },
    "closed": { title: "CLOSED", items: [] },
  };

  datas.forEach((deal) => {
    const stageKey = deal.currentStage.toLowerCase().replace(/\s+/g, '-');
    if (stages[stageKey]) {
      stages[stageKey].items.push({
        ...deal,
        id: `deal-${deal.key}`,
        score: calculateLeadScore(deal) // Calcul du score pour chaque lead
      });
    }
  });

  return stages;
};

const DealPipeline = () => {
  const [columns, setColumns] = useState(getInitialColumns());
  const [filters, setFilters] = useState({
    type: "",
    statut: "",
    localisation: "",
    montant: "",
    score: ""
  });

  const handleFilterChange = (event) => {
    const { name, value } = event.target;
    setFilters({
      ...filters,
      [name]: value
    });
    applyFilters({
      ...filters,
      [name]: value
    });
  };

  const applyFilters = (currentFilters) => {
    const initialColumns = getInitialColumns();
    
    // Si aucun filtre n'est appliqué, réinitialiser
    if (Object.values(currentFilters).every(val => !val)) {
      setColumns(initialColumns);
      return;
    }

    const filteredColumns = { ...initialColumns };
    
    Object.keys(filteredColumns).forEach(columnId => {
      filteredColumns[columnId].items = filteredColumns[columnId].items.filter(deal => {
        // Filtre par type
        if (currentFilters.type && !deal.projectType.toLowerCase().includes(currentFilters.type.toLowerCase())) {
          return false;
        }
        
        // Filtre par statut (confidentiel)
        if (currentFilters.statut === "Confidentiel" && !deal.confidential) {
          return false;
        }
        if (currentFilters.statut === "Public" && deal.confidential) {
          return false;
        }
        
        // Filtre par localisation
        if (currentFilters.localisation && 
            !deal.locations?.some(loc => 
              loc.toLowerCase().includes(currentFilters.localisation.toLowerCase())
            )) {
          return false;
        }
        
        // Filtre par montant (exemple simplifié)
        if (currentFilters.montant) {
          const amount = parseFloat(deal.investmentAmount?.replace(/[^0-9.]/g, '') || 0);
          if (currentFilters.montant === "<500k" && amount >= 500000) return false;
          if (currentFilters.montant === "500k-1M" && (amount < 500000 || amount >= 1000000)) return false;
          if (currentFilters.montant === ">1M" && amount < 1000000) return false;
        }
        
        // Filtre par score
        if (currentFilters.score) {
          if (currentFilters.score === "high" && deal.score < 75) return false;
          if (currentFilters.score === "medium" && (deal.score < 50 || deal.score >= 75)) return false;
          if (currentFilters.score === "low" && deal.score >= 50) return false;
        }
        
        return true;
      });
    });
    
    setColumns(filteredColumns);
  };

  const onDragEnd = (result) => {
    if (!result.destination) return;
    const { source, destination } = result;

    if (source.droppableId !== destination.droppableId) {
      const sourceColumn = columns[source.droppableId];
      const destColumn = columns[destination.droppableId];
      const sourceItems = [...sourceColumn.items];
      const destItems = [...destColumn.items];
      const [removed] = sourceItems.splice(source.index, 1);
      destItems.splice(destination.index, 0, removed);
      
      setColumns({
        ...columns,
        [source.droppableId]: {
          ...sourceColumn,
          items: sourceItems,
        },
        [destination.droppableId]: {
          ...destColumn,
          items: destItems,
        },
      });
    } else {
      const column = columns[source.droppableId];
      const copiedItems = [...column.items];
      const [removed] = copiedItems.splice(source.index, 1);
      copiedItems.splice(destination.index, 0, removed);
      setColumns({
        ...columns,
        [source.droppableId]: {
          ...column,
          items: copiedItems,
        },
      });
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return "";
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('fr-FR', options);
  };

  const getInitials = (name) => {
    if (!name) return "";
    const names = name.split(" ");
    return names.map(n => n[0]).join("").toUpperCase();
  };

  const getScoreColor = (score) => {
    if (score > 75) return "#4caf50";
    if (score > 50) return "#ffc107";
    return "#f44336";
  };

  return (
    <div>
      <Typography variant="h4" gutterBottom sx={{ padding: "20px 20px 0" }}>
        Pipeline des leads
      </Typography>
      
      {/* Barre de filtres en haut */}
      <FiltersContainer>
        <FormControl sx={{ minWidth: 180 }} size="small">
          <InputLabel>Type de projet</InputLabel>
          <Select
            name="type"
            value={filters.type}
            onChange={handleFilterChange}
            label="Type de projet"
          >
            <MenuItem value="">Tous les types</MenuItem>
            <MenuItem value="TIC">TIC</MenuItem>
            <MenuItem value="INDUSTRIE MANUFACTURIÈRE">INDUSTRIE MANUFACTURIÈRE</MenuItem>
            <MenuItem value="Finance">Finance</MenuItem>
            <MenuItem value="Immobilier">Immobilier</MenuItem>
          </Select>
        </FormControl>
        
        <FormControl sx={{ minWidth: 180 }} size="small">
          <InputLabel>Statut</InputLabel>
          <Select
            name="statut"
            value={filters.statut}
            onChange={handleFilterChange}
            label="Statut"
          >
            <MenuItem value="">Tous les statuts</MenuItem>
            <MenuItem value="Confidentiel">Confidentiel</MenuItem>
            <MenuItem value="Public">Public</MenuItem>
          </Select>
        </FormControl>
        
        <FormControl sx={{ minWidth: 180 }} size="small">
          <InputLabel>Localisation</InputLabel>
          <Select
            name="localisation"
            value={filters.localisation}
            onChange={handleFilterChange}
            label="Localisation"
          >
            <MenuItem value="">Toutes localisations</MenuItem>
            <MenuItem value="France">France</MenuItem>
            <MenuItem value="Europe">Europe</MenuItem>
            <MenuItem value="Amérique">Amérique</MenuItem>
            <MenuItem value="Asie">Asie</MenuItem>
          </Select>
        </FormControl>
        
        <FormControl sx={{ minWidth: 180 }} size="small">
          <InputLabel>Montant</InputLabel>
          <Select
            name="montant"
            value={filters.montant}
            onChange={handleFilterChange}
            label="Montant"
          >
            <MenuItem value="">Tous montants</MenuItem>
            <MenuItem value="<500k">Moins de 500k</MenuItem>
            <MenuItem value="500k-1M">500k à 1M</MenuItem>
            <MenuItem value=">1M">Plus de 1M</MenuItem>
          </Select>
        </FormControl>
        
        <FormControl sx={{ minWidth: 180 }} size="small">
          <InputLabel>Score</InputLabel>
          <Select
            name="score"
            value={filters.score}
            onChange={handleFilterChange}
            label="Score"
          >
            <MenuItem value="">Tous scores</MenuItem>
            <MenuItem value="high">Élevé (75+)</MenuItem>
            <MenuItem value="medium">Moyen (50-74)</MenuItem>
            <MenuItem value="low">Faible (&lt;50)</MenuItem>
          </Select>
        </FormControl>
        
        <Button 
          variant="outlined" 
          onClick={() => {
            setFilters({
              type: "",
              statut: "",
              localisation: "",
              montant: "",
              score: ""
            });
            setColumns(getInitialColumns());
          }}
          sx={{ ml: 2 }}
        >
          Réinitialiser
        </Button>
      </FiltersContainer>
      
      <DragDropContext onDragEnd={onDragEnd}>
        <Container>
          <TaskColumnStyles>
            {Object.entries(columns).map(([columnId, column]) => {
              return (
                <Droppable key={columnId} droppableId={columnId}>
                  {(provided, snapshot) => (
                    <TaskList
                      ref={provided.innerRef}
                      {...provided.droppableProps}
                      isDraggingOver={snapshot.isDraggingOver}
                    >
                      <Box sx={{ width: "100%" }}>
                        <Grid container justifyContent="space-between" alignItems="center">
                          <Grid item>
                            <Title>{column.title}</Title>
                            <Typography variant="caption" color="textSecondary">
                              {column.items.length} leads
                            </Typography>
                          </Grid>
                        </Grid>
                      </Box>
                      <Divider sx={{ my: 1 }} />
                      
                      {column.items.map((deal, index) => (
                        <Draggable key={deal.id} draggableId={deal.id} index={index}>
                          {(provided, snapshot) => (
                            <DealCard
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              {...provided.dragHandleProps}
                              isDragging={snapshot.isDragging}
                              style={{
                                ...provided.draggableProps.style,
                                transform: snapshot.isDragging
                                  ? provided.draggableProps.style?.transform
                                  : "translate(0,0)",
                              }}
                            >
                              <ScoreBadge score={deal.score}>
                                {deal.score}
                              </ScoreBadge>
                              
                              <DealHeader>
                                <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                                  {deal.leadName}
                                </Typography>
                                <Avatar sx={{ 
                                  width: 80, 
                                  height: 24, 
                                  fontSize: '0.75rem',
                                  bgcolor: deal.confidential ? '#f44336' : '#4caf50'
                                }}>
                                  {/* {getInitials(deal.owner)} */}
                                   {deal.owner}
                                </Avatar>
                              </DealHeader>
                              
                              <Typography variant="body2" sx={{ mb: 1 }}>
                                {deal.enterprise}
                              </Typography>
                              
                              {deal.investmentAmount && (
                                <DealAmount>{deal.investmentAmount}</DealAmount>
                              )}
                              
                              <Box sx={{ mt: 1, mb: 1 }}>
                                {deal.locations?.map(location => (
                                  <LocationChip 
                                    key={location} 
                                    label={location} 
                                    size="small" 
                                  />
                                ))}
                              </Box>
                              
                              <DealInfo>
                                <strong>Type:</strong> {deal.projectType.split('/')[0].trim()}
                              </DealInfo>
                              
                              {deal.decisionDate && (
                                <DealInfo>
                                  <strong>Décision:</strong> {formatDate(deal.decisionDate)}
                                </DealInfo>
                              )}
                              
                              <DealActivity>
                                <strong>Ajouté:</strong> {deal.pipelineAdded} • {deal.timeInCurrentStage}
                              </DealActivity>
                            </DealCard>
                          )}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                    </TaskList>
                  )}
                </Droppable>
              );
            })}
          </TaskColumnStyles>
        </Container>
      </DragDropContext>
    </div>
  );
};

export default DealPipeline;