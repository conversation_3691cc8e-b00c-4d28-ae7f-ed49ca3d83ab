import { ModalForm } from "@ant-design/pro-components";
import {
  <PERSON><PERSON>,
  <PERSON>,
  Col,
  DatePicker,
  Form,
  Input,
  InputNumber,
  Popconfirm,
  Radio,
  Row,
  Select,
  Table,
} from "antd";

import TextArea from "antd/es/input/TextArea.js";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { companies, intercationTypes } from "./data";

function EditIntercationForm({ open, onCancel, record }) {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const dateFormat = "YYYY-MM-DD";
  const [form] = Form.useForm();
  useEffect(() => {
    if (open) {
      form.setFieldsValue(record);
    }
  }, [open]);

  return (
    <ModalForm
      title={t("Modifier une interaction")}
      form={form}
      open={open}
      modalProps={{
        style: { top: 20 },
      }}
      //   onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.edit"),
          resetText: t("common.actions.cancel"),
        },
      }}
    >
      <Card className="mt-2">
        <Row gutter={4}>
          <Col className="gutter-row" span={24}>
            <Form.Item
              name="entreprise_id"
              label="Nom de l'entreprise"
              rules={[{ required: true, message: "Ce champ est obligatoire" }]}
            >
              <Select placeholder="Sélectionnez un type">
                {companies.map((type) => (
                  <Select.Option key={type.id} value={type.id}>
                    {type.nom}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col className="gutter-row" span={24}>
            <Form.Item
              name="type_intercation_id"
              label="Type d'interaction"
              rules={[{ required: true, message: "Ce champ est obligatoire" }]}
            >
              <Select placeholder="Sélectionnez un type">
                {intercationTypes.map((type) => (
                  <Select.Option key={type.id} value={type.id}>
                    {type.nom}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col className="gutter-row" span={24}>
            <Form.Item
              name="date_intercation"
              label="Date de l'intercation"
              rules={[{ required: true, message: "Ce champ est obligatoire" }]}
            >
              <DatePicker style={{ width: "100%" }} />
            </Form.Item>
          </Col>

          <Col className="gutter-row" span={24}>
            <Form.Item
              name="description"
              label="Description"
              rules={[{ required: true, message: "Ce champ est obligatoire" }]}
            >
              <TextArea rows={4} placeholder="Décrivez l'échange" />
            </Form.Item>
          </Col>
        </Row>
      </Card>
    </ModalForm>
  );
}
export default EditIntercationForm;
