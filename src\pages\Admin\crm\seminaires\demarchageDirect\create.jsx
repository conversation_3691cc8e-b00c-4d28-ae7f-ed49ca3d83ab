import {
  CheckCircleOutlined,
  CheckCircleTwoTone,
  PlusCircleFilled,
} from "@ant-design/icons";
import { ModalForm } from "@ant-design/pro-components";
import {
  Button,
  Card,
  Checkbox,
  Col,
  DatePicker,
  Divider,
  Form,
  Input,
  message,
  Modal,
  Popover,
  Radio,
  Row,
  Select,
  Steps,
} from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";
// import {
//   activites,
//   categories,
//   delegations,
//   gouvernorats,
//   nationalites,
//   secteurs,
// } from "./data";
const initialSteps = [
  {
    id: 1,
    title: "Proposée",
    done: false,
    motif: false,
    reason: "",
    status: "finish",
  },
  {
    id: 2,
    title: "Programmée",
    done: false,
    motif: false,
    reason: "",
    status: "finish",
  },
  {
    id: 3,
    title: "Réalisée",
    done: false,
    motif: false,
    reason: "",
    status: "wait",
  },
  {
    id: 4,
    title: "Reportée",
    done: false,
    motif: true,
    reason: "",
    status: "finish",
  },
  {
    id: 5,
    title: "Ann<PERSON>ée",
    done: false,
    motif: true,
    reason: "",
    status: "finish",
  },
  {
    id: 6,
    title: "Non programmée",
    done: false,
    motif: false,
    reason: "",
    status: "wait",
  },
  {
    id: 7,
    title: "Validée",
    done: false,
    motif: false,
    reason: "",
    status: "wait",
  },
];
function CreateDemarchageDirect({ open, onCancel, onSuccess }) {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const handleSubmit = (values) => {};


  return (
    <ModalForm
      title={t("Ajouter un contact : démarchage direct")}      
      form={form}
      open={open}
      modalProps={{
        style: { top: 20 },
      }}
      onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.save"),
          resetText: t("common.actions.cancel"),
        },
      }}
    >
      <Card className="mt-2">
        <Form.Item name={"action"} label={t("Action")}>
          <Checkbox.Group
            options={[
              "Proposée",
              "Programmée",
              "Réalisée",
              "Reportée",
              "Annulée",
            ]}
          />
        </Form.Item>

        <Row gutter={16}>
          <Col md={24} xs={24}>
            <Form.Item name={"presentation"} label={t("Présentation")}>
              <Input.TextArea />
            </Form.Item>
          </Col>
          <Col md={16} xs={24}>
            <Form.Item name={"initiateur"} label={t("Initiateur")}>
              <Select />
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item name={"inclure"} label={t("Inclure")}>
              <Select />
            </Form.Item>
          </Col>
           <Col md={16} xs={24}>
            <Form.Item name={"secteur"} label={t("Secteur")}>
              <Select />
            </Form.Item>
          </Col>
           <Col md={8} xs={24}>
            <Form.Item name={"groupe_secteur"} label={t("Groupe secteur")}>
              <Select />
            </Form.Item>
          </Col>
              <Col md={8} xs={24}>
            <Form.Item name={"pays_id"} label={t("Pays")}>
              <Select />
            </Form.Item>
          </Col>
              <Col md={16} xs={24}>
            <Form.Item name={"régions"} label={t("Régions")}>
              <Input />
            </Form.Item>
          </Col>
           <Col md={12} xs={24}>
            <Form.Item name={"date_debut"} label={t("Date début")}>
              <DatePicker />
            </Form.Item>
          </Col>
           <Col md={12} xs={24}>
            <Form.Item name={"date_fin"} label={t("Date fin")}>
              <DatePicker />
            </Form.Item>
          </Col>
           <Col md={12} xs={24}>
            <Form.Item name={"conjointe"} label={t("Conjointe/Non conjointe")}>
              <Select />
            </Form.Item>
          </Col>
            <Col md={12} xs={24}>
            <Form.Item name={"cadre_siege"} label={t("Cadre du siège")}>
              <Select />
            </Form.Item>
          </Col>
           <Col md={12} xs={24}>
            <Form.Item name={"objectif_initiateur"} label={t("Objectif contacts intéressants initiateur")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name={"objectif_binôme"} label={t("Objectif contacts intéressants binôme")}>
              <Input />
            </Form.Item>
          </Col>
        </Row>
      </Card>
    </ModalForm>
  );
}
export default CreateDemarchageDirect;
