import { ModalForm } from "@ant-design/pro-components";
import { Form, Input, message, Select } from "antd";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { banques } from "../banque/data";
import { devises, entreprises } from "./data";

function ModalRib({ open, onCancel, isEditing, show }) {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  useEffect(() => {
    console.log(isEditing?.banque?.id)
    console.log(banques)
    form.setFieldsValue({
      ...isEditing,
      entreprise: isEditing?.entreprise?.id,
      devise: isEditing?.devise?.id,
      banque_id: isEditing?.banque?.id,
    });
  }, [isEditing, open]);

  const handleSubmit = async (values) => {
    setLoading(true);
    try {
      const submissionData = {
        ...values,
      };
      await new Promise((resolve) => setTimeout(resolve, 2000));

      message.success("Rib ajouté avec succès !");
    } catch (error) {
      console.log(error);
      message.error("Erreur lors de l'ajout du rib !");
    } finally {
      setLoading(false);
      onCancel();
    }
  };
  return (
    <ModalForm
      title={(isEditing&&!show) ? t("finance.ribs.edit") :show? t("finance.ribs.view") : t("finance.ribs.add")}
      open={open}
      onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={show ? false : {  // Désactiver le footer si show est true
        searchConfig: {
          submitText: t("common.actions.save"),
          resetText: t("common.actions.cancel"),
        },
        submitButtonProps: {
          loading,
        },
      }}
      modalProps={{
        destroyOnClose: true,
      }}
    >
      <Form layout="vertical" disabled={show} form={form}>
        <Form.Item
          name="rib"
          label={t("finance.ribs.fields.rib")}
          rules={[
            {
              required: true,
              message: "finance.ribs.validations.required_rib",
            },
          ]}
        >
          <Input placeholder={t("finance.ribs.fields.rib")} />
        </Form.Item>
        <Form.Item
          name="banque_id"
          label={t("finance.ribs.fields.banque_id")}
          rules={[
            {
              required: true,
              message: "finance.ribs.validations.required_banque_id",
            },
          ]}
        >
          <Select placeholder={t("finance.ribs.fields.banque_id")}>
            {banques.map((banque) => (
              <Select.Option key={banque.id} value={banque.id}>
                {banque.libelle}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item
          name="entreprise"
          label={t("finance.ribs.fields.entreprise")}
          rules={[
            {
              required: true,
              message: "finance.ribs.validations.required_entreprise",
            },
          ]}
        >
          <Select placeholder={t("finance.ribs.fields.entreprise")}>
            {entreprises.map((e) => (
              <Select.Option key={e.id} value={e.id}>
                {e.libelle}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item
          name="devise"
          label={t("finance.ribs.fields.devise")}
          rules={[
            {
              required: true,
              message: "finance.ribs.validations.required_devise",
            },
          ]}
        >
          <Select placeholder={t("finance.ribs.fields.devise")}>
            {devises.map((e) => (
              <Select.Option key={e.id} value={e.id}>
                {e.libelle}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
      </Form>
    </ModalForm>
  );
}

export default ModalRib;
