import React, { useState } from "react";
import {
  Card,
  Form,
  Input,
  DatePicker,
  Select,
  Button,
  Table,
  Tag,
  message,
  Row,
  Col,
  Checkbox,
} from "antd";
import dayjs from "dayjs";
import { ModalForm } from "@ant-design/pro-components";
import { useTranslation } from "react-i18next";
import { MinusCircleOutlined, PlusCircleOutlined } from "@ant-design/icons";

const { Option } = Select;

const Interactions = ({ open, onCancel, onSuccess }) => {
  const [interactions, setInteractions] = useState([]);
  const [form] = Form.useForm();
  const { t } = useTranslation();

  const handleAddInteraction = (values) => {
    console.log(values);
    const newInteraction = {
      id: Date.now(),
      type: values.type,
      date: values.date,
      sujet: values.sujet,
      description: values.description,
    };
    setInteractions([newInteraction, ...interactions]);
    form.resetFields();
  };
  const handleAdd = () => {
    const currentData = form.getFieldValue("Items") || [];

    const date_intercation = form.getFieldValue("date_intercation");
    const type = form.getFieldValue("type");
    const sujet = form.getFieldValue("sujet");
    const description = form.getFieldValue("description");

    if (date_intercation && type && sujet && description) {
      const key = Date.now();
      form.setFieldsValue({
        Items: [
          ...currentData,
          {
            key: key,
            type: type,
            date_intercation: date_intercation,
            description: description,
            sujet: sujet,
          },
        ],
      });
      form.resetFields(["date_intercation", "type", "sujet", "description"]);
    } else {
      message.error(t("Merci de bien vouloir remplir tous les champs."));
    }
  };
  const columns = [
    {
      title: "Date",
      dataIndex: "date",
      key: "date",
      valueType: "date",
    },
    {
      title: "Type",
      dataIndex: "type",
      key: "type",
      render: (type) => {
        const colorMap = {
          Reunion: "blue",
          Call: "green",
          Email: "orange",
          Event: "purple",
        };
        return <Tag color={colorMap[type]}>{type}</Tag>;
      },
    },
    {
      title: "Sujet",
      dataIndex: "sujet",
      key: "sujet",
    },

    {
      title: "Description",
      dataIndex: "description",
      key: "description",
      ellipsis: true,
    },
  ];

  return (
    <ModalForm
      title={t("Interactions avec les entreprises")}
      form={form}
      open={open}
      width="70%"
      modalProps={{
        style: { top: 20 },
      }}
      onFinish={handleAdd}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.save"),
          resetText: t("common.actions.cancel"),
        },
      }}
    >
      <Card className="mt-5">
        <Row gutter={16} style={{ marginBottom: "10px" }}>
          <Col span={4}>Type d'Interaction</Col>
          <Col span={4}>Date</Col>
          <Col span={5}>Sujet</Col>
          <Col span={10}>Description</Col>
          <Col span={1}></Col>
        </Row>

        <Form.List name="Items">
          {(fields, { remove }) => (
            <>
              {fields.map((field, index) => (
                <Row gutter={16} align="middle" key={field.key}>
                     <Col span={4}>
                    <Form.Item name={[index, "type"]} rules={[]}>
                      <Select placeholder="Choisir un type">
                        <Option value="Réunion">Réunion</Option>
                        <Option value="Appel">Appel</Option>
                        <Option value="Email">Email</Option>
                        <Option value="Evénement">Evénement</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={4}>
                    <Form.Item name={[index, "date_intercation"]} rules={[]}>
                      <DatePicker allowClear={true} />
                    </Form.Item>
                  </Col>
               
                  <Col span={5}>
                    <Form.Item name={[index, "sujet"]} rules={[]}>
                      <Input allowClear={true} />
                    </Form.Item>
                  </Col>
                  <Col span={10}>
                    <Form.Item name={[index, "description"]} rules={[]}>
                      <Input.TextArea rows={1} />
                    </Form.Item>
                  </Col>

                  <Col span={1}>
                    <Form.Item>
                      <MinusCircleOutlined
                        style={{
                          color: "red",
                          fontSize: "18px",
                          cursor: "pointer",
                        }}
                        onClick={() => remove(index)}
                      />
                    </Form.Item>
                  </Col>
                </Row>
              ))}
            </>
          )}
        </Form.List>

        <Row gutter={16} align="middle" style={{ marginBottom: "5px" }}>
          <Col span={4}>
            <Form.Item name="type">
              <Select placeholder="Choisir un type">
                <Option value="Réunion">Réunion</Option>
                <Option value="Appel">Appel</Option>
                <Option value="Email">Email</Option>
                <Option value="Evénement">Evénement</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={4}>
            <Form.Item name={`date_intercation`} rules={[]}>
              <DatePicker allowClear={true} />
            </Form.Item>
          </Col>
          <Col span={5}>
            <Form.Item name={`sujet`} rules={[]}>
              <Input allowClear={true} />
            </Form.Item>
          </Col>
          <Col span={10}>
            <Form.Item name={`description`} rules={[]}>
              <Input.TextArea rows={1} />
            </Form.Item>
          </Col>
          <Col span={1}>
            <Form.Item>
              <PlusCircleOutlined
                style={{
                  color: "green",
                  fontSize: "18px",
                  cursor: "pointer",
                }}
                onClick={() => handleAdd()}
              />
            </Form.Item>
          </Col>
        </Row>
      </Card>
    </ModalForm>
  );
};

export default Interactions;
