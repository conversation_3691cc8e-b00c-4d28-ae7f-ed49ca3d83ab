import { ModalForm } from "@ant-design/pro-components";
import {
  <PERSON><PERSON>,
  Card,
  Checkbox,
  Col,
  DatePicker,
  Form,
  Input,
  InputNumber,
  Modal,
  Radio,
  Row,
  Select,
  Statistic,
  Tag,
  message,
} from "antd";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

import {
  MinusCircleOutlined,
  PlusCircleOutlined,
  PlusOutlined,
} from "@ant-design/icons";
function ProjectCard({
  nom,
  statut,
  montantInvestissement,
  retombeeEconomique,
}) {
  const statutColors = {
    "En cours": "blue",
    Terminé: "green",
    Suspendu: "red",
  };

  return (
    <Card
      title={nom}
      style={{
        width: 350,
        borderRadius: 12,
        boxShadow: "0 2px 10px rgba(0,0,0,0.1)",
      }}
    >
      <Tag color={statutColors[statut]}>{statut}</Tag>
      <Statistic
        title="Montant de l’investissement"
        value={montantInvestissement}
        prefix="€"
      />
      <div style={{ marginTop: 16 }}>
        <strong>Retombées économiques :</strong>
        <p>{retombeeEconomique}</p>
      </div>
    </Card>
  );
}
function CompanyContacts({ view, open, onCancel, record }) {
  const { t } = useTranslation();

  const handleAdd = () => {
    const currentData = form.getFieldValue("Items") || [];

    const contact = form.getFieldValue("contact");
    const fonction = form.getFieldValue("fonction");
    const tel = form.getFieldValue("tel");
    const email = form.getFieldValue("email");

    if (contact && fonction &&  tel && email ) {

      const key = Date.now();
      form.setFieldsValue({
        Items: [
          ...currentData,
          {
            key: key,
            contact: contact,
            fonction: fonction,
            tel: tel,
            email: email 
          },
        ],
      });
     form.resetFields(["contact" ,"fonction" ,"tel" ,"email"]);
    } else {
      message.error(t("Merci de bien vouloir remplir tous les champs."));
    }
  };

  const [form] = Form.useForm();

  return (
    <ModalForm
      title={t("Contacts")}
      form={form}
      open={open}
      modalProps={{
        style: { top: 20 },
      }}
      //   onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.save"),
          resetText: t("common.actions.cancel"),
        },
      }}
    >
      <Card className="mt-5">
        <Row gutter={16} style={{ marginBottom: "10px" }}>
          <Col span={6}>Contact</Col>
          <Col span={6}>Fonction</Col>
          <Col span={5}>Téléphone</Col>
          <Col span={6}>Email</Col>
       
          <Col span={1}></Col>
        </Row>

        <Form.List name="Items">
          {(fields, { remove }) => (
            <>
              {fields.map((field, index) => (
                <Row gutter={16} align="middle" key={field.key}>
                  <Col span={6}>
                    <Form.Item name={[index, "contact"]} rules={[]}>
                      <Input></Input>
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item name={[index, "fonction"]} rules={[]}>
                    <Select>
                <Select.Option value="PDG">PDG</Select.Option>
                <Select.Option value="Directeur">Directeur</Select.Option>
                <Select.Option value="Responsable">Responsable</Select.Option>
              </Select>
                    </Form.Item>
                  </Col>

                  <Col span={5}>
                    <Form.Item name={[index, "tel"]} rules={[]}>
                      <Input allowClear={true} />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item name={[index, "email"]} rules={[]}>
                      <Input />
                    </Form.Item>
                  </Col>

                  <Col span={1}>
                    <Form.Item>
                      <MinusCircleOutlined
                        style={{
                          color: "red",
                          fontSize: "18px",
                          cursor: "pointer",
                        }}
                        onClick={() => remove(index)}
                      />
                    </Form.Item>
                  </Col>
                </Row>
              ))}
            </>
          )}
        </Form.List>

        <Row gutter={16} align="middle" style={{ marginBottom: "5px" }}>
          <Col span={6}>
            <Form.Item name="contact">
             <Input></Input>
            </Form.Item>
          </Col>
         
          <Col span={6}>
            <Form.Item name={`fonction`} rules={[]}>
              <Select>
                <Select.Option value="PDG">PDG</Select.Option>
                <Select.Option value="Directeur">Directeur</Select.Option>
                <Select.Option value="Responsable">Responsable</Select.Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={5}>
            <Form.Item name={`tel`} rules={[]}>
              <Input />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item name={`email`} rules={[]}>
              <Input />
            </Form.Item>
          </Col>
          <Col span={1}>
            <Form.Item>
              <PlusCircleOutlined
                style={{
                  color: "green",
                  fontSize: "18px",
                  cursor: "pointer",
                }}
                onClick={() => handleAdd()}
              />
            </Form.Item>
          </Col>
        </Row>
      </Card>
    </ModalForm>
  );
}
export default CompanyContacts;
