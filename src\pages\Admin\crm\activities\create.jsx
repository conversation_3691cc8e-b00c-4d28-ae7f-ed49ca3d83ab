import React, { useState, useEffect } from "react";
import {
  ProForm,
  ProFormText,
  ProFormSelect,
  ProFormDatePicker,
  ProFormTextArea,
  ProFormUploadButton,
  ProFormRadio,
  ProFormCheckbox,
  ModalForm,
} from "@ant-design/pro-form";
import {
  Card,
  Divider,
  Tabs,
  Button,
  Modal,
  Row,
  Col,
  Typography,
  Form,
  Select,
  Input,
} from "antd";
import {
  FileTextOutlined,
  CheckSquareOutlined,
  UserOutlined,
  FormOutlined,
  PaperClipOutlined,
  PlusOutlined,
} from "@ant-design/icons";
import dayjs from "dayjs";
import TypeCreate from "../typesactivities/create";
import PlanCreate from "../plansactivities/createPlanActivity";
import ContactCreate from "../contacts/create";
import Entreprise from "../contacts_entreprises/entreprises/createsecond";
import "./Style.css";

const { Text } = Typography;

const CreateTask = ({ onCancel, setDataSource }) => {
  const [loading, setLoading] = useState(false);
  const [category, setCategory] = useState("taskType");
  const [contacts, setContacts] = useState([]);
  const [plans, setPlans] = useState([]);
  const [taskTypes, setTaskTypes] = useState([]);
  const [assignedPer, setAssignedPer] = useState([
    { label: "Mohamed", value: "Mohamed" },
    { label: "Mourad", value: "Mourad" },
    { label: "Sana", value: "Sana" },
  ]);
  const [priorities, setPriority] = useState([
    { label: "High", value: "High" },
    { label: "Medium", value: "Medium" },
    { label: "Low", value: "Low" },
  ]);
  const [modalData, setModalData] = useState({ open: false, type: "", value: "" });
  const [activePhase, setActivePhase] = useState(0);
  const [activeTab, setActiveTab] = useState("details");
  const [form] = ProForm.useForm();
  const [selectedStage, setSelectedStage] = useState(null);

  const pipelinePhases = [
    {
      name: "Pre-Pipeline",
      stages: [{ id: 1, name: "Entreprise non encore contactée" }],
    },
    {
      name: "Outreach",
      stages: [
        { id: 2, name: "Entreprise contactée" },
        { id: 3, name: "Appel téléphonique réalisé" },
        { id: 4, name: "Réunion tenue" },
      ],
    },
    {
      name: "Lead",
      stages: [{ id: 5, name: "Intérêt précoce manifesté" }],
    },
    {
      name: "Prospect",
      stages: [{ id: 6, name: "Confirmation d'implantation" }],
    },
    {
      name: "Active Project",
      stages: [
        { id: 7, name: "Projet identifié" },
        { id: 8, name: "Idée de projet communiquée" },
        { id: 9, name: "Visite de prospection" },
        { id: 10, name: "Projet en négociation" },
        { id: 11, name: "Projet en attente" },
      ],
    },
    {
      name: "Investment Success",
      stages: [
        { id: 12, name: "Projet annoncé" },
        { id: 13, name: "Projet déclaré" },
      ],
    },
    {
      name: "Closed / Inactive",
      stages: [
        { id: 14, name: "Aucun intérêt" },
        { id: 15, name: "Opportunité perdue" },
      ],
    },
  ];

  const breadcrumbItems = pipelinePhases.map((phase) => phase.name);

  const openModal = (type) => setModalData({ open: true, type, value: "" });
  const closeModal = () => setModalData({ open: false, type: "", value: "" });

  useEffect(() => {
    const uniqueContacts = [
      { label: "Contact 1", value: "contact1" },
      { label: "Contact 2", value: "contact2" },
    ];
    setContacts(uniqueContacts);

    const uniquePlans = [
      { label: "Plan A", value: "plan.a" },
      { label: "Plan B", value: "plan.b" },
    ];
    setPlans(uniquePlans);

    const uniqueTaskTypes = [
      { label: "Type 1", value: "type1" },
      { label: "Type 2", value: "type2" },
    ];
    setTaskTypes(uniqueTaskTypes);

    setSelectedStage(pipelinePhases[0].stages[0].name);
    form.setFieldsValue({ status: pipelinePhases[0].stages[0].name });
  }, [form]);

  const handleSubmit = async (values) => {
    try {
      setLoading(true);
      const newKey = Date.now();
      const formattedDueDate = values.dueDate
        ? dayjs(values.dueDate).format("YYYY-MM-DD")
        : null;
      const formattedReminderDate = values.reminder
        ? dayjs(values.reminder).format("YYYY-MM-DD")
        : null;
      const formattedDecisionDate = values.decisionDate
        ? dayjs(values.decisionDate).format("YYYY-MM-DD")
        : null;
      const formattedAnnouncementDate = values.announcementDate
        ? dayjs(values.announcementDate).format("YYYY-MM-DD")
        : null;

      const newTask = {
        key: newKey,
        title: values.title,
        description: values.description,
        category: category,
        type: category === "taskType" ? values.type : null,
        plan: category === "plan" ? values.plan : null,
        dueDate: formattedDueDate,
        priority: values.priority,
        assignedTo: values.assignedTo,
        companyOrContact: values.companyOrContact,
        leadEmail: values.leadEmail,
        enterpriseContact: values.enterpriseContact,
        stakeholder: values.stakeholder,
        reminder: formattedReminderDate,
        notes: values.notes,
        attachments: values.attachments?.fileList?.map((file) => file.name) || [],
        status: values.status || selectedStage,
        enterprise: values.enterprise,
        leadName: values.title,
        confidential: values.confidential ? "Yes" : "No",
        investmentType: values.investmentType,
        investmentPlan: values.investmentPlan,
        investmentAmount: values.investmentAmount,
        jobsDirect: values.jobsDirect,
        jobsIndirect: values.jobsIndirect,
        salary: values.salary,
        locations: values.locations,
        landNeed: values.landNeed,
        landOffer: values.landOffer,
        opportunityOrigin: values.opportunityOrigin,
        opportunityUrl: values.opportunityUrl,
        agency: values.agency,
        cost: values.cost,
        decisionDate: formattedDecisionDate,
        announcementDate: formattedAnnouncementDate,
        projectType: values.projectType,
        productService: values.productService,
      };

      setDataSource((prev) => [...prev, newTask]);
      onCancel();
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddNewItem = (newItem) => {
    const updatedItems =
      modalData.type === "taskType"
        ? [
            ...taskTypes,
            { label: newItem, value: newItem.toLowerCase().replace(/\s+/g, ".") },
          ]
        : modalData.type === "plan"
        ? [
            ...plans,
            { label: newItem, value: newItem.toLowerCase().replace(/\s+/g, ".") },
          ]
        : modalData.type === "contact"
        ? [
            ...contacts,
            { label: newItem, value: newItem.toLowerCase().replace(/\s+/g, ".") },
          ]
        : modalData.type === "entreprise"
        ? [
            ...contacts,
            { label: newItem.raison_social, value: newItem.raison_social.toLowerCase().replace(/\s+/g, ".") },
          ]
        : [];

    if (modalData.type === "taskType") setTaskTypes(updatedItems);
    if (modalData.type === "plan") setPlans(updatedItems);
    if (modalData.type === "contact" || modalData.type === "entreprise") {
      setContacts(updatedItems);
      form.setFieldsValue({ enterprise: newItem.raison_social });
    }

    closeModal();
  };

  const tabItems = [
    {
      key: "details",
      label: (
        <span>
          <FileTextOutlined />
          Informations générales
        </span>
      ),
    },
    {
      key: "stages",
      label: (
        <span>
          <CheckSquareOutlined />
          Détails du lead
        </span>
      ),
    },
    {
      key: "contacts",
      label: (
        <span>
          <UserOutlined />
          Contacts
        </span>
      ),
    },
    {
      key: "notes",
      label: (
        <span>
          <FormOutlined />
          Notes
        </span>
      ),
    },
    {
      key: "documents",
      label: (
        <span>
          <PaperClipOutlined />
          Documents
        </span>
      ),
    },
  ];

  return (
    <div style={{ padding: "24px" }} className="satusActivies">
      <Card>
        <div style={{ overflowX: "auto", margin: "20px 0" }}>
          <div className="breadcrumb-scroll-container">
            <ul className="breadcrumb-arrows">
              {breadcrumbItems.map((item, index) => (
                <li
                  key={index}
                  className={`crumb ${index === activePhase ? "activechoise" : ""} ${
                    index === breadcrumbItems.length - 1 ? "last" : ""
                  }`}
                  onClick={() => {
                    setActivePhase(index);
                    setSelectedStage(pipelinePhases[index].stages[0].name);
                    form.setFieldsValue({ status: pipelinePhases[index].stages[0].name });
                  }}
                >
                  <a href="#">{item}</a>
                </li>
              ))}
            </ul>
          </div>
        </div>

        <div style={{ marginBottom: "16px" }}>
          <ProFormSelect
            name="status"
            label="Étape sélectionnée"
            options={pipelinePhases[activePhase].stages.map((stage) => ({
              label: stage.name,
              value: stage.name,
            }))}
            fieldProps={{
              value: selectedStage,
              onChange: (value) => setSelectedStage(value),
            }}
            rules={[{ required: true, message: "Veuillez sélectionner une étape" }]}
          />
        </div>

        <Row gutter={16}>
          <Col span={18}>
            <Tabs activeKey={activeTab} onChange={setActiveTab} items={tabItems} />
            <ProForm
              form={form}
              onFinish={handleSubmit}
              submitter={false}
            >
              {activeTab === "details" && (
                <div>
                  <Row gutter={24} style={{ marginBottom: "20px" }}>
                    <Col span={12}>
                      <ProForm.Group
                        style={{ display: "flex", flexDirection: "column", gap: "8px" }}
                      >
                        <span>Entreprise</span>
                        <div
                          style={{
                            display: "flex",
                            marginTop: "-5px",
                            alignItems: "center",
                            border: "1px solid #d9d9d9",
                            borderRadius: "6px",
                            overflow: "hidden",
                            height: "32px",
                          }}
                        >
                          <ProFormSelect
                            name="enterprise"
                            options={contacts}
                            rules={[{ required: true, message: "Veuillez sélectionner une entreprise" }]}
                            width="sm"
                            fieldProps={{ bordered: false }}
                            style={{ flex: 1, marginTop: "15px" }}
                          />
                          <Button
                            type="primary"
                            icon={<PlusOutlined />}
                            onClick={() => openModal("entreprise")}
                            style={{
                              borderRadius: "0px",
                              height: "37px",
                              padding: "0 12px",
                              borderLeft: "1px solid #d9d9d9",
                              display: "flex",
                              alignItems: "center",
                            }}
                          />
                        </div>
                      </ProForm.Group>
                    </Col>
                  </Row>
                  <ProFormTextArea
                    name="stageDetails"
                    label="Détails du stade (optionnel)"
                    placeholder="Détails supplémentaires"
                  />
                  <ProFormText
                    name="title"
                    label="Titre de lead"
                    placeholder="Titre"
                    rules={[{ required: true, message: "Veuillez entrer un titre" }]}
                  />
                </div>
              )}
              {activeTab === "stages" && (
                <div>
                  <Divider>
                    <CheckSquareOutlined /> Informations sur le lead
                  </Divider>
                  <ProFormCheckbox name="confidential" label="Projet confidentiel" />
                  <ProFormSelect
                    name="investmentType"
                    label="Type d'investissement prévu"
                    options={[
                      { label: "Expansion", value: "Expansion" },
                      { label: "Fusion et acquisition", value: "Fusion et acquisition" },
                      { label: "Non spécifié", value: "Non spécifié" },
                      { label: "Nouveau (friche industrielle)", value: "Nouveau (friche industrielle)" },
                      { label: "Nouveau (investissement, greenfield)", value: "Nouveau (investissement, greenfield)" },
                    ]}
                    rules={[{ required: true, message: "Veuillez sélectionner un type d'investissement" }]}
                  />
                  <ProFormText
                    name="investmentPlan"
                    label="Plan d'investissement"
                    placeholder="Détails du plan"
                  />
                  <ProFormText
                    name="investmentAmount"
                    label="Montant d'investissement prévu"
                    placeholder="Ex: 2500000"
                  />
                  <ProFormText
                    name="jobsDirect"
                    label="Nombre d'emplois directs prévus"
                    placeholder="Ex: 400"
                  />
                  <ProFormText
                    name="jobsIndirect"
                    label="Nombre d'emplois indirects"
                    placeholder="Ex: 100"
                  />
                  <ProFormText
                    name="salary"
                    label="Salaire moyen pour les emplois directs"
                    placeholder="Ex: 50000"
                  />
                  <ProFormSelect
                    name="locations"
                    label="Lieu d'implantation prévu"
                    mode="multiple"
                    options={[
                      { label: "Tunisia", value: "Tunisia" },
                      { label: "Germany", value: "Germany" },
                      { label: "France", value: "France" },
                      { label: "Algeria", value: "Algeria" },
                      { label: "Jordan", value: "Jordan" },
                      { label: "Qatar", value: "Qatar" },
                    ]}
                  />
                  <ProFormText
                    name="landNeed"
                    label="Besoin en biens fonciers"
                    placeholder="Détails"
                  />
                  <ProFormText
                    name="landOffer"
                    label="Offre terrain et locaux envoyée"
                    placeholder="Détails"
                  />
                </div>
              )}
              {activeTab === "contacts" && (
                <div>
                  <Divider>
                    <UserOutlined /> Contacts
                  </Divider>
                  <Row gutter={24} style={{ marginBottom: "20px" }}>
                    <Col span={12}>
                      <ProForm.Group
                        style={{ display: "flex", flexDirection: "column", gap: "8px" }}
                      >
                        <span>Contact de l'entreprise</span>
                        <div
                          style={{
                            display: "flex",
                            marginTop: "-5px",
                            alignItems: "center",
                            border: "1px solid #d9d9d9",
                            borderRadius: "6px",
                            overflow: "hidden",
                            height: "32px",
                          }}
                        >
                          <ProFormSelect
                            name="enterpriseContact"
                            options={contacts}
                            width="sm"
                            fieldProps={{ bordered: false }}
                            style={{ flex: 1 }}
                          />
                          <Button
                            type="primary"
                            icon={<PlusOutlined />}
                            onClick={() => openModal("contact")}
                            style={{
                              borderRadius: "0px",
                              height: "37px",
                              padding: "0 12px",
                              borderLeft: "1px solid #d9d9d9",
                              display: "flex",
                              alignItems: "center",
                            }}
                          />
                        </div>
                      </ProForm.Group>
                    </Col>
                    <Col span={12}>
                      <ProForm.Group
                        style={{ display: "flex", flexDirection: "column", gap: "8px" }}
                      >
                        <span>Contact libre</span>
                        <div
                          style={{
                            display: "flex",
                            marginTop: "-5px",
                            alignItems: "center",
                            border: "1px solid #d9d9d9",
                            borderRadius: "6px",
                            overflow: "hidden",
                            height: "32px",
                          }}
                        >
                          <ProFormSelect
                            name="stakeholder"
                            options={contacts}
                            width="sm"
                            fieldProps={{ bordered: false }}
                            style={{ flex: 1, marginTop: "25px" }}
                          />
                          <Button
                            type="primary"
                            icon={<PlusOutlined />}
                            onClick={() => openModal("contact")}
                            style={{
                              borderRadius: "0px",
                              height: "37px",
                              padding: "0 12px",
                              borderLeft: "1px solid #d9d9d9",
                              display: "flex",
                              alignItems: "center",
                            }}
                          />
                        </div>
                      </ProForm.Group>
                    </Col>
                  </Row>
                </div>
              )}
              {activeTab === "notes" && (
                <div>
                  <Divider>
                    <FormOutlined /> Notes
                  </Divider>
                  <ProFormTextArea
                    name="notes"
                    label="Notes supplémentaires"
                    placeholder="Ajoutez des détails"
                  />
                </div>
              )}
              {activeTab === "documents" && (
                <div>
                  <Divider>
                    <PaperClipOutlined /> Documents
                  </Divider>
                  <ProFormUploadButton
                    name="attachments"
                    label="Pièces jointes"
                    title="Téléverser un fichier"
                  />
                  <ProFormDatePicker
                    name="reminder"
                    label="Rappel"
                    placeholder="Sélectionnez une date de rappel"
                    width="sm"
                  />
                </div>
              )}
            </ProForm>
          </Col>

          <Col span={6}>
            <Card title="Suivi (Follow up)" size="small">
              <ProFormRadio.Group
                name="category"
                options={[{ label: "Type de lead", value: "taskType" }, { label: "Plan", value: "plan" }]}
                fieldProps={{ value: category, onChange: (e) => setCategory(e.target.value) }}
              />
              <ProForm.Group style={{ display: "flex", flexDirection: "column", gap: "8px" }}>
                <div
                  style={{
                    display: "flex",
                    marginTop: "28px",
                    alignItems: "center",
                    border: "1px solid #d9d9d9",
                    borderRadius: "6px",
                    overflow: "hidden",
                    height: "32px",
                    marginBottom: "32px",
                  }}
                >
                  {category === "taskType" && (
                    <ProFormSelect
                      name="type"
                      options={taskTypes}
                      rules={[{ required: true, message: "Veuillez sélectionner un type de lead" }]}
                      width="sm"
                      fieldProps={{ bordered: false }}
                      style={{ flex: 1,marginTop:'11px' }}
                    />
                  )}
                  {category === "plan" && (
                    <ProFormSelect
                      name="plan"
                      options={plans}
                      rules={[{ required: true, message: "Veuillez sélectionner un plan" }]}
                      width="sm"
                      fieldProps={{ bordered: false }}
                      style={{ flex: 1,marginTop:'11px' }}
                    />
                  )}
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={() => openModal(category === "taskType" ? "taskType" : "plan")}
                    style={{
                      borderRadius: "0px",
                      height: "37px",
                      padding: "0 12px",
                      borderLeft: "1px solid #d9d9d9",
                      display: "flex",
                      alignItems: "center",
                    }}
                  />
                </div>
                <ProFormDatePicker
                  name="dueDate"
                  label="Date d'échéance"
                  rules={[{ required: true, message: "Veuillez sélectionner une date d'échéance" }]}
                  width="sm"
                />
                <ProFormSelect
                  name="assignedTo"
                  label="Assigné à"
                  options={assignedPer}
                  rules={[{ required: true, message: "Veuillez sélectionner un assigné" }]}
                  width="sm"
                />
              </ProForm.Group>
              <ProFormSelect
                name="priority"
                label="Priorité"
                options={priorities}
                rules={[{ required: true, message: "Veuillez sélectionner une priorité" }]}
                width="sm"
              />
            </Card>
          </Col>
        </Row>

        <div style={{ display: "flex", justifyContent: "flex-end", marginTop: "20px" }}>
          <Button
            type="primary"
            loading={loading}
            onClick={() => form.submit()}
            style={{ marginRight: "10px" }}
          >
            Enregistrer
          </Button>
          <Button onClick={onCancel}>Annuler</Button>
        </div>
      </Card>

      <Modal
        title={`Ajouter une nouvelle ${modalData.type}`}
        open={modalData.open}
        onOk={() => {
          if (modalData.type === "entreprise") {
            form.validateFields().then((values) => {
              handleAddNewItem(values);
            });
          } else {
            handleAddNewItem(modalData.value);
          }
        }}
        onCancel={closeModal}
        okText="Ajouter"
        cancelText="Annuler"
        width={modalData.type === "entreprise" ? 1000 : 500}
      >
        {modalData.type === "taskType" ? (
          <TypeCreate onSave={setModalData} />
        ) : modalData.type === "plan" ? (
          <PlanCreate onSave={setModalData} />
        ) : modalData.type === "contact" ? (
          <ContactCreate onSave={setModalData} />
        ) : modalData.type === "entreprise" ? (
          <Form form={form} layout="vertical">
            <Row gutter={24}>
              <Divider>Information générale</Divider>
              <Col span={12}>
                <Form.Item
                  name="raison_social"
                  label="Raison Sociale"
                  rules={[{ required: true, message: "Veuillez entrer la raison sociale" }]}
                >
                  <Input />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="maison_mere" label="Nom de la maison mère">
                  <Input />
                </Form.Item>
              </Col>
              <Divider>HQ</Divider>
              <Col span={12}>
                <Form.Item name="pays" label="Pays">
                  <Input />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="etat" label="État">
                  <Input />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="ville" label="Ville">
                  <Input />
                </Form.Item>
              </Col>
              <Divider>Adresse</Divider>
              <Col span={12}>
                <Form.Item name="adresse" label="Adresse">
                  <Input />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="code_postal" label="Code postal">
                  <Input />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="presence_mondiale" label="Présence mondiale : Pays">
                  <Input />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="source_identification" label="Source d'identification de l'entreprise">
                  <Select />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item name="source_details" label="Source d'identification de l'entreprise (détails)">
                  <Input />
                </Form.Item>
              </Col>
              <Divider>Profil</Divider>
              <Col span={24}>
                <Form.Item name="description" label="Description de l'entreprise">
                  <Input.TextArea allowClear />
                </Form.Item>
              </Col>
              <Divider>Industrie</Divider>
              <Col span={12}>
                <Form.Item name="industrie" label="Industrie">
                  <Select />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="secteur" label="Secteur">
                  <Select />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="sous_secteur" label="Sous-secteur">
                  <Select />
                </Form.Item>
              </Col>
              <Divider>Taille et finances</Divider>
              <Col span={12}>
                <Form.Item name="produits" label="Produits/Services offerts">
                  <Input />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="type_societe" label="Type de société">
                  <Select />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="annee_creation" label="Année de création">
                  <Input />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="historique" label="Historique des investissements à l'international">
                  <Select />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item name="brevets" label="Brevets, Certifications, Marques, Awards">
                  <Input />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="chiffre_affaire" label="Chiffre d'affaires (montant actuel)">
                  <Input />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="nb_emploi" label="Nombre d'emplois (actuel)">
                  <Input />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="nb_emploi_year" label="Nombre d'emplois (année)">
                  <Input />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="depenses" label="Dépenses de R&D (montant réel)">
                  <Input />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="depenses_year" label="Dépenses de R&D (année)">
                  <Input />
                </Form.Item>
              </Col>
              <Divider>Informations de contact</Divider>
              <Col span={12}>
                <Form.Item name="tel" label="Tél.">
                  <Input />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="fax" label="Fax">
                  <Input />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="email" label="E-mail">
                  <Input />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="site_web" label="Site web">
                  <Input />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="linkedin" label="LinkedIn">
                  <Input />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="twitter" label="Twitter">
                  <Input />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="facebook" label="Facebook">
                  <Input />
                </Form.Item>
              </Col>
              <Divider>Assignation</Divider>
              <Col span={24}>
                <Form.Item name="assign" label="Utilisateurs">
                  <Select />
                </Form.Item>
              </Col>
            </Row>
          </Form>
        ) : null}
      </Modal>
    </div>
  );
};

export default CreateTask;