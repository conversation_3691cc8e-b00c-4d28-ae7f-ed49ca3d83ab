import { lazy } from 'react';

// import AuthGuard from '../../../helpers/Guards/AuthGuard';
// import RoleGuard from '../../../helpers/Guards/RoleGuard';
// import AdminLayout from '../../../layouts/AdminLayout';
const Emplacements = lazy(() => import('../pages/emplacements'));
const StockUnities = lazy(() => import('../pages/stock-unities'));
const Depots = lazy(() => import('../pages/depots'));
const Articles = lazy(() => import('../pages/articles'));
const Familles =lazy(() => import( '../pages/famille'));
const SousFamilles =lazy(() => import( '../pages/sousFamille'));
// const isAuthenticated = true;
// const userRole = 'admin';

const routes = [
  {
    path: 'stock/emplacements',
    element: (<Emplacements />),
  }, 
  {
    path: 'stock/stock_unities',
    element: (<StockUnities />),
  },
  {
    path: 'stock/depot',
    element: (<Depots />),
  },
  {
    path: 'stock/articles',
    element: (<Articles />),
  }, 
  {
    path: 'stock/familles',
    element: (<Familles />),
  }, 
  {
    path: 'stock/sub_familles',
    element: (<SousFamilles />),
  }, 
 
];

export default routes;
