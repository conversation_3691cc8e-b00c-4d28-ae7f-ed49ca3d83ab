import { Form, Input, Button, message } from "antd";
import { useState } from "react";

const CreateGouvernorat = ({ onCancel, setDataSource }) => {
  const [form] = Form.useForm();

  const handleSubmit = async (values) => {
    try {
      const newKey = Date.now().toString();
      const newGouvernorat = { key: newKey, ...values };

      const updatedData = [...gouvernorats, newGouvernorat];
      setDataSource(updatedData);

      message.success("Gouvernorat créé avec succès!");
      onCancel();
    } catch (error) {
      console.error(error);
      message.error("Erreur lors de la création du Gouvernorat");
    }
  };

  return (
    <Form form={form} layout="vertical" onFinish={handleSubmit}>
      <Form.Item
        name="code"
        label="Code Gouvernorat"
        rules={[{ required: true, message: "Veuillez saisir le code" }]}
      >
        <Input placeholder="Saisissez le code" />
      </Form.Item>
      <Form.Item
        name="libelle"
        label="Libellé"
        rules={[{ required: true, message: "Veuillez saisir le libellé" }]}
      >
        <Input placeholder="Saisissez le libellé" />
      </Form.Item>
      <Form.Item style={{ textAlign: 'right' }}>
      <Button type="primary" htmlType="submit">
      Enregister
      </Button>
      </Form.Item>
    </Form>
  );
};

export default CreateGouvernorat;
