
export interface IEntreprise {
  id: number;
  entreprise_id: number;
  designation: string;
  caution_mnt_max: number;
  deleted_at?: string;
  created_at: string;
  updated_at: string;
}

export interface IClient {
  id: number;
  designation: string;
  entreprise_id: number;
  adresse: string;
  deleted_at?: string;
  created_at: string;
  updated_at: string;
  enterprise: IEntreprise;
}

export interface IDepartement {
    id: number;
    enterprise_id: number;
    department_id:number;
    responsable_id:number;
    designation: string;
    deleted_at: string;
    created_at:string;
    updated_at:string;
    enterprise: IEntreprise;
    department: IDepartement;
} 

export interface IProject {
  id: number;
  tier_id: number;
  enterprise_id: number;
  department_id: number;
  designation: string;
  reference: string;
  project_id?: any;
  date_ordre_service: string;
  duree_exec?: any;
  lien_partage?: any;
  signature_contrat: string;
  deleted_at?: any;
  created_at: Date;
  updated_at: Date;
  tier: IClient;
  department: IDepartement;
  projet?: any;
  enterprise?:IEntreprise;
  reception_definitive:string;
  reception_provisoire:string;
  duree_exec_prov:number;
  contract_maintenance_option:boolean;
  contract_maintenance:string;
  lettre_participation:boolean;
  lettre_bonne_execution:boolean;
}

export interface IProjectStage {
  key: React.Key;
  id: number;
  designation: string;
  date_debut: string;
  date_fin: string;
  duree: number;
  step_ordre: number;
  montant: number;
  description: string;
  cloturer: number;
  project_id: number;
  pour_montant: number;
  progress: number;
  timeline: any[];
}