import { ModalForm } from "@ant-design/pro-components";
import {
  Card,
  Col,
  DatePicker,
  Form,
  Input,
  InputNumber,
  Radio,
  Row,
  Select,
} from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { companies, intercationTypes } from "./data";
import TextArea from "antd/es/input/TextArea.js";
function CreateIntercationForm({ open, onCancel, onSuccess }) {
  const { t } = useTranslation();
  const [form] = Form.useForm();
 
  return (
    <ModalForm
      title={t("Ajouter une interaction")}
      form={form}
      open={open}
      modalProps={{
        style: { top: 20 },
      }}
      //   onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.save"),
          resetText: t("common.actions.cancel"),
        },
      }}
    >
      <Card className="mt-2">
        <Row gutter={4}>
          <Col className="gutter-row" span={24}>
            <Form.Item
              name="companyName"
              label="Nom de l'entreprise"
              //  rules={[{ required: true, message: "Ce champ est obligatoire" }]}
            >
              <Select placeholder="Sélectionnez une entreprise">
                {companies.map((type) => (
                  <Select.Option key={type.id} value={type.id}>
                    {type.nom}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col className="gutter-row" span={24}>
            <Form.Item
              name="type"
              label="Type d'interaction"
              //rules={[{ required: true, message: "Ce champ est obligatoire" }]}
            >
              <Select placeholder="Sélectionnez un type">
                {intercationTypes.map((type) => (
                  <Select.Option key={type.id} value={type.id}>
                    {type.nom}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col className="gutter-row" span={24}>
            <Form.Item
              name="date"
              label="Date de l'intercation"
              //   rules={[{ required: true, message: "Ce champ est obligatoire" }]}
            >
              <DatePicker style={{ width: "100%" }} />
            </Form.Item>
          </Col>

          <Col className="gutter-row" span={24}>
            <Form.Item
              name="description"
              label="Description"
              // rules={[{ required: true, message: "Ce champ est obligatoire" }]}
            >
              <TextArea rows={4} placeholder="Description" />
            </Form.Item>
          </Col>
        </Row>
      </Card>
    </ModalForm>
  );
}
export default CreateIntercationForm;
