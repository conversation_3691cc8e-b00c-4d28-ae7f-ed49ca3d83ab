{"stock": {"dashboard": {"title": "Stock Dashboard"}, "categories": {"title": "Categories", "list": "Categories list", "add": "Add category", "edit": "Edit category", "view": "Category details", "fields": {"code": "name", "description": "description"}, "messages": {"confirm_delete": "Are you sure you want to delete?", "success_delete": "Audit type deleted successfully", "success_create": "Audit type created successfully", "success_update": "Audit type updated successfully", "error_load": "Error loading data", "error_save": "Error saving data"}}, "articles": {"title": "Articles", "list": "Articles list", "add": "Add article", "edit": "Edit article", "view": "Article details", "fields": {"code": "Code", "designation": "Désignation", "categorie": "Category", "fournisseur": "Supplier", "tax": "Tax", "prix_achat": "Purchase price", "prix_vente": "Sell price"}, "messages": {"confirm_delete": "Are you sure you want to delete?", "success_delete": "Audit type deleted successfully", "success_create": "Audit type created successfully", "success_update": "Audit type updated successfully", "error_load": "Error loading data", "error_save": "Error saving data"}}, "depots": {"title": "Warehouses", "list": "Warehouses list", "add": "Add warehouse", "edit": "Edit warehouse", "view": "Warehouse details", "fields": {"designation": "Designation", "adresse": "Address", "ville": "City", "pays": "Country", "notes": "Notes"}, "messages": {"confirm_delete": "Are you sure you want to delete?", "success_delete": "Warehouse deleted successfully", "success_create": "Warehouse created successfully", "success_update": "Warehouse updated successfully", "error_load": "Error loading data", "error_save": "Error saving data"}}}, "finance": {"dashboard": {"title": "Finance Dashboard"}}, "common": {"search": "Search...", "filter": "Filter", "actions": {"title": "Actions", "view": "View", "edit": "Edit", "delete": "Delete", "save": "Save", "cancel": "Cancel", "back": "Back", "upload": "Upload", "download": "Download", "confirm": "Confirm"}, "status": {"active": "Active", "inactive": "Inactive", "non_active": "Not active", "draft": "Draft", "published": "Published"}, "validation": {"required": "This field is required", "invalid_date": "Invalid date", "positive_number": "Number must be positive", "min_length": "Must contain at least {min} characters", "max_length": "Must not exceed {max} characters", "invalid_format": "Invalid format"}}}