const SideNavFinance = [
    {
        "base_nav": "finance",
        "id": 5314,
        "label": "finance.dashboard.title",
        "order": 3,
        "menu_parent": null,
        "active": 1,
        "link": "/finance/dashboard",
        "permission_id": null,
        "icon": "    <svg\n      width=\"20\"\n      height=\"20\"\n      viewBox=\"0 0 20 20\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      key={0}\n    >\n      <path\n        d=\"M3 4C3 3.44772 3.44772 3 4 3H16C16.5523 3 17 3.44772 17 4V6C17 6.55228 16.5523 7 16 7H4C3.44772 7 3 6.55228 3 6V4Z\"\n        fill={color}\n      ></path>\n      <path\n        d=\"M3 10C3 9.44771 3.44772 9 4 9H10C10.5523 9 11 9.44771 11 10V16C11 16.5523 10.5523 17 10 17H4C3.44772 17 3 16.5523 3 16V10Z\"\n        fill={color}\n      ></path>\n      <path\n        d=\"M14 9C13.4477 9 13 9.44771 13 10V16C13 16.5523 13.4477 17 14 17H16C16.5523 17 17 16.5523 17 16V10C17 9.44771 16.5523 9 16 9H14Z\"\n        fill={color}\n      ></path>\n    </svg>",
        "created_at": null,
        "updated_at": null,
        "child_recursive": []
    },
];

export default SideNavFinance;
