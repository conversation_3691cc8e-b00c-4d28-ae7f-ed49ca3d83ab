import { ModalForm } from "@ant-design/pro-components";
import { Col, Divider, Form, Input, Radio, Row, Select } from "antd";

import TextArea from "antd/es/input/TextArea.js";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { DepotForm } from "../../components/DepotForm";
import FamilleForm from "../../components/FamilleForm";



function EditFamilleForm({  open, setOpen, record, tvas,}){
  const {t} = useTranslation()
  const [form] = Form.useForm();
  useEffect(() => {
    if (open) {
        form.setFieldsValue(record);
    }
  }, [open])
  
  return(
    <ModalForm
      title={t("familles.edit")}
    
      form={form}
      open={open}
    //   onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          setOpen(false);
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.edit"),
          resetText: t("common.actions.cancel"),
        },
      }}
    >
        <FamilleForm tvas={tvas}/>
    </ModalForm>
  )
}
export default EditFamilleForm;