import { v4 as uuidv4 } from "uuid";

// import "" from "../../../../assets/Recrutement/inde."

export const CandidatesData = [
  {
    id: "1",
    task: "Lorem ipsum dolor sit amet.",
    assigned_To: 'Faris',
    assignee: '<PERSON>',
    Status: 'À faire',
    priority: 'Faible',
    due_Date: "25-Mai-2020",
  },
  {
    id: "2",
    task: "Corriger le style",
    assigned_To: '<PERSON>',
    assignee: '<PERSON>',
    Status: 'À faire',
    priority: 'Faible',
    due_Date: "26-Mai-2020",
  },
  {
    id: "3",
    task: "<PERSON><PERSON>rer les spécifications des portes",
    assigned_To: '<PERSON><PERSON>',
    assignee: 'Layla',
    Status: 'Test',
    priority: 'Faible',
    due_Date: "27-Mai-2020",
  },
  {
    id: "4",
    task: "morbi",
    assigned_To: 'Khaled',
    assignee: '<PERSON>ain<PERSON>',
    Status: 'Termin<PERSON>',
    priority: 'Haute',
    due_Date: "23-Août-2020",
  },
  {
    id: "5",
    task: "proin",
    assigned_To: 'Tariq',
    assignee: '<PERSON><PERSON>',
    Status: 'En cours',
    priority: 'Moyenne',
    due_Date: "05-Jan-2021",
  },
];

export const columnsFromBackend = {
  [uuidv4()]: {
    title: 'À faire',
    items: CandidatesData,
  },
  [uuidv4()]: {
    title: 'En cours',
    items: [],
  },
  [uuidv4()]: {
    title: 'Test',
    items: [],
  }
};
