import React from 'react';
import {Row,Col, Form, Input, Select, Button, message,Upload
 } from 'antd';
import { categories, formats } from './data';
import { UploadOutlined } from '@ant-design/icons';

const { Option } = Select;
const { TextArea } = Input;

const DocumentEdit = ({ initialValues, onUpdate }) => {
  const [form] = Form.useForm();
  const [fileList, setFileList] = React.useState([]);

  React.useEffect(() => {
    if (initialValues) {
      form.setFieldsValue(initialValues);
    }
  }, [initialValues, form]);
const uploadProps = {
    onRemove: () => {
      setFileList([]);
    },
    beforeUpload: (file) => {
      setFileList([file]);
      return false;
    },
    fileList,
    maxCount: 1
  };
  const onFinish = (values) => {
    const updatedDocument = {
      ...initialValues,
      ...values,
      updatedAt: new Date().toISOString()
    };
    onUpdate(updatedDocument);
    message.success('Document updated successfully!');
  };

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={onFinish}
      initialValues={initialValues}
    >
        <Form.Item
        name="name"
        label="Nom de la Document "
        rules={[{ required: true, message: 'Please input the document name!' }]}
      >
        <Input placeholder="e.g. Client FAQ" />
      </Form.Item>

      <Form.Item
        name="summary"
        label="Discription "
        rules={[{ required: true, message: 'Please input a summary!' }]}
      >
        <TextArea rows={3} placeholder="Brief description of the document" />
      </Form.Item>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            name="category"
            label="Catégorie"
            rules={[{ required: true, message: 'Please select a category!' }]}
          >
            <Select placeholder="Select category">
              {categories.map(category => (
                <Option key={category} value={category}>{category}</Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="format"
            label="Format"
            rules={[{ required: true, message: 'Please select a format!' }]}
          >
            <Select placeholder="Select format">
              {formats.map(format => (
                <Option key={format} value={format}>{format.toUpperCase()}</Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
      </Row>

      <Form.Item
        label="Télécharger un fichier"
        rules={[{ required: true, message: 'Please upload a file!' }]}
      >
        <Upload {...uploadProps}>
          <Button icon={<UploadOutlined />}>Choisir un fichier</Button>
        </Upload>
      </Form.Item>
  <Form.Item>
           <div style={{ textAlign: 'right' }}>
             <Button type="primary" htmlType="submit">
               Enregistrer
             </Button>
           </div>
         </Form.Item>
    </Form>
  );
};

export default DocumentEdit;