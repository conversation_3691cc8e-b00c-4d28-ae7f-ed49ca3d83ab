import React, {Suspense} from 'react';
import {BrowserRouter as Router, Routes, Route, Navigate, useLocation} from 'react-router-dom';
import routes from './routes/index';
import {useSelector} from 'react-redux';
import './configs/i18n'; // Import i18n configuration
import "./assets/styles/main.css";
import "./assets/styles/responsive.css";
import "./assets/styles/custom.css";
import {ConfigProvider} from 'antd';
import enEN from "antd/lib/locale/en_US";
import enFR from "antd/lib/locale/fr_FR";
import {useTranslation} from 'react-i18next';
import Loader from "../src/components/Loader";
import {Toaster} from 'sonner';
import SignIn from "@pages/Auth/Login";
import AuthGuard from "@helpers/Guards/AuthGuard";
import RoleGuard from "@helpers/Guards/RoleGuard";
import AdminLayout from "@layouts/AdminLayout";
import dayjs from 'dayjs';
import { RootState } from "@src/features/store";

const App: React.FC = () => {
  const {i18n} = useTranslation();
  const isLoading = useSelector((state: RootState) => state.loader?.isLoading);
  const appColor = "#16a3dd";
  dayjs.locale(i18n.language === "en" ? "en" : "fr");
const isLoggedIn = useSelector((state: RootState)=>state.auth.isLoggedIn);

  return (
    <ConfigProvider
      locale={i18n.language === "en" ? enEN : enFR}
      theme={{
        token: {
          colorPrimary: appColor,
          colorLink: appColor,
          colorError: "#ec7063",
          colorWarning: "#f5b041",
          colorSuccess: "#52be80",
        },
      }}
    >
      <Toaster position="top-right" richColors/>
      {isLoading && <Loader/>}
      <Router>
        <Suspense fallback={<Loader/>}>
          <Routes>
            <Route path="/" element={!localStorage.getItem("token") ? <SignIn/> : <Navigate to="/home"/>}/>
            <Route path="/login" element={!localStorage.getItem("token") ? <SignIn/> : <Navigate to="/home"/>}/>

            {routes.map(({path, element}: {path: string, element: React.ReactNode}, index: number) => (
              <Route key={index} path={path} element={
                <AuthGuard isAuthenticated={isLoggedIn}>
                  <RoleGuard>
                    <AdminLayout>
                      {element}
                    </AdminLayout>
                  </RoleGuard>
                </AuthGuard>
              }
              />
            ))}
          </Routes>
        </Suspense>
      </Router>
    </ConfigProvider>
  );
};

export default App;
