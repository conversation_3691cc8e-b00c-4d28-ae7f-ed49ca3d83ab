import {
  FileOutlined,
  MinusCircleOutlined,
  PlusCircleOutlined,
  UploadOutlined,
} from "@ant-design/icons";
import {
  Card,
  Col,
  DatePicker,
  Form,
  Input,
  message,
  Row,
  Select,
  Table,
  Upload,
} from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";

const Decoration = ({ form, view }) => {
  const { t } = useTranslation();
  const handleAdd = () => {
    const cuurentData = form.getFieldValue("decorations") || [];
    const date = form.getFieldValue("date");
    const invites = form.getFieldValue("invites");
    const decores = form.getFieldValue("decores");
    const qualification = form.getFieldValue("qualification");
    const award = form.getFieldValue("award");

    if (date && invites && decores && qualification && award ) {
      const key = Date.now();
      form.setFieldsValue({
        decorations: [
          ...cuurentData,
          {
            key: key,
            date: date,
            invites : invites ,
            decores : decores ,
            qualification: qualification ,
            award : award 
          },
        ],
      });
      form.resetFields(["date" , "invites" , "decores" , "qualification" , "award"]);
    } else {
      message.error(t("Merci de bien vouloir remplir tous les champs."));
    }
  };

  return (
    <>
      <Form.List name="decorations">
        {(fields, { remove }) => (
          <>
            {fields.map((field, index) => (
                      <Card className="mb-1">
              <Row gutter={16} align="middle" key={field.key}>
                <Col span={24}>Date Décoration</Col>
                <Col span={23}>
                  <Form.Item name={[index , `date`]} rules={[]}>
                    <DatePicker allowClear={true} style={{ width: 250 }} />
                  </Form.Item>
                </Col>
                <Col span={1}>
                {!view && (
                    <Form.Item>
                      <MinusCircleOutlined
                        style={{
                          color: "red",
                          fontSize: "18px",
                          cursor: "pointer",
                        }}
                        onClick={() => remove(index)}
                      />
                    </Form.Item>
                  )}
                </Col>

                <Col span={24}>Personnes invitées</Col>

                <Col span={24}>
                  <Form.Item name={[index ,`invites`]} rules={[]}>
                    <Input allowClear={true} />
                  </Form.Item>
                </Col>

                <Col span={12}>Personne décorée</Col>
                <Col span={12}>Qualification</Col>

                <Col span={12}>
                  <Form.Item name={[index ,`decores`]} rules={[]}>
                    <Input allowClear={true} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name={[index ,`qualification`]} rules={[]}>
                    <Select
                      allowClear={true}
                      options={[
                        { value: 1, label: "option 1 " },
                        { value: 2, label: "option 2 " },
                      ]}
                    />
                  </Form.Item>
                </Col>
                <Col span={24}>Award</Col>
                <Col span={24}>
                  <Form.Item name={[index ,`award`]} rules={[]}>
                    <Select
                      allowClear={true}
                      options={[
                        { value: 1, label: "award option 1 " },
                        { value: 2, label: "award option 2 " },
                      ]}
                    />
                  </Form.Item>
                </Col>
               
              </Row>
              </Card>
            ))}
          </>
        )}
      </Form.List>

      {!view && (
        <Card>
          <Row gutter={16}>
            <Col span={24}>Date Décoration</Col>

            <Col span={23}>
              <Form.Item name={`date`} rules={[]}>
                <DatePicker allowClear={true} style={{ width: 250 }} />
              </Form.Item>
            </Col>
            <Col span={1}>
              <Form.Item>
                <PlusCircleOutlined
                  style={{
                    color: "green",
                    fontSize: "18px",
                    cursor: "pointer",
                  }}
                  onClick={() => handleAdd()}
                />
              </Form.Item>
            </Col>

            <Col span={24}>Personnes invitées</Col>

            <Col span={24}>
              <Form.Item name={`invites`} rules={[]}>
                <Input allowClear={true} />
              </Form.Item>
            </Col>

            <Col span={12}>Personne décorée</Col>
            <Col span={12}>Qualification</Col>

            <Col span={12}>
              <Form.Item name={`decores`} rules={[]}>
                <Input allowClear={true} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name={`qualification`} rules={[]}>
                <Select
                  allowClear={true}
                  options={[
                    { value: 1, label: "option 1 " },
                    { value: 2, label: "option 2 " },
                  ]}
                />
              </Form.Item>
            </Col>
            <Col span={24}>Award</Col>
            <Col span={24}>
              <Form.Item name={`award`} rules={[]}>
                <Select
                  allowClear={true}
                  options={[
                    { value: 1, label: "award option 1 " },
                    { value: 2, label: "award option 2 " },
                  ]}
                />
              </Form.Item>
            </Col>
          </Row>
        </Card>
      )}
    </>
  );
};

export default Decoration;
