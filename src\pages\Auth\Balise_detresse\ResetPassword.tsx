import React from "react";
import { EyeInvisibleOutlined, EyeTwoTone } from "@ant-design/icons";
import logo_signIn from '../../../assets/images/logo_erp.png';
import {
    Layout,
    Button,
    Card,
    Form,
    Input,
    Row,
    Col
} from "antd";
import { useTranslation } from "react-i18next";


const { Header, Content } = Layout;


export default function SignIn() {
    const { t } = useTranslation();
    const handleLogin = (values) => {
    };

    const onFinish = (values) => {
        handleLogin(values);
    };

    const onFinishFailed = (errorInfo) => {
        console.log("Failed:", errorInfo);
    };

    return (
        <div style={{ height: "93vh", position: 'relative' }}>
            <div className="layout-default ant-layout layout-sign-up h-full" style={{margin: 0}}>
                <Header>
                </Header>

                <Content className="p-0">
                    <div className="sign-up-header"></div>

                    <Card
                        className="card-signup header-solid h-full ant-card "
                        title=""
                        bordered="false"
                    >

                        <Form
                            name="basic"
                            initialValues={{ remember: true }}
                            onFinish={onFinish}
                            onFinishFailed={onFinishFailed}
                            className="row-col"
                            layout="vertical"
                        >

                            <div style={{
                                display: 'flex',
                                justifyContent: 'center',
                                alignItems: 'center',
                                marginBottom: '20px',
                            }}>
                                <img src={logo_signIn} alt="" />
                            </div>
                            <div style={{
                                display: 'flex',
                                justifyContent: 'center',
                                alignItems: 'center',
                                marginBottom: '30px'
                            }}>
                                <h5> {t('auth_reset_password')} </h5>
                            </div>
                            <p className="text-muted" style={{
                                marginBottom: '30px'
                                }} >
                                {t('auth_reset_pw_desc')}

                            </p>
                            <Form.Item
                                name="password" label={t('auth_reset_pw.label')}
                                rules={[
                                    {
                                        required: true,
                                        message:t('auth_password.required')
                                    },
                                    {
                                      min: 8,
                                      message: t("profile.form.validation.new_password_min_length"),
                                    },
                                    {
                                      pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[+-.!@\$%\^&\*()])/,
                                      message: t("profile.form.validation.new_password_pattern"),
                                    },
                                ]}
                                hasFeedback
                            >
                                <Input.Password
                                    placeholder="********"
                                    autoComplete="On"
                                    iconRender={(visible) =>
                                        visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
                                    }
                                />
                            </Form.Item>
                            <Form.Item
                                name="password-confirm" label={t('auth_confirm_pw.label')}
                                rules={[
                                    { 
                                        required: true,
                                        message: t('auth_confirm_pw.required')
                                    },
                                    ({ getFieldValue }) => ({
                                        validator(_, value) {
                                          if (!value || getFieldValue('password') === value) {
                                            return Promise.resolve();
                                          }
                                          return Promise.reject(new Error(t('auth_confirm_pw.validator')));
                                        },
                                      }),
                                ]}
                                hasFeedback
                            >
                                <Input.Password
                                    placeholder="********"
                                    autoComplete="On"
                                    iconRender={(visible) =>
                                        visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
                                    }
                                />
                            </Form.Item>
                            <Form.Item style={{marginBottom: "10px"}}>
                                <Button
                                    style={{ width: "100%" }}
                                    type="primary"
                                    htmlType="submit"
                                >
                                    {t('auth_reset')}
                                </Button>
                            </Form.Item>
                        </Form>
                    </Card>
                </Content>
                <div>
                    <div style={{ padding: '8px', display: 'flex', justifyContent: 'center' }}>
                        © {new Date().getFullYear()}, Powered by &nbsp;
                        <a href="#" className="font-weight-bold" target="_blank" style={{ textDecoration: 'none', fontWeight: 700, color: '#000' }}>
                            TAC-TIC
                        </a>
                    </div>
                </div>
            </div>

        </div>
    );
}
