import { ModalForm } from "@ant-design/pro-components";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { DepotForm } from "../../components/DepotForm";
import { Form } from "antd";

function CreateDepotForm({ open, setOpen, principale, tableRef, entreprises, responsables}){
    const {t} = useTranslation();
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const options={
      principale, 
      entreprises, 
      responsables,
      form
    }
  
    return(
    <ModalForm
      title={t("depots.add")}
      open={open}
    //   onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
        setOpen();
        }
      }}
      form={form}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.save"),
          resetText: t("common.actions.cancel"),
        },
      }}
    >
      <DepotForm {...options}/>
    </ModalForm>
    )
}
export default CreateDepotForm;