/***********BEGIN CRM CSS************/
.breadcrumb-arrows{
  margin-bottom: 25px;
}
.breadcrumb-arrows li {
  display: inline-block;
  line-height: 30px;
  position: relative;
  margin-right: 2px;
}
  .breadcrumb-arrows li:before  {
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    left: -2px;
    border-style: solid;
    border-width: 15px 0 15px 15px;
    border-color: transparent transparent transparent #fff;
    z-index: 0; }
  .breadcrumb-arrows li:first-child:before {
    border-color: transparent; }

    .breadcrumb-arrows a:after ,.activechoise a:after{
      content: " ";
      height: 0;
      width: 0;
      position: absolute;
      right: -15px;
      border-style: solid;
      border-width: 15px 0 15px 15px;
      border-color: transparent transparent transparent #ccc;
      z-index: 10; }




      .breadcrumb-arrows .last>a:after{
        content: " ";
        height: 0;
        width: 0;
        position: absolute;
        right: -15px;
        border-style: solid;
        border-width: 15px 0 15px 15px;
        border-color: #ccc #ccc #ccc #ccc;
        z-index: 10; }


.breadcrumb-arrows a {
  display: block;
  background: #ccc;
  padding: 0 20px;

}

.breadcrumb-arrows a:focus  {
background: #6CB14F!important;
color:white!important;
}


.breadcrumb-arrows li.a:focus{
  background: #6CB14F!important;
  color:white!important;

  }

.activeitem a,.activeitem li{
    background: #6CB14F!important;
    color:white!important;
    border-color: transparent transparent transparent #6CB14F!important;

    }
    .activeitemfinale a,.activeitemfinale li{
      background: #6CB14F!important;
      color:white!important;
      border-color: #6CB14F #6CB14F #6CB14F #6CB14F!important;

      }
    .activechoise a,.activechoise li{
      background: #6CB14F!important;
      color:white!important;
      border-color: transparent transparent transparent #6CB14F!important;
      color:white!important;

      }
      .activechoise a:after{
        border-color: transparent transparent transparent #6CB14F!important;
        color:white!important;


      }

.breadcrumb-arrows a:focus:after{
  border-color: transparent transparent transparent #6CB14F!important;
  color:white!important;

}

.breadcrumb-arrows .activeitemfinale>a:focus:after{
  border-color: #6CB14F #6CB14F #6CB14F #6CB14F!important;
  color:white!important;

}

.activeitem a:after{
  border-color: transparent transparent transparent #6CB14F!important;
  color:white!important;

}


.activeitemfinale a:after{
  border-color: #6CB14F #6CB14F #6CB14F #6CB14F!important;
  color:white!important;

}
.card {
  // border-left: 3px solid #4096ff;
  width: 100%;
  float: left;
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  padding: 15px;
  background: #fff;
  box-shadow: 3px 1px 12px #0000001a;
  border-radius: 4px;
  margin-bottom: 15px;
  cursor: move;
}
button.ant-btn.css-dev-only-do-not-override-qnu6hi.ant-btn-default.ant-btn-icon-only {
  margin-left: 10px;
  border: none;
  color: white;
  background-color: #6cb14f;
 padding-bottom: 0px !important;
}
.ant-flex.css-dev-only-do-not-override-qnu6hi.ant-flex-justify-center {
  margin-bottom: 30px;
  margin-top: 30px;
}

.ant-flex.css-dev-only-do-not-override-qnu6hi.ant-flex-justify-center .ant-btn-primary:hover,
.ant-flex.css-dev-only-do-not-override-qnu6hi.ant-flex-justify-center .ant-btn-primary:focus,
.ant-flex.css-dev-only-do-not-override-qnu6hi.ant-flex-justify-center .ant-btn-primary:active,
.ant-flex.css-dev-only-do-not-override-qnu6hi.ant-flex-justify-center .ant-btn-primary:focus-visible,
.ant-flex.css-dev-only-do-not-override-qnu6hi.ant-flex-justify-center .ant-btn-primary:visited,
.ant-flex.css-dev-only-do-not-override-qnu6hi.ant-flex-justify-center .ant-btn-primary::selection {
  color: #1677ff;
  border: 1px solid #1677ff;
  background-color: #4096ff2b !important;
}

.ant-flex.css-dev-only-do-not-override-qnu6hi.ant-flex-justify-center .ant-btn-primary:focus {
  outline: 2px solid #1677ff;
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.4);
}

.ant-flex.css-dev-only-do-not-override-qnu6hi.ant-flex-justify-center .ant-btn-primary:active {
  background-color: #0055cc !important;
  border: 1px solid #0055cc;
}

.ant-flex.css-dev-only-do-not-override-qnu6hi.ant-flex-justify-center .ant-btn-primary:focus-visible {
  outline: 2px solid #1677ff;
  box-shadow: 0 0 0 3px rgba(22, 119, 255, 0.5);
}

.ant-flex.css-dev-only-do-not-override-qnu6hi.ant-flex-justify-center .ant-btn-primary:visited {
  color: #1677ff;
}

.img img {
  width: 40px;
  height: 40px;
}
.status,
.days,
.time {
  font-size: 14px;
  margin-bottom: 7px;
  text-align: left;
}
.card_right {
  width: auto;
  float: left;
  display: flex;
  flex-wrap: wrap;
  flex-direction: column;
  align-items: flex-start;
}
.order0 .status {
  background: #fff1de;
  color: #494949;
  padding: 3px 10px;
  border-radius: 18px;
}
.order1 .status {
  background: #c8ebdd;
  color: #494949;
  padding: 3px 10px;
  border-radius: 18px;
}
.order2 .status {
  background: #cfd8f0;
  color: #494949;
  padding: 3px 10px;
  border-radius: 18px;
}
.order3 .status {
  background: #eac8d7;
  color: #494949;
  padding: 3px 10px;
  border-radius: 18px;
}
.App {
  font-family: sans-serif;
  text-align: center;
  background: #fbfcfd;
  height: 100vh;
  padding: 40px 0px;
}
h4 {
  font-family: sans-serif;
  font-size: 12px;
  text-transform: uppercase;
  margin-top: 0px;
  color: #416183;
}
.container {
  height: 100%;
  width: 100%;
  margin: 0 auto;
  padding: 0;
  display: flex;
  //justify-content: space-evenly;
}
.drag_column {
  width: 335px;
  float: left;
  display: flex;
  padding: 30px;
  height: fit-content;
  margin-left: 5px;
  margin-right: 5px;
  background-color: #91caff29;
}
.drag_row {
  width: 100%;

  //display: flex;
  flex-wrap: wrap;
}

.drag_row h5 {
  font-weight: 700;
  color:#214984;;
}

.btn-rec{
 border-radius: 0;
}
.ant-col.ant-col-6.css-dev-only-do-not-override-qnu6hi {
  font-weight: 500;
  font-size: 18px;
  color: #2577ef;

}

.small-box {
  width: 22%;
  background: #f7f8fa;
  height: max-content;
}

.task {
  border: 1px solid white;
  margin: 10px;
  box-shadow: 1px 3px 10px gray;
  height: 8vh;
  display: flex;
  align-items: center;
  justify-content: center;
}



.bt-op:focus,.bt-op:active {
  background-color: #6CB14F;
  transform: translateY(4px);
}






/***********END CRM CSS************/


// .ant-form-item-label
//   > label.ant-form-item-required:not(
//     .ant-form-item-required-mark-optional
//   )::before {
//   display: inline-block;
//   margin-right: 4px;
//   color: #ff4d4f;
//   font-size: 14px;
//   font-family: SimSun, sans-serif;
//   line-height: 1;
//   content: "";
// }
// .ant-form-item-label
//   > label.ant-form-item-required:not(
//     .ant-form-item-required-mark-optional
//   )::after {
//   display: inline-block;
//   margin-right: 4px;
//   color: #ff4d4f;
//   font-size: 14px;
//   font-family: SimSun, sans-serif;
//   line-height: 1;
//   content: "*";
// }

#components-layout-demo-custom-trigger .trigger {
  padding: 0 24px;
  font-size: 18px;
  line-height: 64px;
  cursor: pointer;
  transition: color 0.3s;
}

#components-layout-demo-custom-trigger .trigger:hover {
  color: #1890ff;
}

#components-layout-demo-custom-trigger .logo {
  height: 32px;
  margin: 16px;
  background: rgba(255, 255, 255, 0.3);
}

@max994: ~"(max-width: 994px)";
.site-layout {
  margin-left: 200px;
  background-color: #e5e7eb;
  @media @max994 {
    margin-left: 0px !important;
  }
}

@max994: ~"(max-width: 994px)";
.header-mobile {
  display: none;

  @media @max994 {
    display: flex;
    justify-content: space-between;
    padding: 0 10px !important;
    align-items: center;
    margin-left: 0px !important;
  }
}
.header-desktop {
  background-color: red;
  display: flex;
  // margin-left: 200px;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px !important;
  @media @max994 {
    display: none;
    background-color: green;
    margin-left: 0px;
  }
}


html,
body {
  overflow-x: hidden !important;
  min-height: 100vh;
  background-color: #e5e7eb;
  // correct bug 9-15-2024
  width: 100% !important;
}

.leaflet-container {
  width: 100%;
  min-height: 100%;
}

//card body
.ant-card-body {
  padding: 12px !important;
}
.ant-card {
  border-width: 1px;
  border-color: #e5e7eb;
}
.ant-pro-card-body {
  padding: 12px !important;
}
.ant-pro-card {
  border-width: 1px;
  border-color: #e5e7eb;
}
//table
.ant-table-thead > tr > th {
  background: #f2f3f4 !important;
}
.ant-table {
  border-radius: 8px;
  border-width: 1px;
  border-color: #e5e7eb;
}

//primary button
.ant-btn-primary {
  background: #2577ef !important;
}
.ant-btn-primary:disabled {
  background: rgba(0, 0, 0, 0.04) !important;
}

//form disable (details)
.ant-select-disabled.ant-select:not(.ant-select-customize-input)
  .ant-select-selector,
.ant-input[disabled],
.ant-picker.ant-picker-disabled,
.ant-picker-input > input[disabled],
.ant-input-number-disabled .ant-input-number-input {
  color: rgb(0 0 0 / 85%) !important;
  cursor: text !important;
}

//icon in button
svg {
  vertical-align: baseline !important;
}

//ant-pro-card
.ant-pro-card {
  border-radius: 8px !important;
}

//Switch
.ant-switch {
  background-color: rgb(0 0 0 / 25%) !important;
}
.ant-switch.ant-switch-checked {
  background: #1677ff !important;
}

//HSiderMobile
.HSiderMobile .ant-drawer-body {
  padding: 0px !important;
}

.succesMessage .ant-message-notice-content {
  background: #52c41a2e;
}

//antd table border-radius
.ant-table.ant-table-middle {
  border-radius: 8px;
}

//list icons select
.MenuDetails .ant-select-selection-item {
  display: flex !important;
  align-items: center;
}
.ant-select-item-option-content {
  display: flex !important;
  align-items: center;
}

.MenuDetails .icon-select {
  vertical-align: middle;
  margin-right: 2px;
}

.ant-pro-form-login-page {
  background-repeat: no-repeat;
}

.table-row-light {
  background-color: #ffffff;
}
.table-row-dark {
  background-color: #fbfbfb;
}

.editable-cell {
  position: relative;
}

.editable-cell-value-wrap {
  padding: 5px 12px;
  cursor: pointer;
}

.editable-row:hover .editable-cell-value-wrap {
  padding: 4px 11px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
}
.fc .fc-daygrid-body-unbalanced .fc-daygrid-day-events {
  overflow: visible !important;
}

.ql-editor {
  height: 150px !important;
}


td.ant-picker-cell.ant-picker-cell-week {
  color: red;
}


:where(.css-dev-only-do-not-override-gzal6t).ant-table-wrapper .ant-table.ant-table-middle .ant-table-tbody .ant-table-wrapper:only-child .ant-table{
  margin-inline: 0px !important;
}

.text-editor-content ul{
  list-style:inside  !important;
}


/* style icons delete view edit */
.anticon.anticon-delete {
  color: #ff4d4f !important;
}

.anticon.anticon-edit, .anticon.anticon-undo {
  color: #87d068 !important;
}

.anticon.anticon-eye, .anticon.anticon-check {
  color: #108ee9 !important;
}
.btn-add-new-order {
  background: #87d068 !important;
  color: white !important;
}
.btn-add-new-order:hover {
  color: white;
  border-color: #72aa59 !important;
}
.btnAnnuler.btn-confir {
  padding: 0;
  box-shadow: none;
  border: none;
}
.pointage_anomalie:where(.css-dev-only-do-not-override-gzal6t).ant-steps .ant-steps-item-process .ant-steps-item-icon >.ant-steps-icon .ant-steps-icon-dot {
  background: #52c41a;
}

.active-row {
  background-color: #b8b8bff6;
  pointer-events: none;
}


.ant-pro-table-list-toolbar-right {
  flex: none;
}
