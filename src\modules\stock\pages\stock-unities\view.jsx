import { useTranslation } from "react-i18next";
import StockUnitiesForm from "../../components/StockUnitiesForm";
import { Modal } from "antd";

const ShowStockUnityModal = ({ open, onCancel, stockUnity }) => {
  const { t } = useTranslation();

  return (
    <Modal
      width={800}
      title={t("stock-unities.view")}
      open={open}
      onOk={onCancel}
      onCancel={onCancel}
    >
      <StockUnitiesForm disabled={true} stockUnity={stockUnity} />
    </Modal>
  );
};

export default ShowStockUnityModal;
