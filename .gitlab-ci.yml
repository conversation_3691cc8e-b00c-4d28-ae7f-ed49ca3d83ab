stages:
  - deploy
variables :
  GIT_STRATEGY : clone
  GIT_DEPTH : "1"
deploy :
  stage : deploy
  script:
    - apt-get update -y
    - apt-get install -y sshpass
    - echo "Deploying ..."
    - sshpass -V
    - sshpass  -p $DEPLOY_PASSWORD ssh -o -p $DEPLOY_PORT StrictHostKeyChecking=no $DEPLOY_USER@$DEPLOY_HOST "cd /public_html && rm -rf main  && git clone https://dep_user:<EMAIL>/Hwassim/skeleton-transport.git main && cd main/ && npm install && npm run build"
  only :
    - dev 