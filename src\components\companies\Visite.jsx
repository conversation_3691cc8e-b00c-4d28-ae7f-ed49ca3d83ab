import {
    FileOutlined,
    MinusCircleOutlined,
    PlusCircleOutlined,
    UploadOutlined,
  } from "@ant-design/icons";
  import { Card, Col, DatePicker, Form, Input, message, Row, Select, Upload } from "antd";
  import { useState } from "react";
  import { useTranslation } from "react-i18next";
  
  const Visite = ({ form, view }) => {
    const { t } = useTranslation();
    const handleAdd = () => {
        const currentmotifs= form.getFieldValue("motifs") || [];
       
        const motif = form.getFieldValue("motif");
        if (motif) {
          const key = Date.now();
          form.setFieldsValue({
            motifs: [
              ...currentmotifs,
              {
                key: key,
               motif:motif
              },
            ],
          });
          form.resetFields([
            "motif"
          ]);
        } else {
          message.error(t("companyMsg.msg_required"));
        }
      };
    return (
      <>
      
        <Form.List name="motifs">
          {(fields, { remove }) => (
            <>
              {fields.map((field, index) => (
                <Row
                  gutter={16}
                  align="middle"
                  key={field.key}
                >
                 
                  <Col span={18}>
                    <Form.Item name={[index, "motif"]} rules={[]}>
                      <Input
                        allowClear={true}
                        disabled={view}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={1}>
                    {!view && (
                      <Form.Item>
                        <MinusCircleOutlined
                          style={{
                            color: "red",
                            fontSize: "18px",
                            cursor: "pointer",
                          }}
                          onClick={() => remove(index)}
                        />
                      </Form.Item>
                    )}
                  </Col>
                </Row>
              ))}
            </>
          )}
        </Form.List>
  
        {!view && (
          <Card>
          <Row gutter={16}>
            <Col span={24}>Date de la visite</Col>
         
            <Col span={23}>
              <Form.Item name={`date`} rules={[]}>
                <DatePicker allowClear={true} style={{ width: 250 }} />
              </Form.Item>
            </Col>
            <Col span={1}>
            <Form.Item>
            <PlusCircleOutlined
              style={{
                color: "green",
                fontSize: "18px",
                cursor: "pointer",
              }}
              onClick={() => handleAdd()}
            />
            </Form.Item>
            </Col>
         
            <Col span={12}>Personne contactée</Col>
            <Col span={12}>Qualification</Col>
            <Col span={12}>
              <Form.Item name={`date`} rules={[]}>
                <Input allowClear={true} />
              </Form.Item>
            </Col>     
            <Col span={12}>
              <Form.Item name={`date`} rules={[]}>
                <Select allowClear={true} />
              </Form.Item>
            </Col>
            <Col span={24}>Cadre FIPA</Col>
         
            <Col span={24}>
              <Form.Item name={`date`} rules={[]}>
                <Input allowClear={true}  />
              </Form.Item>
            </Col>
            <Col span={24}>Origine Action Promot</Col>
            <Col span={4}>
              <Form.Item name={`date`} rules={[]}>
                <Select allowClear={true}  />
              </Form.Item>
            </Col>
            <Col span={20}>
              <Form.Item name={`date`} rules={[]}>
                <Input allowClear={true}  />
              </Form.Item>
            </Col>
            <Col span={24}>Rapport de la visite</Col>
            <Col span={24}>
              <Form.Item name={`date`} rules={[]}>
                <Input.TextArea allowClear={true}  />
              </Form.Item>
            </Col>
          </Row>
        </Card>
        )}
      </>
    );
  };
  
  export default Visite;
  