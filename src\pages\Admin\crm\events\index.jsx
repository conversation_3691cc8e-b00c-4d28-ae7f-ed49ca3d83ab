import React, { useState } from "react";
import { Table, Button, Space, Tag, message, Card, Input, Typography, Modal, Form, DatePicker, InputNumber, Alert, Tabs, <PERSON>ltip, Popconfirm } from "antd";
import { EditOutlined, DeleteOutlined, EyeOutlined, PlusOutlined, SearchOutlined, CheckCircleOutlined, LinkOutlined } from "@ant-design/icons";
import { eventsData } from "./datas";
import Create from "./create";
import Edit from "./edit";
import View from "./view";
import Kanban from './kanbans.jsx';

const EventsList = () => {
  const [data, setData] = useState(eventsData);
  const [filteredData, setFilteredData] = useState(eventsData);
  const [isCreateVisible, setIsCreateVisible] = useState(false);
  const [isEditVisible, setIsEditVisible] = useState(false);
  const [isViewVisible, setIsViewVisible] = useState(false);
  const [isFollowUpVisible, setIsFollowUpVisible] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [followUpData, setFollowUpData] = useState([]);

  const handleCreate = (newEvent) => {
    const eventWithId = { id: data.length + 1, ...newEvent, status: "Planifié" };
    const newData = [...data, eventWithId];
    setData(newData);
    setFilteredData(newData);
    message.success("Événement ajouté avec succès");
    setIsCreateVisible(false);
  };

  const handleEdit = (updatedEvent) => {
    const updatedData = data.map(event => event.id === updatedEvent.id ? updatedEvent : event);
    setData(updatedData);
    setFilteredData(updatedData);
    message.success("Événement modifié avec succès");
    setIsEditVisible(false);
  };

  const handleDelete = (id) => {
    const updatedData = data.filter(event => event.id !== id);
    setData(updatedData);
    setFilteredData(updatedData);
    message.success("Événement supprimé");
  };

  const handleSearch = (e) => {
    const value = e.target.value.toLowerCase();
    const filtered = data.filter(event => event.title.toLowerCase().includes(value));
    setFilteredData(filtered);
  };

  const handleFollowUp = (values) => {
    setFollowUpData([...followUpData, { ...values, eventId: selectedEvent.id }]);
    message.success("Suivi ajouté avec succès");
    setIsFollowUpVisible(false);
  };

  const columns = [
    { title: "Titre", dataIndex: "title", key: "title" },
    { title: "Secteur", dataIndex: "sector", key: "sector" },
    { title: "Pays", dataIndex: "country", key: "country" },
    { title: "Date", dataIndex: "date", key: "date" },
    {
      title: "Statut",
      dataIndex: "status",
      key: "status",
      render: (status) => (
        <Tag color={status === "Terminé" ? "green" : status === "En cours" ? "blue" : "orange"}>{status}</Tag>
      )
    },
    {
      title: "Actions",
      key: "actions",
      render: (_, record) => (

<Space>
        <Tooltip title="Voir">
        <Button type="link" icon={<EyeOutlined />} onClick={() => {
      { setSelectedEvent(record); setIsViewVisible(true); }
        }} />
      </Tooltip>
      <Tooltip title="Modifier">
        <Button type="link" style={{ color: "#f5b041" }} icon={<EditOutlined />} onClick={() =>{setSelectedEvent(record); setIsEditVisible(true);}} />
      </Tooltip>
      <Tooltip title="Supprimer">
        <Popconfirm title="Confirmer la suppression ?" onConfirm={() => {
          handleDelete(record.id)
        }}>
          <Button type="link" style={{ color: "#ec7063" }} icon={<DeleteOutlined />} />
        </Popconfirm>
      </Tooltip>
      {record.status === "Terminé" && (
          <Tooltip title="Suivi">
            <Button type='link' icon={<LinkOutlined />} onClick={() => { setSelectedEvent(record); setIsFollowUpVisible(true); }}/>
              
         
            </Tooltip>

          )}
    </Space>
     
      )
    }
  ];

  return (


    <Tabs
    defaultActiveKey="1"
    items={[
      {
        label: 'Grid Affichage',
        key: '1',
        children:  <Card
        bordered
        title={<Typography.Title level={4}>Liste des Événements</Typography.Title>}
        extra={
          <Space>
            <Input placeholder="Rechercher" prefix={<SearchOutlined />} onChange={handleSearch} />
            <Button type="primary" icon={<PlusOutlined />} onClick={() => setIsCreateVisible(true)}>
              Ajouter
            </Button>
          </Space>
        }
      >
        <Table columns={columns} dataSource={filteredData} rowKey="id" />
  
        <Create visible={isCreateVisible} onCreate={handleCreate} onCancel={() => setIsCreateVisible(false)} />
  
        <Edit visible={isEditVisible} onEdit={handleEdit} onCancel={() => setIsEditVisible(false)} event={selectedEvent} />
  
        <View visible={isViewVisible} onCancel={() => setIsViewVisible(false)} event={selectedEvent} />
  
        <Modal
          title="Suivi et Évaluation des Résultats"
          open={isFollowUpVisible}
          onCancel={() => setIsFollowUpVisible(false)}
          footer={null}
          width={800}
        >
          <Alert
            message="Analyse des retours"
            description="Après chaque séminaire, visite ou délégation, la FIPA analyse les retours des participants afin d’évaluer l'efficacité de l'événement, mesurer l’intérêt suscité par les opportunités d’investissement et identifier les leads qualifiés."
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
  
          <Alert
            message="Adaptation des stratégies"
            description="En fonction des résultats, des ajustements sont réalisés pour optimiser les campagnes futures, qu'il s'agisse de la sélection des secteurs à promouvoir, de la personnalisation des messages ou de la révision des stratégies de communication."
            type="success"
            showIcon
          />
  
          <Form onFinish={handleFollowUp} layout="vertical" style={{ marginTop: 24 }}>
            <Form.Item
              name="meeting"
              label="Réunion Post-Événement"
              rules={[{ required: true, message: "Veuillez ajouter des détails sur la réunion" }]}
            >
              <Input.TextArea rows={4} placeholder="Détails de la réunion post-événement" />
            </Form.Item>
  
            <Form.Item
              name="documents"
              label="Documents Complémentaires"
              rules={[{ required: true, message: "Veuillez ajouter des documents complémentaires" }]}
            >
              <Input.TextArea rows={4} placeholder="Documents à envoyer après l'événement" />
            </Form.Item>
  
            <Form.Item
              name="negotiation"
              label="Sessions de Négociation"
              rules={[{ required: true, message: "Veuillez ajouter des détails sur la session de négociation" }]}
            >
              <Input.TextArea rows={4} placeholder="Détails des sessions de négociation ou partenariats" />
            </Form.Item>
  
            <Form.Item
              name="date"
              label="Date du Suivi"
              rules={[{ required: true, message: "Veuillez sélectionner une date" }]}
            >
              <DatePicker style={{ width: "100%" }} />
            </Form.Item>
  
            <Form.Item style={{ textAlign: 'right' }}>
              <Button type="primary" htmlType="submit" block>
              Enregister
              </Button>
            </Form.Item>
          </Form>
        </Modal>
      </Card>,
      },
      {
        label: 'Tab 2',
        key: '2',
        children: <Kanban />,
        
      },
      {
        label: 'Tab 3',
        key: '3',
        children: 'Tab 3',
      },
    ]}
  />
   
  );
};

export default EventsList;
