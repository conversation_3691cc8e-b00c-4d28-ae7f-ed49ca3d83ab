import {
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  PlusOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import { ProColumns, ProTable } from "@ant-design/pro-components";
import useDebounce from "@/hooks/useDebounce";
import {
  Button,
  Card,
  Input,
  Popconfirm,
  Space,
  Tooltip,
  Typography,
} from "antd";
import { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { depots, entreprisesData, responsablesData } from "./data";

import { IDepot } from "../interfaces";
import CreateDepotForm from "./create";
import EditDepotForm from "./edit";
import ShowDepotForm from "./view";


function Depot(){
  const tableRef = useRef();
  const {t} = useTranslation();
  const [tableParams, setTableParams] = useState({}); // Paramètres de recherche et de filtrage
  const [isLoading, setIsLoading] = useState(false); // Indique si les données sont en cours de chargement
  const [data, setData] = useState<IDepot[]>(depots); // Données affichées dans la table
  const [total, setTotal] = useState(data.length); // Nombre total de lignes affichées
  const [pageNumber, setPageNumber] = useState(1); // Numéro de page actuelle
  const [dataSource, setDataSource] = useState([]);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [viewModalVisible, setViewModalVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState(null);
  const [viewingRecord, setViewingRecord] = useState(null);
  const [isPrincipale, setIsPrincipale] = useState(false);
  const [entreprises, setEntreprises] = useState([]);
  const [responsables, setResponsables] = useState([]);
  
  // Déclenchement de la recherche avec un délai (700ms)
  const handleSearch = (e) => {
  const searchValue = e.target.value; // Récupération de la valeur de recherche
  setTableParams({...tableParams, search: searchValue}); // Mise à jour des paramètres
  };

  const debouncedOnChange = useDebounce(handleSearch, 700);


  const columns : ProColumns<IDepot>[] =[
    {
        title: t('depots.fields.designation'),
        dataIndex: "designation",
        key: "nom_depot",
        ellipsis: true,
        order: 1,
        render: (text) => {
            return (
                <Typography.Text ellipsis={{tooltip: text}} >
                    {text}
                </Typography.Text>
            );
        },
    },
    {
        title: t('depots.fields.adresse'),
        dataIndex: "adresse",
        key: "adresse",
        ellipsis: true,
        order: 1,
        render: (text) => {
            return (
                <Typography.Text ellipsis={{tooltip: text}} >
                    {text}
                </Typography.Text>
            );
        },
    },
    {
        title: t('depots.fields.entreprise'),
        dataIndex: "entreprise_id",
        key: "entreprise_id",
        ellipsis: true,
        valueType:"select",
        valueEnum:()=>Object.fromEntries(entreprises.map((value: { id: number; nom: string }) => [value.id, { text: value.nom }])),
        order: 1,
        render: (text) => {
            return (
                <Typography.Text ellipsis={{tooltip: text}} >
                    {text}
                </Typography.Text>
            );
        },
    },
    {
        title: t('depots.fields.manager'),
        dataIndex: "manager_id",
        key: "manager_id",
        ellipsis: true,
        valueType:"select",
        valueEnum:()=>Object.fromEntries(responsables.map((value: { id: number; nom: string }) => [value.id, { text: value.nom }])),
        order: 1,
        render: (text) => {
            return (
                <Typography.Text ellipsis={{tooltip: text}} >
                    {text}
                </Typography.Text>
            );
        },
    },
    {
        title: t('common.actions.title'),
        key: 'action',
        align: "center",
        sorter:false,
        search: false,
        render: (_, record) => (
          <Space size="middle">
            <Tooltip title={t('common.actions.view')}>
              <Button
                type="link"
                icon={<EyeOutlined/>}
                onClick={() => {
                  setViewingRecord(record);
                  setViewModalVisible(true);
                }}
              />
            </Tooltip>
            <Tooltip title={t('common.actions.edit')}>
              <Button
                type="link"
                style={{color: "#f5b041"}}
                icon={<EditOutlined/>}
                onClick={() => {
                  searchPrincipale(record)
                  setEditingRecord(record);
                  setEditModalVisible(true);
                }}
              />
            </Tooltip>
            <Tooltip title={t('common.actions.delete')}>
              <Popconfirm
                title={t('common.messages.confirm_delete')}
                onConfirm={() => {
                  const newDataSource = dataSource.filter(item => item.key !== record.key);
                  setDataSource(newDataSource);
                }}
              >
                <Button type="link" style={{color: "#ec7063"}} icon={<DeleteOutlined/>}/>
              </Popconfirm>
            </Tooltip>
          </Space>
        ),
    },
  ];
  useEffect(()=>{
    setEntreprises(entreprisesData.map(item=>{return {...item, value: item.id, label: item.nom }}))
    setResponsables(responsablesData.map(item=>{return {...item, value: item.id, label: item.nom }}))
  },[])

  const searchPrincipale=(record)=>{

    data.forEach((item) => {            
      if(item?.type_depot_id === 1){
          setIsPrincipale(true);
          console.log('pint',true);
          
      }
      if(!!record && item?.type_depot_id === 1 && item.id === record?.id){
          setIsPrincipale(false);
          console.log('pint',false);
      }
  })
  } 

  const createObj={
    open: createModalVisible,
    setOpen : setCreateModalVisible,
    tableRef,
    entreprises : entreprises,
    responsables : responsables,
    principale : isPrincipale
  }
  const editObj={
    open: editModalVisible,
    setOpen : setEditModalVisible,
    tableRef,
    entreprises : entreprises,
    responsables : responsables,
    principale : isPrincipale,
    record: editingRecord
  }
  const showObj={
    open: viewModalVisible,
    setOpen : setViewModalVisible,
    tableRef,
    entreprises : entreprises,
    responsables : responsables,
    principale : isPrincipale,
    record : viewingRecord
  }
  return(  
      <Card
          bordered
          title={
              <Typography.Title /*style={{textWrap: "wrap"}} */level={4}>
                {t('depots.list')}
              </Typography.Title>
          }
          extra={
              <Space>
                {/* Champ de recherche */}
                <Input
                  size="large"
                  placeholder={t('common.search')}
                  suffix={<SearchOutlined style={{color: "#bfbfbf"}}/>}
                  onChange={debouncedOnChange}
                />
                <Button
                  key="add"
                  type="primary"
                  icon={<PlusOutlined/>}
                  onClick={() =>{
                    searchPrincipale(null)
                    setCreateModalVisible(true)
                  }}
                >
                  {t('depots.add')}
                </Button>
              </Space>
            }
      >

      <ProTable
          actionRef={tableRef} // Référence pour accéder aux actions
          search={{
              searchText: t('common.filter'),
              labelWidth: "auto",
              defaultCollapsed: false,
          }}
          loading={isLoading}
          columns={columns}
          dataSource={data}
          rowKey="id"
          params={tableParams}
          scroll={{x: 'max-content'}}
          options={false}
          request={async (params) => {
              setIsLoading(true); // Démarrage du chargement
              console.log(params);
              setTimeout(() => { // Simule une requête serveur
                  setIsLoading(false);
              }, 500);
              return { // Retourne les données
                  data: data,
                  success: true,
              };
          }}
          pagination={{
              showSizeChanger: true, // Permet de modifier la taille des pages
              defaultPageSize: 3, // Taille de page par défaut
              total: total, // Nombre total de lignes
              onChange: (page) => setPageNumber(page), // Fonction de changement de page
          }}
      />
      {createModalVisible && (
          <CreateDepotForm {...createObj} />
      )}
      {editModalVisible && (
          <EditDepotForm {...editObj} />
      )}
      {viewModalVisible && (
          <ShowDepotForm {...showObj} />
      )}
      </Card>
  )
}
export default Depot;