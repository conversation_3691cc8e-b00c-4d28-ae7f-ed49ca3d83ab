import { Card, Spin } from "antd";

const ViewActivity = ({ record }) => {

    console.log(record);

    
  if (!record) {
    return <Spin size="large" />;
  }

  return (
    <>
      <h2>Détails de l'activité</h2>
      <Card title={record.nom} bordered={false}>
        <p><strong>Titre:</strong> {record.nom}</p>
        <p><strong>Résumé:</strong> {record.resume}</p>
        <p><strong>Planifié le:</strong> {record.planifier}</p>
      </Card>
    </>
  );
};

export default ViewActivity;

