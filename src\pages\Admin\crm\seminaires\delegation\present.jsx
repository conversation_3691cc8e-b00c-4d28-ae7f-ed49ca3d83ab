import {
  DeleteOutlined,
  Minus<PERSON>ircleOutlined,
  PlusCircleOutlined,
} from "@ant-design/icons";
import { ModalForm } from "@ant-design/pro-components";
import {
  <PERSON><PERSON>,
  Card,
  Col,
  Collapse,
  DatePicker,
  Divider,
  Form,
  Input,
  message,
  Row,
  Select,
  Table,
} from "antd";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

function PresentForm({ open, onCancel }) {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [activeKey, setActiveKey] = useState(["1"]);
  const handleAdd = () => {
    const presents = form.getFieldValue("presents") || [];
    const key = Date.now();
    form.setFieldsValue({
      presents: [
        ...presents,
        {
          key: key,
        },
      ],
    });
 
  };
  const handleSubmit = async (values) => {
    try {
      const newKey = Date.now();
      const newPlanActivity = {
        key: newKey,
        name: values.name,
        description: values.description,
        activities: activities.map((activity, index) => ({
          nom: values[`nom_${index}`],
          resume: values[`resume_${index}`],
          planifier: values[`planifier_${index}`],
        })),
      };
      setDataSource((prevData) => [...prevData, newPlanActivity]);
      message.success("Plan d'activité créé avec succès!");
      onCancel();
    } catch (error) {
      console.log(error);
      message.error("Erreur lors de la création");
    }
  };

  return (
    <ModalForm
      title={t("Liste des présents")}
      form={form}
      open={open}
      width="55%"
      modalProps={{
        style: { top: 20 },
      }}
      onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.save"),
          resetText: t("common.actions.cancel"),
        },
      }}
    >
    
      <Card className="mt-2">
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: "15px",
          }}
        >
          <h2></h2>
          <Form.Item>
            <PlusCircleOutlined
              style={{
                color: "green",
                fontSize: "22px",
                cursor: "pointer",
              }}
              onClick={() => handleAdd()}
            />
          </Form.Item>
        </div>
  <Form.List name="presents">
        {(fields, { remove }) => (
          <>
            {fields.map((field, index) => (
              <div
                key={field.key}
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: "10px",
                  width: "100%",
                }}
              >
                <Collapse
                  style={{ flex: 1, marginBottom: "10px" }}
                  activeKey={activeKey}
                  onChange={(keys) => setActiveKey(keys)}
                  items={[
                    {
                      key: field.key,
                      label: `Présent n° : ${index + 1}`,
                      children: (
                        <>
                          <Row gutter={16} style={{ marginBottom: "10px" }}>
                            <Col span={24}>
                              <Form.Item
                                label="Entreprise"
                                name={[index, "entreprise"]}
                                rules={[{ required: true }]}
                              >
                                <Input />
                              </Form.Item>
                            </Col>
                            <Col span={12}>
                              <Form.Item
                                label="Contact"
                                name={[index, "contact"]}
                                rules={[{ required: true }]}
                              >
                                <Input />
                              </Form.Item>
                            </Col>
                            <Col span={12}>
                              <Form.Item
                                label="Fonction"
                                name={[index, "fonction"]}
                                rules={[{ required: true }]}
                              >
                                <Input />
                              </Form.Item>
                            </Col>
                            <Col span={12}>
                              <Form.Item
                                label="Nationnalité"
                                name={[index, "nationnalite"]}
                                rules={[{ required: true }]}
                              >
                                <Select />
                              </Form.Item>
                            </Col>
                            <Col span={12}>
                              <Form.Item
                                label="Secteur"
                                name={[index, "secteur"]}
                                rules={[{ required: true }]}
                              >
                                <Select />
                              </Form.Item>
                            </Col>
                            <Col span={8}>
                              <Form.Item
                                label="Tél"
                                name={[index, "tel"]}
                                rules={[{ required: true }]}
                              >
                                <Input />
                              </Form.Item>
                            </Col>
                            <Col span={8}>
                              <Form.Item
                                label="Fax"
                                name={[index, "fax"]}
                                rules={[{ required: true }]}
                              >
                                <Input />
                              </Form.Item>
                            </Col>
                            <Col span={8}>
                              <Form.Item
                                label="E-mail"
                                name={[index, "email"]}
                                rules={[{ required: true }]}
                              >
                                <Input />
                              </Form.Item>
                            </Col>
                          </Row>
                        </>
                      ),
                    },
                  ]}
                />
                <Form.Item>
                  <MinusCircleOutlined
                    style={{
                      color: "red",
                      fontSize: "22px",
                      cursor: "pointer",
                    }}
                     onClick={() => remove(index)}
                  />
                </Form.Item>
              </div>
            ))}
          </>
        )}
      </Form.List>
      
      </Card>
    </ModalForm>
  );
}
export default PresentForm;
