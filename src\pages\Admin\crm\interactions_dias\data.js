import React from 'react';

export const interactionFields = [
  {
    name: 'type',
    label: "Type d'interaction",
    type: 'select',
    required: true,
    options: ['Em<PERSON>', 'Appel téléphonique', 'Réunion en personne', 'Événement', 'Visite']
  },
  {
    name: 'date',
    label: 'Date',
    type: 'date',
    required: true
  },
  {
    name: 'diasporaProfile',
    label: 'Profil Diaspora',
    type: 'select',
    required: true,
    options: [
      '<PERSON>',
      '<PERSON>',
      '<PERSON><PERSON><PERSON>',
      '<PERSON><PERSON>',
      '<PERSON><PERSON>',
      '<PERSON><PERSON>',
      '<PERSON>',
      '<PERSON><PERSON>',
      '<PERSON><PERSON>',
      '<PERSON><PERSON>'
    ]
  },
  {
    name: 'object',
    label: 'Objet',
    type: 'text',
    required: true
  },
  {
    name: 'notes',
    label: 'Notes',
    type: 'textarea'
  },
  {
    name: 'nextSteps',
    label: 'Prochaines étapes',
    type: 'textarea'
  }
];

export const mockInteractions = [
  {
    id: 1,
    type: 'Réunion en personne',
    date: '2023-06-15',
    diasporaProfile: '<PERSON>',
    object: "Projet d'investissement technologique",
    notes: 'Intéressé par un partenariat avec des startups tunisiennes',
    nextSteps: 'Envoyer la liste des startups compatibles',
    createdAt: '2023-06-10',
    updatedAt: '2023-06-10'
  },
  {
    id: 2,
    type: 'Email',
    date: '2023-07-05',
    diasporaProfile: 'Sonia Mbarek',
    object: 'Demande de partenariat bancaire',
    notes: 'Souhaite explorer des opportunités avec les banques tunisiennes',
    nextSteps: 'Organiser un appel avec la BCT',
    createdAt: '2023-07-01',
    updatedAt: '2023-07-01'
  },
  {
    id: 3,
    type: 'Visite',
    date: '2023-08-20',
    diasporaProfile: 'Omar Mejri',
    object: 'Découverte des zones industrielles',
    notes: 'Prévoit d’investir dans l’automatisation industrielle',
    nextSteps: 'Partager un dossier de présentation des zones industrielles',
    createdAt: '2023-08-15',
    updatedAt: '2023-08-15'
  },
  {
    id: 4,
    type: 'Appel téléphonique',
    date: '2023-09-10',
    diasporaProfile: 'Ameni Gharbi',
    object: 'Proposition de mission UX pour startup tunisienne',
    notes: 'Disponible pour un projet UX Design à distance',
    nextSteps: 'Envoyer les profils des startups pertinentes',
    createdAt: '2023-09-08',
    updatedAt: '2023-09-08'
  },
  {
    id: 5,
    type: 'Événement',
    date: '2023-10-12',
    diasporaProfile: 'Leila Baccar',
    object: 'Participation à un salon fintech à Tunis',
    notes: 'Souhaite rencontrer des investisseurs locaux',
    nextSteps: 'Organiser des rendez-vous B2B pendant l’événement',
    createdAt: '2023-10-01',
    updatedAt: '2023-10-01'
  }
];