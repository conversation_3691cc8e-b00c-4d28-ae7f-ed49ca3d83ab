import { useEffect, useRef, useState } from "react";
import {
  Card,
  Col,
  Row,
  message,
  Space,
  Button,
  Typo<PERSON>,
  Divider,
  <PERSON><PERSON><PERSON>,
  Popconfirm,
} from "antd";
import { ProTable, ProColumns } from "@ant-design/pro-components";
import {
  ClusterOutlined,
  DeleteOutlined,
  EditOutlined,
  ExportOutlined,
  EyeOutlined,
  FundViewOutlined,
  PlusOutlined,
} from "@ant-design/icons";
import { useDispatch, useSelector } from "react-redux";
import { can } from "@/features/auth/authSlice";
import { IProject } from "../interfaces";
import {
  deleteProject,
  exportProjects,
  getAllProjects,
  getProjects,
} from "../../features/projects/projectSlice";
import { getAllEnterprises } from "../../features/enterprises/enterpriseSlice";
import { getAllDepartments } from "../../features/departments/departmentSlice";
import { getAllTiers } from "../../features/tiers/tierSlice";
import Create from "./create";
import Edit from "./edit";
import Show from "./show";
import { useTranslation } from "react-i18next";
import ProjectSteps from "./ProjectStep";
import { CalendarArrowUp } from "lucide-react";
import ExportStepProgress from "./exportStepProgress";
const { Title } = Typography;

const Projects: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const formRef = useRef<any>();
  const enterprises = useSelector((state: any) => state.enterprises.data.data);
  const departments = useSelector((state: any) => state.departments.data.data);
  const projects = useSelector((state: any) => state.projects.data.data);
  const tiers = useSelector((state: any) => state.tiers.data.data);
  const tableRef = useRef<any>();
  const [messageApi, contextHolder] = message.useMessage();
  const [isLoading, setIsLoading] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [expandedRowKeys, setExpandedRowKeys] = useState([]);
  const [createProject, setCreateProject] = useState(false);
  const [projectDetails, setProjectDetails] = useState(false);
  const [editProject, setEditProject] = useState(false);
  const [project, setProject] = useState<IProject>();
  const [exportStepsProgressModalVisible, setExportStepsProgressModalVisible] =
    useState<boolean>(false);

  const columns: ProColumns<IProject>[] = [
    {
      title: t("projects.columns.enterprise"),
      key: "enterprise_id",
      dataIndex: ["enterprise", "designation"],
      valueType: "select",
      fieldProps: {
        options: enterprises?.map((item) => ({
          label: item.designation,
          value: item.id,
        })),
      },
    },
    {
      title: t("projects.columns.department"),
      key: "department_id",
      dataIndex: ["department", "designation"],
      valueType: "select",
      fieldProps: {
        options: departments?.map((item) => ({
          label: item.designation,
          value: item.id,
        })),
      },
    },
    {
      title: t("projects.columns.project"),
      key: "id",
      dataIndex: "designation",
      valueType: "select",
      fieldProps: {
        options: projects?.map((item) => ({
          label: item.designation,
          value: item.id,
        })),
      },
    },
    {
      title: t("projects.columns.client"),
      key: "tier_id",
      dataIndex: ["tier", "designation"],
      valueType: "select",
      fieldProps: {
        options: tiers?.map((item) => ({
          label: item.designation,
          value: item.id,
        })),
      },
      responsive: ["lg"],
    },
    {
      title: t("projects.columns.parent_project"),
      key: "parent_id",
      dataIndex: ["parent", "designation"],
      valueType: "select",
      fieldProps: {
        options: projects?.map((item) => ({
          label: item.designation,
          value: item.id,
        })),
      },
      responsive: ["lg"],
    },
    {
      title: t("projects.columns.provisional_reception"),
      key: "reception_provisoire",
      dataIndex: "reception_provisoire",
      valueType: "select",
      valueEnum: {
        true: { text: t("common.with") },
        false: { text: t("common.without") },
      },
      responsive: ["xl"],
    },
    {
      title: t("projects.columns.final_reception"),
      key: "reception_definitive",
      dataIndex: "reception_definitive",
      valueType: "select",
      valueEnum: {
        true: { text: t("common.with") },
        false: { text: t("common.without") },
      },
      responsive: ["xl"],
    },
    // {
    //   title: t("projects.columns.maintenance_contract"),
    //   key: "contract_maintenance",
    //   dataIndex: "contract_maintenance",
    //   valueType: "date",
    //   responsive: ["lg"],
    // },
    {
      title: t("common.columns.action"),
      valueType: "option",
      key: "option",
      render: (text, item, _, action) => [
        <Space size="small" split={<Divider type="vertical" />}>
          {can("view-projectsteps") && (
            <Tooltip title={t("common.actions.steps")}>
              <ClusterOutlined
                key={0}
                style={{ color: "#27ae60" }}
                onClick={() => {
                  if (expandedRowKeys.indexOf(item.id) === -1) {
                    setProject(item);
                    setExpandedRowKeys([item.id]);
                  } else {
                    setExpandedRowKeys([]);
                  }
                }}
              />
            </Tooltip>
          )}
          <Tooltip title={t("common.actions.view")}>
            <EyeOutlined
              onClick={() => {
                setProject(item);
                setProjectDetails(true);
              }}
              key={1}
            />
          </Tooltip>
          {can("update-projects") && (
            <Tooltip title={t("common.actions.edit")}>
              <EditOutlined
                key={2}
                style={{ color: "#f5b041" }}
                onClick={() => {
                  setProject(item);
                  setEditProject(true);
                }}
              />
            </Tooltip>
          )}
          {can("delete-projects") && (
            <Tooltip title={t("common.actions.delete")}>
              <Popconfirm
                title={t("common.messages.confirm_delete")}
                onConfirm={() => {
                  handleDeleteProject(item.id);
                }}
              >
                <DeleteOutlined
                  key={3}
                  style={{ color: "#ec7063" }}
                  onClick={() => {}}
                />
              </Popconfirm>
            </Tooltip>
          )}
        </Space>,
      ],
    },
  ];

  const handleGetProjects: any = async (params) => {
    try {
      setIsLoading(true);
      const response = await dispatch(getProjects(params)).unwrap();
      return {
        data: response.data.data.map((item: any) => ({
          ...item,
          key: item.id,
        })),
        success: true,
        total: response.data.pagination.total || response.data.data.length,
      };
    } catch (error) {
      console.error("Error fetching data:", error);
      return {
        data: [],
        success: false,
        total: 0,
      };
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteProject = async (id) => {
    setIsLoading(true);
    try {
      await dispatch(deleteProject(id)).unwrap();
      messageApi.open({
        key: "updatable",
        type: "success",
        content: t("common.messages.success"),
        duration: 2,
      });
      tableRef.current.reload();
      dispatch(getAllProjects());
    } catch (error) {
      messageApi.open({
        key: "updatable",
        type: "error",
        content: t("common.messages.error"),
        duration: 2,
      });
      console.error("Error deleting project:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Exporter vers Excel
  const handleExportExcel = async () => {
    if (exportLoading) return;
    setExportLoading(true);
    messageApi.loading({ content: t("common.messages.loading") });
    try {
      // Récupérer les paramètres de filtrage actuels de la table
      const params = formRef.current?.getFieldsValue();

      // Appeler l'action d'export avec les mêmes paramètres que la table
      const response = await dispatch(exportProjects(params)).unwrap();

      // Créer un lien de téléchargement
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute(
        "download",
        `projects_export_${new Date().toISOString().slice(0, 10)}.xlsx`
      );
      document.body.appendChild(link);
      link.click();

      // Nettoyer
      link.parentNode?.removeChild(link);
      window.URL.revokeObjectURL(url);

      messageApi.success(t("common.messages.success"));
    } catch (error) {
      console.error("Export error:", error);
      messageApi.error(t("common.messages.error"));
    } finally {
      setExportLoading(false);
    }
  };

  useEffect(() => {
    if (!enterprises?.length) {
      dispatch(getAllEnterprises()).unwrap();
    }
    if (!departments?.length) {
      dispatch(getAllDepartments()).unwrap();
    }
    if (!projects?.length) {
      dispatch(getAllProjects()).unwrap();
    }
    if (!tiers?.length) {
      dispatch(getAllTiers()).unwrap();
    }
  }, []);

  return (
    <div>
      {contextHolder}
      <Row className="mt-5" gutter={[12, 24]}>
        <Col xs={24}>
          <Card
            title={<Title level={4}>{t("projects.title")}</Title>}
            bordered={false}
            extra={
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  setCreateProject(true);
                }}
              >
                {t("projects.actions.add_project")}
              </Button>
            }
          >
            <ProTable<IProject>
              columns={columns}
              actionRef={tableRef}
              formRef={formRef}
              loading={isLoading}
              toolBarRender={() => [
                <Tooltip
                  title={t(
                    "projects.actions.export_projects_steps_progress_between_two_dates_to_excel"
                  )}
                >
                  <CalendarArrowUp
                    className="mr-2 w-4 cursor-pointer"
                    onClick={() => setExportStepsProgressModalVisible(true)}
                  />
                </Tooltip>,
                <Tooltip title={t("common.actions.export_excel")}>
                  <ExportOutlined
                    className="mr-1"
                    onClick={handleExportExcel}
                  />
                </Tooltip>,
              ]}
              columnsState={{
                persistenceKey: "pro-table-singe-demos",
                persistenceType: "localStorage",
              }}
              search={{
                labelWidth: "auto",
              }}
              pagination={{
                showSizeChanger: true,
                defaultPageSize: 10,
              }}
              expandable={{
                expandedRowRender: (record) => (
                  <div className="flex justify-center">
                    <div style={{ width: "95%" }} className="p-5">
                      <ProjectSteps project={record} messageApi={messageApi} />
                    </div>
                  </div>
                ),
                showExpandColumn: false,
                expandedRowKeys: expandedRowKeys,
              }}
              request={handleGetProjects}
            />
          </Card>
        </Col>
      </Row>
      <Create
        visible={createProject}
        setVisible={setCreateProject}
        tableRef={tableRef}
        entreprises={enterprises}
        departements={departments}
        clients={tiers}
        projects={projects}
        messageApi={messageApi}
      ></Create>

      <Edit
        visible={editProject}
        setVisible={setEditProject}
        tableRef={tableRef}
        entreprises={enterprises}
        departements={departments}
        clients={tiers}
        projects={projects}
        messageApi={messageApi}
        project={project}
      ></Edit>

      <Show
        visible={projectDetails}
        setVisible={setProjectDetails}
        entreprises={enterprises}
        departements={departments}
        clients={tiers}
        projects={projects}
        project={project}
      ></Show>

      <ExportStepProgress
        visible={exportStepsProgressModalVisible}
        setVisible={setExportStepsProgressModalVisible}
        formRef={formRef}
        messageApi={messageApi}
      ></ExportStepProgress>
    </div>
  );
};

export default Projects;
