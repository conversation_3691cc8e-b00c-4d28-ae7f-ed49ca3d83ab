import React from "react";
import { useTranslation } from "react-i18next";
import logo_signIn from '../../../assets/images/logo_erp.png';
import { useNavigate} from "react-router-dom";
import {
    Layout,
    Button,
    Card,
    Form,
    Input
} from "antd";

const { Header, Content } = Layout;


export default function SignIn() {
    const {t} = useTranslation();
    const navigate = useNavigate();
    const handleLogin = (values) => {
    };

    const onFinish = (values) => {
        handleLogin(values);
    };

    const onFinishFailed = (errorInfo) => {
        console.log("Failed:", errorInfo);
    };

    return (
        <div style={{ height: "93vh", position: 'relative' }}>
            <div className="layout-default ant-layout layout-sign-up h-full" style={{margin: 0}}>
                <Header>
                </Header>

                <Content className="p-0">
                    <div className="sign-up-header"></div>

                    <Card
                        className="card-signup header-solid h-full ant-card "
                        title=""
                        bordered="false"
                    >

                        <Form
                            name="basic"
                            initialValues={{ remember: true }}
                            onFinish={onFinish}
                            onFinishFailed={onFinishFailed}
                            className="row-col"
                            layout="vertical"
                        >

                            <div style={{
                                display: 'flex',
                                justifyContent: 'center',
                                alignItems: 'center',
                                marginBottom: '20px',
                            }}>
                                <img src={logo_signIn} alt="" />
                            </div>
                            <div style={{
                                display: 'flex',
                                justifyContent: 'center',
                                alignItems: 'center',
                                marginBottom: '30px'
                            }}>
                                <h5>{t('auth_forgot_password')} </h5>
                            </div>

                            <p className="text-muted" style={{
                                marginBottom: '30px'
                                }} >
                                {t('auth_forgot_pw_desc')}
                            </p>
                            <Form.Item
                                name="email"
                                label={t('auth_email.label')}
                                rules={[
                                    {
                                        type: 'email',
                                        message: t('auth_email.type'),
                                    },
                                    { 
                                        required: true,
                                        message: t('auth_email.required')
                                    },
                                ]}
                            >
                                <Input placeholder="<EMAIL>" />
                            </Form.Item>
                            <Form.Item style={{marginBottom: "10px"}}>
                                <Button
                                    style={{ width: "100%" }}
                                    type="primary"
                                    htmlType="submit"
                                >
                                    {t('send')}
                                </Button>
                            </Form.Item>
                            <Form.Item style={{marginBottom: "10px"}}>
                                <Button
                                    onClick={ () => navigate("/distress-beacon/login")}
                                    type="link"
                                    style={{ width: "100%" }}
                                >
                                    {t('Rollback')}
                                </Button>
                            </Form.Item>
                        </Form>
                    </Card>
                </Content>
                <div>
                    <div style={{ padding: '8px', display: 'flex', justifyContent: 'center' }}>
                        © {new Date().getFullYear()}, Powered by &nbsp;
                        <a href="#" className="font-weight-bold" target="_blank" style={{ textDecoration: 'none', fontWeight: 700, color: '#000' }}>
                            TAC-TIC
                        </a>
                    </div>
                </div>
            </div>

        </div>
    );
}
