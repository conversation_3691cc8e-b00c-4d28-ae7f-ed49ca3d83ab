import { ClockCircleOutlined, PlusCircleFilled } from "@ant-design/icons";
import { ModalForm } from "@ant-design/pro-components";
import {
  Collapse,
  Form
} from "antd";

import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import Motif from "../../components/companies/Motif";
import Participant from "../../components/companies/Participant";
import Emploi from "../../components/companies/Emploi";
import Realisation from "../../components/companies/Realisation";
import Historique from "../../components/companies/Historique";
import Autre from "../../components/companies/Autre";
import Produit from "../../components/companies/Produit";
import Visite from "../../components/companies/Visite";
import Decoration from "../../components/companies/Decoration";

function ProgressCompanyForm({ open, onCancel, record }) {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
 
  const items = [
    {
      key: '1',
      label: 'Motifs de cessation de l\'Activité',
      children: <Motif form={form} view={false}/>,
    },
    {
      key: '2',
      label: 'Structure des participations étrangères',
      children: <Participant form={form} view={false}/>,
    },
    {
      key: '3',
      label: 'Annualisation des Emplois',
      children: <Emploi form={form} view={false} />,
    },
    {
      key: '4',
      label: 'Réalisation Annualisées',
      children: <Realisation form={form} view={false}/>,
    },
    {
      key: '5',
      label: 'Historique',
      children:<Historique form={form} view={false}/>, 
    },
    {
      key: '6',
      label: 'Autres activités de l\'Entreprise',
      children: <Autre form={form} view={false}/>, 
    },

    {
      key: '7',
      label: 'Décoration TIF',
      children: <Decoration form={form} view={false}/>, 
    },
    {
      key: '8',
      label: 'Visite de l\'entreprise',
      children: <Visite form={form} view={false}/>, 
    },
    {
      key: '9',
      label: 'Produits Fabriqués',
      children: <Produit form={form} view={false}/>, 
    },
  ];

  return (
    <ModalForm
      title={t("Informations de l'entreprise")}
      form={form}
      open={open}
        width ='70%'
      modalProps={{
        style: { top: 20 },
      }}
      //   onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.save"),
          resetText: t("common.actions.cancel"),
        },
      }}
    >
   
    <Collapse items={items} defaultActiveKey={['1']} className="mt-5 mr-10" />
    </ModalForm>
  );
}
export default ProgressCompanyForm;
