.calendar-container {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.fc-event-content {
  cursor: pointer;
  border-radius: 4px;
  padding: 6px 8px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.fc-event-content:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.fc-event-title {
  font-weight: 600;
  display: block;
}

.fc-event-time {
  font-size: 11px;
  opacity: 0.9;
}

.fc-event-resources, .fc-event-participants {
  display: flex;
  gap: 4px;
  margin-top: 4px;
}

.fc .fc-toolbar {
  background-color: transparent;
  padding: 8px 0;
}

.fc .fc-button {
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.fc .fc-button-primary {
  background-color: #0052cc;
  border-color: #0052cc;
}

.fc .fc-button-primary:hover {
  background-color: #0065ff;
  border-color: #0065ff;
}

.ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  transition: box-shadow 0.2s ease;
}

.ant-card:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.12);
}

.ant-modal-content {
  border-radius: 8px;
}

.ant-btn {
  border-radius: 6px;
}

.ant-select-selector, .ant-input, .ant-picker {
  border-radius: 6px !important;
}

.ant-input-number {
  width: 100%;
  border-radius: 6px;
}

.sider {
  background-color: #fff;
  border-right: 1px solid #e8e8e8;
}

.layout-content {
  background-color: #f0f2f5;
  padding: 16px;
}

.fc .fc-daygrid-day-frame {
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.fc .fc-daygrid-day:hover {
  background-color: #f5f5f5;
}