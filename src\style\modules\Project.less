.Projects .ant-card-body{
    font-weight:600;
    height: 80px;
}
.Projects svg{
    margin-top: -5px;
    margin-right:4px;
}

.CreateProject .deleteMember:hover, .CreateProject .deleteTask:hover, .ProjectTeam .deleteMember:hover{
    cursor: pointer;
    color:#E74C3C ;
}

//Kanban
.Kanban{
    width: 103%;
}

.Kanban .column{
    background: #fff;
    padding: 8px 10px;
    border-radius: 8px;
    text-align: center;
    border-width: 1px;
    border-color: #e5e7eb ;
}

.Kanban .column .title{
    color: #616A6B;
    font-weight: bolder;
    padding-top: 5px;
    margin-bottom: 20px;
}

//Task

.Task .ant-card-body {
    padding: 2px !important; 
    height: 85px;
}

.Task .title{
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
}

//ProjectSettings
.ProjectSettings .archiveProject{
    display: flex;
    cursor: pointer;
}
.ProjectSettings .archiveProject:hover{
    color: #E74C3C;
}

//TaskComments
.TaskCommentsScrollZone {
    width: 105%;
    padding-right: 1%;
    height: 360px;
    overflow: auto;
}
#TaskCommentsScrollZone::-webkit-scrollbar {
    width: 8px;
  }
  
  /* Track */
    #TaskCommentsScrollZone::-webkit-scrollbar-track {
    background: #f1f1f1; 
    // visibility: hidden;

  }
   
  /* Handle */
  #TaskCommentsScrollZone::-webkit-scrollbar-thumb {
    background: #B3B6B7; 
    border-radius:5px;
    // visibility: hidden;

  }
  
  /* Handle on hover */
  #TaskCommentsScrollZone::-webkit-scrollbar-thumb:hover {
    background: #888; 
  }

  #TaskComments p{
    display:inline-block;
    background: #F2F3F4;
    padding: 8px;
    border-radius: 8px;
}