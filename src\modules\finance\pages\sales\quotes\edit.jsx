import { ModalForm } from "@ant-design/pro-components";
import { <PERSON><PERSON>, <PERSON>, Col, DatePicker, Form, Input, InputNumber, Popconfirm, Row, Select, Table } from "antd";

import TextArea from "antd/es/input/TextArea.js";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";


import { entreprises , departements , etats, clients , articles , unites , tvas} from "./data";
import { DeleteOutlined, PlusOutlined } from "@ant-design/icons";
import ItemTable from "@src/modules/finance/components/quotes/DevisItemTable";

function EditQuoteForm({ open, onCancel ,record}){
    const {t} = useTranslation();
    const [loading, setLoading] = useState(false);
    const [entrepriseId , setEntrepriseId] =  useState(null);
    const [departementId , setDepartementId] =  useState(null);
    const [tierId , setTierId] =  useState(null);
    const dateFormat = "YYYY-MM-DD";
    const [form] = Form.useForm();
    const [value, setValue] = useState(2);
      useEffect(() => {
        if (open) {
           setEntrepriseId(record?.entreprise_id) ;
           setDepartementId(record?.departement_id) ;
           setTierId(record?.tier_id) ;
           showTierData(record?.tier_id) ;
            form.setFieldsValue(record);
         
        }
      }, [open]);
      const showTierData = (e) => {
        const tier = clients.find((t) => t.id == e);
        form.setFieldsValue({
          email: tier?.email,
          adresse: tier?.adresse,
          phone: tier?.phone,
          matricule_fiscale: tier?.matricule_fiscale,
        });
      };
  
      const resetTierData = () => {
        form.setFieldsValue({
          email: null ,
          adresse: null,
          phone: null,
          matricule_fiscale:  null,
        });
      } 
      const footer = () => {
        return (
          <>
            <div style={{ textAlign: "right" }}>
              <div
                style={{
                  display: "inline-block",
                  borderRight: "1px solid #ddd",
                  padding: "0 16px",
                }}
              >
                <strong>Total HT : </strong>
                <InputNumber
                  style={{ width: "150px" }}
                  disabled={true}
                 // value={totalHT}
                  min={0}
                  precision={3}
                  step={0.001}
                />
                <br />
                <br />
                <br />
              </div>
              <div
                style={{
                  display: "inline-block",
                  borderRight: "1px solid #ddd",
                  padding: "0 16px",
                }}
              >
                <strong>Remise (%) : </strong>
                <InputNumber
                  min={0}
                  style={{ width: "150px" }}
                 // value={remise}
                  //onChange={(value) => handleRemiseDevis(value)}
                  disabled={true}
                />
                <br />
                <br />
                <strong>Total HT après remise : </strong>
                <InputNumber
                  style={{ width: "150px" }}
                  disabled={true}
              //    value={totalHTRemise}
                  min={0}
                  precision={3}
                  step={0.001}
                />
              </div>
      
              <div
                style={{
                  display: "inline-block",
                  padding: "0 16px",
                }}
              >
                <strong>Total TVA : </strong>
                <InputNumber
                  style={{ width: "150px" }}
                  disabled={true}
                  //value={totalTVA}
                  min={0}
                  precision={3}
                  step={0.001}
                />
                <br />
                <br />
                <strong>Total TTC : </strong>
                <InputNumber
                  style={{ width: "150px" }}
                  disabled={true}
                 // value={totalTTC}
                  min={0}
                  precision={3}
                  step={0.001}
                />
              </div>
            </div>
          </>
        );
      };
    return(
    <ModalForm
      title={t("devis.edit")}
    
      form={form}
      open={open}
        width ='90%'
    //   onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.edit"),
          resetText: t("common.actions.cancel"),
        },
      }}
    >
       <Row className="mt-0" gutter={[12, 24]}>
            <Col span={12}>
              <Card title= {t('devis.general_info')} >
                <Form.Item
                  name={"num_devis"}
                  label={t('devis_cols.num_devis')}
                  style={{
                    display: "inline-block",
                    width: "45%",
                    margin: "0 8px 8px",
                  }}
                >
                  <Input disabled={true} />
                </Form.Item>
                <Form.Item
                  name={"duree_vie"}
                  label={t('devis_cols.valid_day')}
                  style={{
                    display: "inline-block",
                    width: "45%",
                    margin: "0 8px 8px",
                  }}
                >
                  <InputNumber
                    min={1}
                    step={1}
                    precision={0}
                    style={{ width: "100%" }}
                  />
                </Form.Item>
                <Form.Item
                  name={"entreprise_id"}
                  label={
                    <span>
                      {t('devis_cols.entreprise')} <span style={{ color: "red" }}>*</span>
                    </span>
                  }
                  style={{
                    display: "inline-block",
                    width: "45%",
                    margin: "0 8px 8px",
                  }}
                  rules={[
                    {
                      required: true,
                      message:  t('devis_message.required_field')   , 
                    },
                  ]}
                >
                  <Select  
                    allowClear
                    placeholder= {t('devis_cols.entreprise')}
                    options={entreprises.map((item) => ({ value: item.id, label: item.nom }))}  
                    value={entrepriseId}   
                    onSelect={(e) => {
                     setEntrepriseId(e);
                    }}
                    onClear={() => {
                    setEntrepriseId(null);
                    setDepartementId(null);
                    setTierId(null);
                    }}         
                  />
                </Form.Item>
                <Form.Item
                  name="departement_id"
                  label={
                    <span>
                       {t('devis_cols.departement')}  <span style={{ color: "red" }}>*</span>
                    </span>
                  }
                  style={{
                    display: "inline-block",
                    width: "45%",
                    margin: "0 8px 8px",
                  }}
                  rules={[
                    {
                      required: true,
                      message:  t('devis_message.required_field')   , 
                    },
                  ]}
                >
                  <Select
                    allowClear
                    placeholder= {t('devis_cols.departement')}  
                    value={departementId}   
                    onSelect={(e) => {setDepartementId(e);}}  
                    onClear={() => { setDepartementId(null); }}  
                    options={departements.filter( (dept) => dept.entreprise_id === entrepriseId).map((item) => ({ value: item.id, label: item.nom }))}                 
                  />
                </Form.Item>
                <Form.Item
                  name={"date_devis"}
                  label={
                    <span>
                   {t('devis_cols.date_devis')} <span style={{ color: "red" }}>*</span>
                    </span>
                  }
                  style={{
                    display: "inline-block",
                    width: "45%",
                    margin: "0 8px 8px",
                  }}
                  rules={[
                    { required: true,  message:  t('devis_message.required_field') } 
                  ]}
                >
                  <DatePicker
                    format={dateFormat}
                    style={{ width: "100%" }}
                    placeholder ={t('devis_cols.date_devis')}
                  />
                </Form.Item>
                <Form.Item
                  name={"etat_id"}
                  label={
                    <span>
                     {t('devis_cols.etat')} <span style={{ color: "red" }}>*</span>
                    </span>
                  }
                  style={{
                    display: "inline-block",
                    width: "45%",
                    margin: "0 8px 8px",
                  }}
                  rules={[
                    { required: true, message: t('devis_message.required_field') },
                  ]}
                >
                  <Select
                    allowClear
                    placeholder={t('devis_cols.etat')}
                  
                    options={etats.map((item) => ({ value: item.id, label: item.nom }))}  
                  />
                </Form.Item>
              </Card>
            </Col>
            <Col span={12}>
              <Card title= {t('devis.client_info')}  >
                <Form.Item
                  name={"tier_id"}
                  label={
                    <span>
                     {t('devis_cols.client')} <span style={{ color: "red" }}>*</span>
                    </span>
                  }
                  style={{
                    display: "inline-block",
                    width: "45%",
                    margin: "0 8px 8px",
                  }}
                  rules={[
                    { required: true, message: t('devis_message.required_field') },
                  ]}
                >
                  <Select
                    allowClear
                    value={tierId}   
                    onSelect={(e) => {setTierId(e);}}  
                    onClear={() => { setTierId(null); }}  
                    placeholder={t('devis_cols.client')}
                    options={clients.filter(tier => tier.entreprise_id === entrepriseId &&  tier.departement_id === departementId).map((item) => ({ value: item.id, label: item.nom }))}  
                  />
                </Form.Item>
                <Form.Item
                  name={"matricule_fiscale"}
                  label={t('devis.matricule')}
                  style={{
                    display: "inline-block",
                    width: "45%",
                    margin: "0 8px 8px",
                  }}
                >
                  <Input autoFocus placeholder={t('devis.matricule')} />
                 </Form.Item>

                <Form.Item
                  name="email"
                  label={t('devis.email')}
                  style={{
                    display: "inline-block",
                    width: "45%",
                    margin: "0 8px 8px",
                  }}
                >
                  <Input autoFocus placeholder={t('devis.email')} />
                </Form.Item>
                <Form.Item
                  name="phone"
                  label={t('devis.tel')}
                  style={{
                    display: "inline-block",
                    width: "45%",
                    margin: "0 8px 8px",
                  }}
                >
                  <Input autoFocus  placeholder={t('devis.tel')}/>
                </Form.Item>
                <Form.Item
                  name="adresse"
                  label={t('devis.address')}
                  style={{
                    display: "inline-block",
                    width: "94%",
                    margin: "0 8px 8px",
                  }}
                >
                  <Input autoFocus placeholder={t('devis.address')} />
                </Form.Item>
              </Card>
            </Col>

            
            <Col span={24}></Col>
            {/* <Col
              span={24}
              style={{ display: "flex", justifyContent: "center" }}
            >
              <Flex gap="small" wrap>
                <Button >  
                  Retour
                </Button>

                {true ? (
                  <>
                    <Button
                    // loading={pdfdevisloading}
                   //   onClick={() => handleDownload()}
                      type="primary"
                      // htmlType="submit"
                      style={{
                        marginBottom: 16,
                      }}
                    >
                      Télécharger PDF
                    </Button>
                  </>
                ) : true ? (
                  <>
                    <Button
                    //  loading={updatedevisloading}
                      type="primary"
                      htmlType="submit"
                      style={{
                        marginBottom: 16,
                      }}
                    >
                      Mettre à jour
                    </Button>
                  </>
                ) : (
                  <>
                    {" "}
                    <Button
                     // loading={devisloading}
                      type="primary"
                      htmlType="submit"
                      style={{
                        marginBottom: 16,
                      }}
                    >
                      Valider Devis
                    </Button>
                  </>
                )}
              </Flex>
            </Col> */}
          </Row>

          <ItemTable form={form} articles ={articles} record={record}/>
    </ModalForm>
    )
}
export default EditQuoteForm;