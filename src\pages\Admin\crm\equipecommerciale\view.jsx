import { Descriptions, Modal } from "antd";

const ViewCommercial = ({ record, onCancel }) => {
  if (!record) return null;

  return (
    <Descriptions title="Détails de l'équipe commerciale" bordered>
      <Descriptions.Item label="Nom">{record.name}</Descriptions.Item>
      <Descriptions.Item label="Chef d'équipe">{record.chef}</Descriptions.Item>
      <Descriptions.Item label="Membres">{record.membres.join(", ")}</Descriptions.Item>
      <Descriptions.Item label="Alias">{record.alias}</Descriptions.Item>
      <Descriptions.Item label="Objectif">{record.objectif}</Descriptions.Item>
    </Descriptions>
  );
};

export default ViewCommercial;
