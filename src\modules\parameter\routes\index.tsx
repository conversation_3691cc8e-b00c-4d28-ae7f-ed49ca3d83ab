import React, { lazy } from 'react';

// Lazy-loaded pages
const Menu = lazy(() => import('@/modules/parameter/pages/Menu/Menu'));
const Permissions = lazy(() => import('@/modules/parameter/pages/Permission/Permissions'));
const Roles = lazy(() => import('@/modules/parameter/pages/Role/Roles'));

const routes = [
  // rout for module parameters
  {
    path: '/accueil/menu',
    element: (<Menu/>),
  },
  {
    path: '/accueil/permissions',
    element: (<Permissions/>),
  },
  {
    path: '/accueil/roles',
    element: (<Roles/>),
  },
];

export default routes;
