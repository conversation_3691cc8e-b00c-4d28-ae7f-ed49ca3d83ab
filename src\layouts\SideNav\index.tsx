// import { useState } from "react";
import { Menu, Space } from "antd";
import { NavLink, useLocation } from "react-router-dom";
import { useEffect } from "react";
import sideNavLogo from "../../assets/images/logo_erp.png";
import stockSidenav from "./stock-sidenav";
import financeSidenav from "./finance-sidenav";
import SideNavCRM, { useDynamicSideNav } from "./crm-sidenav";
import { useTranslation } from "react-i18next";

function Sidenav({ color }) {
  const { pathname } = useLocation();
  const page = pathname;
  const pageArray = page.split("/");
  const { t } = useTranslation();

  // Use dynamic CRM navigation that responds to module selection
  const dynamicCrmNav = useDynamicSideNav();

  useEffect(() => {}, []);

  var data = [
    ...stockSidenav,
    ...financeSidenav,
    ...dynamicCrmNav
  ];
  const dataFinal = data.filter(
    (item) => item.base_nav && pageArray[1] === item.base_nav
  );

  // Fonction pour déterminer les clés des sous-menus ouverts
  const getOpenKeys = () => {
    let openKeys = [];
    data.forEach((item) => {
      if (item.child_recursive && item.child_recursive.length > 0) {
        item.child_recursive.forEach((child) => {
          if (pathname === child.link) {
            openKeys.push(item.id.toString());
          }
        });
      }
    });
    return openKeys;
  };
  const generateMenuItems = (data) => {
    return data.map((item) => {
      const menuItem = {
        label: (
          <NavLink to={item.link}>
            <span
              className="icon"
              style={{ background: page === item.link ? color : "" }}
              dangerouslySetInnerHTML={{ __html: item.icon }}
            />
            <span className="label">{t(item.label)}</span>
          </NavLink>
        ),
        key: item.id,
      };

      if (item.child_recursive && item.child_recursive.length > 0) {
        // Si l'élément a des sous-menus, ajouter un SubMenu
        return {
          ...menuItem,
          type: "subMenu",
          children: generateMenuItems(item.child_recursive), // Générer les sous-items
        };
      }
      return menuItem;
    });
  };

  return (
    <>
      <Space className="ml-2 brand">
        <img src={sideNavLogo} alt="logo" />
        <span style={{ color: "#23537c", fontWeight: "900" }}>
          EL-ERP
        </span>
      </Space>
      <hr />
      <Menu
        theme="light"
        mode="inline"
        items={generateMenuItems(dataFinal)}
        defaultOpenKeys={getOpenKeys()}
        className="navMenu"
      />
    </>
  );
}

export default Sidenav;
