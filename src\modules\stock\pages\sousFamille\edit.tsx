import { ModalForm } from "@ant-design/pro-components";
import { Col, Divider, Form, Input, Radio, Row, Select } from "antd";

import TextArea from "antd/es/input/TextArea.js";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { DepotForm } from "../../components/DepotForm";
import SousFamilleForm from "../../components/SousFamilleForm";



function EditSousFamilleForm({  open, setOpen, record, familles, tvas,}){
  const {t} = useTranslation()
  const [form] = Form.useForm();
  useEffect(() => {
    if (open) {
        form.setFieldsValue(record);
    }
  }, [open])
  
  return(
    <ModalForm
      title={t("sous_familles.edit")}
    
      form={form}
      open={open}
    //   onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          setOpen(false);
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.edit"),
          resetText: t("common.actions.cancel"),
        },
      }}
    >
        <SousFamilleForm familles={familles} tvas={tvas}/>
    </ModalForm>
  )
}
export default EditSousFamilleForm;