import { ModalForm } from "@ant-design/pro-components";
import { message } from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import StockUnitiesForm from "../../components/StockUnitiesForm";

const AddStockUnityModal = ({ open, onCancel }) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (values) => {
    setLoading(true);
    try {
      const submissionData = {
        ...values,
      };
      await new Promise((resolve) => setTimeout(resolve, 2000));

      message.success("Stock unités ajouté avec succès !");
    } catch (error) {
      console.log(error);
      message.error("Erreur lors de l'ajout du stock unité !");
    } finally {
      setLoading(false);
      onCancel();
    }
  };

  return (
    <ModalForm
      title={t("stock-unities.add")}
      open={open}
      onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.save"),
          resetText: t("common.actions.cancel"),
        },
        submitButtonProps: {
          loading,
        },
      }}
      modalProps={{
        destroyOnClose: true,
      }}
    >
      <StockUnitiesForm disabled={false} />
    </ModalForm>
  );
};

export default AddStockUnityModal;
