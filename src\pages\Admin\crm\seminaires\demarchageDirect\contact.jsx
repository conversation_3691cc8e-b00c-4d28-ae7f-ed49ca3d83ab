import {
  DeleteOutlined,
  MinusCircleOutlined,
  PlusCircleOutlined,
} from "@ant-design/icons";
import { ModalForm } from "@ant-design/pro-components";
import {
  <PERSON><PERSON>,
  Card,
  Col,
  Collapse,
  DatePicker,
  Divider,
  Form,
  Input,
  message,
  Row,
  Select,
  Table,
} from "antd";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

function ContactForm({ open, onCancel }) {
  const { t } = useTranslation();
  const handleAdd = () => {
    const currentData = form.getFieldValue("Items") || [];

    const contact = form.getFieldValue("contact");
    const fonction = form.getFieldValue("fonction");
    const tel = form.getFieldValue("tel");
    const email = form.getFieldValue("email");

    if (contact && fonction &&  tel && email ) {

      const key = Date.now();
      form.setFieldsValue({
        Items: [
          ...currentData,
          {
            key: key,
            contact: contact,
            fonction: fonction,
            tel: tel,
            email: email 
          },
        ],
      });
     form.resetFields(["contact" ,"fonction" ,"tel" ,"email"]);
    } else {
      message.error(t("Merci de bien vouloir remplir tous les champs."));
    }
  };

  const [form] = Form.useForm();
  const handleSubmit = async (values) => {
    try {
      const newKey = Date.now();
      const newPlanActivity = {
        key: newKey,
        name: values.name,
        description: values.description,
        activities: activities.map((activity, index) => ({
          nom: values[`nom_${index}`],
          resume: values[`resume_${index}`],
          planifier: values[`planifier_${index}`],
        })),
      };
      setDataSource((prevData) => [...prevData, newPlanActivity]);
      message.success("Plan d'activité créé avec succès!");
      onCancel();
    } catch (error) {
      console.log(error);
      message.error("Erreur lors de la création");
    }
  };

  return (
    <ModalForm
      title={t("Liste des contacts")}
      form={form}
      open={open}
      width="55%"
      modalProps={{
        style: { top: 20 },
      }}
      onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.save"),
          resetText: t("common.actions.cancel"),
        },
      }}
    >
    
     <Card className="mt-5">
        <Row gutter={16} style={{ marginBottom: "10px" }}>
          <Col span={6}>Contact</Col>
          <Col span={6}>Fonction</Col>
          <Col span={5}>Téléphone</Col>
          <Col span={6}>Email</Col>
       
          <Col span={1}></Col>
        </Row>

        <Form.List name="Items">
          {(fields, { remove }) => (
            <>
              {fields.map((field, index) => (
                <Row gutter={16} align="middle" key={field.key}>
                  <Col span={6}>
                    <Form.Item name={[index, "contact"]} rules={[]}>
                      <Input></Input>
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item name={[index, "fonction"]} rules={[]}>
                    <Select>
                <Select.Option value="PDG">PDG</Select.Option>
                <Select.Option value="Directeur">Directeur</Select.Option>
                <Select.Option value="Responsable">Responsable</Select.Option>
              </Select>
                    </Form.Item>
                  </Col>

                  <Col span={5}>
                    <Form.Item name={[index, "tel"]} rules={[]}>
                      <Input allowClear={true} />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item name={[index, "email"]} rules={[]}>
                      <Input />
                    </Form.Item>
                  </Col>

                  <Col span={1}>
                    <Form.Item>
                      <MinusCircleOutlined
                        style={{
                          color: "red",
                          fontSize: "18px",
                          cursor: "pointer",
                        }}
                        onClick={() => remove(index)}
                      />
                    </Form.Item>
                  </Col>
                </Row>
              ))}
            </>
          )}
        </Form.List>

        <Row gutter={16} align="middle" style={{ marginBottom: "5px" }}>
          <Col span={6}>
            <Form.Item name="contact">
             <Input></Input>
            </Form.Item>
          </Col>
         
          <Col span={6}>
            <Form.Item name={`fonction`} rules={[]}>
              <Select>
                <Select.Option value="PDG">PDG</Select.Option>
                <Select.Option value="Directeur">Directeur</Select.Option>
                <Select.Option value="Responsable">Responsable</Select.Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={5}>
            <Form.Item name={`tel`} rules={[]}>
              <Input />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item name={`email`} rules={[]}>
              <Input />
            </Form.Item>
          </Col>
          <Col span={1}>
            <Form.Item>
              <PlusCircleOutlined
                style={{
                  color: "green",
                  fontSize: "18px",
                  cursor: "pointer",
                }}
                onClick={() => handleAdd()}
              />
            </Form.Item>
          </Col>
        </Row>
      </Card>
    </ModalForm>
  );
}
export default ContactForm;
