import { ModalForm } from "@ant-design/pro-components";
import { Form } from "antd";

import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";


import { ArticleForm } from "../../components/ArticleForm";

function EditArticleForm({ open, onCancel ,unites, familles, dataRecord, sousFamilles, fournisseurs, tvas, articles,}){
  const {t} = useTranslation();
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  // const [value, setValue] = useState(2);
  const [resetFields, setResetFields] = useState(false);
  const [tarifValues,  setTarifValues] = useState({});
  const [activeKey, setActiveKey] = useState("1");
  const [isBien, setIsBien] = useState(false);
  const handleTabChange = (key) => {
    form.validateFields()
        .then(() => {
            setActiveKey(key);
        })
        ;
  };
  const handleNextClick =async (values) => {
    if (activeKey === "1") {
      await form.validateFields(['type_article_id', 'status','code', 'designation'])
        .then(() => {
          setActiveKey(isBien?"2":"3");
        })
    }
    if (activeKey === "2") {
      setActiveKey("3");
    }
    return
  };
  
  const editProps={
    unites, familles, dataRecord, sousFamilles, articles,
    fournisseurs, tvas, isBien, activeKey, resetFields, 
    setIsBien, handleTabChange, setTarifValues
  }
  useEffect(() => {
    if (open) {
      form.setFieldsValue(dataRecord);
      setIsBien(dataRecord?.type == 'bien');
    }
  }, [open]);

  return(
  <ModalForm 
    modalProps={{
      style:{
        top: 5,
      },
      styles:{
        body:{
          minHeight:'640px'
        }
      }
    }}
    title={t("articles.edit")}
    open={open}
    // width={850}
    form={form}
    onFinish={handleNextClick}
    onOpenChange={(open) => {
      if (!open) {
        onCancel();
      }
    }}
    submitter={{
      searchConfig: {
        submitText: activeKey==="3"? t("common.actions.edit"):t("common.actions.next"),
        resetText:  activeKey==="1"?  t("common.actions.cancel"):t("common.actions.previous"),
      },
      resetButtonProps: {
        onClick: (e) => {
          e.preventDefault(); // Prevent default Ant Design behavior
          if(activeKey==="3"){
            setActiveKey(isBien ? "2" : "1")
          } else if(activeKey==="2"){
            setActiveKey("1")
          } else {
            onCancel();
          }
        },
      }
    }}
    // disabled
  >
    <ArticleForm {...editProps}/>
  </ModalForm>
  )
}
export default EditArticleForm;