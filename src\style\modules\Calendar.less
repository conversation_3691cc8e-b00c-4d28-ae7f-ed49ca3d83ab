.fc-header-toolbar{
    margin-bottom: 30px !important;
  } 
  .fc-theme-standard .fc-scrollgrid {
    border: none !important;
  }
  .calendar th{
    text-align: right !important;
  }
  .calendar tr,.calendar th,.calendar td{
    border-right: none !important;
  }
  
  .fc .fc-scroller {
    overflow: hidden !important;
  }
  
  .fc-day-today {
    background: #EBF5FB !important;
  } 
  
  .fc-daygrid-dot-event:hover {
    background: rgba(0,0,0,.1);
    color:#1a252f !important;
  }
  
  .fc-daygrid-day-events{
    /* width: 105%;
    padding-right: 1%; */
    height: 100px;
    overflow: auto;
  }
  
  .fc-daygrid-day-events::-webkit-scrollbar {
    width: 4px;
  }
  
  /* Track */
    .fc-daygrid-day-events::-webkit-scrollbar-track {
    background: #f1f1f1; 
  
  }
   
  /* Handle */
  .fc-daygrid-day-events::-webkit-scrollbar-thumb {
    background: #B3B6B7; 
    border-radius:5px;
  }
  
  /* Handle on hover */
  .fc-daygrid-day-events::-webkit-scrollbar-thumb:hover {
    background: #888; 
  }
  
  .fc-button{
    background-color:#1677ff!important;
    border-color: #1677ff!important;
    height: 33px !important;
    padding-top: 3px !important;
  }
  .fc-button-active{
    background-color:#005de9!important;
    border-color: #005de9!important;
    box-shadow: none;
  }