import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>,
  Divider,
  Popconfirm,
  Space,
  Table,
  Tag,
  Form,
  DatePicker,
  Progress,
  Popover,
  Timeline,
} from "antd";
import type { TableColumnsType } from "antd";
import {
  DeleteOutlined,
  EditOutlined,
  HistoryOutlined,
  LockOutlined,
  PlusOutlined,
  StopOutlined,
} from "@ant-design/icons";
import { useDispatch } from "react-redux";
import dayjs from "dayjs";
import { can } from "@/features/auth/authSlice";
import { IProject, IProjectStage } from "../../interfaces";
import {
  closeProjectStep,
  deleteProjectStep,
  getProjectSteps,
} from "@src/modules/Project/features/projects/projectStepSlice";
import Create from "./create";
import Edit from "./edit";
import { useTranslation } from "react-i18next";
import { Tooltip } from "@mui/material";
import ProgressForm from "./ProgressForm";
import type { ProgressProps } from "antd";

const ProjectSteps: React.FC<{
  project: IProject;
  messageApi: any;
}> = ({ project, messageApi }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const [form] = Form.useForm();
  const [stopForm] = Form.useForm();
  const [createProjectStage, setCreateProjectStage] = useState<boolean>(false);
  const [projectStageDetails, setProjectStageDetails] =
    useState<boolean>(false);
  const [projectStage, setProjectStage] = useState<IProjectStage>(null);
  const [refresh, forceRefresh] = useState<number>(null);
  const [tableLoadding, setTableLoadding] = useState<boolean>(false);
  const [data, setData] = useState<IProjectStage[]>([]);
  const [closeStageOpen, setCloseStageOpen] = useState<number>(null);
  const [stopStageOpen, setStopStageOpen] = useState<number>(null);
  const [advanceStageOpen, setAdvanceStageOpen] = useState<boolean>(false);
  const [step, setStep] = useState<IProjectStage>(null);

  const conicColors: ProgressProps["strokeColor"] = {
    "0%": "#f5b041 ",
    "50%": "#f4d03f",
    "100%": "#52be80",
  };

  const closeStep = (item) => (
    <Popconfirm
      key={0}
      title={t("project_steps.messages.confirm_close")}
      description={() => (
        <div>
          <p className="my-2">{t("project_steps.messages.close_warning")}</p>
          <Form form={form} requiredMark={false}>
            <Form.Item
              name="date_cloture"
              label={t("project_steps.columns.closing_date")}
              rules={[
                {
                  required: true,
                  message: t("project_steps.messages.closing_date_required"),
                },
              ]}
            >
              <DatePicker
                style={{ width: "100%" }}
                format={"DD-MM-YYYY"}
                allowClear
              />
            </Form.Item>
          </Form>
        </div>
      )}
      onConfirm={(e) => {
        handleClosingStage(item);
      }}
      onCancel={() => {
        form.resetFields();
        setCloseStageOpen(null);
      }}
      okText={t("common.yes")}
      cancelText={t("common.no")}
      open={item.id === closeStageOpen}
    >
      <Tooltip title={t("project_steps.actions.close")}>
        <LockOutlined
          style={{ color: "#27ae60" }}
          onClick={() => {
            setCloseStageOpen(item.id);
          }}
        />
      </Tooltip>
    </Popconfirm>
  );

  const progressDetails = (items) => (
    <Timeline
      className="custom-ant-timeline p-5"
      style={{ width: "250px" }}
      mode={'left'}
      items={items.map((item) => {
        switch (item.type) {
          case "advancement":
            return {
              label: dayjs(item.date).format("DD-MM-YYYY"),
              children:
                t('project_steps.options.advancement') + " " +
                item.percentage +
                "%", 
              color: "blue",
            };
          case "suspension":
            return {
              label: dayjs(item.date).format("DD-MM-YYYY"),
              children: t("project_steps.options.suspension"),
              color: "red",
            };
          default:
            return {
              label: dayjs(item.date).format("DD-MM-YYYY"),
              children: t("project_steps.options.resumption"),
              color: "green",
            };
        }
      })}
    />
  );

  const columns: TableColumnsType<IProjectStage> = [
    {
      title: t("project_steps.columns.order"),
      dataIndex: "step_ordre",
      key: "step_ordre",
    },
    {
      title: t("project_steps.columns.title"),
      dataIndex: "designation",
      key: "designation",
    },
    {
      title: t("project_steps.columns.start_date"),
      dataIndex: "date_debut",
      key: "date_debut",
      responsive: ["lg"],
    },
    {
      title: t("project_steps.columns.end_date"),
      dataIndex: "date_fin",
      key: "date_fin",
      responsive: ["lg"],
    },
    {
      title: t("project_steps.columns.duration"),
      dataIndex: "duree",
      key: "duree",
      responsive: ["xl"],
    },
    {
      title: t("project_steps.columns.payment"),

      render: (item) =>
        item.montant
          ? item.montant + " " + t("project_steps.amount_with_value")
          : item.pour_montant
          ? item.pour_montant + t("project_steps.percentage_with_value")
          : "-",
      key: "paiement",
    },
    {
      title: t("project_steps.columns.progress"),
      key: "progress",
      dataIndex: "progress",
      render: (_, record) =>
        record.timeline.length > 0 ? (
          <Popover content={progressDetails(record.timeline)}>
            <Progress
              percent={record.progress}
              status={
                record.timeline[record.timeline.length - 1]?.type ===
                "suspension"
                  ? "exception"
                  : "active"
              }
              size={"small"}
              strokeColor={conicColors}
            />
          </Popover>
        ) : (
          <Progress
            percent={record.progress}
            status={"active"}
            size={"small"}
            strokeColor={conicColors}
          />
        ),
    },
    {
      title: t("project_steps.columns.status"),
      render: (item) =>
        item.cloturer ? (
          <Tag color="green">
            {t("project_steps.status.closed_on")}{" "}
            {item.date_cloture
              ? dayjs(item.date_cloture).format("DD-MM-YYYY")
              : "-"}
          </Tag>
        ) : (
          <Tag>{t("project_steps.status.unfinished")}</Tag>
        ),
      key: "etat",
    },
    {
      title: t("common.columns.action"),
      dataIndex: "",
      key: "x",
      render: (_, item) =>
        item.cloturer == 0 && (
          <Space size="small" split={<Divider type="vertical" />}>
            {/* {can("update-projectsteps") && stopStep(item)} */}
            {can("update-projectsteps") && (
              <Tooltip title={t("project_steps.actions.advance")}>
                <HistoryOutlined
                  style={{ color: "#23537c" }}
                  onClick={() => {
                    setStep(item);
                    setAdvanceStageOpen(true);
                  }}
                />
              </Tooltip>
            )}
            {can("update-projectsteps") && closeStep(item)}
            {can("update-projectsteps") && (
              <EditOutlined
                style={{ color: "#f5b041" }}
                onClick={() => {
                  setProjectStage(item);
                  setProjectStageDetails(true);
                }}
                key={1}
              />
            )}
            {can("delete-projectsteps") && (
              <Popconfirm
                key={2}
                title={t("common.messages.confirm_delete")}
                onConfirm={() => {
                  handleDeleteProjectStep(item.id);
                }}
              >
                <DeleteOutlined
                  style={{ color: "#ec7063" }}
                  onClick={() => {}}
                />
              </Popconfirm>
            )}
          </Space>
        ),
    },
  ];

  const handleClosingStage = async (step) => {
    form.validateFields().then((vals) => {
      messageApi.open({
        key: "updatable",
        type: "loading",
        content: t("common.messages.loading"),
      });
      dispatch(
        closeProjectStep({
          ...step,
          date_cloture: dayjs(vals.date_cloture, "DD-MM-YYYY").format(
            "YYYY-MM-DD"
          ),
        })
      )
        .unwrap()
        .then((originalPromiseResult) => {
          messageApi.open({
            key: "updatable",
            type: "success",
            content: t("common.messages.success"),
            duration: 2,
          });
          forceRefresh(Math.random());
          form.resetFields();
        })
        .catch((rejectedValueOrSerializedError) => {
          console.log(rejectedValueOrSerializedError);
          messageApi.open({
            key: "updatable",
            type: "error",
            content: t("common.messages.error"),
            duration: 2,
          });
        });
    });
  };

  const handleDeleteProjectStep = async (id) => {
    setTableLoadding(true);
    messageApi.open({
      key: "updatable",
      type: "loading",
      content: t("common.messages.loading"),
    });
    dispatch(deleteProjectStep(id))
      .unwrap()
      .then((originalPromiseResult) => {
        messageApi.open({
          key: "updatable",
          type: "success",
          content: t("common.messages.success"),
          duration: 2,
        });
        setTableLoadding(false);
        forceRefresh(Math.random());
      })
      .catch((rejectedValueOrSerializedError) => {
        console.log(rejectedValueOrSerializedError);
        messageApi.open({
          key: "updatable",
          type: "error",
          content: t("common.messages.error"),
          duration: 2,
        });
        setTableLoadding(false);
      });
  };

  useEffect(() => {
    setTableLoadding(true);
    dispatch(getProjectSteps(project.id))
      .unwrap()
      .then((originalPromiseResult) => {
        setData(
          originalPromiseResult.data.map((item, index) => ({
            ...item,
            key: item.id,
          }))
        );
        setTableLoadding(false);
      })
      .catch((rejectedValueOrSerializedError) => {
        messageApi.open({
          type: "error",
          content: t("common.messages.error"),
          duration: 2,
        });
        setTableLoadding(false);
      });
  }, [project, refresh]);
  return (
    <Card
      title={t("project_steps.title") + " " + project?.designation}
      bordered={false}
      extra={
        <Space>
          {can("create-projectsteps") && (
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setCreateProjectStage(true);
              }}
            >
              {t("project_steps.actions.add_step")}
            </Button>
          )}
        </Space>
      }
    >
      <Table
        className="custom-ant-table-wrapper"
        loading={tableLoadding}
        columns={columns}
        expandable={{
          expandedRowRender: (record) => (
            <div
              className="text-editor-content"
              style={{
                margin: 20,
                wordBreak: "break-word",
                whiteSpace: "pre-wrap",
              }}
              dangerouslySetInnerHTML={{ __html: record.description }}
            />
          ),
          rowExpandable: (record) => record.description !== null,
        }}
        pagination={false}
        dataSource={data}
      />
      <Create
        visible={createProjectStage}
        setVisible={setCreateProjectStage}
        forceRefresh={forceRefresh}
        project={project}
        stagesCount={data.length}
        messageApi={messageApi}
      />
      <Edit
        visible={projectStageDetails}
        setVisible={setProjectStageDetails}
        forceRefresh={forceRefresh}
        projectStage={projectStage}
        stagesCount={data.length - 1}
        messageApi={messageApi}
      />
      <ProgressForm
        step={step}
        advanceStageOpen={advanceStageOpen}
        setAdvanceStageOpen={setAdvanceStageOpen}
        forceRefresh={forceRefresh}
        messageApi={messageApi}
      />
    </Card>
  );
};

export default ProjectSteps;
