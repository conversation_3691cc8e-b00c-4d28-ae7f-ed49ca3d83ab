import { Table } from "antd";

const opportunitiesData = [
  { status: "New", count: 10, value: 50000 },
  { status: "Qualified", count: 8, value: 72000 },
  { status: "Proposal Sent", count: 5, value: 45000 },
  { status: "Negotiation", count: 4, value: 60000 },
  { status: "Won", count: 7, value: 90000 },
  { status: "Lost", count: 3, value: 20000 },
];

const columns = [
  {
    title: "Statut",
    dataIndex: "status",
    key: "status",
  },
  {
    title: "Nombre d'opportunités",
    dataIndex: "count",
    key: "count",
    sorter: (a, b) => a.count - b.count,
  },
  {
    title: "Valeur totale (DNT)",
    dataIndex: "value",
    key: "value",
    sorter: (a, b) => a.value - b.value,
    render: (value) => `${value.toLocaleString()} DNT`,
  },
];

const StatOOportunites = () => {
  return (
    <div style={{ padding: "0px" }}>
      {/* <h2>📊 Statistiques des Opportunités</h2> */}
      <Table
        dataSource={opportunitiesData}
        columns={columns}
        pagination={false}
        rowKey="status"
      />
    </div>
  );
};

export default StatOOportunites;
