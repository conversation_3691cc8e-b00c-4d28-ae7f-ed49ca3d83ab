import React from "react";

export interface IContact {
  key: React.Key;
  firstName: string;
  lastName: string;
  civilite: string;
  email: string;
  poste: string;
  pays: string;
  gouvernorat: string;
  delegation: string;
  adresse: string;
  codePostal: string;
  phone: string;
  phoneFix: string;
  fax: string;
}

export interface ITier {
  key: React.Key;
  code: string;
  type: string;
  tierType: string;
  firstName?: string;
  lastName?: string;
  civilite?: string;
  poste?: string;
  raisonSociale?: string;
  adresse_1: string;
  adresse_2: string;
  codePostal: string;
  entrepriseId: string; // id must be number
  departementId: string;
  paysId: string;
  matriculeFiscale: string;
  assujettiTVA: boolean;
  contacts?: IContact[];
}
