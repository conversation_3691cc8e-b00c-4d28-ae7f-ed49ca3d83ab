import { useState, useRef } from "react";
import { ProTable } from "@ant-design/pro-components";
import {
  Button,
  Popconfirm,
  Space,
  Input,
  Tooltip,
  Typography,
  Card,
  Modal,
  Radio,
  Form,
} from "antd";

import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  SearchOutlined,
  SettingOutlined,
  RightCircleOutlined,
  PrinterOutlined,
  ContactsOutlined,
  UserAddOutlined,
} from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { salons, pays , organisateurs } from "./data";

import EditSeminaireMP from "./edit";

// import PdfComponent from "../../components/companies/PdfComponent";
import { useReactToPrint } from "react-to-print";
import PreparationForm from "./preparation";
import ResultatForm from "./resultat";
import PresentForm from "./present";
import CreateSalon from "./create";
import EditSalon from "./edit";

function Salons() {
  const tableRef = useRef();
  const { t } = useTranslation();
  const [tableParams, setTableParams] = useState({}); // Paramètres de recherche et de filtrage
  const [isLoading, setIsLoading] = useState(false); // Indique si les données sont en cours de chargement
  const [data, setData] = useState(salons); // Données affichées dans la table
  const [total, setTotal] = useState(data.length); // Nombre total de lignes affichées
  const [pageNumber, setPageNumber] = useState(1); // Numéro de page actuelle
  const [dataSource, setDataSource] = useState([]);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [show, setShow] = useState(false);
  const [editingRecord, setEditingRecord] = useState(null);
  const [prepModalVisible, setPrepModalVisible] = useState(false);
  const [resModalVisible, setResModalVisible] = useState(false);

    const [presentModalVisible, setPresentModalVisible] = useState(false);
 
   
  const [loadingDuplicate, setLoadingDuplicate] = useState({
    status: false,
    id: 0,
  });
  const [form] = Form.useForm();
  // Déclenchement de la recherche avec un délai (700ms)
  const handleSearch = (e) => {
    const searchValue = e.target.value; // Récupération de la valeur de recherche
    setTableParams({ ...tableParams, search: searchValue }); // Mise à jour des paramètres
  };

  //  const debouncedOnChange = useDebounce(handleSearch, 700);

  const formatter = new Intl.NumberFormat("fr-TN", {
    style: "currency",
    currency: "TND",
    minimumFractionDigits: 3,
  });
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [tansferLoading, setTansferLoading] = useState(false);
  const showModal = (id) => {
    setIsModalOpen(true);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };
  var contentToPrintRef = useRef(null);
  const componentRefs = useRef({});
  // Set up the print function for each row
  const handlePrint = useReactToPrint({
    documentTitle: "Title",
    contentRef: contentToPrintRef,
  });
  // Set the ref and trigger print for a row
  const handleSetRefAndPrint = (record) => {
    // Check if the record has a valid key before setting the ref
    if (record.id) {
      contentToPrintRef = componentRefs.current[record.id];
      handlePrint();
    }
  };

  const columns = [
    {
      title: t("Intitulé"),
      dataIndex: "intitule",
      key: "intitule",
      ellipsis: true,
      valueType: "select",
      valueEnum: () =>
        Object.fromEntries(
          salons.map((value) => [value.id, { text: value.intitule }])
        ),
      order: 1,
      render: (text) => {
        return (
          <Typography.Text ellipsis={{ tooltip: text }}>{text}</Typography.Text>
        );
      },
    },
    {
      title: t("Oraganisateur"),
      dataIndex: "organisateur_id",
      key: "organisateur_id",
      ellipsis: true,
      valueType: "select",
      valueEnum: () =>
        Object.fromEntries(
          organisateurs.map((value) => [value.id, { text: value.nom }])
        ),
      order: 1,
      render: (text) => {
        return (
          <Typography.Text ellipsis={{ tooltip: text }}>{text}</Typography.Text>
        );
      },
    },
    {
      title: t("Thème"),
      dataIndex: "theme",
      key: "theme",
      ellipsis: true,
      order: 1,
      render: (text) => {
        return (
          <Typography.Text ellipsis={{ tooltip: text }}>{text}</Typography.Text>
        );
      },
    },
    {
      title: t("Date début"),
      dataIndex: "date_debut",
      key: "date_debut",
      ellipsis: true,
      valueType: "date",
      order: 1,
      render: (text) => {
        return (
          <Typography.Text ellipsis={{ tooltip: text }}>{text}</Typography.Text>
        );
      },
    },
 
    {
      title: t("Actions"),
      key: "action",
      align: "center",
      sorter: false,
      search: false,
      render: (_, record) => (
        <Space size="auto">
          <Tooltip title={t("common.actions.view")}>
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => {
                setEditingRecord(record);
                setEditModalVisible(true);
                setShow(true);
              }}
            />
          </Tooltip>
          <Tooltip title={t("common.actions.edit")}>
            <Button
              type="link"
              style={{ color: "#f5b041" }}
              icon={<EditOutlined />}
              onClick={() => {
                setEditingRecord(record);
                setEditModalVisible(true);
                setShow(false);
              }}
            />
          </Tooltip>

          <Tooltip title={t("Préparation du salon")}>
            <Button
              type="link"
              style={{ color: "gray" }}
              icon={<SettingOutlined />}
              onClick={() => {
                setPrepModalVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title={t("Résultat du salon")}>
            <Button
              type="link"
              style={{ color: "green" }}
              icon={<RightCircleOutlined />}
              onClick={() => {
                setResModalVisible(true);
              }}
            ></Button>
          </Tooltip>
            <Tooltip title={t("Liste des présents")}>
            <Button
              type="link"
              style={{ color: "#000ac5" }}
              icon={<UserAddOutlined />}
              onClick={() => {     
                setPresentModalVisible(true);
              }}
            ></Button>
          </Tooltip>
           <Tooltip title={t("Imprimer la Fiche")}>
            <Button
              type="link"
              style={{ color: "#fa021f" }}
              icon={<PrinterOutlined />}
              onClick={() => {
                handleSetRefAndPrint(record);
              }}
            />
          </Tooltip>
          <Tooltip title={t("common.actions.delete")}>
            <Popconfirm
              title={t("common.messages.confirm_delete")}
              onConfirm={() => {
                const newDataSource = dataSource.filter(
                  (item) => item.key !== record.key
                );
                setDataSource(newDataSource);
              }}
            >
              <Button
                type="link"
                style={{ color: "#ec7063" }}
                icon={<DeleteOutlined />}
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  const onFinish = (values) => {
    setTansferLoading(true);
    setTimeout(() => {
      setTansferLoading(false);
      setIsModalOpen(false);
    }, [1000]);
  };

  return (
    <>
      <Card
        title={
          <Typography.Title level={4}>
            {t("Salons")}
          </Typography.Title>
        }
        extra={
          <Space>
            {/* Champ de recherche */}
            <Input
              size="large"
              placeholder={t("Rechercher")}
              suffix={<SearchOutlined style={{ color: "#bfbfbf" }} />}
              //    onChange={debouncedOnChange}
            />
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setCreateModalVisible(true)}
            >
              {t("Ajouter")}
            </Button>
          </Space>
        }
      >
        <ProTable
          actionRef={tableRef} // Référence pour accéder aux actions
          search={{
            searchText: t("Filtrer"),
            labelWidth: "auto",
            defaultCollapsed: false,
          }}
          loading={isLoading}
          columns={columns}
          dataSource={data}
          rowKey="id"
          params={tableParams}
          scroll={{ x: "max-content" }}
          options={false}
          request={async (params) => {
            setIsLoading(true); // Démarrage du chargement
            console.log(params);
            setTimeout(() => {
              // Simule une requête serveur
              setIsLoading(false);
            }, 500);
            return {
              // Retourne les données
              data: data,
              success: true,
            };
          }}
          pagination={{
            showSizeChanger: true, // Permet de modifier la taille des pages
            defaultPageSize: 3, // Taille de page par défaut
            total: total, // Nombre total de lignes
            onChange: (page) => setPageNumber(page), // Fonction de changement de page
          }}
        />
        {createModalVisible && (
          <CreateSalon
            open={createModalVisible}
            onCancel={() => setCreateModalVisible(false)}
            onSuccess={(newDomain) => {
              setDataSource([...dataSource, newDomain]);
              setCreateModalVisible(false);
            }}
          />
        )}

        {editModalVisible && (
          <EditSalon
            open={editModalVisible}
            record={editingRecord}
            onCancel={() => {
              setEditModalVisible(false);
              setEditingRecord(null);
            }}
            show={show}
          />
        )}
        {prepModalVisible && (
          <PreparationForm
            open={prepModalVisible}
            onCancel={() => {
              setPrepModalVisible(false);
            
            }}
          />
        )}
         {resModalVisible && (
          <ResultatForm
            open={resModalVisible}
            onCancel={() => {
              setResModalVisible(false);
            
            }}
          />
        )}
   {presentModalVisible && (
          <PresentForm
            open={presentModalVisible}
            onCancel={() => {
              setPresentModalVisible(false);
            
            }}
          />
        )}
       
        {/* {data.map((record) => (
          <div key={record.id} style={{ display: "none" }}>
            <PdfComponent ref={contentToPrintRef} record={record} />
          </div>
        ))} */}
      </Card>
      <Modal
        title="Transférer le devis"
        open={isModalOpen}
        onCancel={handleCancel}
        footer={[
          <Button key="back" onClick={handleCancel}>
            Annuler
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={tansferLoading}
            onClick={() => form.submit()}
          >
            Confirmer
          </Button>,
        ]}
      >
        <Form onFinish={onFinish} layout="vertical" form={form}>
          <Form.Item
            label="Type de document"
            name="documentType"
            rules={[
              {
                required: true,
                message: "Veuillez sélectionner un type de document!",
              },
            ]}
          >
            <Radio.Group>
              <Radio value={1}>Bon de Commande</Radio>
              <Radio value={2} disabled={true}>
                Bon de Livraison
              </Radio>
              <Radio value={3} disabled={true}>
                Facture
              </Radio>
            </Radio.Group>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}
export default Salons;
