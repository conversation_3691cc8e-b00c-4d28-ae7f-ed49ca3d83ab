import { ModalForm } from "@ant-design/pro-components";
import {
  Card,
  Col,
  DatePicker,
  Divider,
  Form,
  Input,
  InputNumber,
  Radio,
  Row,
  Select,
} from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import {
  activites,
  categories,
  delegations,
  gouvernorats,
  nationalites,
  secteurs,
} from "./data";

function CreateCompanyForm({ open, onCancel, onSuccess }) {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [value, setValue] = useState(2);

  const [entrepriseId, setEntrepriseId] = useState(null);
  const [departementId, setDepartementId] = useState(null);
  const [tierId, setTierId] = useState(null);
  const dateFormat = "YYYY-MM-DD";

  const [form] = Form.useForm();
  const addDevis = (values) => {};

  const showTierData = (e) => {
    const tier = clients.find((t) => t.id == e);
    form.setFieldsValue({
      email: tier?.email,
      adresse: tier?.adresse,
      phone: tier?.phone,
      matricule_fiscale: tier?.matricule_fiscale,
    });
  };

  const resetTierData = () => {
    form.setFieldsValue({
      email: null,
      adresse: null,
      phone: null,
      matricule_fiscale: null,
    });
  };

  return (
    <ModalForm
      title={t("Ajouter une entreprise")}
      width="70%"
      form={form}
      open={open}
      modalProps={{
        style: { top: 20 },
      }}
      //   onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.save"),
          resetText: t("common.actions.cancel"),
        },
      }}
    >
      <Divider>Codes de l'entreprise</Divider>
      <Card className="mt-2">
        <Row gutter={16}>
          <Col md={6} xs={24}>
            <Form.Item
              name={"code_fipa"}
              label={t("Code FIPA")}
              style={{
                display: "inline-block",
                margin: "0 8px 8px",
              }}
            >
              <Input placeholder={t("Code FIPA")} />
            </Form.Item>
          </Col>
          <Col md={6} xs={24}>
            <Form.Item
              name={"code_douane"}
              label={t("Code douane")}
              style={{
                display: "inline-block",
                margin: "0 8px 8px",
              }}
            >
              <Input placeholder={t("Code douane")} />
            </Form.Item>
          </Col>
          <Col md={6} xs={24}>
            <Form.Item
              name={"code_ins"}
              label={t("Code INS")}
              style={{
                display: "inline-block",
                margin: "0 8px 8px",
              }}
            >
              <Input placeholder={t("Code INS")} />
            </Form.Item>
          </Col>
          <Col md={6} xs={24}>
            <Form.Item
              name={"valide"}
              label={t("Valide")}
              style={{
                display: "inline-block",
                margin: "0 8px 8px",
              }}
            >
              <Input placeholder={t("Valide")} />
            </Form.Item>
          </Col>
          <Col md={6} xs={24}>
            <Form.Item
              name={"code_api"}
              label={t("Code API")}
              style={{
                display: "inline-block",
                margin: "0 8px 8px",
              }}
            >
              <Input placeholder={t("Code API")} />
            </Form.Item>
          </Col>
          <Col md={6} xs={24}>
            <Form.Item
              name={"code_cnss"}
              label={t("Code CNSS")}
              style={{
                display: "inline-block",
                margin: "0 8px 8px",
              }}
            >
              <Input placeholder={t("Code CNSS")} />
            </Form.Item>
          </Col>
          <Col md={6} xs={24}>
            <Form.Item
              name={"matricule_fiscal"}
              label={t("Matricule fiscal")}
              style={{
                display: "inline-block",
                margin: "0 8px 8px",
              }}
            >
              <Input placeholder={t("Matricule fiscal")} />
            </Form.Item>
          </Col>
        </Row>
      </Card>
      <Divider>Données générales de l'entreprise </Divider>
      <Card className="mt-2">
        <Row gutter={16}>
          <Col md={16} xs={24}>
            <Form.Item
              name={"raison_social"}
              label={t("Raison sociale")}
              style={{
                display: "inline-block",
                width: "100%",
                margin: "0 8px 8px",
              }}
            >
              <Input placeholder={t("Raison sociale")} />
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item
              name={"date_maj"}
              label={t("Date MAJ")}
              style={{
                display: "inline-block",
                width: "100%",
                margin: "0 8px 8px",
              }}
            >
              <DatePicker placeholder={t("Date MAJ")} />
            </Form.Item>
          </Col>
          <Col md={16} xs={24}>
            <Form.Item
              name={"rns_etranger"}
              label={t("RSN à l'étranger")}
              style={{
                display: "inline-block",
                width: "100%",
                margin: "0 8px 8px",
              }}
            >
              <Input placeholder={t("RSN à l'étranger")} />
            </Form.Item>
          </Col>

          <Col md={8} xs={24}>
            <Form.Item
              name={"denomination"}
              label={t("Dénomination")}
              style={{
                display: "inline-block",
                margin: "0 8px 8px",
              }}
            >
              <Input placeholder={t("Dénomination")} />
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item
              name={"category_id"}
              label={t("Catégorie")}
              style={{
                display: "inline-block",
                width: "90%",
                margin: "0 8px 8px",
              }}
            >
              <Select
                allowClear
                placeholder={t("Catégorie")}
                options={categories.map((item) => ({
                  value: item.id,
                  label: item.category,
                }))}
                onSelect={(e) => {}}
                onClear={() => {}}
              />
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item
              name={"total_export"}
              label={" "}
              style={{
                display: "inline-block",
                width: "90%",
                margin: "0 8px 8px",
              }}
            >
              <Radio value={1}> {t("Totalement export")} </Radio>
            </Form.Item>
          </Col>

          <Col md={16} xs={24}>
            <Form.Item
              name={"secteur_id"}
              label={t("Secteur Activité")}
              style={{
                display: "inline-block",
                width: "100%",
                margin: "0 8px 8px",
              }}
            >
              <Select
                allowClear
                placeholder={t("Secteur Activité")}
                options={secteurs.map((item) => ({
                  value: item.id,
                  label: item.secteur,
                }))}
              />
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item
              name={"group_secteur_one"}
              label={t("Groupe secteur 1")}
              style={{
                display: "inline-block",
                margin: "0 8px 8px",
              }}
            >
              <Select
                allowClear
                placeholder={t("Groupe secteur 1")}
                options={secteurs.map((item) => ({
                  value: item.id,
                  label: item.secteur,
                }))}
              />
            </Form.Item>
          </Col>
          <Col md={16} xs={24}>
            <Form.Item
              name={"activite_id"}
              label={t("Activité")}
              style={{
                display: "inline-block",
                width: "100%",
                margin: "0 8px 8px",
              }}
            >
              <Select
                allowClear
                placeholder={t("Activité")}
                options={activites.map((item) => ({
                  value: item.id,
                  label: item.libelle,
                }))}
              />
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item
              name={"group_secteur_two"}
              label={t("Groupe secteur 2")}
              style={{
                display: "inline-block",
                margin: "0 8px 8px",
              }}
            >
              <Select
                allowClear
                placeholder={t("Groupe secteur 2")}
                onSelect={(e) => {}}
                onClear={() => {}}
              />
            </Form.Item>
          </Col>
          <Col md={24} xs={24}>
            <Form.Item
              name={"promoteur"}
              label={t("Promoteur")}
              style={{
                display: "inline-block",
                width: "100%",
                margin: "0 8px 8px",
              }}
            >
              <Input placeholder={t("Promoteur")} />
            </Form.Item>
          </Col>
          <Col md={16} xs={24}>
            <Form.Item
              name={"responsable"}
              label={t("Responsable")}
              style={{
                display: "inline-block",
                width: "100%",
                margin: "0 8px 8px",
              }}
            >
              <Input placeholder={t("Responsable")} />
            </Form.Item>
          </Col>
          <Col md={4} xs={24}>
            <Form.Item
              name={"qualite"}
              label={t("Qualité")}
              style={{
                display: "inline-block",
                width: "100%",
                margin: "0 8px 8px",
              }}
            >
              <Input placeholder={t("Qualité")} />
            </Form.Item>
          </Col>
          <Col md={4} xs={24}>
            <Form.Item
              name={"sexe"}
              label={t("Sexe")}
              style={{
                display: "inline-block",
                width: "100%",
                margin: "0 8px 8px",
              }}
            >
              <Select placeholder={t("Sexe")} />
            </Form.Item>
          </Col>
          <Col md={24} xs={24}>
            <Form.Item
              name={"adresse_siege"}
              label={t("Adresse siège")}
              style={{
                display: "inline-block",
                width: "100%",
                margin: "0 8px 8px",
              }}
            >
              <Input placeholder={t("Adresse siège")} />
            </Form.Item>
          </Col>
          <Col md={16} xs={24}>
            <Form.Item
              name={"adresse_usine"}
              label={t("Adresse usine")}
              style={{
                display: "inline-block",
                width: "100%",
                margin: "0 8px 8px",
              }}
            >
              <Input placeholder={t("Adresse usine")} />
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item
              name={"superficie"}
              label={t("Superficie")}
              style={{
                display: "inline-block",
                width: "100%",
                margin: "0 8px 8px",
              }}
            >
              <Input placeholder={t("Superficie")} />
            </Form.Item>
          </Col>

          <Col md={8} xs={24}>
            <Form.Item
              name={"code_postal"}
              label={t("Code postal")}
              style={{
                display: "inline-block",
                width: "100%",
                margin: "0 8px 8px",
              }}
            >
              <Input placeholder={t("Code postal")} />
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item
              name={"gouvernorat_id"}
              label={t("Gouvernorat")}
              style={{
                display: "inline-block",
                width: "100%",
                margin: "0 8px 8px",
              }}
            >
              <Select
                allowClear
                placeholder={t("Gouvernorat")}
                options={gouvernorats.map((item) => ({
                  value: item.id,
                  label: item.nom,
                }))}
              />
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item
              name={"delegation_id"}
              label={t("Délégation")}
              style={{
                display: "inline-block",
                width: "100%",
                margin: "0 8px 8px",
              }}
            >
              <Select
                allowClear
                placeholder={t("Délégation")}
                options={delegations.map((item) => ({
                  value: item.id,
                  label: item.nom,
                }))}
              />
            </Form.Item>
          </Col>

          <Col md={8} xs={24}>
            <Form.Item
              name={"tel"}
              label={t("Tél.")}
              style={{
                display: "inline-block",
                width: "100%",
                margin: "0 8px 8px",
              }}
            >
              <Input placeholder={t("Tél.")} />
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item
              name={"gsm"}
              label={t("Gsm")}
              style={{
                display: "inline-block",
                width: "100%",
                margin: "0 8px 8px",
              }}
            >
              <Input placeholder={t("Gsm")} />
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item
              name={"Localité"}
              label="localite "
              style={{
                display: "inline-block",
                width: "100%",
                margin: "0 8px 8px",
              }}
            >
              <Input placeholder={t("Localité")} />
            </Form.Item>
          </Col>

          <Col md={12} xs={24}>
            <Form.Item
              name={"fax"}
              label={t("Fax")}
              style={{
                display: "inline-block",
                width: "100%",
                margin: "0 8px 8px",
              }}
            >
              <Input placeholder={t("Fax")} />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item
              name={"forme_juridique"}
              label="F.J."
              style={{
                display: "inline-block",
                width: "100%",
                margin: "0 8px 8px",
              }}
            >
              <Input placeholder="F.J." />
            </Form.Item>
          </Col>

          <Col md={12} xs={24}>
            <Form.Item
              name={"email"}
              label="E-mail"
              style={{
                display: "inline-block",
                width: "100%",
                margin: "0 8px 8px",
              }}
            >
              <Input placeholder="E-mail" />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item
              name={"site_web"}
              label={t("Site web")}
              style={{
                display: "inline-block",
                width: "100%",
                margin: "0 8px 8px",
              }}
            >
              <Input placeholder={t("Site web")} />
            </Form.Item>
          </Col>

          <Col md={12} xs={24}>
            <Form.Item
              name={"zone_industriel"}
              label="Z.I."
              style={{
                display: "inline-block",
                width: "100%",
                margin: "0 8px 8px",
              }}
            >
              <Input placeholder="Z.I." />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item
              name={"holding"}
              label={t("Holding")}
              style={{
                display: "inline-block",
                width: "100%",
                margin: "0 8px 8px",
              }}
            >
              <Input placeholder={t("Holding")} />
            </Form.Item>
          </Col>

          <Col md={4} xs={24}>
            {" "}
            <Form.Item
              name={"success_story"}
              label={t("Success Story")}
              style={{
                display: "inline-block",
                width: "100%",
                margin: "0 8px 8px",
              }}
            >
              <Input placeholder={t("Success Story")} />
            </Form.Item>
          </Col>
          <Col md={4} xs={24}>
            {" "}
            <Form.Item
              name={"rech_dvp"}
              label={t("R et D")}
              style={{
                display: "inline-block",
                width: "100%",
                margin: "0 8px 8px",
              }}
            >
              <Input placeholder={t("R et D")} />
            </Form.Item>
          </Col>
          <Col md={16} xs={24}>
            {" "}
            <Form.Item
              name={"domaine"}
              label={t("Domaine")}
              style={{
                display: "inline-block",
                width: "100%",
                margin: "0 8px 8px",
              }}
            >
              <Input placeholder={t("Domaine")} />
            </Form.Item>
          </Col>

          <Col md={24} xs={24}>
            <Form.Item
              name={"nationalite_id"}
              label={t("Nationalités")}
              style={{
                display: "inline-block",
                width: "45%",
                margin: "0 8px 8px",
              }}
            >
              <Select
                allowClear
                placeholder={t("Nationalités")}
                options={nationalites.map((item) => ({
                  value: item.id,
                  label: item.nom,
                }))}
              />
            </Form.Item>
          </Col>
          <Col md={24} xs={24}>
            <Form.Item
              name={"produits"}
              label={t("Produits")}
              style={{
                display: "inline-block",
                width: "100%",
                margin: "0 8px 8px",
              }}
            >
              <Input placeholder={t("Produits")} />
            </Form.Item>
          </Col>
        </Row>
      </Card>
      <Divider>Données sur l'investissement</Divider>
      <Card className="mt-2">
        <Row>
          <Col md={6} xs={24}>
            <Form.Item
              name={"investissement"}
              label={t("Investissement")}
              style={{
                display: "inline-block",
                margin: "0 8px 8px",
              }}
            >
              <Input placeholder={t("Investissement")} />
            </Form.Item>
          </Col>
          <Col md={6} xs={24}>
            {" "}
            <Form.Item
              name={"inv_reel"}
              label={t("Inv Réel")}
              style={{
                display: "inline-block",

                margin: "0 8px 8px",
              }}
            >
              <Input placeholder={t("Inv Réel")} />
            </Form.Item>
          </Col>
          <Col md={6} xs={24}>
            {" "}
            <Form.Item
              name={"taux_part"}
              label={t("%T.P.E")}
              style={{
                display: "inline-block",

                margin: "0 8px 8px",
              }}
            >
              <Input placeholder={t("%T.P.E")} />
            </Form.Item>
          </Col>
          <Col md={6} xs={24}>
            <Form.Item
              name={"effectif_total"}
              label={t("Effectif total")}
              style={{
                display: "inline-block",

                margin: "0 8px 8px",
              }}
            >
              <Input placeholder={t("Effectif total")} />
            </Form.Item>
          </Col>
        </Row>
      </Card>
      <Divider>Production</Divider>
      <Card className="mt-2">
        <Row>
          <Col md={8} xs={24}>
            <Form.Item
              name={"chiffre_affaire"}
              label={t("C.A")}
              style={{
                display: "inline-block",

                margin: "0 8px 8px",
              }}
            >
              <Input placeholder={t("C.A")} />
            </Form.Item>
          </Col>
          <Col md={4} xs={24}>
            <Form.Item
              name={"pourc_chiffre"}
              label={t("Dont")}
              style={{
                display: "inline-block",

                margin: "0 8px 8px",
              }}
            >
              <Input placeholder={t("Dont")} />
            </Form.Item>
          </Col>
          <Col md={4} xs={24}>
            <br /> <br /> % exportés
          </Col>
          <Col md={8} xs={24}>
            {" "}
            <Form.Item
              name={"date_production"}
              label={t("Date entrée en production")}
              style={{
                display: "inline-block",

                margin: "0 8px 8px",
              }}
            >
              <DatePicker placeholder={t("Date entrée en production")} />
            </Form.Item>
          </Col>
        </Row>
      </Card>
      <Divider>Situation de l'entreprise</Divider>
      <Card className="mt-2">
        <Row>
          <Col md={8} xs={24}>
            <Form.Item
              name={"situation_act"}
              label={t("Situation actuelle")}
              style={{
                display: "inline-block",

                margin: "0 8px 8px",
              }}
            >
              <Select placeholder={t("Situation actuelle")} />
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item
              name={"date_declaration"}
              label={t("Date de déclaration")}
              style={{
                display: "inline-block",

                margin: "0 8px 8px",
              }}
            >
              <DatePicker placeholder={t("Date de déclaration")} />
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item
              name={"date_ferm"}
              label={t("Date de fermeture")}
              style={{
                display: "inline-block",

                margin: "0 8px 8px",
              }}
            >
              <DatePicker placeholder={t("Date de fermeture")} />
            </Form.Item>
          </Col>
          <Col md={24} xs={24}>
            <Form.Item
              name={"remarque"}
              label={t("Remarque")}
              style={{
                display: "inline-block",
                width: "100%",
                margin: "0 8px 8px",
              }}
            >
              <Input placeholder={t("Remarque")} />
            </Form.Item>
          </Col>
        </Row>
      </Card>
    </ModalForm>
  );
}
export default CreateCompanyForm;
