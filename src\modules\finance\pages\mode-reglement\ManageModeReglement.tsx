import {ProTable} from "@ant-design/pro-components";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Col, Popconfirm, Row, Space, message,
  Typography,
  Tooltip,
  Divider,
  Input
} from "antd";
import {ProColumns} from "@ant-design/pro-components";
import {useDispatch} from "react-redux";
import {DeleteOutlined, EditOutlined, EyeOutlined, PlusOutlined} from "@ant-design/icons";
import {useRef, useState, useEffect} from "react";
import CreateModeReglement from "./CreateModeReglement";
import {deleteModeReglement, getModeReglement, getMoyenPaiement} from "@/modules/finance/features/mode-reglement/ModeReglementSlice";
import EditModeReglment from "./EditModeReglement";
import {useTranslation} from "react-i18next";


const { Title} = Typography;

const ManageModeReglement: React.FC<any> = () => {

  const {t} = useTranslation();
  const dispatch = useDispatch();
  const tableRef = useRef<any>();
  const tableRefChild = useRef<any>();
  const [messageApi, contextHolder] = message.useMessage();
  const [visibleForm, setVisibleForm] = useState(false);

  const [visibleFormChild, setVisibleFormChild] = useState(false);
  const [dataRecord, setDataRecord] = useState(false);
  const [moyenPaiements, setMoyenPaiements] = useState([]);
  const [totalData, settotalData] = useState<number>();
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [code, setCode] = useState<string>();
  const [libelle, setLibelle] = useState<string>();
  const [show, setShow] = useState(false);

  const columns: ProColumns<any>[] = [
    {
      title: t("code"),
      dataIndex: "code",
      key: "code",
      renderFormItem: (item, {type, defaultRender, ...rest}, form) => {
        return (
          <Input
            allowClear
            placeholder="Saisir un code"
            onChange={(e) => {
              setCode(e.target.value);
            }}
          />
        );
      }
    },
    {
      title: t("libelle"),
      dataIndex: "libelle",
      key: "libelle",
      renderFormItem: (item, {type, defaultRender, ...rest}, form) => {
        return (
          <Input
            allowClear
            placeholder="Saisir une libellé"
            onChange={(e) => {
              setLibelle(e.target.value);
            }}
          />
        );
      }
    },
    {
      title: t("action"),
      valueType: "option",
      key: "option",
      render: (_, record) => (
          <Space size="middle">
            <Tooltip title={t("common.actions.view")}>
              <Button
                  type="link"
                  icon={<EyeOutlined />}
                  onClick={() => handleShowModeReglement(record)}
              />
            </Tooltip>
            <Tooltip title={t("common.actions.edit")}>
              <Button
                  type="link"
                  style={{ color: "#f5b041" }}
                  icon={<EditOutlined />}
                  onClick={() => handleEditModeReglement(record)}
              />
            </Tooltip>
            <Tooltip title={t("common.actions.delete")}>
              <Popconfirm
                  title={t("common.messages.confirm_delete")}
                  onConfirm={() => {
                    handleDeleteModeReglement(record.id);
                  }}
                  okText={t("common.actions.yes")}
                  cancelText={t("common.actions.no")}
              >
                <Button
                    type="link"
                    style={{ color: "#ec7063" }}
                    icon={<DeleteOutlined />}
                />
              </Popconfirm>
            </Tooltip>
          </Space>
      ),
    },

  ];
  const nestedColumns: any = [
    {
      title: t("mode_reglement.pourcentage"),
      dataIndex: "pourcentage",
      key: "pourcentage",
      search: false,
    },
    {
      title: t("mode_reglement.nbr_jours"),
      dataIndex: "nbr_jours",
      key: "nbr_jours",
      search: false,
    },
    {
      title: t("mode_reglement.type_reglement"),
      dataIndex: "type_reglement",
      key: "type de réglement",
      render: (_, record) => record?.type_reglement,
      search: false,
    },
    {
      title: t("mode_reglement.jour_reglement"),
      dataIndex: "jour_reglement",
      key: "jour_reglement",
      search: false,
    },
    {
      title: t("mode_reglement.moyen_paiement"),
      dataIndex: "moyen_paiement_id",
      key: "moyen_paiement_id",
      render: (_, record) => record?.moyen_paiement?.libelle,
      search: false,
    },
  ];
  const handleGetMoyenReglement = () => {
    return dispatch(getModeReglement(
      {
        pageNumber: pageNumber,
        code: code,
        libelle: libelle,
      }
    ))
      .unwrap()
      .then((res) => {
        settotalData(res.total);
        return res.data;
      })
      .catch((error: any) => {
        console.log(error);
      })
  };
  const handleDeleteModeReglement = (id) => {
    dispatch(deleteModeReglement(id))
      .unwrap()
      .then(res => {
        messageApi.success("Mode de réglement supprimé");
        tableRef.current.reload();
      })
      .catch(error => {
        console.log(error);
      });
  };
  const handleEditModeReglement = (record) => {
    setDataRecord(record);
    setVisibleFormChild(true);
    setShow(false);
  };
  const handleShowModeReglement = (record) => {
    setDataRecord(record);
    setVisibleFormChild(true);
    setShow(true);
  };
  const obj = {
    visible: visibleForm,
    setVisible: setVisibleForm,
    messageApi,
    tableRef,
    moyenPaiements
  }
  const edit = {
    visible: visibleFormChild,
    setVisible: setVisibleFormChild,
    dataRecord: dataRecord,
    tableRef,
    moyenPaiements,
    show: show,
    messageApi
  }
  useEffect(() => {
    dispatch(getMoyenPaiement())
      .unwrap()
      .then(res => {
        let ar = res.data.map(item => {
          return {
            value: item?.id,
            label: item?.libelle
          }
        })
        setMoyenPaiements(ar);
      })
      .catch(error => {
        console.log(error);
      })
  }, []);
  return (
    <>
      {contextHolder}
      <Row className="mt-5" gutter={[12, 24]}>
        <Col xs={24}>
          <Card
            title={<Title level={4}>{t("mode_reglement.gestion")}</Title>}
            bordered={false}
            extra={
              <Button
                  type="primary"
                  icon={<PlusOutlined/>}
                  onClick={() => {
                    setVisibleForm(true);
                  }}
              >
                {t("mode_reglement.ajouter")}
              </Button>
            }
          >
            <ProTable<any>
              rowKey={(record) => record.id}
              columns={columns}
              actionRef={tableRef}
              bordered
              columnsState={{
                persistenceKey: "pro-table-singe-demos",
                persistenceType: "localStorage",
              }}
              search={{
                labelWidth: "auto",
              }}
              options={false}
              request={async (params: any) => {
                var dataFilter = await handleGetMoyenReglement();
                return {
                  data: dataFilter,
                  success: true,
                };
              }}
              pagination={{
                pageSize: 10,
                total: totalData,
                onChange: (page) => setPageNumber(page),
                showSizeChanger: false,
              }}
              expandable={{
                rowExpandable: (record) => true,
                expandedRowRender: (record: any) => {
                  return (
                    <>
                      <ProTable<any>
                        actionRef={tableRefChild}
                        bordered
                        columnsState={{
                          persistenceKey: "pro-table-singe-demos",
                          persistenceType: "localStorage",
                          onChange(value) {
                            //  console.log("value: ", value);
                          },
                        }}
                        defaultSize={"small"}
                        search={false}
                        options={false}
                        columns={nestedColumns}
                        request={async () => {
                          tableRefChild.current.reload();
                          return {
                            data: record?.mode_reglement_values?.map((item) => {
                              return {...item, key: item.id};
                            }),
                            success: true,
                          };
                        }}
                      />
                    </>
                  );
                },
              }}

            />
          </Card>
        </Col>
      </Row>
      <EditModeReglment {...edit}/>
      <CreateModeReglement {...obj} />
    </>
  );
}
export default ManageModeReglement;
