import {
  FileOutlined,
  MinusCircleOutlined,
  PlusCircleOutlined,
  UploadOutlined,
} from "@ant-design/icons";
import { ModalForm } from "@ant-design/pro-components";
import { Card, Col, DatePicker, Form, Input, message, Row, Upload } from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";

const Historique = ({ open, onCancel }) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const handleAdd = () => {
    const currentdata = form.getFieldValue("participants") || [];
    const pays = form.getFieldValue("code_pays");
    const taux = form.getFieldValue("taux");
    const date_info = form.getFieldValue("date_info");
    if (pays && taux && date_info) {
      const key = Date.now();
      form.setFieldsValue({
        participants: [
          ...currentdata,
          {
            key: key,
            code_pays: pays,
            taux: taux,
            date_info: date_info,
          },
        ],
      });
      form.resetFields(["code_pays", "taux", "date_info"]);
    } else {
      message.error(t("Merci de bien vouloir remplir tous les champs."));
    }
  };
  return (
    <>
      <ModalForm
        title={t("Historique des postes occupés")}
        form={form}
        open={open}
        modalProps={{
          style: { top: 20 },
        }}
        onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
        submitter={{
          searchConfig: {
            submitText: t("common.actions.save"),
            resetText: t("common.actions.cancel"),
          },
        }}
      >
        <Card className="mt-2">
          <Row gutter={16}>
            <Col span={4}>Date début</Col>
            <Col span={7}>Sté</Col>
            <Col span={7}>Poste</Col>
            <Col span={4}>Date fin</Col>
            <Col span={1}></Col>
          </Row>
          <Form.List name="participants">
            {(fields, { remove }) => (
              <>
                {fields.map((field, index) => (
                  <Row gutter={16} align="middle" key={field.key}>
                    <Col span={12}>
                      <Form.Item name={[index, "code_pays"]} rules={[]}>
                        <Input allowClear={true} />
                      </Form.Item>
                    </Col>
                    <Col span={4}>
                      <Form.Item name={[index, "taux"]} rules={[]}>
                        <Input allowClear={true} />
                      </Form.Item>
                    </Col>
                    <Col span={7}>
                      <Form.Item name={[index, "date_info"]} rules={[]}>
                        <DatePicker allowClear={true} />
                      </Form.Item>
                    </Col>
                    <Col span={1}>
                      <Form.Item>
                        <MinusCircleOutlined
                          style={{
                            color: "red",
                            fontSize: "18px",
                            cursor: "pointer",
                          }}
                          onClick={() => remove(index)}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                ))}
              </>
            )}
          </Form.List>

          <Row gutter={16} align="middle" style={{ marginBottom: "5px" }}>
            <Col span={4}>
              <Form.Item name={`code_pays`} rules={[]}>
                <DatePicker allowClear={true} />
              </Form.Item>
            </Col>
            <Col span={7}>
              <Form.Item name={`taux`} rules={[]}>
                <Input allowClear={true} />
              </Form.Item>
            </Col>
            <Col span={7}>
              <Form.Item name={`date_info`} rules={[]}>
                <Input allowClear={true} />
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item name={`code_pays`} rules={[]}>
                <DatePicker allowClear={true} />
              </Form.Item>
            </Col>
            <Col span={1}>
              <Form.Item>
                <PlusCircleOutlined
                  style={{
                    color: "green",
                    fontSize: "18px",
                    cursor: "pointer",
                  }}
                  onClick={() => handleAdd()}
                />
              </Form.Item>
            </Col>
          </Row>
        </Card>
      </ModalForm>
    </>
  );
};

export default Historique;
