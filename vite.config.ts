import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import * as path from "path";
// https://vite.dev/config/
export default defineConfig({
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@src': path.resolve(__dirname, './src'),
      '@layouts': path.resolve(__dirname, './src/layouts'),
      '@components': path.resolve(__dirname, './src/components'),
      '@features': path.resolve(__dirname, './src/features'),
      '@pages': path.resolve(__dirname, './src/pages'),
      '@assets': path.resolve(__dirname, './src/assets'),
      '@apis': path.resolve(__dirname, './src/apis'),
      '@routes': path.resolve(__dirname, './src/routes'),
      '@redux': path.resolve(__dirname, './src/redux'),
      '@helpers': path.resolve(__dirname, './src/helpers'),
      '@configs': path.resolve(__dirname, './src/configs'),
      '@hooks': path.resolve(__dirname, 'src/hooks/'),
    },
  },
  plugins: [react()],
  define: { "process.env": process.env },
})
