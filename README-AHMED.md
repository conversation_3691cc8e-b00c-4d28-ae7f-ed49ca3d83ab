## Add some global features to sideNav

#### Add translation to sideNav

just use your translation key in label and it will be displayed in sidebar
like in this example : **"audit.dashboard.title"**

```json
{
    ...
    "label": "audit.dashboard.title",
    ...
}
```

### Set sideNav by module

just add your module name like `audit` or `parametrage` ... in your own sideNav like `ahmed-sidenav` 
in the key `base_nav` and it will be displayed in the specific sideNav Module

```json
{
    ...
    "base_nav": "audit",
    ...
}
```
