import axios from 'axios';

const baseURL= import.meta.env.VITE_API_URL
// Create Axios instance
const axiosInstance = axios.create({
  baseURL: baseURL,
  timeout: 50000,
  withCredentials: true, // Ensure cookies are sent with requests
  // timeout: 10000,
  // withCredentials: true, // Ensure cookies are sent with requests
  // withXSRFToken: true
});

axiosInstance.interceptors.request.use(
  (config) => {
    // Retrieve the token from localStorage, cookies, or any other storage
    const token = localStorage.getItem('token'); // Example: Fetch token from localStorage

    if (token) {
      // Attach the token to the Authorization header
      config.headers['Authorization'] = `Bearer ${token}`;
    }

    return config;
  },
  (error) => {
    // Handle errors before the request is sent
    return Promise.reject(error);
  }
);

// Refresh token request function
const refreshAccessToken = async () => {
  try {
    const response = await axios.post(
      baseURL+'/auth/refresh',
      {},
      { withCredentials: true }
    );

    const { accessToken } = response.data; // Ensure your backend sends the new access token
    return accessToken;
  } catch (error) {
    console.error('Failed to refresh token', error);
    throw error;
  }
};

// Request interceptor
axiosInstance.interceptors.request.use(
  (config) => {
    // Access tokens stored in cookies are automatically included via "withCredentials"
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor
// axiosInstance.interceptors.response.use(
//   (response) => response, // Pass through successful responses
//   async (error) => {
//     const originalRequest = error.config;
//     // Check if the error is due to an expired token (401) and not already retried
//     if (error.response?.status === 401 && !originalRequest._retry && localStorage.getItem("token")) {
//       originalRequest._retry = true; // Prevent infinite retry loops
//       try {
//         const newAccessToken = await refreshAccessToken();
//         // Add the new token to the request headers
//         originalRequest.headers['Authorization'] = `Bearer ${newAccessToken}`;
//         // Retry the original request
//         return axiosInstance(originalRequest);
//       } catch (refreshError) {
//         console.error('Token refresh failed. Redirecting to login.');
//         window.location.href = '/login'; // Redirect to login if refresh fails
//         return Promise.reject(refreshError);
//       }
//     }
//     return Promise.reject(error);
//   }
// );

export default axiosInstance;
