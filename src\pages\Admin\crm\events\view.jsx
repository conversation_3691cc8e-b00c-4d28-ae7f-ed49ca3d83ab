import React from "react";
import { Modal, Descriptions } from "antd";

const view = ({ visible, onCancel, event }) => {
  return (
    <Modal title="Détails de l'Événement" visible={visible} onCancel={onCancel} footer={null}>
      <Descriptions bordered>
        <Descriptions.Item label="Titre">{event?.title}</Descriptions.Item>
        <Descriptions.Item label="Secteur">{event?.sector}</Descriptions.Item>
        <Descriptions.Item label="Pays">{event?.country}</Descriptions.Item>
        <Descriptions.Item label="Date">{event?.date}</Descriptions.Item>
        <Descriptions.Item label="Statut">{event?.status}</Descriptions.Item>
      </Descriptions>
    </Modal>
  );
};

export default view