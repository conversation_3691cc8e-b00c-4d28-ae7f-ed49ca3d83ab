import React, { useEffect, useState } from "react";
import {
  Button,
  Drawer,
  Input,
  Form,
  Row,
  Col,
} from "antd";
import { useDispatch, useSelector } from "react-redux";
import { updateRole } from "@/modules/parameter/features/roles/roleSlice";

const UpdateRole = ({ setVisible, visible, role, messageApi, tableRef}) => {
  const dispatch = useDispatch();
  const [form] = Form.useForm()
  const [buttonLoading, setButtonLoading] = useState<boolean>(false);
  const onClose = () => {
    setVisible(false);
  };
  const handleUpdateRole = (e) => {
    setButtonLoading(true);
    messageApi.open({
      key: "updatable",
      type: "loading",
      content: "Loading...",
    });
    dispatch(updateRole({id:role.id,name:e.name}))
      .unwrap()
      .then((originalPromiseResult) => {
        messageApi.open({
          key: "updatable",
          type: "success",
          content: "Success ",
          duration: 2,
        });
        tableRef.current.reload()
        setButtonLoading(false);
        setVisible(false);
      })
      .catch((rejectedValueOrSerializedError) => {
        messageApi.open({
          key: "updatable",
          type: "error",
          content: "Error",
          duration: 2,
        });
        setButtonLoading(false);
        console.log(rejectedValueOrSerializedError);
        return [];
      });
  };

  useEffect(()=>{
  },[visible])
  return (
    <Drawer
      title="Modifier rôle"
      width={window.innerWidth > 520 ? 480 : "90%"}
      className="CautionForm"
      onClose={onClose}
      open={visible}
      styles={{ body: { paddingBottom: 80 } }}

    >
      <Form layout="vertical" hideRequiredMark onFinish={handleUpdateRole}
      form={form}
      onReset={()=>{
        form.setFieldsValue({
            name:role.role
          });
      }}
      fields={[
        {
            name: ["name"],
            value: role?.role,
          },
      ]}
      >
        <Row gutter={16}>
          <Col xs={24} sm={24} md={24} lg={24} xl={24} xxl={24}>
            <Form.Item
              name="name"
              label="Rôle"
              rules={[
                {
                  required: true,
                  message: "Veuillez entrer le Rôle",
                },
              ]}
            >
              <Input placeholder="Label du rôle" />
            </Form.Item>
          </Col>

          <Form.Item
            style={{
              width: "100%",
              textAlign: "right",
              marginRight: "10px",
            }}
          >
            <Button htmlType="reset" style={{ marginRight: "10px" }}>
              Annuler
            </Button>
            <Button type="primary" htmlType="submit" loading={buttonLoading}>
              Modifier
            </Button>
          </Form.Item>
        </Row>
      </Form>
    </Drawer>
  )
}

export default UpdateRole
