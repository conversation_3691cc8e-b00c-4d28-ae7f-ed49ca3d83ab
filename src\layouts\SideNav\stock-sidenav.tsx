const SideNavStock = [

    {
        "base_nav": "stock",
        "id": 5314,
        "label": "stock.dashboard.title",
        "order": 3,
        "menu_parent": null,
        "active": 1,
        "link": "/stock",
        "permission_id": null,
        "icon": "    <svg\n      width=\"20\"\n      height=\"20\"\n      viewBox=\"0 0 20 20\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      key={0}\n    >\n      <path\n        d=\"M3 4C3 3.44772 3.44772 3 4 3H16C16.5523 3 17 3.44772 17 4V6C17 6.55228 16.5523 7 16 7H4C3.44772 7 3 6.55228 3 6V4Z\"\n        fill={color}\n      ></path>\n      <path\n        d=\"M3 10C3 9.44771 3.44772 9 4 9H10C10.5523 9 11 9.44771 11 10V16C11 16.5523 10.5523 17 10 17H4C3.44772 17 3 16.5523 3 16V10Z\"\n        fill={color}\n      ></path>\n      <path\n        d=\"M14 9C13.4477 9 13 9.44771 13 10V16C13 16.5523 13.4477 17 14 17H16C16.5523 17 17 16.5523 17 16V10C17 9.44771 16.5523 9 16 9H14Z\"\n        fill={color}\n      ></path>\n    </svg>",
        "created_at": null,
        "updated_at": null,
        "child_recursive": []
    },
    {
        "base_nav": "stock",
        "id": 5314,
        "label": "stock.categories.title",
        "order": 3,
        "menu_parent": null,
        "active": 1,
        "link": "/stock/categories",
        "permission_id": null,
        "icon": "    <svg\n      width=\"20\"\n      height=\"20\"\n      viewBox=\"0 0 20 20\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      key={0}\n    >\n      <path\n        d=\"M3 4C3 3.44772 3.44772 3 4 3H16C16.5523 3 17 3.44772 17 4V6C17 6.55228 16.5523 7 16 7H4C3.44772 7 3 6.55228 3 6V4Z\"\n        fill={color}\n      ></path>\n      <path\n        d=\"M3 10C3 9.44771 3.44772 9 4 9H10C10.5523 9 11 9.44771 11 10V16C11 16.5523 10.5523 17 10 17H4C3.44772 17 3 16.5523 3 16V10Z\"\n        fill={color}\n      ></path>\n      <path\n        d=\"M14 9C13.4477 9 13 9.44771 13 10V16C13 16.5523 13.4477 17 14 17H16C16.5523 17 17 16.5523 17 16V10C17 9.44771 16.5523 9 16 9H14Z\"\n        fill={color}\n      ></path>\n    </svg>",
        "created_at": null,
        "updated_at": null,
        "child_recursive": []
    },
    {
        "base_nav": "stock",
        "id": 5314,
        "label": "stock.articles.title",
        "order": 3,
        "menu_parent": null,
        "active": 1,
        "link": "/stock/articles",
        "permission_id": null,
        "icon": "    <svg\n      width=\"20\"\n      height=\"20\"\n      viewBox=\"0 0 20 20\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      key={0}\n    >\n      <path\n        d=\"M3 4C3 3.44772 3.44772 3 4 3H16C16.5523 3 17 3.44772 17 4V6C17 6.55228 16.5523 7 16 7H4C3.44772 7 3 6.55228 3 6V4Z\"\n        fill={color}\n      ></path>\n      <path\n        d=\"M3 10C3 9.44771 3.44772 9 4 9H10C10.5523 9 11 9.44771 11 10V16C11 16.5523 10.5523 17 10 17H4C3.44772 17 3 16.5523 3 16V10Z\"\n        fill={color}\n      ></path>\n      <path\n        d=\"M14 9C13.4477 9 13 9.44771 13 10V16C13 16.5523 13.4477 17 14 17H16C16.5523 17 17 16.5523 17 16V10C17 9.44771 16.5523 9 16 9H14Z\"\n        fill={color}\n      ></path>\n    </svg>",
        "created_at": null,
        "updated_at": null,
        "child_recursive": []
    },
    {
        "base_nav": "stock",
        "id": 5315,
        "label": "emplacements.title",
        "order": 4,
        "menu_parent": null,
        "active": 1,
        "link": "/stock/emplacements",
        "permission_id": null,
        "icon": '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="size-6"><path fill-rule="evenodd" d="m11.54 22.351.07.04.028.016a.76.76 0 0 0 .723 0l.028-.015.071-.041a16.975 16.975 0 0 0 1.144-.742 19.58 19.58 0 0 0 2.683-2.282c1.944-1.99 3.963-4.98 3.963-8.827a8.25 8.25 0 0 0-16.5 0c0 3.846 2.02 6.837 3.963 8.827a19.58 19.58 0 0 0 2.682 2.282 16.975 16.975 0 0 0 1.145.742ZM12 13.5a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z" clip-rule="evenodd" /></svg>',
        "created_at": null,
        "updated_at": null,
        "child_recursive": []
    },
    {
        "base_nav": "stock",
        "id": 5316,
        "label": "stock-unities.title",
        "order": 5,
        "menu_parent": null,
        "active": 1,
        "link": "/stock/stock-unities",
        "permission_id": null,
        "icon": '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6"><path stroke-linecap="round" stroke-linejoin="round" d="M6 6.878V6a2.25 2.25 0 0 1 2.25-2.25h7.5A2.25 2.25 0 0 1 18 6v.878m-12 0c.235-.083.487-.128.75-.128h10.5c.263 0 .515.045.75.128m-12 0A2.25 2.25 0 0 0 4.5 9v.878m13.5-3A2.25 2.25 0 0 1 19.5 9v.878m0 0a2.246 2.246 0 0 0-.75-.128H5.25c-.263 0-.515.045-.75.128m15 0A2.25 2.25 0 0 1 21 12v6a2.25 2.25 0 0 1-2.25 2.25H5.25A2.25 2.25 0 0 1 3 18v-6c0-.98.626-1.813 1.5-2.122" /></svg>',
        "created_at": null,
        "updated_at": null,
        "child_recursive": []
    },
];

export default SideNavStock;
