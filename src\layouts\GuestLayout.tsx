// src/layouts/AuthLayout.js
import { Button } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';

const GuestLayout = ({ children }) => {
  const { i18n } = useTranslation();

  const changeLanguage = (lang) => {
    
    i18n.changeLanguage(lang);
  };


  return (
    <div className='w-full'>
      <main className='p-8 h-5/6'>{children}</main>
    </div>
  );
};

export default GuestLayout;
