import { ModalForm } from "@ant-design/pro-components";
import PurchaseInfo from "@src/modules/finance/components/purchases/PurchaseInfo";
import PurchaseItem from "@src/modules/finance/components/purchases/PurchaseItem";
import {
  Form,
} from "antd";
import {  useState } from "react";
import { useTranslation } from "react-i18next";



function ShowQuoteForm({ open, onCancel, record }) {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  return (
    <ModalForm
      title={t("cmd.view")}
      disabled
      form={form}
      open={open}
      width="90%"
      //   onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        resetButtonProps: { disabled: false },
        submitButtonProps: { hidden: true },
        searchConfig: {
          resetText: t("common.actions.cancel"),
        },
      }}
    >    
      <PurchaseInfo form={form}  record={record} open={open} />
      <PurchaseItem  form={form}   record={record} open={open}/>
    </ModalForm>
  );
}
export default ShowQuoteForm;
