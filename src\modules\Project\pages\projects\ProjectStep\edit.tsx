import React, { useEffect, useState } from "react";
import {
  Space,
  Input,
  Form,
  Row,
  Col,
  Select,
  DatePicker,
  InputNumber,
} from "antd";
import dayjs from "dayjs";
import { useDispatch } from "react-redux";
import { IProjectStage } from "../../interfaces";
import { ModalForm } from "@ant-design/pro-components";
import { useTranslation } from "react-i18next";
import { updateProjectStep } from "@src/modules/Project/features/projects/projectStepSlice";
const { Option } = Select;

const Edit: React.FC<{
  visible: boolean;
  setVisible: React.Dispatch<React.SetStateAction<boolean>>;
  forceRefresh: React.Dispatch<React.SetStateAction<number>>;
  projectStage: IProjectStage;
  stagesCount: number;
  messageApi: any;
}> = ({
  visible,
  setVisible,
  forceRefresh,
  projectStage,
  stagesCount,
  messageApi,
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [montantType, setMontantType] = useState<number>(1);
  const [montant, setMontant] = useState<number>(null);
  const [pourcentage, setPourcentage] = useState<number>(null);

  const onClose = (open) => {
    if (!open) {
      setVisible(false);
      form.resetFields();
      setMontantType(1);
      setMontant(null);
      setPourcentage(null);
    }
  };

  const handleCalculDuree = () => {
    if (form.getFieldValue("date_debut") && form.getFieldValue("date_fin")) {
      const dateDebut = dayjs(form.getFieldValue("date_debut"), "YYYY-MM-DD");
      const dateFin = dayjs(form.getFieldValue("date_fin"), "YYYY-MM-DD");
      const difference = dateFin.diff(dateDebut, "day");
      form.setFieldsValue({ duree: difference });
    } else {
      form.setFieldsValue({ duree: null });
    }
  };

  const handleInputErrors = (error) => {
    const apiErrors = error.errors;
    const fields: any = Object.entries(apiErrors).map(
      ([fieldName, messages]) => ({
        name: fieldName,
        errors: messages,
      })
    );
    form.setFields(fields);
  };

  const handleSubmit = async (values) => {
    setIsLoading(true);
    messageApi.open({
      key: "updatable",
      type: "loading",
      content: t("common.messages.loading"),
    });
    dispatch(
      updateProjectStep({
        id: projectStage.id,
        project_id: projectStage.project_id,
        ...values,
        date_debut: values.date_debut && dayjs(values.date_debut).format('YYYY-MM-DD'),
        date_fin: values.date_fin && dayjs(values.date_fin).format('YYYY-MM-DD'),
        pour_montant: montantType === 1 ? pourcentage : null,
        montant: montantType === 2 ? montant : null,
      })
    )
      .unwrap()
      .then((originalPromiseResult) => {
        messageApi.open({
          key: "updatable",
          type: "success",
          content: t("common.messages.success"),
          duration: 2,
        });
        forceRefresh(Math.random());
        setIsLoading(false);
        onClose(false);
      })
      .catch((rejectedValueOrSerializedError) => {
        console.log(rejectedValueOrSerializedError);
        messageApi.open({
          key: "updatable",
          type: "error",
          content: t("common.messages.error"),
          duration: 2,
        });
        setIsLoading(false);
        handleInputErrors(rejectedValueOrSerializedError);
      });
  };

  useEffect(() => {
    if (projectStage) {
      setMontantType(projectStage.montant ? 2 : 1);
      setMontant(projectStage.montant);
      setPourcentage(projectStage.pour_montant);
      form.setFieldsValue({
        ...projectStage,
        date_debut:
          projectStage.date_debut &&
          dayjs(projectStage.date_debut, "YYYY-MM-DD"),
        date_fin:
          projectStage.date_fin && dayjs(projectStage.date_fin, "YYYY-MM-DD"),
        description: projectStage.description,
      });
    }
    handleCalculDuree();
  }, [projectStage, visible]);

  return (
    <ModalForm
      className="pt-2"
      title={t("project_steps.edit.title") + " : " + projectStage?.designation}
      width={850}
      onOpenChange={onClose}
      open={visible}
      layout="vertical"
      onValuesChange={(changedValues) => {
        const fieldName = Object.keys(changedValues)[0];
        form.setFields([{ name: fieldName, errors: [] }]);
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.save"),
          resetText: t("common.actions.cancel"),
        },
      }}
      onFinish={handleSubmit}
      form={form}
      loading={isLoading}
    >
      <Row gutter={16}>
        <Col xs={24} sm={12} md={12} lg={12} xl={12} xxl={12}>
          <Form.Item
            name="step_ordre"
            label={t("project_steps.columns.order")}
            rules={[
              {
                required: true,
                message: t("project_steps.messages.order_required"),
              },
            ]}
          >
            <Select placeholder={t("project_steps.placeholders.select_order")}>
              {[...Array(stagesCount + 1)].map((_, i) => (
                <Option key={i + 1} value={i + 1}>
                  {i + 1}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={12} lg={12} xl={12} xxl={12}>
          <Form.Item
            name="designation"
            label={t("project_steps.columns.title")}
            rules={[
              {
                required: true,
                message: t("project_steps.messages.title_required"),
              },
            ]}
          >
            <Input placeholder={t("project_steps.placeholders.enter_title")} />
          </Form.Item>
        </Col>

        <Col xs={24} sm={12} md={12} lg={12} xl={12} xxl={12}>
          <Form.Item
            name="date_debut"
            label={t("project_steps.columns.start_date")}
            rules={[
              {
                required: true,
                message: t("project_steps.messages.start_date_required"),
              },
            ]}
          >
            <DatePicker
              style={{ width: "100%" }}
              format="YYYY-MM-DD"
              allowClear
              onChange={handleCalculDuree}
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={12} lg={12} xl={12} xxl={12}>
          <Form.Item
            name="date_fin"
            label={t("project_steps.columns.end_date")}
            rules={[
              {
                required: true,
                message: t("project_steps.messages.end_date_required"),
              },
            ]}
          >
            <DatePicker
              style={{ width: "100%" }}
              format="YYYY-MM-DD"
              allowClear
              onChange={handleCalculDuree}
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={12} lg={12} xl={12} xxl={12}>
          <Form.Item 
            name="duree" 
            label={t("project_steps.columns.duration")} 
            rules={[{ 
              type: 'number', 
              min: 0, 
              message: t("project_steps.messages.duration_positive") 
            }]}
          >
            <InputNumber
              style={{ width: "100%" }}
              placeholder={t("project_steps.placeholders.duration_calculated")}
              disabled
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={12} lg={12} xl={12} xxl={12}>
          <Form.Item name="paiement" label={t("project_steps.columns.payment")}>
            <Space.Compact className="w-full">
              <InputNumber
                onChange={(e) => {
                  montantType == 1 ? setPourcentage(e) : setMontant(e);
                }}
                value={montantType == 1 ? pourcentage : montant}
                style={{ width: "70%" }}
                placeholder={t(
                  montantType == 1 
                    ? "project_steps.placeholders.enter_percentage" 
                    : "project_steps.placeholders.enter_amount"
                )}
                min={0}
                max={montantType == 1 ? 100 : null}
              />
              <Select
                style={{ width: "30%" }}
                value={montantType}
                onSelect={(e) => {
                  setMontantType(e);
                  setMontant(null);
                  setPourcentage(null);
                }}
                options={[
                  {
                    value: 1,
                    label: "%",
                  },
                  {
                    value: 2,
                    label: t("project_steps.currency"),
                  },
                ]}
              />
            </Space.Compact>
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item 
            name="description" 
            label={t("project_steps.columns.description")}
          >
            <Input.TextArea rows={4} />
          </Form.Item>
        </Col>
      </Row>
    </ModalForm>
  );
};

export default Edit;