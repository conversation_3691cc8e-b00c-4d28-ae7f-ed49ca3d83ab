import dayjs from 'dayjs';

// Date formatting
export const formatDateTime = (date) => dayjs(date).format('DD/MM/YYYY HH:mm');
export const formatDate = (date) => dayjs(date).format('DD/MM/YYYY');

// Filters
export const filterBySector = (items, sector) => 
  items.filter(item => item.sector === sector);

export const filterByStatus = (items, status) =>
  items.filter(item => item.status === status);

// Notification check
export const getUpcomingItems = (items, hoursAhead = 24) => {
  const now = dayjs();
  return items.filter(item => 
    dayjs(item.start).isBetween(now, now.add(hoursAhead, 'hour')))
};


// Add to helpers.js
export const checkForUpcomingEvents = () => {
  const items = getAllCalendarItems();
  const upcoming = getUpcomingItems(items);
  
  upcoming.forEach(item => {
    if (Notification.permission === 'granted') {
      new Notification(`Upcoming: ${item.title}`, {
        body: `Starts at ${formatDateTime(item.start)}`
      });
    }
  });
};

// Call this periodically (e.g., every hour)
setInterval(checkForUpcomingEvents, 3600000);