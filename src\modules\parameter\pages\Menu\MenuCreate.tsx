import {
  But<PERSON>,
  Col,
  Drawer,
  Form,
  Input,
  Row,
  Select,
  TreeSelect,
} from "antd";
import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";

import { icons } from "lucide-react";
import { AddMenu } from "@/modules/parameter/features/menus/menuSlice";
import { getPermissions } from "@/modules/parameter/features/permissions/permissionSlice";
import { IPermission } from "@/modules/parameter/pages/Role/interfaces";

const { Option } = Select;

export const ordreOptions = (n: number) => {
  const options = [];
  for (let i = 1; i <= n + 1; i++) {
    options.push(
      <Option key={i} value={i}>
        {i}
      </Option>
    );
  }
  return (
    <Select placeholder="Veuillez choisir l'ordre" showSearch>
      {options}
    </Select>
  );
};

const MenuCreate: React.FC<{
  visible: boolean;
  setVisible: React.Dispatch<React.SetStateAction<boolean>>;
  menu: any;
}> = ({ visible, setVisible, menu }) => {
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const [value, setValue] = useState<string>();
  const [parentMenu, setParentMenu] = useState<any>(null);
  const [buttonLoading, setButtonLoading] = useState(false);
  const [permissions, setPermissions] = useState<IPermission[]>([]);
  const [data, setData] = useState<IPermission[]>([]);
  const [optionsIcons, setOptionsIcons] = useState([]);

  const Icon = ({ name, color = "#888", size = 15 }: any) => {
    try {
      const LucideIcon = icons[name];
      if (!LucideIcon) {
        console.log("icon not defined in library - fix by ch");
      } else {
        return (
          <LucideIcon
            color={color}
            size={size}
            style={{ display: "inline-block", marginRight: 4 }}
          />
        );
      }
    } catch (ex) {
      console.log(ex);
    }
  };
  const onClose = () => {
    setVisible(false);
    form.resetFields();
  };
  const onChange = (newValue: string) => {
    setValue(newValue);
  };

  const filterOption = (input, option) =>{
    return (option?.label ?? "").toLowerCase().includes(input.toLowerCase());
  }

  const filterSort = (optionA, optionB) => {
    return (
      (optionA?.label ?? "")
        .toLowerCase()
        .localeCompare((optionB?.label ?? "").toLowerCase()) * -1
    );
  };

  const langs = JSON.parse(localStorage.getItem("languages"));

  const allPermissions = useSelector((state: any) => state.permission.data);


  const handleSubmit = (values) => {
    setButtonLoading(true);
    const titles = langs.reduce((acc, lang) => {
      const fieldName = `title_${lang.code}`;
      const inputValue = values[fieldName];

      acc[lang.code] = inputValue;
      return acc;
    }, {});
    dispatch(
      AddMenu({
        designation_fr: titles,
        ordre: values.ordre,
        menu_parant: values.parent,
        link: values.path,
        icon: values.icon,
        permission_id: values.permission,
      })
    )
      .unwrap()
      .then((originalPromiseResult) => {
        setButtonLoading(false);
        onClose();
      })
      .catch((rejectedValueOrSerializedError) => {
        setButtonLoading(false);
        console.log(rejectedValueOrSerializedError);
        return [];
      });
  };

  useEffect(() => {
    setOptionsIcons(
      Object.keys(icons).map((k) => {
        return { label: k, value: k };
      })
    );
  }, []);

  useEffect(() => {
    if (allPermissions && typeof allPermissions == "object") {
      console.log(allPermissions);
      setPermissions(
        Object.keys(allPermissions).reduce((acc, title, index) => {
          acc.push({
            title: <div style={{ color: "black" }}>{title}</div>,
            value: title,
            disabled: true,
            children: allPermissions[title].map((item, cp) => {
              return {
                value: item.id,
                title: item.name,
              };
            }),
          });
          return acc;
        }, [])
      );
      setData(
        Object.keys(allPermissions).reduce((acc, title, index) => {
          acc.push({
            title: <div style={{ color: "black" }}>{title}</div>,
            value: title,
            disabled: true,
            children: allPermissions[title].map((item, cp) => {
              return {
                value: item.id,
                title: item.name,
              };
            }),
          });
          return acc;
        }, [])
      );
    }
  }, [allPermissions]);


  return (
    <Drawer
      width={window.innerWidth > 580 ? 560 : "90%"}
      onClose={onClose}
      open={visible}
      style={{
        paddingBottom: 80,
      }}
      title={"Ajouter Menu"}
      className="MenuDetails"
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        hideRequiredMark
      >
        <Row gutter={16}>
          <Col span={24}>
            {langs.map((lang, index) => (
              <Form.Item
                key={index}
                name={`title_${lang.code}`}
                label={`Titre - ${lang.name}`}
                rules={[
                  {
                    required: true,
                    message: `Veuillez entrer le titre pour ${lang.name}`,
                  },
                ]}
              >
                <Input
                  placeholder={`Veuillez entrer le titre pour ${lang.name}`}
                />
              </Form.Item>
            ))}
          </Col>
          <Col span={24}>
            <Form.Item name="path" label="Path">
              <Input placeholder="Veuillez entrer le path" />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item name="parent" label="Menu Parent">
              <TreeSelect
                allowClear
                treeData={menu?.map((item, index) => {
                  return {
                    title: item.title,
                    value: item.id,
                    long: item.children.length,
                    children: item.children.map((child, cp) => {
                      return {
                        title: child.title,
                        value: child.id,
                        long: child.children.length,
                      };
                    }),
                  };
                })}
                placeholder="Veuillez choisir le menu parent"
                treeDefaultExpandAll
                onSelect={(e, values) => {
                  setParentMenu(values);
                  form.setFieldsValue({
                    ordre: values.long + 1,
                  });
                  if (values.children === undefined) {
                    form.setFieldsValue({
                      icon: null,
                    });
                  }
                }}
                onClear={() => {
                  setParentMenu(null);
                  form.setFieldsValue({
                    icon: null,
                    parent: null,
                  });
                }}
              />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item
              name="ordre"
              label="Ordre"
              rules={[
                {
                  required: true,
                  message: "Veuillez choisir l'ordre",
                },
              ]}
            >
              {parentMenu
                ? ordreOptions(parentMenu.long)
                : ordreOptions(menu.length)}
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item name="permission" label="Permission">
              <TreeSelect
                showSearch
                onSearch={(value) => {
                  setPermissions(
                    data
                      .map((item) => {
                        const filteredChildren = item.children.filter(
                          (child) =>
                            child.title
                              .toUpperCase()
                              .indexOf(value.toUpperCase()) > -1
                        );
                        return { ...item, children: filteredChildren };
                      })
                      .filter((item) => item.children.length)
                  );
                }}
                filterTreeNode={(value) => {
                  return true;
                }}
                style={{ width: "100%" }}
                value={value}
                dropdownStyle={{ maxHeight: 400, overflow: "auto" }}
                placeholder="Veuillez choisir les permissions"
                allowClear
                onChange={onChange}
                treeData={permissions}
              />
            </Form.Item>
            <Form.Item name="icon" label="Icône">
              <Select
                showSearch
                filterOption={filterOption}
                filterSort={filterSort}
                // disabled={!parentMenu || parentMenu.children === undefined}
              >
                {optionsIcons.map((v, i) => (
                  <Option key={i} value={v.value} label={v.label}>
                    <Icon name={v.value} />
                    <span className="ml-2">{v.value}</span>
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>
        <Form.Item style={{ textAlign: "right" }}>
          <Button htmlType="reset" style={{ marginRight: "10px" }}>
            Annuler
          </Button>
          <Button type="primary" htmlType="submit" loading={buttonLoading}>
            Envoyer
          </Button>
        </Form.Item>
      </Form>
    </Drawer>
  );
};

export default MenuCreate;
