import { <PERSON>, Spin } from "antd";

const ViewScoring = ({ record }) => {
  if (!record) {
    return <Spin size="large" />;
  }

  return (
    <>
      <h2>Vue d'un score</h2>
      <Card title={record.label} bordered={false}>
        <p><strong>Clé:</strong> {record.key}</p>
        <p><strong>Étiquette:</strong> {record.label}</p>
        <p><strong>Conditions:</strong></p>
        <ul>
          {record.conditions.map((cond, index) => (
            <li key={index}>
              {Array.isArray(cond.value) ? cond.value.join(", ") : cond.value}: {cond.points} points
            </li>
          ))}
        </ul>
      </Card>
    </>
  );
};

export default ViewScoring;