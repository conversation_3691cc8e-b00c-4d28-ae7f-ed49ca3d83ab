import { Card, Descriptions } from "antd";

const ViewPostalCode = ({ record, onCancel }) => {
  return (
    <Card title="Détails du Code Postal" bordered={false}>
      <Descriptions bordered>
        <Descriptions.Item label="Code Postal">{record.code}</Descriptions.Item>
        <Descriptions.Item label="Ville">{record.city}</Descriptions.Item>
        <Descriptions.Item label="Gouvernorat">{record.governorate}</Descriptions.Item>
      </Descriptions>
    </Card>
  );
};

export default ViewPostalCode;
