import { useState, useRef } from "react";
import { ProTable } from "@ant-design/pro-components";
import { Button, Popconfirm, Tooltip, Space, Input, Typography, Card, Modal } from "antd";
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined, SearchOutlined } from "@ant-design/icons";
import { datas } from "./datas";
import CreateCommercial from "./create";
import EditCommercial from "./edit";
import ViewCommercial from "./view";

function CommercialTeam() {
  const tableRef = useRef();
  const [dataSource, setDataSource] = useState(datas);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [viewModalVisible, setViewModalVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState(null);
  const [viewingRecord, setViewingRecord] = useState(null);

  const handleEdit = (record) => {
    setEditingRecord(record);
    setEditModalVisible(true);
  };

  const handleView = (record) => {
    setViewingRecord(record);
    setViewModalVisible(true);
  };

  const columns = [
    { title: "Nom", dataIndex: "name", key: "name" },
    { title: "Chef d'équipe", dataIndex: "chef", key: "chef" },
    { title: "Membres", dataIndex: "membres", key: "membres", render: (membres) => membres.join(", ") },
    { title: "Alias", dataIndex: "alias", key: "alias" },
    { title: "Objectif", dataIndex: "objectif", key: "objectif" },
    {
      title: "Actions",
      key: "action",
      align: "center",
      render: (_, record) => (
        <Space>
        <Tooltip title="Voir">
            <Button type="link" icon={<EyeOutlined />} onClick={() => {
              setViewingRecord(record);
              setViewModalVisible(true);
            }} />
          </Tooltip>
          <Tooltip title="Modifier">
            <Button type="link" style={{ color: "#f5b041" }} icon={<EditOutlined />} onClick={() => handleEdit(record)} />
          </Tooltip>
          <Tooltip title="Supprimer">
            <Popconfirm title="Confirmer la suppression ?" onConfirm={() => {
              const newDataSource = dataSource.filter(item => item.key !== record.key);
              setDataSource(newDataSource);
            }}>
              <Button type="link" style={{ color: "#ec7063" }} icon={<DeleteOutlined />} />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <Card title={<Typography.Title level={4}>Équipe Commerciale</Typography.Title>}
      extra={
        <Button type="primary" icon={<PlusOutlined />} onClick={() => setCreateModalVisible(true)}>
          Ajouter
        </Button>
      }
    >
      <ProTable
        actionRef={tableRef}
        columns={columns}
        dataSource={dataSource}
        rowKey="key"
        search={false}
        options={false}
      />

      {/* Create Modal */}
      <Modal
        title="Ajouter un Commercial"
        visible={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        footer={null}
      >
        <CreateCommercial onCancel={() => setCreateModalVisible(false)} setDataSource={setDataSource} />
      </Modal>

      {/* Edit Modal */}
      <Modal
        title="Modifier un Commercial"
        visible={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        footer={null}
      >
        <EditCommercial record={editingRecord} onCancel={() => setEditModalVisible(false)} setDataSource={setDataSource} />
      </Modal>

      {/* View Modal */}
      <Modal
        title="Voir un Commercial"
        visible={viewModalVisible}
        onCancel={() => setViewModalVisible(false)}
        footer={null}
      >
        <ViewCommercial record={viewingRecord} onCancel={() => setViewModalVisible(false)} />
      </Modal>
    </Card>
  );
}

export default CommercialTeam;