# Transport project frontend ( React 18 )

## Install the app

#### Install node.js

Use npm as package manager

Open the project folder & run those commands in your command line

```bash
npm install
npm run dev
```


## React Folder Structure

### 1- Apis Folder

contains all files related to the API by default 

- axiosInterceptor.js ( don't mess with this file )

###  2-  Assets Folder

Contains all assets files ( logo , images , documents ... )

### 3- Components Folder

Contains all components , by default :

* LayoutRoute ( don't mess with this file )

* Shared

Contains all shared components ( Navbar , Sidebar , Footer ) 

* Loader ( don't mess with this file )

* NotFound ( don't mess with this file )

* Unauthorized ( don't mess with this file )

### 4- Configs Folder

Contains all configurations files

### 5- Helpers Folder

Contains all helpers files in the application. 

### 6- Hooks Folder

Contains all custom hooks created for your components

### 7- Layouts Folder

Contains the App layouts
* AdminLayout
* UserLayout
* GuestLayout

### 8- Pages Folder

contains all the App pages organized by the actors for example :

* Admin/DashboardAdmin
* User/DashboardUser
* Guest/accueil
* Auth/Login

### 9- Redux Folder

this folder contains the redux configurations :
* Actions
* Constants 
* Reducers 
* store.jsx ( don't mess with this file )

### 10- Language Folder

contains all languages files
###  Packages added for module CRM 
Below is a list of the installed packages along with their descriptions:

@ant-design/plots - A React chart library based on Ant Design and G2Plot.

@emotion/styled - A library for writing CSS-in-JS styles in React components.

@fullcalendar/core - The core package for FullCalendar, used for event and scheduling features.

@mui/icons-material - A collection of Material UI icons for use in React applications.

@mui/material - The official Material UI component library for building UI with React.

apexcharts - A modern charting library that supports interactive and responsive charts.

moment - A JavaScript library for parsing, validating, manipulating, and formatting dates.

react-beautiful-dnd - A drag-and-drop library for React with great user experience.

recharts - A charting library built with React and D3.

sonner - A simple and customizable toast notification library for React.

uuid - A package for generating unique identifiers (UUIDs).


Installation: 

npm install @ant-design/plots @emotion/styled @fullcalendar/core @mui/icons-material @mui/material apexcharts moment  react-beautiful-dnd recharts sonner uuid
