import { lazy } from "react";
import AuthGuard from "../../../helpers/Guards/AuthGuard";
import RoleGuard from "../../../helpers/Guards/RoleGuard";

// Types d'activités Pages
import Activities from "../../../pages/Admin/crm/typesactivities";

// Plans d'activités Pages
import PlansActivities from "../../../pages/Admin/crm/plansactivities";

// Status des activités Pages
import ActivityStatuses from "../../../pages/Admin/crm/statusactivities";

// activités pages
import Tasks from "../../../pages/Admin/crm/activities";

// contacts pages
import Contacts from "../pages/contacts";

// pipeline pages

import ManagePipeline from "../../../pages/Admin/crm/pipelines/DataFetch";

// equipecommerciale

import EquipeCommerce from "../pages/equipecommerciale";
// landingpage

import Dashboard from "../../../pages/Admin/crm/pipelines/Dashboard";

// events

import Events from "../pages/events/index";

// events

import Dashboardevents from "../pages/events/dashboardevents";

// gouvernaurats

import Gouvernorats from "../pages/governaurats";

// delegation

import Delegation from "../pages/delegations";

// code-postaux

import CodePostaux from "../pages/codes_postaux";

// train-locaux

import TrainLocaux from "../../../pages/Admin/crm/trains_locaux/index";

// demandes

import Demandes from "../pages/demandes-locals/index";

// stat_terain

import Statterain from "../../../pages/Admin/crm/statitiques";

// gestion profils diaspora

import ProfilDias from "../../../pages/Admin/crm/profiles_dias";

// Interraction  avec diaspora

import InteractionDias from "../../../pages/Admin/crm/interactions_dias";

// Reporting and analysing  diaspora

import AnalyseDias from "../../../pages/Admin/crm/reporting_dias";




//gestion des calendriers

import Calendrier from  "../../../pages/Admin/crm/calendrier/index"
//entreprises etrangeres
const Companies = lazy(() => import("../pages/companies"));
const CompanyDashboard = lazy(() => import("../pages/companyDashbord"));
import InteractionHistory from "../pages/interactionHistory";
import DemandeIncident from "../pages/demandes";
import CompanyActivity from "../pages/companyActivity";
import Secteur from "../pages/companySecteur";
import Pays from "../../../pages/Admin/crm/pays";
import GroupPays from "../pages/groupPays";
import Departement from "../pages/departements";
import Contact from "../pages/contacts_entreprises/contacts";
import Company from "../pages/contacts_entreprises/entreprises";
import SeminaireMP from "../../../pages/Admin/crm/seminaires/seminaireMp";
import SeminaireMS from "../../../pages/Admin/crm/seminaires/seminaireMS";
import Salons from "../../../pages/Admin/crm/seminaires/salon";
import Media from "../../../pages/Admin/crm/seminaires/media";
import SeminaireDelegation from "../../../pages/Admin/crm/seminaires/delegation";
import Ctes from "../../../pages/Admin/crm/seminaires/cte";
import DemarchageDirect from "../../../pages/Admin/crm/seminaires/demarchageDirect";
import AutreSourcesContact from "../../../pages/Admin/crm/seminaires/autreSourcesContact";
import Visite from "../../../pages/Admin/crm/seminaires/visite";
import Projet from "../../../pages/Admin/crm/projets";




//gestion scoring

import Scoring from '../../../pages/Admin/crm/scorings/index'


//gestion gestion documents

import GestionBiblitheques from '../pages/gestion_documents/index'


//gestion EmailsTemplates

import GestionEmailsTemplates from '../pages/gestion_emails/index'


//gestion category

import GestionCategory from '../../../pages/Admin/crm/params_categories/index'


const isAuthenticated = true;
const userRole = "admin";

const routes = [
  {
    path: "crm/dashboard",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <Dashboard />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/typeactivities",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <Activities />
        </RoleGuard>
      </AuthGuard>
    ),
  },

  // Routes pour les Plans d'activités
  {
    path: "crm/plansactivities",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <PlansActivities />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  // Routes pour les Status des activités
  {
    path: "crm/activitystatuses",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <ActivityStatuses />
        </RoleGuard>
      </AuthGuard>
    ),
  },

  {
    path: "crm/leads",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <Tasks />
        </RoleGuard>
      </AuthGuard>
    ),
  },
   {
    path: "crm/gestion_documents",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <GestionBiblitheques />
        </RoleGuard>
      </AuthGuard>
    ),
  },
 {
    path: "crm/templates_emails",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <GestionEmailsTemplates />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/contacts",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <Contacts />
        </RoleGuard>
      </AuthGuard>
    ),
  },

  {
    path: "crm/piplines",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <ManagePipeline />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/equipescommerciales",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <EquipeCommerce />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/dashboardevents",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <Dashboardevents />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/events",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <Events />
        </RoleGuard>
      </AuthGuard>
    ),
  },

  {
    path: "crm/events",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <Events />
        </RoleGuard>
      </AuthGuard>
    ),
  },

  {
    path: "crm/gouvernorats",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <Gouvernorats />
        </RoleGuard>
      </AuthGuard>
    ),
  },


  {
    path: "crm/delegation",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <Delegation />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/code-postaux",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <CodePostaux />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/train-locaux",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <TrainLocaux />
        </RoleGuard>
      </AuthGuard>
    ),
  },

  {
    path: "crm/demandes",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <Demandes />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/stat_terain",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <Statterain />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/companies-dashboard",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <CompanyDashboard />
        </RoleGuard>
      </AuthGuard>
    ),
  },
    {
    path: "crm/entreprises-etrangeres",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <Companies />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/entreprises-interactions",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <InteractionHistory />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/entreprises-demandes",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <DemandeIncident />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/parametres/activites",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <CompanyActivity />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/parametres/secteurs",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <Secteur />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/parametres/pays",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <Pays />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/parametres/group-pays",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <GroupPays />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/parametres/departements",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <Departement />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/entreprises-contacts",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <Contact />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/entreprises",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <Company />
        </RoleGuard>
      </AuthGuard>
    ),
  },

  // Gestion des profils diaspora
  {
    path: "crm/gestion_profil_dias",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <ProfilDias />
        </RoleGuard>
      </AuthGuard>
    ),
  },

  // Interaction avec diaspora
  {
    path: "crm/interraction_dias",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <InteractionDias />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  // Reporting et analyse
  {
    path: "crm/report_analyse_dias",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <AnalyseDias />
        </RoleGuard>
      </AuthGuard>
    ),
  },

  {
    path: "crm/seminaires/pays",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <SeminaireMP />
        </RoleGuard>
      </AuthGuard>
    ),
  },
   {
    path: "crm/seminaires/secteur",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <SeminaireMS />
        </RoleGuard>
      </AuthGuard>
    ),
  },
    {
    path: "crm/seminaires/salons",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <Salons />
        </RoleGuard>
      </AuthGuard>
    ),
  },

     {
    path: "crm/seminaires/media",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <Media />
        </RoleGuard>
      </AuthGuard>
    ),
  },
    {
      path: 'crm/interraction_dias',
      element: (
        <AuthGuard isAuthenticated={isAuthenticated}>
          <RoleGuard userRole={userRole} allowedRoles={['admin']}>
            <InteractionDias />
          </RoleGuard>
        </AuthGuard>
      ),
    },
    // Reporting et analyse
    {
      path: 'crm/report_analyse_dias',
      element: (
        <AuthGuard isAuthenticated={isAuthenticated}>
          <RoleGuard userRole={userRole} allowedRoles={['admin']}>
            <AnalyseDias />
          </RoleGuard>
        </AuthGuard>
      ),
    },

     // Scoring
    {
      path: 'crm/gestion_scorring',
      element: (
        <AuthGuard isAuthenticated={isAuthenticated}>
          <RoleGuard userRole={userRole} allowedRoles={['admin']}>
            <Scoring />
          </RoleGuard>
        </AuthGuard>
      ),
    },
    {
    path: "crm/seminaires/ctes",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <Ctes />
        </RoleGuard>
      </AuthGuard>
    ),
  },
     {
    path: "crm/seminaires/delegations",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <SeminaireDelegation />
        </RoleGuard>
      </AuthGuard>
    ),
  },
   {
    path: "crm/seminaires/demarchage-direct",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <DemarchageDirect />
        </RoleGuard>
      </AuthGuard>
    ),
  },
 {
    path: "crm/seminaires/autre-source",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <AutreSourcesContact />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  
 {
    path: "crm/gestion_calendrier",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <Calendrier />
           </RoleGuard>
      </AuthGuard>
    ),
  },
   {
    path: "crm/seminaires/visites",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
        <Visite />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/projets",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
        <Projet />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/entreprises-statistiques",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
        <CompanyDashboard />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  
  {
    path: "crm/parametres/category",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
        <GestionCategory />
        </RoleGuard>
      </AuthGuard>
    ),
  },

 
];

export default routes;
