import { ModalForm } from "@ant-design/pro-components";
import { message } from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import TierInfoGeneralForm from "@src/modules/finance/components/TierInfoGeneralForm";

const AddProspectModal = ({ open, onCancel }) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (values) => {
    setLoading(true);
    try {
      const submissionData = {
        ...values,
      };
      await new Promise((resolve) => setTimeout(resolve, 2000));

      message.success("Prospect ajouté avec succès !");
    } catch (error) {
      console.log(error);
      message.error("Erreur lors de l'ajout du prospect !");
    } finally {
      setLoading(false);
      onCancel();
    }
  };

  return (
    <ModalForm
      title={t("vente.prospect.add")}
      open={open}
      onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.save"),
          resetText: t("common.actions.cancel"),
        },
        submitButtonProps: {
          loading,
        },
      }}
      modalProps={{
        destroyOnClose: true,
      }}
      width={900}
    >
      <div className="px-2">
        <TierInfoGeneralForm disabled={false} tier={null} />
      </div>
    </ModalForm>
  );
};

export default AddProspectModal;
