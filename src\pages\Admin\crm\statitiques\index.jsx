import { useState } from 'react';
import { Column, Pie } from '@ant-design/plots'; 
import { Card, Col, Row, Select, Typography } from 'antd';

function Stats() {
  const data = [
    { gouvernorat: 'Tunis', type: 'Terrain', value: 10 },
    { gouvernorat: 'Tunis', type: 'Local', value: 15 },
    { gouvernorat: 'Tunis', type: 'Terrain + Local', value: 5 },
    { gouvernorat: 'Sfax', type: 'Terrain', value: 8 },
    { gouvernorat: 'Sfax', type: 'Local', value: 12 },
    { gouvernorat: 'Sfax', type: 'Terrain + Local', value: 7 },
    { gouvernorat: 'Sousse', type: 'Terrain', value: 6 },
    { gouvernorat: 'Sousse', type: 'Local', value: 10 },
    { gouvernorat: 'Sousse', type: 'Terrain + Local', value: 4 },
  ];
  const data2 = [
    { sector: 'Commerce', type: 'Terrain', value: 20 },
    { sector: 'Commerce', type: 'Local', value: 25 },
    { sector: 'Commerce', type: 'Terrain + Local', value: 12 },
    { sector: 'Industrie', type: 'Terrain', value: 15 },
    { sector: 'Industrie', type: 'Local', value: 20 },
    { sector: 'Industrie', type: 'Terrain + Local', value: 10 },
    { sector: 'Agriculture', type: 'Terrain', value: 30 },
    { sector: 'Agriculture', type: 'Local', value: 18 },
    { sector: 'Agriculture', type: 'Terrain + Local', value: 8 },
  ];

  const [selectedSector, setSelectedSector] = useState('All');

  const filteredData2 = selectedSector === 'All' 
    ? data2 
    : data2.filter(item => item.sector === selectedSector);
  const config = {
    data,
    xField: 'gouvernorat',
    yField: 'value',
    seriesField: 'type', 
    isStack: true, 
    color: ['#69c0ff', '#95de64', '#ff7875'], 
    label: {
      position: 'middle',
      style: { fill: '#fff', fontWeight: 'bold' },
    },
    columnStyle: {
      radius: [4, 4, 0, 0],
    },
    legend: {
      position: 'top',
      marker: { symbol: 'square' },
    },
    interactions: [{ type: 'element-active' }],
  };
  const config2 = {
    data: filteredData2,
    angleField: 'value',
    colorField: 'type',
    radius: 0.8,
    color: ['#ff6347', '#32cd32', '#ffd700'],
    label: {
      type: 'spider',
      labelHeight: 28,
      content: '{percentage}',
      style: { fontSize: 12 },
    },
    interactions: [{ type: 'element-active' }],
  };
  return (
    <>
    <Card bordered title={<Typography.Title level={4}>Nombre de Terrains et Locaux par Gouvernorat</Typography.Title>}>
      <Column {...config} />

      <Row gutter={16} style={{ marginTop: 20 }}>
        <Col span={24}>
          <Select 
            defaultValue="All" 
            style={{ width: 200 }} 
            onChange={value => setSelectedSector(value)}
          >
            <Select.Option value="All">All Sectors</Select.Option>
            <Select.Option value="Commerce">Commerce</Select.Option>
            <Select.Option value="Industrie">Industrie</Select.Option>
            <Select.Option value="Agriculture">Agriculture</Select.Option>
          </Select>
        </Col>
      </Row>

      <Card bordered style={{ marginTop: 20 }} title={<Typography.Title level={4}>Répartition des Demandes par Secteur</Typography.Title>}>
        <Pie {...config2} />
      </Card>
    </Card>





    </>
  );
}

export default Stats;
