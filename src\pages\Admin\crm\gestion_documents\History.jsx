import React from 'react';
import { ProTable } from '@ant-design/pro-components';
import { Button, Input, Tag } from 'antd';
import dayjs from 'dayjs';

const History = ({ history }) => {
  const columns = [
    {
      title: 'Action',
      dataIndex: 'action',
      key: 'action',
      render: (action) => {
        const colorMap = {
          Created: 'green',
          Edited: 'blue',
          Viewed: 'cyan',
          Deleted: 'red',
          Downloaded: 'purple',
        };
        return <Tag color={colorMap[action] || 'default'}>{action}</Tag>;
      },
    },
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      render: (date) => (date ? dayjs(date).format('YYYY-MM-DD HH:mm:ss') : '-'),
      sorter: (a, b) => dayjs(a.date).unix() - dayjs(b.date).unix(),
    },
    {
      title: 'Person',
      dataIndex: 'person',
      key: 'person',
      render: (person) => person || '-',
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm }) => (
        <div style={{ padding: 8 }}>
          <Input
            placeholder="Search person"
            value={selectedKeys[0]}
            onChange={(e) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={confirm}
            style={{ width: 188, marginBottom: 8, display: 'block' }}
          />
          <Button type="primary" onClick={confirm} size="small" style={{ width: 90 }}>
            Search
          </Button>
        </div>
      ),
    },
  ];

  return (
    <ProTable
      columns={columns}
      dataSource={history}
      rowKey="id"
      pagination={{ pageSize: 5 }}
      search={false}
      options={false}
      size="small"
    />
  );
};

export default History;