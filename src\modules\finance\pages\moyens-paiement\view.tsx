import { useTranslation } from "react-i18next";
import MoyenPaiementForm from "../../components/MoyenPaiementForm";
import { Modal } from "antd";

const ShowMoyenPaiementModal = ({ open, onCancel, moyenPaiement }) => {
  const { t } = useTranslation();

  return (
    <Modal
      width={800}
      title={t("tvas.view")}
      open={open}
      onOk={onCancel}
      onCancel={onCancel}
    >
      <MoyenPaiementForm disabled={true} moyenPaiement={moyenPaiement} />
    </Modal>
  );
};

export default ShowMoyenPaiementModal;
