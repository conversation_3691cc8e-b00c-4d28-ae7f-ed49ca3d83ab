// src/redux/actions/dataActions.js

import axiosInstance from '../../apis/axiosInterceptor';
import { FETCH_LOGIN_FAILURE, FETCH_LOGIN_START, FETCH_LOGIN_SUCCESS, FETCH_REGISTER_FAILURE, FETCH_REGISTER_START, FETCH_REGISTER_SUCCESS } from '../constants/authConstants';
import { hideLoader, showLoader } from './loadingActions';
import { toast } from 'sonner';
  
  
  export const login = (user) => async (dispatch) => {

    

    dispatch(({ type : FETCH_LOGIN_START}));
    dispatch(showLoader())
    try {
      
      const response = await axiosInstance.post('/auth/login',user);
      console.log(response);
      
     
      dispatch(({ type : FETCH_LOGIN_SUCCESS , payload : response.data.data}));
      localStorage.setItem('token', response.data.data.token)
      dispatch(hideLoader())

return true;

    } catch (error) {
        toast.error("Verifier vos coordonnes");
      console.log(error);
      dispatch(({ type : FETCH_LOGIN_FAILURE}));
      dispatch(hideLoader())
      return false;
    }
  };

  export const register = (user) => async (dispatch) => {
    dispatch(({ type : FETCH_REGISTER_START}));
    dispatch(showLoader())
    try {
      
        const response = await axiosInstance.post('/auth/register',user);
      console.log(response);
      
     
      dispatch(({ type : FETCH_REGISTER_SUCCESS}));
      dispatch(hideLoader())
    } catch (error) {
        toast.error("Erreur lors de la récupération des catégories");
      console.log(error);
      dispatch(({ type : FETCH_REGISTER_FAILURE}));
      dispatch(hideLoader())
    }
  };
  