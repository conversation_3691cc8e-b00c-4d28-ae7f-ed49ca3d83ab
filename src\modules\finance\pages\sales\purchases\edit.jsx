import { ModalForm } from "@ant-design/pro-components";
import { <PERSON><PERSON>, <PERSON>, Col, DatePicker, Form, Input, InputNumber, Popconfirm, Row, Select, Table } from "antd";

import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

import PurchaseInfo from "@src/modules/finance/components/purchases/PurchaseInfo";
import PurchaseItem from "@src/modules/finance/components/purchases/PurchaseItem";

function EditQuoteForm({ open, onCancel ,record}){
    const {t} = useTranslation();
    const [loading, setLoading] = useState(false);
    const [form] = Form.useForm();
    
   
  return (
    <ModalForm
      title={t("cmd.edit")}  
      form={form}
      open={open}
      width="90%"
      //   onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.edit"),
          resetText: t("common.actions.cancel"),
        },
      }}
    >    
      <PurchaseInfo form={form}  record={record} open={open} />
      <PurchaseItem  form={form}   record={record} open={open} />
    </ModalForm>
  );
}
export default EditQuoteForm;