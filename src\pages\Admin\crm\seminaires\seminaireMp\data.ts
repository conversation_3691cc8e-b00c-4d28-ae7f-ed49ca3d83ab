import dayjs from "dayjs";

const seminaires = [
  {
    id: 1,
    intitule: "100173TF01",
    pays_id: 1,
    region: "nom de la région ",
    responsable : "resposable Fipa",
    propose_par : "Proposé par",
    objectifs : " Ad distinctio cupiditate et accusantium perferendis quo nihil voluptas ea delectus corrupti ut praesentium quisquam sit earum obcaecati sit doloribus culpa. ",
    theme: "Test",
    lieu: "lieu du séminaire ",
    action_conjointe : 1 ,
    date_debut: dayjs("2025-01-10"),
    date_fin: dayjs("2025-01-10"),
    initialSteps: [
      {
        id: 1,
        title: "Proposée",
        done: false,
        motif: false,
        reason: "",
        status: "finish",
      },
      {
        id: 2,
        title: "Programmée",
        done: false,
        motif: false,
        reason: "",
        status: "finish",
      },
      {
        id: 3,
        title: "Réalis<PERSON>",
        done: false,
        motif: false,
        reason: "",
        status: "wait",
      },
      {
        id: 4,
        title: "<PERSON><PERSON>",
        done: false,
        motif: true,
        reason: "",
        status: "finish",
      },
      {
        id: 5,
        title: "Annulée",
        done: false,
        motif: true,
        reason: "",
        status: "finish",
      },
      {
        id: 6,
        title: "Non programmée",
        done: false,
        motif: false,
        reason: "",
        status: "wait",
      },
      {
        id: 7,
        title: "Validée",
        done: false,
        motif: false,
        reason: "",
        status: "wait",
      },
    ],
  },
    {
    id: 2,
    intitule: "100174TF02",
    pays_id: 2,
    region: "Région Centre",
    responsable: "Mme Khelifa",
    propose_par: "Direction Centrale",
    objectifs:
      "Facilis asperiores quisquam voluptatem eos distinctio et tenetur. Amet tempora adipisci hic vitae sit.",
    theme: "Innovation & Industrie",
    lieu: "Sfax",
    action_conjointe: 0,
    date_debut: dayjs("2025-03-05"),
    date_fin: dayjs("2025-03-07"),
    initialSteps: [
      { id: 1, title: "Proposée", done: true, motif: false, reason: "", status: "finish" },
      { id: 2, title: "Programmée", done: true, motif: false, reason: "", status: "finish" },
      { id: 3, title: "Réalisée", done: false, motif: false, reason: "", status: "wait" },
      { id: 4, title: "Reportée", done: false, motif: false, reason: "", status: "wait" },
      { id: 5, title: "Annulée", done: false, motif: false, reason: "", status: "wait" },
      { id: 6, title: "Non programmée", done: false, motif: false, reason: "", status: "wait" },
      { id: 7, title: "Validée", done: false, motif: false, reason: "", status: "wait" },
    ],
  },
  {
    id: 3,
    intitule: "100175TF03",
    pays_id: 3,
    region: "Nord-Ouest",
    responsable: "Mr. Trabelsi",
    propose_par: "Partenaire Local",
    objectifs:
      "Consectetur nemo quod sequi. Doloremque explicabo aliquid quos earum in iusto laboriosam esse.",
    theme: "Agritech",
    lieu: "Béja",
    action_conjointe: 1,
    date_debut: dayjs("2025-04-15"),
    date_fin: dayjs("2025-04-16"),
    initialSteps: [
      { id: 1, title: "Proposée", done: true, motif: false, reason: "", status: "finish" },
      { id: 2, title: "Programmée", done: true, motif: false, reason: "", status: "finish" },
      { id: 3, title: "Réalisée", done: true, motif: false, reason: "", status: "finish" },
      { id: 4, title: "Reportée", done: false, motif: false, reason: "", status: "wait" },
      { id: 5, title: "Annulée", done: false, motif: false, reason: "", status: "wait" },
      { id: 6, title: "Non programmée", done: false, motif: false, reason: "", status: "wait" },
      { id: 7, title: "Validée", done: true, motif: false, reason: "", status: "finish" },
    ],
  },
   {
    id: 4,
    intitule: "100176TF04",
    pays_id: 4,
    region: "Sud",
    responsable: "Mme Ben Youssef",
    propose_par: "Ambassade",
    objectifs:
      "Quis velit eum atque dolores fugit. Modi voluptates tempora eveniet doloribus nesciunt.",
    theme: "Energie renouvelable",
    lieu: "Tataouine",
    action_conjointe: 1,
    date_debut: dayjs("2025-05-12"),
    date_fin: dayjs("2025-05-14"),
    initialSteps: [
      { id: 1, title: "Proposée", done: true, motif: false, reason: "", status: "finish" },
      { id: 2, title: "Programmée", done: true, motif: false, reason: "", status: "finish" },
      { id: 3, title: "Réalisée", done: true, motif: false, reason: "", status: "finish" },
      { id: 4, title: "Reportée", done: false, motif: false, reason: "", status: "wait" },
      { id: 5, title: "Annulée", done: false, motif: false, reason: "", status: "wait" },
      { id: 6, title: "Non programmée", done: false, motif: false, reason: "", status: "wait" },
      { id: 7, title: "Validée", done: false, motif: false, reason: "", status: "wait" },
    ],
  },
  {
    id: 5,
    intitule: "100177TF05",
    pays_id: 1,
    region: "Grand Tunis",
    responsable: "Dr. Mabrouk",
    propose_par: "Direction Technique",
    objectifs:
      "Iusto porro repellendus molestiae earum. Vitae eveniet omnis laboriosam voluptatibus sunt nisi.",
    theme: "Tech & Digital",
    lieu: "Tunis",
    action_conjointe: 0,
    date_debut: dayjs("2025-06-01"),
    date_fin: dayjs("2025-06-02"),
    initialSteps: [
      { id: 1, title: "Proposée", done: true, motif: false, reason: "", status: "finish" },
      { id: 2, title: "Programmée", done: false, motif: false, reason: "", status: "wait" },
      { id: 3, title: "Réalisée", done: false, motif: false, reason: "", status: "wait" },
      { id: 4, title: "Reportée", done: false, motif: false, reason: "", status: "wait" },
      { id: 5, title: "Annulée", done: false, motif: false, reason: "", status: "wait" },
      { id: 6, title: "Non programmée", done: false, motif: false, reason: "", status: "wait" },
      { id: 7, title: "Validée", done: false, motif: false, reason: "", status: "wait" },
    ],
  },
  {
    id: 6,
    intitule: "100178TF06",
    pays_id: 3,
    region: "Nord-Est",
    responsable: "Mr. Haddad",
    propose_par: "Ministère partenaire",
    objectifs:
      "Ullam pariatur quas maxime dolorem dolorum. Sequi minima quos libero laborum.",
    theme: "Investissement & Financement",
    lieu: "Bizerte",
    action_conjointe: 1,
    date_debut: dayjs("2025-07-10"),
    date_fin: dayjs("2025-07-11"),
    initialSteps: [
      { id: 1, title: "Proposée", done: true, motif: false, reason: "", status: "finish" },
      { id: 2, title: "Programmée", done: true, motif: false, reason: "", status: "finish" },
      { id: 3, title: "Réalisée", done: false, motif: false, reason: "", status: "wait" },
      { id: 4, title: "Reportée", done: false, motif: false, reason: "", status: "wait" },
      { id: 5, title: "Annulée", done: false, motif: false, reason: "", status: "wait" },
      { id: 6, title: "Non programmée", done: false, motif: false, reason: "", status: "wait" },
      { id: 7, title: "Validée", done: false, motif: false, reason: "", status: "wait" },
    ],
  },
];
const pays = [
  { id: 1, nom: "France" },
  { id: 2, nom: "Italie" },
  { id: 3, nom: "Allemagne" },
  { id: 4, nom: "Espagne" },
  { id: 5, nom: "États-Unis" },
  { id: 6, nom: "Canada" },
  { id: 7, nom: "Algérie" },
  { id: 8, nom: "Maroc" },
  { id: 9, nom: "Libye" },
  { id: 10, nom: "Turquie" },
  { id: 11, nom: "Arabie Saoudite" },
  { id: 12, nom: "Émirats arabes unis" },
  { id: 13, nom: "Qatar" },
  { id: 14, nom: "Égypte" },
  { id: 15, nom: "Chine" },
  { id: 16, nom: "Japon" },
  { id: 17, nom: "Corée du Sud" },
  { id: 18, nom: "Belgique" },
  { id: 19, nom: "Suisse" },
  { id: 20, nom: "Russie" },
];
export { seminaires, pays };
