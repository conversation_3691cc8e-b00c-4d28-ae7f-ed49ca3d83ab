import { EyeFilled, UploadOutlined } from "@ant-design/icons";
import { ModalForm } from "@ant-design/pro-components";
import { Button, Card, Col, DatePicker, Form, Input, Row, Upload } from "antd";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

function ResultatForm({ open, onCancel, record }) {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  
  return (
    <ModalForm
      title={t("Résultat de l'action")}
      form={form}
      open={open}
    
      modalProps={{
        style: { top: 20 },
      }}
      //   onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.save"),
          resetText: t("common.actions.cancel"),
        },
      }}
    >
      <Card className="mt-2">
        <Row gutter={16} style={{ marginBottom: "10px" }}>
          <Col span={12}>
            <Form.Item label="Nombre de réponses positives obtenues">
              <Input />
            </Form.Item>
          </Col>
          <Col span={12}></Col>
            <Col span={24}>
            <Form.Item label="Explication">
              <Input.TextArea />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="Résultat de l'action">
              <Input.TextArea />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="Evaluation et appréciation de l'action">
              <Input.TextArea />
            </Form.Item>
          </Col>
        
          <Col span={12}>
            <Form.Item label="Total contacts démarchage Réalisés">
              <Input />
            </Form.Item>
          </Col>
        </Row>
      </Card>
    </ModalForm>
  );
}
export default ResultatForm;
