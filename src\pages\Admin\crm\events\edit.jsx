import React, { useEffect } from "react";
import { Modal, Form, Input, DatePicker, Button } from "antd";
import moment from "moment";

const edit = ({ visible, onEdit, onCancel, event }) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (event) {
      form.setFieldsValue({
        ...event,
        date: moment(event.date, "YYYY-MM-DD")
      });
    }
  }, [event, form]);

  return (
    <Modal
      title="Modifier l'Événement"
      visible={visible}
      onCancel={() => {
        form.resetFields();
        onCancel();
      }}
      footer={null}
    >
      <Form form={form} layout="vertical" onFinish={(values) => onEdit({ ...values, date: values.date.format("YYYY-MM-DD"), id: event.id })}>
        <Form.Item name="title" label="Titre" rules={[{ required: true, message: "Veuillez entrer le titre" }]}> 
          <Input />
        </Form.Item>
        <Form.Item name="sector" label="Secteur" rules={[{ required: true, message: "Veuillez entrer le secteur" }]}> 
          <Input />
        </Form.Item>
        <Form.Item name="country" label="Pays" rules={[{ required: true, message: "Veuillez entrer le pays" }]}> 
          <Input />
        </Form.Item>
        <Form.Item name="date" label="Date" rules={[{ required: true, message: "Veuillez sélectionner la date" }]}> 
          <DatePicker style={{ width: "100%" }} />
        </Form.Item>
        <Form.Item style={{ textAlign: 'right' }}>
          <Button type="primary" htmlType="submit" block>
            Modifier
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default edit;