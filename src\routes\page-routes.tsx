import GuestLayout from '@src/layouts/GuestLayout';
import { lazy } from 'react';

// Lazy-loaded pages
const ProfilePage = lazy(() => import('../pages/Admin/Profile'));
const Home = lazy(() => import('../pages/Home/index.jsx'));

const routes = [
  {
    path: '/',
    element: (<Home/>),
  },
  {
    path: '/home',
    element: (
      <GuestLayout>
        <Home/>
      </GuestLayout>
    ),
  },
  {
    path: '/profile',
    element: (<ProfilePage />),
  },
];

export default routes;
