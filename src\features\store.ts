import { configureStore } from "@reduxjs/toolkit";
import auth from "./auth/authSlice";
import loader from "./loadingSlice";
import parameterStore from "@/modules/parameter/features/parameterStore";
import financeStore from "@/modules/finance/features/financeStore";
import projectStore from "@src/modules/Project/features/projectStore";

export const store = configureStore({
  reducer: {
    loader,
    auth,
    ... parameterStore,
    ... financeStore,
    ... projectStore
  },
});

// Infer the `RootState` type from the store itself
export type RootState = ReturnType<typeof store.getState>;
// Infer the `AppDispatch` type from the store itself
export type AppDispatch = typeof store.dispatch;
