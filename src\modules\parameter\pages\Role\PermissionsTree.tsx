import React, { useEffect, useMemo, useState } from "react";
import { Input, Tree, Tooltip } from "antd";
import type { DataNode } from "antd/es/tree";
import { IPermission } from "./RolePermissions";
import { SearchOutlined } from '@ant-design/icons';
const getParentKey = (key: React.Key, tree: DataNode[]): React.Key => {
  let parentKey: React.Key;
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i];
    if (node.children) {
      if (node.children.some((item) => item.key === key)) {
        parentKey = node.key;
      } else if (getParentKey(key, node.children)) {
        parentKey = getParentKey(key, node.children);
      }
    }
  }
  return parentKey!;
};

const PermissionsTree: React.FC<{ data: IPermission[]; setCheckList: any; checkList:string[] }> = ({
  data,
  setCheckList,
  checkList
}) => {
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [autoExpandParent, setAutoExpandParent] = useState(true);
  const [permissions, setPermissions] = useState<IPermission[]>();

  const onExpand = (newExpandedKeys: React.Key[]) => {
    setExpandedKeys(newExpandedKeys);
    setAutoExpandParent(false);
  };

  const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    setPermissions(
      data
        .map((item) => {
          const filteredChildren = item.children.filter(
            (child) =>
              child.title.toUpperCase().indexOf(value.toUpperCase()) > -1
          );
          return { ...item, children: filteredChildren };
        })
        .filter((item) => item.children.length)
    );
    if (value !== "") {
      setExpandedKeys(permissions.map((item) => item.key));
    } else {
      setExpandedKeys([]);
    }
  };
  useEffect(()=>{
    setPermissions(data)
  },[data])
  return (
    <div>
      <Input
        style={{ marginBottom: 8 }}
        placeholder=" Rechercher"
        onChange={onChange}
        allowClear
        prefix={<SearchOutlined className="text-[#BDC3C7]" />}
      />
      {permissions&&<Tree<any>
          showIcon
          checkable
        onExpand={onExpand}
        expandedKeys={expandedKeys}
        autoExpandParent={autoExpandParent}
        checkedKeys={checkList}
        onCheck={(e) => {
          setCheckList(e);
        }}
        // titleRender={(node)=><Tooltip title="description de permission...">{node.title}</Tooltip>}
        treeData={permissions.filter((item) => item.children.length !== 0)}
        selectable={false}
      />}
    </div>
  );
};

export default PermissionsTree;
