import React, { useEffect, useState } from "react";
import {
  Button,
  Space,
  Radio,
  Input,
  Breadcrumb,
  Card,
  Col,
  Row,
  Typography,
  Divider,
  Popconfirm,
  Tree,
  Spin,
  message,
  Badge,
} from "antd";
import { SearchOutlined } from "@ant-design/icons";
import type { DataNode, TreeProps } from "antd/es/tree";
import {
  ProTable,
  TableDropdown,
  ProColumns,
} from "@ant-design/pro-components";
import { useDispatch, useSelector } from "react-redux";
import { IPermission } from "@/modules/parameter/pages/Role/interfaces";
import { getPermissions } from "@/modules/parameter/features/permissions/permissionSlice";
import { getRoles } from "@/modules/parameter/features/roles/roleSlice";
import PermissionCreate from "./PermissionCreate";
import PermissionDetails from "./PermissionDetails";
import { RootState } from "@features/store";

const { Title } = Typography;
const Permissions = () => {
  const dispatch = useDispatch();
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [autoExpandParent, setAutoExpandParent] = useState(true);
  const [permissions, setPermissions] = useState<IPermission[]>();
  const [data, setData] = useState<IPermission[]>();
  const [node, setNode] = useState({});
  const [visibleDetails, setVisibleDetails] = useState<boolean>(false);
  const [visibleCreate, setVisibleCreate] = useState<boolean>(false);
  const [messageApi, contextHolder] = message.useMessage();

  const onSelect: TreeProps["onSelect"] = (selectedKeys, info) => {
    if (info.node["id"] !== undefined) {
      setNode(info.node);
      setVisibleDetails(true);
    }
  };

  const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    setPermissions(
      data
        .map((item) => {
          const filteredChildren = item.children.filter(
            (child) =>
              child.title.toUpperCase().indexOf(value.toUpperCase()) > -1
          );
          return { ...item, children: filteredChildren };
        })
        .filter((item) => item.children.length)
    );
    if (value !== "") {
      setExpandedKeys(permissions.map((item) => item.key));
    } else {
      setExpandedKeys([]);
    }
  };
  const onExpand = (newExpandedKeys: React.Key[]) => {
    setExpandedKeys(newExpandedKeys);
    setAutoExpandParent(false);
  };
  const allPermissions = useSelector(
    (state: RootState) => state.permission.data
  );

  useEffect(() => {
    if (allPermissions && typeof allPermissions === "object") {
      try {
        setData(
          Object.keys(allPermissions).reduce((acc, title, index) => {
            acc.push({
              title,
              key: title,
              children: allPermissions[title].map((item, cp) => {
                return {
                  id: item.id,
                  title: item.name,
                  key: title + "-" + item.id.toString(),
                  groupe: title,
                  related_permission: item.related_permission,
                };
              }),
            });
            return acc;
          }, [])
        );
        setPermissions(
          Object.keys(allPermissions).reduce((acc, title, index) => {
            acc.push({
              title,
              key: title,
              children: allPermissions[title].map((item, cp) => {
                return {
                  id: item.id,
                  title: item.label,
                  icon: (
                    <Badge
                      color="#1890FF"
                      count={item.related_permission?.length}
                    />
                  ),
                  name: item.name,
                  key: title + "-" + item.id.toString(),
                  groupe: title,
                  related_permission: item.related_permission,
                };
              }),
            });
            return acc;
          }, [])
        );
      } catch (e) {
        console.log(e);
      }
    }
  }, [allPermissions]);

  const loadData = async () => {
    if (!allPermissions) {
      await dispatch(getPermissions());
      console.log("permissions fetching");
    }
  };

  useEffect(() => {
    console.log("permissions mounted");
    loadData();
  }, []);

  return (
    <div>
      {contextHolder}
      <Card
        title={<Title level={4}>Gestion des permissions</Title>}
        bordered={false}
        // extra={
        //   <Button
        //     type="primary"
        //     onClick={() => {
        //       setVisibleCreate(true)
        //     }}
        //   >
        //     Ajouter
        //   </Button>
        // }
      >
        <div className="p-4">
          <Input
            style={{ marginBottom: 8 }}
            placeholder=" Rechercher"
            onChange={onChange}
            allowClear
            prefix={<SearchOutlined className="text-[#BDC3C7]" />}
          />
          {permissions ? (
            <Tree<any>
              showIcon
              className="mt-2 ml-5"
              onExpand={onExpand}
              onSelect={onSelect}
              expandedKeys={expandedKeys}
              autoExpandParent={autoExpandParent}
              treeData={permissions.filter(
                (item) => item.children.length !== 0
              )}
              selectable
            />
          ) : (
            <div className="text-center	h-80 mt-40">
              <Spin size="large" />
            </div>
          )}
        </div>
      </Card>
      <PermissionCreate
        visible={visibleCreate}
        setVisible={setVisibleCreate}
        permissions={data}
        messageApi={messageApi}
      />
      <PermissionDetails
        visible={visibleDetails}
        setVisible={setVisibleDetails}
        permissions={data}
        permission={node}
        messageApi={messageApi}
      />
    </div>
  );
};

export default Permissions;
