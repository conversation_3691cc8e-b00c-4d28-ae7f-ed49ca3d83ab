import { ModalForm } from "@ant-design/pro-components";
import {
  Card,
  Col,
  DatePicker,
  Form,
  Input,
  Typography,
  List,
  Radio,
  Row,
  Button,
  Space,
  Tag,
  Select,
} from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { companies, responsables } from "./data";
import TextArea from "antd/es/input/TextArea.js";
const { Text } = Typography;
import { MessageOutlined, PlusOutlined } from "@ant-design/icons";
import dayjs from "dayjs";
const { Title } = Typography;



function ActionDemandeForm({ open, onCancel, onSuccess }) {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);


  const [form] = Form.useForm();
  const [exchanges, setExchanges] = useState([]);

  const exchangeTypes = [
    { value: 'reunion', label: 'Réunion', icon: <PlusOutlined />, color: 'blue' },
    { value: 'assistance', label: 'Assistance', icon: <PlusOutlined />, color: 'green' },
    { value: 'intervention', label: 'Intervention', icon: <PlusOutlined />, color: 'red' },
  ];

  const onFinish = (values) => {
    const newExchange = {
      id: Date.now(),
      companyName: values.companyName,
      date: dayjs(values.date).format('DD/MM/YYYY'),
      type: values.type,
      description: values.description
    };

    setExchanges([...exchanges, newExchange]);
    form.resetFields();
  };

 
  return (
    <ModalForm
      title={t("Actions Correctives")}
      form={form}
      open={open}
      modalProps={{
        style: { top: 20 },
      }}
       onFinish={onFinish}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        resetButtonProps: { disabled: false },
        submitButtonProps: { hidden: true },
        searchConfig: {
          resetText: t("common.actions.cancel"),
        },
      }}
    >
      <Card className="mt-5">
      
          <Form.Item
            name="date"
            label="Date de l'action"
            //rules={[{ required: true, message: 'Ce champ est obligatoire' }]}
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="description"
            label="Action"
            rules={[{ required: true, message: 'Ce champ est obligatoire' }]}
          >
            <TextArea rows={4} placeholder="Action" />
          </Form.Item>

          <Button type="primary" htmlType="submit">
            Ajouter
          </Button>


     
      </Card>
      <List 
        dataSource={exchanges}
        renderItem={(item) => (
          <List.Item>
            <Card style={{ width: '100%' }}>
            
                <Tag color={"blue"}>
                {item.date}
                </Tag>
         
              <Text>{item.description}</Text>
            </Card>
          </List.Item>
        )}
      />
    </ModalForm>
  );
}
export default ActionDemandeForm;
