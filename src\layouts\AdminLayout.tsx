import { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";
import { Layout, Affix, Drawer, Space, Breadcrumb, Col } from "antd";
import Header from "./Header";
import Footer from "./Footer";
import "./layout.css";
import { HomeFilled } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import SidenavExample from "@/layouts/Sidenav.jsx";

const { Header: AntHeader, Content, Sider } = Layout;

function Main({ children }) {
  const [visible, setVisible] = useState(false);
  const [sidenavColor, setSidenavColor] = useState("#23537b");
  const [sidenavType, setSidenavType] = useState("transparent");
  const [collapseMenu, setCollapseMenu] = useState(
    localStorage.getItem("collapseMenu") == "true" ? true : false
  );
  const openDrawer = () => setVisible(!visible);
  const handleSidenavType = (type) => setSidenavType(type);
  const handleSidenavColor = (color) => setSidenavColor(color);
  const { t, i18n } = useTranslation();
  let { pathname } = useLocation();
  const pathSegments = pathname.split("/").filter((segment) => segment);
  const breadcrumbItems = [
    {
      href: "/accueil",
      title: (
        <Space>
          <HomeFilled />
        </Space>
      ),
    },
    ...pathSegments
      .filter((segment) => segment)
      .map((segment, index) => ({
        href: `/${pathSegments.slice(0, index + 1).join("/")}`,
        title: segment.charAt(0).toUpperCase() + segment.slice(1), // Capitalize the segment
      })),
  ];

  pathname = pathname.replace("/", "");

  useEffect(() => {}, [pathname]);

  return (
    <Layout className={`layout-dashboard`}>
      {/* <Toaster /> */}
      <Drawer
        title={false}
        placement={"left"}
        closable={false}
        onClose={() => setVisible(false)}
        open={visible}
        key={"left"}
        width={280}
        className={`drawer-sidebar`}
      >
        <Layout className={`layout-dashboard`}>
          <Sider
            trigger={null}
            width={290}
            theme="light"
            className={`sider-primary ant-layout-sider-primary ${
              sidenavType === "#fff" ? "active-route" : ""
            }`}
            style={{ background: sidenavType }}
          >
            <SidenavExample color={sidenavColor} />
          </Sider>
        </Layout>
      </Drawer>

      {collapseMenu ||
      pathname == "" ||
      pathname == "home" ||
      pathname == "profile" ? null : (
        <Sider
          breakpoint="lg"
          collapsedWidth="0"
          onCollapse={(collapsed, type) => {}}
          trigger={null}
          width={230}
          theme="light"
          className={`sider-primary ant-layout-sider-primary ${
            sidenavType === "#fff" ? "active-route" : ""
          }`}
          style={{ background: sidenavType }}
        >
          <SidenavExample color={sidenavColor} />
        </Sider>
      )}
      <Layout
        className={
          collapseMenu ||
          pathname == "" ||
          pathname == "home" ||
          pathname == "profile"
            ? ""
            : "ant-layout-margin-left"
        }
      >
        <Affix>
          <AntHeader className={"ant-header-fixed"}>
            <Header
              onPress={openDrawer}
              name={pathname}
              subName={pathname}
              handleSidenavColor={handleSidenavColor}
              handleSidenavType={handleSidenavType}
              collapseMenu={collapseMenu}
              setCollapseMenu={setCollapseMenu}
            />
          </AntHeader>
        </Affix>
        <Content
          style={{
            minHeight: "100vh",
            // background: "rgb(249 249 249)",
            padding: "6px 10px",
          }}
          className="content-ant rounded-lg"
        >
          {pathname == "/" ||
          pathname === "home" ||
          pathname === "profile" ? null : (
            <Breadcrumb className="m-2" items={breadcrumbItems}></Breadcrumb>
          )}
          {children}
        </Content>
        <Footer />
      </Layout>
    </Layout>
  );
}

export default Main;
