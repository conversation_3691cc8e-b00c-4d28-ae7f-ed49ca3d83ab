import {createAsyncThunk, createSlice} from "@reduxjs/toolkit";
import { default as api } from "@/apis/axiosInterceptor";

export const getAllEnterprises : any = createAsyncThunk(
  "enterprises/fetchAll",
  async (_, thunkAPI) => {
    try {
      const response = await api.get(`/enterprise-all`);
      return response.data;
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error?.message || "An error occurred.");
    }
  }
);

export const enterpriseSlice = createSlice({
  name: 'enterprises',
  initialState: {
    loading: false,
    error: null,
    data: [],
  },
  reducers: {},
  extraReducers: builder => {
       builder
      .addCase(getAllEnterprises.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getAllEnterprises.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
        state.data = action.payload;
      })
      .addCase(getAllEnterprises.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  }
})

export default enterpriseSlice.reducer

