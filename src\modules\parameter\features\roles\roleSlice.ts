import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { logout } from "@features/auth/authSlice";
import { default as api } from "@/apis/axiosInterceptor";

export const getRoles: any = createAsyncThunk(
  "role",
  async (data, thunkAPI) => {
    try {
      let url = `/roles/all`;
      const resp = await api.get(url);
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 401) {
          thunkAPI.dispatch(logout());
        } else if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data.message);
      } else {
        // Erreur lors de la configuration de la requête
        return thunkAPI.rejectWithValue("Une erreur s'est produite.");
      }
    }
  }
);

export const addRoles: any = createAsyncThunk(
  "role",
  async (data, thunkAPI) => {
    try {
      let url = `/roles`;
      const resp = await api.post(url, data);
      // update state to add new permission
      thunkAPI.dispatch(getRoles());
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 401) {
          thunkAPI.dispatch(logout());
        } else if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data.message);
      } else {
        // Erreur lors de la configuration de la requête
        return thunkAPI.rejectWithValue("Une erreur s'est produite.");
      }
    }
  }
);

export const updateRole: any = createAsyncThunk(
  "role",
  async (data: { id: number; name: string }, thunkAPI) => {
    try {
      let url = `/roles/${data.id}`;
      const resp = await api.put(url, data);
      // update state to add new permission
      thunkAPI.dispatch(getRoles());
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 401) {
          thunkAPI.dispatch(logout());
        } else if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data.message);
      } else {
        // Erreur lors de la configuration de la requête
        return thunkAPI.rejectWithValue("Une erreur s'est produite.");
      }
    }
  }
);

export const deleteRole: any = createAsyncThunk(
  "role",
  async (data: { id: number }, thunkAPI) => {
    try {
      let url = `/roles/${data.id}`;
      const resp = await api.delete(url);
      // update state to add new permission
      thunkAPI.dispatch(getRoles());
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 401) {
          thunkAPI.dispatch(logout());
        } else if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data.message);
      } else {
        // Erreur lors de la configuration de la requête
        return thunkAPI.rejectWithValue("Une erreur s'est produite.");
      }
    }
  }
);

export const getRoleById: any = createAsyncThunk(
  "role",
  async (data: { id: number }, thunkAPI) => {
    try {
      let url = `/role/${data.id}`;
      const resp = await api.get(url);
      return resp.data;
    } catch (error: any) {
      if (error.response) {
        const { status, data } = error.response;
        if (status === 401) {
          thunkAPI.dispatch(logout());
        } else if (status === 403) {
          window.location.href = "/unauthorized";
        }
        return thunkAPI.rejectWithValue(data.message);
      } else {
        // Erreur lors de la configuration de la requête
        return thunkAPI.rejectWithValue("Une erreur s'est produite.");
      }
    }
  }
);


const roleSlice = createSlice({
  name: "role",
  initialState: {
    loading: false,
    error: null,
    data: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getRoles.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.data = null;
      })
      .addCase(getRoles.fulfilled, (state, action) => {
        state.data = action.payload.data;
        state.loading = false;
        state.error = null;
      })
      .addCase(getRoles.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        state.data = null;
      })
  },
});


export default roleSlice.reducer;
