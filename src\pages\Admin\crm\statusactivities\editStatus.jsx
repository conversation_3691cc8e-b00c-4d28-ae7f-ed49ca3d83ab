// src/pages/activities/editStatus.jsx
import { useEffect, useState } from "react";
import { ProForm, ProFormText } from "@ant-design/pro-form";
import { Button, Collapse, message, Spin, Divider } from "antd";
import { DeleteOutlined } from "@ant-design/icons";

const EditActivityStatus = ({ record, onCancel, setDataSource }) => {
  const [phase, setPhase] = useState(null);
  const [stages, setStages] = useState([]);
  const [activeKey, setActiveKey] = useState(["1"]);
  const [form] = ProForm.useForm();

  useEffect(() => {
    if (record) {
      setPhase(record);
      setStages(record.stages || [{ id: Date.now(), name: "" }]);
      form.resetFields();
    } else {
      message.error("Phase non trouvée");
      onCancel();
    }
  }, [record, form, onCancel]);

  const addStage = () => {
    const newStage = { id: Date.now(), name: "" };
    setStages([...stages, newStage]);
    setActiveKey([newStage.id]);
  };

  const removeStage = (keyToRemove) => {
    setStages((stages) => stages.filter((stage) => stage.id !== keyToRemove));
  };

  const handleSubmit = async (values) => {
    try {
      const updatedPhase = {
        key: record.key,
        name: values.name,
        description: values.description,
        stages: stages.map((stage, index) => ({
          id: stage.id,
          name: values[`stage_name_${index}`] || "",
        })),
      };

      const updatedData = datas.map((item) =>
        item.key === record.key ? updatedPhase : item
      );

      setDataSource(updatedData);
      message.success("Phase modifiée avec succès!");
      onCancel();
    } catch (error) {
      console.log(error);
      message.error("Erreur lors de la modification");
    }
  };

  if (!phase) {
    return <Spin size="large" />;
  }

  return (
    <>
      <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", marginBottom: "15px" }}>
        <h2>Modifier une phase</h2>
        <Button onClick={addStage} type="primary">+</Button>
      </div>

      <ProForm
        form={form}
        onFinish={handleSubmit}
        initialValues={{
          name: phase.name,
          description: phase.description,
          ...phase.stages.reduce((acc, stage, index) => ({
            ...acc,
            [`stage_name_${index}`]: stage.name,
          }), {}),
        }}
      >
        <ProFormText
          name="name"
          label="Nom de la phase"
          placeholder="Saisissez le nom de la phase"
          rules={[{ required: true }]}
        />
        <ProFormText
          name="description"
          label="Description"
          placeholder="Saisissez la description"
          rules={[{ required: true }]}
        />

        <Divider>Étapes</Divider>

        {stages.map((stage, index) => (
          <div key={stage.id} style={{ display: "flex", alignItems: "center", gap: "10px", width: "100%" }}>
            <Collapse
              style={{ flex: 1, marginBottom: "10px" }}
              activeKey={activeKey}
              onChange={(keys) => setActiveKey(keys)}
              items={[
                {
                  key: stage.id,
                  label: `Étape ${index + 1}`,
                  children: (
                    <div className="stage-group">
                      <ProFormText
                        name={`stage_name_${index}`}
                        label="Nom de l'étape"
                        placeholder="Saisissez le nom de l'étape"
                        rules={[{ required: true }]}
                      />
                    </div>
                  ),
                },
              ]}
            />
            {index !== 0 && (
              <Button onClick={() => removeStage(stage.id)} danger icon={<DeleteOutlined />} />
            )}
          </div>
        ))}
      </ProForm>
    </>
  );
};

export default EditActivityStatus;