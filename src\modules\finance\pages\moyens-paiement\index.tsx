import { useState, useRef, useEffect } from "react";
import { ProColumns, ProTable } from "@ant-design/pro-components";
import {
  Button,
  Popconfirm,
  Space,
  Input,
  Tooltip,
  Typography,
  Card,
} from "antd";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { moyensPaiement } from "./data";
import AddMoyenPaiementModal from "./create";
import ShowMoyenPaiementModal from "./view";
import EditMoyenPaiementModal from "./edit";
import { IMoyenPaiement } from "./interfaces";
// import useDebounce from "@src/hooks/useDebounce";

function MoyensPaiement() {
  const { t } = useTranslation();
  const tableRef = useRef();
  const [dataSource, setDataSource] =
    useState<IMoyenPaiement[]>(moyensPaiement);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [moyenPaiement, setMoyenPaiement] = useState<IMoyenPaiement | null>(
    null
  );
  const [isLoading, setIsLoading] = useState(false);
  const [tableParams, setTableParams] = useState([]);
  const [total, setTotal] = useState(dataSource.length);
  const [filteredData, setFilteredData] =
    useState<IMoyenPaiement[]>(dataSource);
  const [pageNumber, setPageNumber] = useState(1);
  const [showEditModal, setShowEditModal] = useState(false);

  const handleSearchData = (params) => {
    const filtered = dataSource.filter((item) => {
      return (
        (!params.code ||
          item.code?.toLowerCase().includes(params.code.toLowerCase())) &&
        (!params.libelle ||
          item.libelle
            ?.toLowerCase()
            .includes(params.libelle.toLowerCase()))
      );
    });

    setFilteredData(filtered); // Update table with filtered results
  };

  const columns: ProColumns<IMoyenPaiement>[] = [
    {
      title: t("moyens-paiement.fields.code"),
      dataIndex: "code",
      key: "code",
      filters: dataSource.map((item) => ({
        text: item.code,
        value: item.code,
      })),
      onFilter: (value, record) => record.code === value,
      render: (text, record) => {
        return (
          <Typography.Text
            ellipsis={{ tooltip: text }}
            style={{ maxWidth: "auto" }}
          >
            {text}
          </Typography.Text>
        );
      },
    },
    {
      title: t("moyens-paiement.fields.libelle"),
      dataIndex: "libelle",
      key: "libelle",
      filters: dataSource.map((item) => ({
        text: item.libelle,
        value: item.libelle,
      })),
      onFilter: (value, record) => record.libelle === value,
    },
    {
      title: t("common.actions.title"),
      key: "action",
      align: "center",
      width: 200,
      sorter: false,
      search: false,
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title={t("common.actions.view")}>
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => {
                console.log(record);

                setMoyenPaiement(record);
                setShowViewModal(true);
              }}
            />
          </Tooltip>
          <Tooltip title={t("common.actions.edit")}>
            <Button
              type="link"
              style={{ color: "#f5b041" }}
              icon={<EditOutlined />}
              onClick={() => {
                setShowEditModal(true);
                setMoyenPaiement(record);
              }}
            />
          </Tooltip>
          <Tooltip title={t("common.actions.delete")}>
            <Popconfirm
              title={t("common.messages.confirm_delete")}
              onConfirm={() => {
                setIsLoading(true);
                const newDataSource = dataSource.filter(
                  (item) => item.key !== record.key
                );
                setDataSource(newDataSource);
                setFilteredData(newDataSource);
                setIsLoading(false);
              }}
            >
              <Button
                type="link"
                style={{ color: "#ec7063" }}
                icon={<DeleteOutlined />}
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  // const handleSearch = (e) => {
  //   const searchValue = e.target.value;
  //   setTableParams({ ...tableParams, search: searchValue });
  // };

  // const debouncedOnChange = useDebounce(handleSearch, 700);

  useEffect(() => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  }, [dataSource, filteredData]);

  return (
    <Card
      bordered
      title={
        <Typography.Title level={4}>
          {t("moyens-paiement.list")}
        </Typography.Title>
      }
      extra={
        <Space>
          <Input
            size="large"
            placeholder={t("common.search")}
            suffix={<SearchOutlined style={{ color: "#bfbfbf" }} />}
            // onChange={debouncedOnChange}
          />
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setShowAddModal(true)}
          >
            {t("moyens-paiement.add")}
          </Button>
        </Space>
      }
    >
      <ProTable
        actionRef={tableRef}
        loading={isLoading}
        columns={columns}
        dataSource={filteredData}
        rowKey="key"
        search={{
          searchText: t("common.filter"),
          labelWidth: "auto",
          defaultCollapsed: false,
        }}
        options={false}
        params={tableParams}
        scroll={{ x: "max-content" }}
        onSubmit={handleSearchData}
        onReset={() => handleSearchData({})}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: 5,
          total: total,
          onChange: (page) => setPageNumber(page),
        }}
      />
      <AddMoyenPaiementModal
        open={showAddModal}
        onCancel={() => setShowAddModal(false)}
      />
      <ShowMoyenPaiementModal
        open={showViewModal}
        onCancel={() => {
          setShowViewModal(false);
          setMoyenPaiement(null);
        }}
        moyenPaiement={moyenPaiement}
      />
      <EditMoyenPaiementModal
        open={showEditModal}
        onCancel={() => {
          setShowEditModal(false);
          setMoyenPaiement(null);
        }}
        moyenPaiement={moyenPaiement}
      />
    </Card>
  );
}

export default MoyensPaiement;
