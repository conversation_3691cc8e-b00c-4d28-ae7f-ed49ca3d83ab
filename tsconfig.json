{"compilerOptions": {"baseUrl": ".", "paths": {"@src/*": ["./src/*"], "@components/*": ["./src/components/*"], "@features/*": ["./src/features/*"], "@pages/*": ["./src/pages/*"], "@layouts/*": ["./src/layouts/*"], "@helpers/*": ["./src/helpers/*"], "@/*": ["src/*"]}, "target": "ESNext", "useDefineForClassFields": true, "lib": ["DOM", "DOM.Iterable", "ESNext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "strictNullChecks": false, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "ESNext", "moduleResolution": "Node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "noImplicitAny": false}, "include": ["./src", "tailwind.config.js"]}