import { ModalForm } from "@ant-design/pro-components";
import {
  <PERSON><PERSON>,
  <PERSON>,
  Col,
  DatePicker,
  Divider,
  Form,
  Input,
  InputNumber,
  Radio,
  Row,
  Select,
} from "antd";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { activites, pays, secteurs } from "./data";
function ShowCompany({ open, onCancel, record }) {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  useEffect(() => {
    if (open) {
      form.setFieldsValue(record);
    }
  }, [open]);

  return (
    <ModalForm
      title={t("Consulter une entreprise")}
      disabled
      form={form}
      open={open}
      width="70%"
      modalProps={{
        style: { top: 20 },
      }}
      //   onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        resetButtonProps: { disabled: false },
        submitButtonProps: { hidden: true },
        searchConfig: {
          resetText: t("common.actions.cancel"),
        },
      }}
    >
      <Card className="mt-5">
        <Row gutter={4} className="mt-2">
          <Divider>Traduction Francaise</Divider>
          <Col md={12} xs={24}>
            <Form.Item name="raison_social" label={t("Raison Sociale")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name="maison_mere" label={t("Nom de la maison mère")}>
              <Input />
            </Form.Item>
          </Col>
          <Divider>HQ</Divider>

          <Col md={12} xs={24}>
            <Form.Item name="pays_id" label={t("Pays")}>
              <Select
                options={pays.map((item) => ({
                  value: item.id,
                  label: item.nom,
                }))}
              />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name="etat" label={t("Etat")}>
              <Select />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name="ville" label={t("Ville")}>
              <Select />
            </Form.Item>
          </Col>
          <Divider></Divider>
          <Col md={12} xs={24}>
            <Form.Item name="adresse" label={t("Adresse")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name="code_postal" label={t("Code postal")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item
              name="presence_modial"
              label={t("Présence mondiale : Pays")}
            >
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item
              name="source_identification"
              label={t("Source d'identification de l'entreprise")}
            >
              <Select />
            </Form.Item>
          </Col>
          <Col md={24} xs={24}>
            <Form.Item
              name="source_details"
              label={t("Source d'identification de l'entreprise (détails)")}
            >
              <Input />
            </Form.Item>
          </Col>
          <Divider>Profile</Divider>
          <Col md={24} xs={24}>
            <Form.Item
              name="description"
              label={t("Description de l'entreprise")}
            >
              <Input.TextArea allowClear={true} />
            </Form.Item>
          </Col>
          <Divider>Industrie</Divider>
          <Col md={12} xs={24}>
            <Form.Item name="activite_id" label={t("Industrie")}>
              <Select options={activites.map((item) => ({
                  value: item.id,
                  label: item.nom,
                }))}
                />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name="secteur_id" label={t("Secteur")}>
              <Select 
              options={secteurs.map((item) => ({
                  value: item.id,
                  label: item.secteur,
                }))}/>
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name="sous_secteur" label={t("Sous secteur")}>
              <Select />
            </Form.Item>
          </Col>
          <Divider>Size and financials</Divider>
          <Col md={12} xs={24}>
            <Form.Item name="produits" label={t("Produit /services offerts")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name="type_societe" label={t("Type de société")}>
              <Select />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name="annee_create" label={t("Année de création")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item
              name="historique"
              label={t("Historique des investissements à l'international")}
            >
              <Select />
            </Form.Item>
          </Col>
          <Col md={24} xs={24}>
            <Form.Item
              name="brevets"
              label={t("Brevets , Certifications , Marques , Awards")}
            >
              <Input />
            </Form.Item>
          </Col>
          <Divider></Divider>
          <Col md={12} xs={24}>
            <Form.Item
              name="chiffre_affaire"
              label={t("Chiffres d'affaires (montant actuel)")}
            >
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name="nb_emploi" label={t("Nombre d'emplois (actuel)")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item
              name="nb_emploi_year"
              label={t("Nombre d'emplois (année)")}
            >
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item
              name="depenses"
              label={t("Dépenses de R&D (montant réel)")}
            >
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item
              name="depenses_year"
              label={t("Dépenses de R&D (année)")}
            >
              <Input />
            </Form.Item>
          </Col>
          <Divider>Contact information</Divider>
          <Col md={12} xs={24}>
            <Form.Item name="tel" label={t("Tél.")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name="fax" label={t("Fax.")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name="email" label={t("E-mail")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name="site_web" label={t("Site web")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name="linkedin" label={t("Linkedin")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name="twitter" label={t("Twitter")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name="facebook" label={t("Facebook")}>
              <Input />
            </Form.Item>
          </Col>
          <Divider>Assignement / Ownership</Divider>
          <Col md={24} xs={24}>
            <Form.Item name="assign " label={t("Users")}>
              <Select />
            </Form.Item>
          </Col>
        </Row>
      </Card>
    </ModalForm>
  );
}
export default ShowCompany;
