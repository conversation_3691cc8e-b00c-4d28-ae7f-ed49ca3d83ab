import { ProForm, ProFormSelect, ProFormText, ProFormDigit, ProFormCheckbox } from "@ant-design/pro-form";
import {Checkbox,  Input, DatePicker, Select , Divider, message, Modal, Button, Typography, Tabs, Form } from "antd";
import { useState } from "react";

const { Title } = Typography;
const { TabPane } = Tabs;

const CreateTerrainLocal = ({ onCancel, onCreate }) => {
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState("contact");
  const [propertyType, setPropertyType] = useState("combined"); // 'terrain', 'local', 'combined'

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      const newKey = Date.now().toString();
      
      const newProperty = {
        key: newKey,
        type: propertyType,
        ...values,
        disponibilite: "Disponible",
        reference: `PROP-${newKey.slice(-6)}`
      };

      onCreate(newProperty);
      message.success("Bien immobilier créé avec succès!");
      onCancel();
    } catch (error) {
      console.error(error);
      message.error("Erreur lors de la création");
    }
  };

  const gouvernorats = [
    "Tunis", "<PERSON>na", "Ben Arous", "Manouba", "Nabeul", "<PERSON>aghouan",
    "<PERSON>izerte", "Béja", "Jendouba", "<PERSON>f", "Siliana", "<PERSON>rouan",
    "Kasserine", "<PERSON>i Bouzid", "Sfax", "Mahdia", "Monastir",
    "Sousse", "Gabès", "Médenine", "Tataouine", "Gafsa", "Tozeur", "Kebili"
  ];

  const vocations = [
    "Industrielle", "Commerciale", "Résidentielle", "Agricole", "Touristique"
  ];

  const typesConstruction = [
    "Dur", "Charpente", "Mixte"
  ];

  return (
  
      <Form form={form} layout="vertical">
        {/* <ProFormSelect
          name="type"
          label="Type de bien"
          options={[
            { label: "Terrain seul", value: "terrain" },
            { label: "Local seul", value: "local" },
            { label: "Terrain avec local", value: "combined" }
          ]}
          initialValue={propertyType}
          fieldProps={{
            onChange: (value) => setPropertyType(value)
          }}
          rules={[{ required: true }]}
        /> */}

        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          {/* Onglet Contact */}
          <TabPane tab="Contact" key="contact">
            <Form form={form} layout="vertical" >
      <Divider>Informations Générales</Divider>
      <Form.Item label="N° Enregistrement" name="numero">
        <Input disabled placeholder="Numéro généré automatiquement" />
      </Form.Item>
      <Form.Item label="Date Enregistrement" name="dateEnregistrement">
        <DatePicker style={{ width: "100%" }} />
      </Form.Item>
      <Form.Item label="Date MAJ" name="dateMAJ">
        <DatePicker style={{ width: "100%" }} />
      </Form.Item>
      
      <Divider>Propriétaire</Divider>
      <Form.Item label="Propriétaire" name="proprietaire">
        <Select>
          <Option value="prive">Privé</Option>
          <Option value="banque">Banque</Option>
          <Option value="afi">AFI</Option>
          <Option value="zf_zarzis">Z.F. Zarzis</Option>
          <Option value="zf_bizerte">Z.F. Bizerte</Option>
          <Option value="etat_tunisien">État Tunisien</Option>
        </Select>
      </Form.Item>
      <Form.Item label="Valide" name="valide">
        <Select>
          <Option value="oui">Oui</Option>
          <Option value="non">Non</Option>
        </Select>
      </Form.Item>
      <Form.Item label="Nom du Privé" name="nomPrive">
        <Input placeholder="Saisir le nom du propriétaire" />
      </Form.Item>
      <Form.Item label="Adresse" name="adresse">
        <Input placeholder="Saisir l'adresse" />
      </Form.Item>
      <Form.Item label="Code Postal" name="cp">
        <Input placeholder="Saisir le code postal" />
      </Form.Item>
      <Form.Item label="Téléphone" name="telephone">
        <Input placeholder="Saisir le numéro de téléphone" />
      </Form.Item>
      <Form.Item label="Fax" name="fax">
        <Input placeholder="Saisir le numéro de fax" />
      </Form.Item>
      <Form.Item label="Email" name="email">
        <Input type="email" placeholder="Saisir l'email" />
      </Form.Item>

      <Divider>Contact</Divider>
      <Form.Item label="Nom du Contact" name="nomContact">
        <Input placeholder="Saisir le nom du contact" />
      </Form.Item>
      <Form.Item label="Propriétaire" name="proprietaireContact">
        <Select>
          <Option value="oui">Oui</Option>
          <Option value="non">Non</Option>
        </Select>
      </Form.Item>
      <Form.Item label="Adresse Contact" name="adresseContact">
        <Input placeholder="Saisir l'adresse du contact" />
      </Form.Item>
      <Form.Item label="Code Postal Contact" name="cpContact">
        <Input placeholder="Saisir le code postal du contact" />
      </Form.Item>
      <Form.Item label="Téléphone Contact" name="telephoneContact">
        <Input placeholder="Saisir le téléphone du contact" />
      </Form.Item>
      <Form.Item label="Fax Contact" name="faxContact">
        <Input placeholder="Saisir le fax du contact" />
      </Form.Item>
      <Form.Item label="Email Contact" name="emailContact">
        <Input type="email" placeholder="Saisir l'email du contact" />
      </Form.Item>

      <Divider>Informations sur le Terrain/Local</Divider>
      <Form.Item label="Terrain et/ou Local" name="terrainLocal">
        <Select>
          <Option value="terrain">Terrain</Option>
          <Option value="local">Local</Option>
          <Option value="les_deux">Terrain + Local</Option>
        </Select>
      </Form.Item>
      <Form.Item label="Vocation" name="vocation">
        <Select>
          <Option value="agricole">Agricole</Option>
          <Option value="industrielle">Industrielle</Option>
          <Option value="touristique">Touristique</Option>
          <Option value="residentielle">Résidentielle</Option>
          <Option value="commerciale">Commerciale</Option>
        </Select>
      </Form.Item>
      <Form.Item label="Gouvernorat" name="gouvernorat">
        <Input placeholder="Saisir le gouvernorat" />
      </Form.Item>
      <Form.Item label="Coordonnées GPS" name="gps">
        <Input placeholder="Saisir les coordonnées GPS" />
      </Form.Item>
      <Form.Item label="Disponibilité" name="disponibilite">
        <Select>
          <Option value="oui">Oui</Option>
          <Option value="non">Non</Option>
        </Select>
      </Form.Item>
      <Form.Item label="Date Fin de Contrat" name="dateFinContrat">
        <DatePicker style={{ width: "100%" }} />
      </Form.Item>
      <Form.Item label="Observation" name="observation">
        <Input.TextArea placeholder="Ajouter une observation" rows={3} />
      </Form.Item>

      <Divider />
      
    </Form>
          </TabPane>

          {/* Onglet Terrain (visible seulement si type est terrain ou combined) */}
          {(propertyType === 'terrain' || propertyType === 'combined') && (
            <TabPane tab="Terrain" key="terrain" disabled={propertyType === 'local'}>
                <Form form={form} layout="vertical" >
     

      <Divider>Informations sur le Terrain/Local</Divider>
      <Form.Item label="Adresse ou lieu" name="adresseLieu">
        <Input placeholder="Saisir l'adresse ou le lieu du terrain" />
      </Form.Item>
      <Form.Item label="Code Postal" name="cpTerrain">
        <Input placeholder="Saisir le code postal du terrain" />
      </Form.Item>
      <Form.Item label="Superficie en m²" name="superficie">
        <Input placeholder="Saisir la superficie en m²" />
      </Form.Item>
      <Form.Item label="Aménagement" name="amenagement">
        <Select>
          <Option value="totalement">Totalement</Option>
          <Option value="partiellement">Partiellement</Option>
          <Option value="non_amenage">Non aménagé</Option>
        </Select>
      </Form.Item>
      <Form.Item label="Description du terrain et du voisinage" name="description">
        <Input.TextArea rows={3} placeholder="Ajouter une description" />
      </Form.Item>
      <Form.Item name="eau" valuePropName="checked">
        <Checkbox>Raccordé à l'eau</Checkbox>
      </Form.Item>
      <Form.Item name="electricite" valuePropName="checked">
        <Checkbox>Raccordé à l'électricité</Checkbox>
      </Form.Item>
      <Form.Item name="gaz" valuePropName="checked">
        <Checkbox>Raccordé au gaz naturel</Checkbox>
      </Form.Item>
      <Form.Item name="onas" valuePropName="checked">
        <Checkbox>Raccordé aux eaux usées</Checkbox>
      </Form.Item>
      <Form.Item name="telecom" valuePropName="checked">
        <Checkbox>Raccordé aux télécommunications</Checkbox>
      </Form.Item>
      <Form.Item label="Autres aménagements" name="autresAmenagements">
        <Input placeholder="Indiquer d'autres aménagements" />
      </Form.Item>
      <Form.Item label="Prix de Vente (m²)" name="prixVente">
        <Input placeholder="Saisir le prix de vente en DT/m²" />
      </Form.Item>
      <Form.Item label="Loyer annuel (m²)" name="loyerAnnuel">
        <Input placeholder="Saisir le loyer annuel en DT/m²" />
      </Form.Item>
      <Form.Item label="Conditions location" name="conditionsLocation">
        <Input.TextArea placeholder="Indiquer les conditions spécifiques" rows={3} />
      </Form.Item>
      
      <Divider />
     
    </Form>
            </TabPane>
          )}

          {/* Onglet Local (visible seulement si type est local ou combined) */}
          {(propertyType === 'local' || propertyType === 'combined') && (
            <TabPane tab="Local" key="local" disabled={propertyType === 'terrain'}>
              <Divider orientation="left">Caractéristiques du local</Divider>
              <ProFormDigit
                name={['local', 'superficieCouverte']}
                label="Superficie couverte (m²)"
                min={0}
                rules={[{ required: propertyType !== 'terrain' }]}
              />

              <ProFormDigit
                name={['local', 'hauteurMurs']}
                label="Hauteur des murs (m)"
                min={0}
              />

              <ProFormSelect
                name={['local', 'typeConstruction']}
                label="Type de construction"
                options={typesConstruction.map(t => ({ label: t, value: t }))}
              />

              <ProFormDigit
                name={['local', 'anneeConstruction']}
                label="Année de construction"
                min={1900}
                max={new Date().getFullYear()}
              />

              <ProFormSelect
                name={['local', 'etatTravaux']}
                label="État des travaux"
                options={[
                  { label: "Achevés", value: "Achevés" },
                  { label: "En cours", value: "En cours" },
                  { label: "Inachevés", value: "Inachevés" }
                ]}
              />

              <Divider >Usage précédent</Divider>
              <ProFormCheckbox.Group
                name={['local', 'usagePrecedent']}
                options={[
                  { label: "Usine", value: "usine" },
                  { label: "Atelier", value: "atelier" },
                  { label: "Dépôt", value: "depot" }
                ]}
              />

              <Divider>Commodités</Divider>
              <ProFormCheckbox.Group
                name={['local', 'commodites']}
                options={[
                  { label: "Administration", value: "administration" },
                  { label: "Vestiaires", value: "vestiaires" },
                  { label: "Sanitaires", value: "sanitaires" },
                  { label: "Réfectoire", value: "refectoire" },
                  { label: "Dortoir", value: "dortoir" }
                ]}
              />

              <Divider >Infrastructures</Divider>
              <ProFormCheckbox.Group
                name={['local', 'equipements']}
                options={[
                  { label: "Eau", value: "eau" },
                  { label: "Électricité", value: "electricite" },
                  { label: "Force motrice", value: "force" },
                  { label: "Gaz naturel", value: "gaz" },
                  { label: "Téléphone", value: "telephone" },
                  { label: "Monte-charge", value: "monteCharge" }
                ]}
              />

              <Divider >Conditions financières</Divider>
              <ProFormDigit
                name={['local', 'prixVenteM2']}
                label="Prix de vente (DT/m²)"
                min={0}
              />

              <ProFormDigit
                name={['local', 'loyerAnnuelM2']}
                label="Loyer annuel (DT/m²)"
                min={0}
              />
            </TabPane>
          )}
        </Tabs>

        <Form.Item  style={{ textAlign: 'right' }}>
        <Button type="primary" htmlType="submit">Enregister</Button>
      </Form.Item>
      </Form>

  );
};

export default CreateTerrainLocal;