import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { default as api } from "@/apis/axiosInterceptor";

const baseURL = import.meta.env.VITE_API_URL;

export const login: any = createAsyncThunk("auth", async (data, thunkAPI) => {
  try {
    console.log(data);

    // let url = `/auth/login`;
    // const resp = await api.post(url, data);
    // return resp.data;
    localStorage.setItem("token", "fakeToken123");

    localStorage.setItem("module", "1");
    localStorage.setItem(
      "user",
      JSON.stringify({ id: 1, name: "<PERSON>", email: data.email, "roles": ["super_admin"] })
    );
  } catch (error) {
    return thunkAPI.rejectWithValue("something went wrong");
  }
});

export const loginWithGoogle: any = createAsyncThunk(
  "auth",
  async (_, thunkAPI) => {
    try {
      const url = "auth/google";
      const resp = await api.get(url);

      if (resp.data.url) {
        return resp.data;
      } else {
        return thunkAPI.rejectWithValue("Google login URL not found");
      }
    } catch (error) {
      return thunkAPI.rejectWithValue("something went wrong");
    }
  }
);

export const loginWithMicrosoft: any = createAsyncThunk(
  "auth",
  async (_, thunkAPI) => {
    try {
      const url = "auth/microsoft";
      const resp = await api.get(url);

      if (resp.data.url) {
        return resp.data;
      } else {
        return thunkAPI.rejectWithValue("Microsoft login URL not found");
      }
    } catch (error) {
      return thunkAPI.rejectWithValue("something went wrong");
    }
  }
);

export const logout: any = createAsyncThunk("auth", async (data, thunkAPI) => {
  try {
    // let url = `/auth/logout`;
    // const resp = await api.post(url);
    localStorage.removeItem("token");
    localStorage.removeItem("permissions");
    localStorage.removeItem("menu");
    localStorage.removeItem("user");
    localStorage.removeItem("module");
    localStorage.removeItem("employerUser");
    location.reload();
    // return resp.data;
  } catch (error) {
    return thunkAPI.rejectWithValue("something went wrong");
  }
});

export const can: any = (permission) => {
  const loggedUserPermissions = localStorage["permissions"]
    ? JSON.parse(localStorage.getItem("permissions"))
    : [];
  return loggedUserPermissions.includes(permission);
};

export const isAuthenticated: any = () => {
  const token = localStorage["token"];
  return !!token;
};

const authSlice = createSlice({
  name: "auth",
  initialState: {
    loading: false,
    error: null,
    data: null,
    isLoggedIn: !!localStorage.getItem("token"),
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(login.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.data = null;
      })
      .addCase(login.fulfilled, (state, action) => {
        // state.loading = false;
        // state.error = null;
        // state.data = action.payload.data;
        // const menus = action.payload.data.menu;
        // console.log(menus);
        // localStorage.setItem("token", action.payload.data.token);
        // localStorage.setItem(
        //   "permissions",
        //   JSON.stringify(action.payload.data.permissions)
        // );
        // localStorage.setItem("menu", JSON.stringify(menus));
        // localStorage.setItem(
        //   "module",
        //   menus.find((item) => item.link === "/accueil").id
        // );
        // // ch add
        // localStorage.setItem("user", JSON.stringify(action.payload.data.user));
        // localStorage.setItem(
        //   "languages",
        //   JSON.stringify(action.payload.data.languages)
        // );

        // localStorage.setItem(
        //   "employerUser",
        //   JSON.stringify(action.payload.data.employerUser)
        // );

        // if (action.payload.data.roles) {
        //   localStorage.setItem(
        //     "roles",
        //     JSON.stringify(action.payload.data.roles)
        //   );
        // }
        state.isLoggedIn = true;
      })
      .addCase(login.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload.data;
        state.data = null;
        state.isLoggedIn = false;
      });
  },
});

export default authSlice.reducer;
