import { ModalForm } from "@ant-design/pro-components";
import {
  Card,
  Col,
  DatePicker,
  Form,
  Input,
  InputNumber,
  Radio,
  Row,
  Select,
} from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { companies, responsables } from "./data";
import TextArea from "antd/es/input/TextArea.js";
function CreateDemandeForm({ open, onCancel, onSuccess }) {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [value, setValue] = useState(2);

  const [entrepriseId, setEntrepriseId] = useState(null);
  const [departementId, setDepartementId] = useState(null);
  const [tierId, setTierId] = useState(null);
  const dateFormat = "YYYY-MM-DD";

  const [form] = Form.useForm();
  const addDevis = (values) => {};

  const showTierData = (e) => {
    const tier = clients.find((t) => t.id == e);
    form.setFieldsValue({
      email: tier?.email,
      adresse: tier?.adresse,
      phone: tier?.phone,
      matricule_fiscale: tier?.matricule_fiscale,
    });
  };

  const resetTierData = () => {
    form.setFieldsValue({
      email: null,
      adresse: null,
      phone: null,
      matricule_fiscale: null,
    });
  };

  return (
    <ModalForm
      title={t("Ajouter une demande / un incident")}
      form={form}
      open={open}
      modalProps={{
        style: { top: 20 },
      }}
      //   onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.save"),
          resetText: t("common.actions.cancel"),
        },
      }}
    >
      <Card className="mt-5">
      <Row gutter={4} className="mt-2">
      <Col className="gutter-row" span={24}>
            <Form.Item
              name="entreprise_id"
              label={t("Entreprise")}
            
            >
             <Select placeholder="Sélectionnez une entreprise">
                {companies.map((type) => (
                  <Select.Option key={type.id} value={type.id}>
                    {type.nom}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col className="gutter-row" span={24}>
            <Form.Item
              name="date_demande"
              label={t("Date demande")}
            >
             <DatePicker style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Col className="gutter-row" span={24}>
            <Form.Item
              name="responnsable_id"
              label={t("Reponsable")}
            //  rules={[{ required: true, message: "Ce champ est obligatoire" }]}
            >
             <Select placeholder="Sélectionnez un responsable">
                {responsables.map((type) => (
                  <Select.Option key={type.id} value={type.id}>
                    {type.nom}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
  
          <Col className="gutter-row" span={24}>
            <Form.Item
              name="description"
              label="Description"
            //  rules={[{ required: true, message: "Ce champ est obligatoire" }]}
            >
              <TextArea rows={4} placeholder="Description" />
            </Form.Item>
          </Col>
        </Row>
      </Card>
    </ModalForm>
  );
}
export default CreateDemandeForm;
