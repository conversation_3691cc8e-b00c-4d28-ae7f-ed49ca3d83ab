import React, { useState, useEffect } from 'react';
import { Select, Button, Space, Typography } from 'antd';
import { ClearOutlined } from '@ant-design/icons';
import {
  MODULE_CONFIG,
  extractModulesFromRoutes,
  getSelectedModule,
  handleModuleSelection,
  clearModuleSelection,
} from '../utils/crmModuleUtils.jsx';
import crmRoutes from '../routes/crm-routes';
import { useNavigate } from 'react-router-dom';

const { Option } = Select;
const { Text } = Typography;

const CrmModuleSelector = ({ 
  style = {}, 
  placeholder = "Sélectionner un module CRM",
  showClearButton = true,
  size = "default"
}) => {
  const navigate = useNavigate();
  const [selectedModule, setSelectedModule] = useState(getSelectedModule());
  const availableModules = extractModulesFromRoutes(crmRoutes);

  useEffect(() => {
    const handleStorageChange = () => {
      setSelectedModule(getSelectedModule());
    };

    // Listen for storage changes
    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('crmModuleChanged', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('crmModuleChanged', handleStorageChange);
    };
  }, []);

  const handleModuleChange = (moduleName) => {
    if (moduleName) {
      handleModuleSelection(moduleName, navigate);
    }
  };

  const handleClearSelection = () => {
    clearModuleSelection();
    setSelectedModule(null);
  };

  return (
    <Space style={style}>
      <Select
        value={selectedModule}
        placeholder={placeholder}
        onChange={handleModuleChange}
        style={{ minWidth: 200 }}
        size={size}
        allowClear={false}
      >
        {availableModules.map(moduleName => {
          const config = MODULE_CONFIG[moduleName];
          return (
            <Option key={moduleName} value={moduleName}>
              <Space>
                {config.icon}
                {config.title}
              </Space>
            </Option>
          );
        })}
      </Select>
      
      {showClearButton && selectedModule && (
        <Button
          icon={<ClearOutlined />}
          onClick={handleClearSelection}
          size={size}
          title="Afficher tous les modules"
        >
          Tout afficher
        </Button>
      )}
      
      {selectedModule && (
        <Text type="secondary" style={{ fontSize: '12px' }}>
          Module actuel: {MODULE_CONFIG[selectedModule]?.title}
        </Text>
      )}
    </Space>
  );
};

export default CrmModuleSelector;
