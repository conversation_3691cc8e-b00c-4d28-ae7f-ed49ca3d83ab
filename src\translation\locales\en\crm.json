{"common": {"actions": {"title": "Actions", "view": "View", "edit": "Edit", "delete": "Delete"}, "messages": {"confirm_delete": "Are you sure you want to delete this item?"}, "search": "Search..."}, "crm": {"activities": {"list": "Activity List", "add": "Add Activity", "Modifier": "Edit Activity", "view": "View Activity", "fields": {"title": "Title", "type": "Type", "priority": "Priority", "assigned_to": "Assigned To", "status": "Status", "due_date": "Due Date"}, "create": {"title": "Create Activity", "fields": {"title": "Activity Title", "type": "Type", "priority": "Priority", "assigned_to": "Assigned To", "status": "Status", "due_date": "Due Date"}, "messages": {"success": "Activity created successfully!", "error": "Error while creating the activity"}, "placeholders": {"title": "Enter the title", "type": "Enter the type", "priority": "Enter the priority", "assigned_to": "Enter the assignee", "status": "Enter the status", "due_date": "Enter the due date"}}, "edit": {"title": "Edit Activity", "fields": {"title": "Activity Title", "type": "Type", "priority": "Priority", "assigned_to": "Assigned To", "status": "Status", "due_date": "Due Date"}, "messages": {"success": "Activity updated successfully!", "error": "Error while updating the activity", "not_found": "Activity not found."}, "placeholders": {"title": "Edit the title", "type": "Edit the type", "priority": "Edit the priority", "assigned_to": "Edit the assignee", "status": "Edit the status", "due_date": "Edit the due date"}}, "actions": {"title": "Actions", "view": "View", "edit": "Edit", "delete": "Delete"}, "messages": {"confirm_delete": "Are you sure you want to delete this activity?"}, "search": "Search..."}, "activity_status": {"list": "Activity Status List", "add": "Add Activity Status", "edit": "Edit Activity Status", "view": "View Activity Status", "fields": {"name": "Status Name", "description": "Description"}, "create": {"fields": {"name": "Job Title", "description": "Job Description", "requirements": "Requirements", "salary": "Salary"}, "placeholders": {"name": "Enter the job title", "description": "Enter the job description", "requirements": "Enter the job requirements", "salary": "Enter the salary range"}}}, "plansActivities": {"list": "Activity List", "add": "Add Activity", "edit": {"title": "Edit Activity", "fields": {"name": "Activity Name", "description": "Description", "planifier": "Schedule"}, "messages": {"success": "Activity successfully updated!", "error": "Error while updating the activity", "not_found": "Activity not found."}, "placeholders": {"name": "Edit the name", "description": "Edit the description", "planifier": "Edit the schedule"}}, "view": "View Activity", "fields": {"name": "Name", "description": "Description"}, "create": {"title": "Create an Activity", "fields": {"name": "Activity Name", "description": "Description", "planifier": "Schedule"}, "messages": {"success": "Activity successfully created!", "error": "Error while creating the activity"}, "placeholders": {"name": "Enter the name", "description": "Enter the description", "planifier": "Enter the schedule"}}}}}