import AuthGuard from '../helpers/Guards/AuthGuard';
import RoleGuard from '../helpers/Guards/RoleGuard';
import AdminLayout from '../layouts/AdminLayout';
// stock
import DashboardStock from '../pages/Admin/Stock/Dashboard';
import Articles from '../pages/Admin/Stock/Article';
import Categories from '../pages/Admin/Stock/Category';
import EditDomaine from '../pages/Admin/Stock/Article/edit';
import ViewDomaine from '../pages/Admin/Stock/Article/view.jsx';


// Simulate authentication and role
const isAuthenticated = true; // Replace with actual auth state
const userRole = 'admin'; // Replace with actual user role

const routes = [
//   {
//     path: 'stock',
//     element: (<DashboardStock />),
//   },
// // article routes
//   {
//     path: '/stock/articles',
//     element: (<Articles />),
//   },


//   {
//     path: '/stock/articles/edit/:id',
//     element: (<EditDomaine />),
//   },
//   {
//     path: '/stock/articles/view/:id',
//     element: (<ViewDomaine />),
//   },


//   {
//     path: '/stock/categories',
//     element: (
//       <AuthGuard isAuthenticated={isAuthenticated}>
//         <RoleGuard userRole={userRole} allowedRoles={['admin']}>
//           <AdminLayout>
//             <Categories />
//           </AdminLayout>
//         </RoleGuard>
//       </AuthGuard>
//     ),
//   },

];

export default routes;
