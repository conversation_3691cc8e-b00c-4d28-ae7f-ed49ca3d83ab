//TicketComments
.TicketComments{
    max-height: 280px !important;  
}
.TicketCommentsScrollZone {
    height: 360px;
    overflow: auto;
}
#TicketCommentsScrollZone::-webkit-scrollbar {
    width: 8px;
  }
  
  /* Track */
    #TicketCommentsScrollZone::-webkit-scrollbar-track {
    background: #f1f1f1; 
    // visibility: hidden;

  }
   
  /* Handle */
  #TicketCommentsScrollZone::-webkit-scrollbar-thumb {
    background: #B3B6B7; 
    border-radius:5px;
    // visibility: hidden;

  }
  
  /* Handle on hover */
  #TicketCommentsScrollZone::-webkit-scrollbar-thumb:hover {
    background: #888; 
  }

  #TicketComments p{
    display:inline-block;
    background: #F2F3F4;
    padding: 8px;
    border-radius: 8px;
}
//Ticket ouvert
.Ticketing .table-row-en-attente {
  background-color: #FCF3CF ;
animation: clignote 4s linear infinite;
}
@keyframes clignote {  
50% { background-color: #ffffff; }
}
.showTicket .ant-select-selection-item-content{
  color:black !important;
}