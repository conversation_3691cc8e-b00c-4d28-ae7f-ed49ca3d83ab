.home-layout{
    background-image: linear-gradient(rgba(255, 255, 255, 0.781), rgba(255, 255, 255, 0.65)),
                    url('../../assets/images/bg-fipa.jpg');
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
    background-size: cover;
    border-radius: 12px;
}
.bg-layout-blur-1{
    background-color: rgba(255, 255, 255, 0.7) !important;
    backdrop-filter: blur(7px);
    -webkit-backdrop-filter: blur(7px);
}
.bg-layout-blur-2{
    background-color: rgba(255, 255, 255, 0.9) !important;
    backdrop-filter: blur(7px);
    -webkit-backdrop-filter: blur(7px);
}
.ant-card.criclebox {
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03),0 1px 6px -1px rgba(0, 0, 0, 0.02),0 2px 4px 0 rgba(0, 0, 0, 0.02) !important;
}
.ant-card.criclebox:hover {
  box-shadow: 0px 9px 2px hsl(0deg 0% 100% / 31%) !important;
}