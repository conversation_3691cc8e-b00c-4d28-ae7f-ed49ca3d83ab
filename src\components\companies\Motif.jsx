import {
    FileOutlined,
    MinusCircleOutlined,
    PlusCircleOutlined,
    UploadOutlined,
  } from "@ant-design/icons";
  import { Col, DatePicker, Form, Input, message, Row, Upload } from "antd";
  import { useState } from "react";
  import { useTranslation } from "react-i18next";
  
  const Motif = ({ form, view }) => {
    const { t } = useTranslation();
    const handleAdd = () => {
        const currentmotifs= form.getFieldValue("motifs") || [];
       
        const motif = form.getFieldValue("motif");
        if (motif) {
          const key = Date.now();
          form.setFieldsValue({
            motifs: [
              ...currentmotifs,
              {
                key: key,
               motif:motif
              },
            ],
          });
          form.resetFields([
            "motif"
          ]);
        } else {
          message.error(t("Merci de bien vouloir remplir tous les champs."));
        }
      };
    return (
      <>
        <Row gutter={16} style={{  marginBottom: "10px" }}>
         
          <Col span={15}>Intitule du motif de cessation</Col>
          <Col span={1}></Col>
        </Row>
        <Form.List name="motifs">
          {(fields, { remove }) => (
            <>
              {fields.map((field, index) => (
                <Row
                  gutter={16}
                  align="middle"
                  key={field.key}
                >
                 
                  <Col span={18}>
                    <Form.Item name={[index, "motif"]} rules={[]}>
                      <Input
                        allowClear={true}
                        disabled={view}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={1}>
                    {!view && (
                      <Form.Item>
                        <MinusCircleOutlined
                          style={{
                            color: "red",
                            fontSize: "18px",
                            cursor: "pointer",
                          }}
                          onClick={() => remove(index)}
                        />
                      </Form.Item>
                    )}
                  </Col>
                </Row>
              ))}
            </>
          )}
        </Form.List>
  
        {!view && (
          <Row gutter={16} align="middle" style={{ marginBottom: "5px" }}>
            
            <Col span={18}>
              <Form.Item name={`motif`} rules={[]}>
                <Input
                
                  allowClear={true}
                />
              </Form.Item>
            </Col>
            <Col span={1}>
              <Form.Item>
                <PlusCircleOutlined
                  style={{
                    color: "green",
                    fontSize: "18px",
                    cursor: "pointer",
                  }}
                  onClick={() => handleAdd()}
                />
              </Form.Item>
            </Col>
          </Row>
        )}
      </>
    );
  };
  
  export default Motif;
  