// src/redux/reducers/loadingReducer.js
const initialState = {
    isLoading: false,
  };
  
  const loadingReducer = (state = initialState, action) => {
    switch (action.type) {
      case 'SHOW_LOADER':
        return { ...state, isLoading: true };
      case 'HIDE_LOADER':
        return { ...state, isLoading: false };
      default:
        return state;
    }
  };
  
  export default loadingReducer;
  