import { ModalForm } from "@ant-design/pro-components";
import {
  <PERSON><PERSON>,
  Card,
  Checkbox,
  Col,
  DatePicker,
  Divider,
  Form,
  Input,
  InputNumber,
  Modal,
  Popconfirm,
  Radio,
  Row,
  Select,
  Steps,
  Table,
} from "antd";

import TextArea from "antd/es/input/TextArea.js";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { organismes, pays, secteurs } from "./data";

function EditVisite({ open, onCancel, record, show }) {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [steps, setSteps] = useState(record.initialSteps);
  const [form] = Form.useForm();
  useEffect(() => {
    if (open) {
      form.setFieldsValue(record);
    }
  }, [open]);
  const [completedSteps, setCompletedSteps] = useState([]);
  const [showMotifModal, setShowMotifModal] = useState(false);
  const [selectedStepId, setSelectedStepId] = useState(null);
  const [reason, setReason] = useState("");
  const handleStepClick = (step) => {
    if (step.motif && step.status === "wait") {
      setSelectedStepId(step.id);
      setShowMotifModal(true);
    } else {
      toggleStepStatus(step.id);
    }
  };
  const toggleStepStatus = (id) => {
    const updated = steps.map((step) => {
      if (step.id === id) {
        const newStatus = step.status === "finish" ? "wait" : "finish";
        return { ...step, status: newStatus, reason: reason };
      }
      return step;
    });
    setSteps(updated);
  };
  return (
    <ModalForm
      title={
        show ? t("Afficher la fiche visite ") : t("Modifier la fiche visite")
      }
      form={form}
      open={open}
      modalProps={{
        style: { top: 20 },
      }}
      //   onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={ show ? {
         submitButtonProps: { hidden: true },
        searchConfig: {
        
          resetText: t("common.actions.cancel"),
        },
      } : { searchConfig: {
          submitText: t("common.actions.edit"),
          resetText: t("common.actions.cancel"),
        }}}

         disabled={show} 
    >
     <Card className="mt-2">
        <Row gutter={16}>
         
          <Col md={12} xs={24}>
            <Form.Item name={""} label={t("")}>
              <Checkbox > Encadré avec programme </Checkbox>
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name={""} label={t("")}>
              <Checkbox > Entreprise importante </Checkbox>
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name={"organisme_id"} label={t("Initiateur")}>
              <Select options={organismes.map((item) => ({
                  value: item.id,
                  label: item.nom,
                }))} />
            </Form.Item>
          </Col>
          <Col md={6} xs={24}>
            <Form.Item name={"nb_visite"} label={t("Nombre de visites")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={6} xs={24}>
           <Form.Item name={"date_contact"} label={t("Date du contact")}>
              <DatePicker />
            </Form.Item>
          </Col>
          <Col md={24} xs={24}>
            <Form.Item name={"raison_social"} label={t("Raison sociale")}>
              <Input />
            </Form.Item>
          </Col>
           <Col md={12} xs={24}>
            <Form.Item name={"responsable"} label={t("Responsable")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name={"fonction"} label={t("Fonction")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name={"pays_id"} label={t("Nationalité")}>
              <Select  options={pays.map((item) => ({
                  value: item.id,
                  label: item.nom,
                }))}/>
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name={"secteur_id"} label={t("Secteur")}>
              <Select options={secteurs.map((item) => ({
                  value: item.id,
                  label: item.secteur,
                }))} />
            </Form.Item>
          </Col>
          <Col md={24} xs={24}>
            <Form.Item name={"activite"} label={t("Activité")}>
              <Input.TextArea />
            </Form.Item>
          </Col>
          <Col md={24} xs={24}>
            <Form.Item name={"adresse"} label={t("Adresse")}>
              <Input />
            </Form.Item>
          </Col>
           <Col md={12} xs={24}>
            <Form.Item name={"tel"} label={t("Téléphone")}>
              <Input />
            </Form.Item>
          </Col>
           <Col md={12} xs={24}>
            <Form.Item name={"fax"} label={t("Fax")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name={"email"} label={t("E-mail")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name={"site_web"} label={t("Site web")}>
              <Input />
            </Form.Item>
          </Col>
        </Row>
    
      </Card>
    </ModalForm>
  );
}
export default EditVisite;
