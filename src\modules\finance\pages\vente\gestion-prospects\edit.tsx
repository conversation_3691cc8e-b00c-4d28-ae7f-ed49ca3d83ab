import { useTranslation } from "react-i18next";
import { ModalForm } from "@ant-design/pro-components";
import { useState } from "react";
import { message } from "antd";
import TierInfoGeneralForm from "@src/modules/finance/components/TierInfoGeneralForm";

const EditProspectModal = ({ open, onCancel, prospect }) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (values) => {
    setLoading(true);
    try {
      const submissionData = {
        ...values,
      };
      await new Promise((resolve) => setTimeout(resolve, 2000));

      message.success("Prospect modifié avec succès !");
    } catch (error) {
      console.log(error);
      message.error("Erreur lors de la modification du prospect !");
    } finally {
      setLoading(false);
      onCancel();
    }
  };

  return (
    <ModalForm
      width={900}
      title={t("vente.prospect.edit")}
      open={open}
      onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.edit"),
          resetText: t("common.actions.cancel"),
        },
        submitButtonProps: {
          loading,
        },
      }}
      modalProps={{
        destroyOnClose: true,
      }}
    >
      <div className="px-2">
        <TierInfoGeneralForm disabled={false} tier={prospect} />
      </div>
    </ModalForm>
  );
};

export default EditProspectModal;
