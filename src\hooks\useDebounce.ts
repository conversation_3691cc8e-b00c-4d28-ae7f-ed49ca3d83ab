import { useRef, useEffect, useCallback } from "react";

// Custom hook useDebounce
// Permet de retarder l'exécution d'une fonction jusqu'à ce qu'une période spécifiée de silence se soit écoulée.
function useDebounce(callback, delay) {
  // Référence pour stocker le timeout
  const timeoutRef = useRef(null);

  // Fonction debounced : retarde l'exécution de `callback` jusqu'à la fin du délai.
  const debouncedFunction = useCallback(
    (...args) => {
      // Si un timeout est déjà en cours, on le réinitialise
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      // Démarre un nouveau timeout pour exécuter `callback` après `delay` ms
      timeoutRef.current = setTimeout(() => {
        callback(...args);
      }, delay);
    },
    [callback, delay]
  ); // Recrée la fonction si `callback` ou `delay` changent

  // Nettoyage : s'assure que le timeout est annulé si le composant est démonté
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []); // S'exécute une seule fois au montage et démontage

  // Retourne la fonction debounced, prête à être utilisée
  return debouncedFunction;
}

export default useDebounce;
