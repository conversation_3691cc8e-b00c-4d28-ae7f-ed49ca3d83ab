import { useTranslation } from "react-i18next";
import EmplacementForm from "../../components/EmplacementForm";
import { Modal } from "antd";

const ShowLocationModal = ({ open, onCancel, location }) => {
  const { t } = useTranslation();

  return (
    <Modal
      width={800}
      title={t("emplacements.view")}
      open={open}
      onOk={onCancel}
      onCancel={onCancel}
    >
      <EmplacementForm disabled={true} location={location} />
    </Modal>
  );
};

export default ShowLocationModal;
