import { <PERSON>, Col, Flex, Row } from "antd";
import { useNavigate } from "react-router-dom";
import { Typography } from "antd";
import logo_signIn from "../../assets/images/fipa.png";
import "./style.css";
import {
  UserOutlined,
  StockOutlined,
} from "@ant-design/icons";
import {
  MODULE_CONFIG,
  extractModulesFromRoutes,
  getPrimaryRouteForModule,
  handleModuleSelection,
} from "../../utils/crmModuleUtils";
import crmRoutes from "../../routes/crm-routes";



const MenusCRM = () => {
  const navigate = useNavigate();
  const { Title } = Typography;

  // Extract unique modules from CRM routes
  const availableModules = extractModulesFromRoutes(crmRoutes);

  // Generate modules array for display
  const modules = availableModules.map(moduleName => {
    const config = MODULE_CONFIG[moduleName];
    const primaryRoute = getPrimaryRouteForModule(crmRoutes, moduleName);

    return {
      key: moduleName,
      link: primaryRoute,
      title: config.title,
      description: config.description,
      icon: config.icon,
    };
  });

  // Add non-CRM modules (keeping existing functionality)
  const nonCrmModules = [
    {
      key: "user",
      link: "/user/dashboard",
      title: "Gestion des utilisateurs",
      description: "Administration des utilisateurs",
      icon: <UserOutlined />,
    },
    {
      key: "stock",
      link: "/stock/dashboard",
      title: "Gestion des stocks",
      description: "Inventaire et approvisionnement",
      icon: <StockOutlined />,
    },
  ];

  const allModules = [...nonCrmModules, ...modules];
  return (
    <>
      <Flex justify="center" align="center">
        <Title
          className="bg-layout-blur-1 backdrop-blur-sm rounded-[6px]"
          level={2}
        >
          <img width="160px" src={logo_signIn} alt="Logo DGAC" />
        </Title>
      </Flex>
      <div className="m-10 layout-content home-layout p-[30px]">
        <Row className="rowgap-vbox" gutter={[24, 0]}>
          {allModules.map((module) => (
            <Col
              key={module.key}
              xs={24}
              sm={24}
              md={12}
              lg={8}
              xl={6}
              className="mb-24"
            >
              <Card
                className="criclebox min-h-32"
                onClick={() => {
                  // Handle CRM module selection with localStorage
                  if (Object.keys(MODULE_CONFIG).includes(module.key)) {
                    handleModuleSelection(module.key, navigate);
                  } else {
                    // For non-CRM modules, just navigate
                    navigate(module.link);
                  }
                }}
                style={{ cursor: 'pointer' }}
              >
                <div className="number">
                  <Row align="middle" gutter={[24, 0]}>
                    <Col span={18}>
                      <div>
                        <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>
                          {module.title}
                        </div>
                        {module.description && (
                          <div style={{ fontSize: '12px', color: '#666', lineHeight: '1.3' }}>
                            {module.description}
                          </div>
                        )}
                      </div>
                    </Col>
                    <Col span={6}>
                      <div className="icon-box">{module.icon}</div>
                    </Col>
                  </Row>
                </div>
              </Card>
            </Col>
          ))}
        </Row>
      </div>
    </>
  );
};

export default MenusCRM;
