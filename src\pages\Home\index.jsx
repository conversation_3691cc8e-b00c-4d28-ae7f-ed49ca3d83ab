import { Card, Col, Flex, Row } from "antd";
import { useNavigate } from "react-router-dom";
import { Typography } from "antd";
import logo_signIn from "../../assets/images/fipa.png";
import "./style.css";
import {
  UserOutlined,
  StockOutlined,
  SolutionOutlined,
  ContactsOutlined,
  BuildOutlined,
  ContainerOutlined,
  ScheduleOutlined,
  CalendarOutlined,
  FolderOutlined,
  CopyOutlined,
  Bar<PERSON>hartOutlined,
  MailOutlined,
  StarOutlined,
  BorderOutlined,
  AppstoreOutlined,
} from "@ant-design/icons";

const MenusCRM = () => {
  const navigate = useNavigate();
  const { Title } = Typography;
  const modules = [
    {
      key: "user",
      link: "/user/dashboard",
      title: "Gestion des utilisateurs",
      icon: <UserOutlined />,
    },
    {
      key: "stock",
      link: "/stock/dashboard",
      title: "Gestion des rôles",
      icon: <StockOutlined />,
    },
    {
      key: "finance",
      link: "/finance/dashboard",
      title: "Gestion des contacts",
      icon: <ContactsOutlined />,
    },
    {
      key: "CRM",
      link: "/crm/dashboard",
      title: "Gestion des entreprises",
      icon: <BuildOutlined />,
    },
    {
      key: "CRM",
      link: "/crm/dashboard",
      title: "Gestion des séminaires et des évènements",
      icon: <ScheduleOutlined />,
    },
    {
      key: "CRM",
      link: "/crm/dashboard",
      title: "Génération des leads",
      icon: <ContainerOutlined />,
    },
    {
      key: "CRM",
      link: "/crm/dashboard",
      title: "Gestion de l'aftercare",
      icon: <SolutionOutlined />,
    },
    {
      key: "CRM",
      link: "/crm/dashboard",
      title: "Gestion des entreprises installées en Tunisie",
      icon: <AppstoreOutlined />,
    },
    {
      key: "CRM",
      link: "/crm/dashboard",
      title: "Gestion de la diaspora",
      icon: <StarOutlined />,
    },
    {
      key: "CRM",
      link: "/crm/dashboard",
      title: "Gestion des terrains et locaux",
      icon: <BorderOutlined />,
    },
    {
      key: "CRM",
      link: "/crm/dashboard",
      title: "Gestion de l'agenda partagée",
      icon: <CalendarOutlined />,
    },
    {
      key: "CRM",
      link: "/crm/dashboard",
      title: "Gestion des courriers",
      icon: <FolderOutlined />,
    },
    {
      key: "CRM",
      link: "/crm/dashboard",
      title: "Gestion de la bibliothèque",
      icon: <CopyOutlined />,
    },
    {
      key: "CRM",
      link: "/crm/dashboard",
      title: "Gestion des modèles des emails",
      icon: <MailOutlined />,
    },
    {
      key: "CRM",
      link: "/crm/dashboard",
      title: "Tableau de bord et statistiques",
      icon: <BarChartOutlined />,
    },
  ];
  return (
    <>
      <Flex justify="center" align="center">
        <Title
          className="bg-layout-blur-1 backdrop-blur-sm rounded-[6px]"
          level={2}
        >
          <img width="160px" src={logo_signIn} alt="Logo DGAC" />
        </Title>
      </Flex>
      <div className="m-10 layout-content home-layout p-[30px]">
        <Row className="rowgap-vbox" gutter={[24, 0]}>
          {modules.map((c, index) => (
            <Col
              key={index}
              xs={24}
              sm={24}
              md={12}
              lg={8}
              xl={6}
              className="mb-24"
            >
              <Card
                bordered={false}
                className="criclebox min-h-32"
                onClick={() => navigate(c.link)}
              >
                <div className="number">
                  <Row align="middle" gutter={[24, 0]}>
                    <Col span={18}>
                      <span>{c.title}</span>
                    </Col>
                    <Col span={6}>
                      <div className="icon-box">{c.icon}</div>
                    </Col>
                  </Row>
                </div>
              </Card>
            </Col>
          ))}
        </Row>
      </div>
    </>
  );
};

export default MenusCRM;
