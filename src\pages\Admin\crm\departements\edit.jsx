import { ModalForm } from "@ant-design/pro-components";
import { Card, Col, Form, Input, Row, Select } from "antd";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { pays } from "./data";

function EditDepartementForm({ open, onCancel, record }) {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  useEffect(() => {
    if (open) {
      form.setFieldsValue(record);
    }
  }, [open]);

  return (
    <ModalForm
      title={t("Modifier un département")}
      form={form}
      open={open}
      modalProps={{
        style: { top: 20 },
      }}
      //   onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.edit"),
          resetText: t("common.actions.cancel"),
        },
      }}
    >
      <Card className="mt-2">
        <Row gutter={4} >
          <Col className="gutter-row" span={24}>
            <Form.Item name="code" label={t("Code")}>
              <Input />
            </Form.Item>
          </Col>
          <Col className="gutter-row" span={24}>
            <Form.Item
              name="libelle"
              label={t("Libelle")}
              //  rules={[{ required: true, message: "Ce champ est obligatoire" }]}
            >
              <Input />
            </Form.Item>
          </Col>
          <Col className="gutter-row" span={24}>
            <Form.Item
              name="pays_id"
              label={t("Pays")}
              //  rules={[{ required: true, message: "Ce champ est obligatoire" }]}
            >
              <Select
                allowClear
                placeholder={t("Pays")}
                options={pays.map((item) => ({
                  value: item.id,
                  label: item.libelle,
                }))}
                onSelect={(e) => {}}
                onClear={() => {}}
              />
            </Form.Item>
          </Col>
        </Row>
      </Card>
    </ModalForm>
  );
}
export default EditDepartementForm;
