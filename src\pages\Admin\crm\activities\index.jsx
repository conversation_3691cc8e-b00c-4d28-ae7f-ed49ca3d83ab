import { useState, useRef, useEffect } from "react";
import { ProTable } from "@ant-design/pro-components";
import { Button, Popconfirm, Space, Input, Tooltip, Typography, Card, Modal, Upload, message, Row, Col, Select } from "antd";
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined, SearchOutlined, UploadOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { calculateLeadScore } from "../../../../modules/crm/utils/scoring";
import * as XLSX from "xlsx";

import CreateTask from "./create";
import EditTask from "./edit";
import ViewTask from "./view";
import { datas } from "./datas";
import dayjs from "dayjs";

function Tasks() {
  const { t } = useTranslation();
  const tableRef = useRef();

  const [dataSource, setDataSource] = useState(datas);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [viewModalVisible, setViewModalVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState(null);
  const [viewingRecord, setViewingRecord] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [importModalVisible, setImportModalVisible] = useState(false);

  const [filters, setFilters] = useState({
    source: null,
    secteur: null,
    scoreRange: null,
    status: null,
  });

  const calculateScores = (leads) => {
    return leads.map((lead) => {
      const leadForScoring = {
        sector: lead.sector || "Autre",
        location: lead.locations?.[0] || "Other",
        projectSize: lead.projectSize || "Petit",
        investmentType: lead.investmentType || "Non spécifié",
        potential: {
          jobCreation: parseInt(lead.jobsDirect) || 0,
          techTransfer: lead.techTransfer || false,
          investmentAmount: parseFloat(lead.investmentAmount) || 0,
          infrastructureImpact: lead.infrastructureImpact || "Faible",
        },
        interactions: lead.notes ? [{ type: "email_response" }] : [],
      };
      const score = calculateLeadScore(lead);
      return { ...lead, score };
    });
  };

  useEffect(() => {
    const scoredData = calculateScores(dataSource);
    const sortedDataSource = [...scoredData].sort((a, b) => b.score - a.score);
    setDataSource(sortedDataSource);
  }, [dataSource.length]);

  const handleCancel = () => {
    setViewModalVisible(false);
  };

  const handleEdit = (record) => {
    setEditingRecord(record);
    setEditModalVisible(true);
  };

  const handleCreateSuccess = (newLead) => {
    const newData = [...dataSource, newLead];
    const scoredData = calculateScores(newData);
    const sortedData = [...scoredData].sort((a, b) => b.score - a.score);
    setDataSource(sortedData);
    setCreateModalVisible(false);
  };

  const handleEditSuccess = (updatedLead) => {
    const newData = dataSource.map((item) => (item.key === updatedLead.key ? updatedLead : item));
    const scoredData = calculateScores(newData);
    const sortedData = [...scoredData].sort((a, b) => b.score - a.score);
    setDataSource(sortedData);
    setEditModalVisible(false);
  };

  const handleDelete = (record) => {
    const newDataSource = dataSource.filter((item) => item.key !== record.key);
    setDataSource(newDataSource);
  };

  const handleFileUpload = (file) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: "array" });
        const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
        const jsonData = XLSX.utils.sheet_to_json(firstSheet);

        const processedData = jsonData.map((item, index) => ({
          key: dataSource.length + index + 1,
          leadName: item["Lead Name"] || item["leadName"] || "",
          enterprise: item["Enterprise"] || item["enterprise"] || "",
          campaign: item["Campaign"] || item["campaign"] || "",
          stageDetails: item["Stage Details"] || item["stageDetails"] || "",
          confidential: item["Confidential"] || item["confidential"] || false,
          investmentType: item["Investment Type"] || item["investmentType"] || "Nouveau (friche industrielle)",
          investmentPlan: item["Investment Plan"] || item["investmentPlan"] || "",
          investmentAmount: item["Investment Amount"] || item["investmentAmount"] || "",
          jobsDirect: item["Jobs Direct"] || item["jobsDirect"] || 0,
          jobsIndirect: item["Jobs Indirect"] || item["jobsIndirect"] || 0,
          salary: item["Salary"] || item["salary"] || "",
          locations: Array.isArray(item["Locations"])
            ? item["Locations"]
            : typeof item["Locations"] === "string"
            ? item["Locations"].split(",")
            : [],
          landNeed: item["Land Need"] || item["landNeed"] || "",
          landOffer: item["Land Offer"] || item["landOffer"] || "",
          opportunityOrigin: item["Opportunity Origin"] || item["opportunityOrigin"] || "",
          opportunityUrl: item["Opportunity URL"] || item["opportunityUrl"] || "",
          agency: item["Agency"] || item["agency"] || "",
          cost: item["Cost"] || item["cost"] || "",
          decisionDate: item["Decision Date"] || item["decisionDate"] || "",
          announcementDate: item["Announcement Date"] || item["announcementDate"] || "",
          projectType: item["Project Type"] || item["projectType"] || "",
          productService: item["Product Service"] || item["productService"] || "",
          companyOrContact: item["Company Or Contact"] || item["companyOrContact"] || "",
          category: item["Category"] || item["category"] || "taskType",
          type: item["Type"] || item["type"] || "taskType",
          dueDate: item["Due Date"] || item["dueDate"] || "",
          assignedTo: item["Assigned To"] || item["assignedTo"] || "Test User",
          reminder: item["Reminder"] || item["reminder"] || "",
          notes: item["Notes"] || item["notes"] || "",
          status: item["Status"] || item["status"] || "Pre-Pipeline",
          pipelineAdded: item["Pipeline Added"] || item["pipelineAdded"] || new Date().toLocaleDateString("en-US", { year: "numeric", month: "short", day: "numeric" }),
          timeInCurrentStage: item["Time In Current Stage"] || item["timeInCurrentStage"] || "TODAY",
          owner: item["Owner"] || item["owner"] || "Test User",
          currentStage: item["Current Stage"] || item["currentStage"] || "Pre-Pipeline",
          leadSource: item["Lead Source"] || item["leadSource"] || "Marketing digital",
          flowUps: item["Flow Ups"] || item["flowUps"] || [], // Support flowUps in import
        }));

        const newData = [...dataSource, ...processedData];
        const scoredData = calculateScores(newData);
        const sortedData = [...scoredData].sort((a, b) => b.score - a.score);
        setDataSource(sortedData);

        message.success(`${processedData.length} leads imported successfully`);
        setImportModalVisible(false);
      } catch (error) {
        console.error("Error parsing file:", error);
        message.error("Error importing leads. Please check the file format.");
      }
    };
    reader.readAsArrayBuffer(file);
    return false;
  };

  const filterData = (data) => {
    return data.filter((lead) => {
      if (filters.source && lead.leadSource !== filters.source) return false;
      if (filters.secteur && lead.projectType && !lead.projectType.toLowerCase().includes(filters.secteur.toLowerCase())) return false;
      if (filters.scoreRange) {
        const score = lead.score || 0;
        switch (filters.scoreRange) {
          case "high":
            if (score < 75) return false;
            break;
          case "medium":
            if (score < 50 || score >= 75) return false;
            break;
          case "low":
            if (score >= 50) return false;
            break;
        }
      }
      if (filters.status && lead.status !== filters.status) return false;
      return true;
    });
  };

  const filteredDataSource = filterData(dataSource);

  const resetFilters = () => {
    setFilters({
      source: null,
      secteur: null,
      scoreRange: null,
      status: null,
    });
  };

  const FilterSection = () => (
    <div style={{ marginBottom: 16 }}>
      <Row gutter={16} align="middle">
        <Col span={6}>
          <div>
            <Select
              style={{ width: "100%" }}
              placeholder="Sélectionner une source"
              options={[
                { value: "Marketing digital", label: "Marketing digital" },
                { value: "Conférence", label: "Conférence" },
                { value: "Site web", label: "Site web" },
                { value: "Partenaire", label: "Partenaire" },
                { value: "Réseau", label: "Réseau" },
              ]}
              value={filters.source}
              onChange={(value) => setFilters({ ...filters, source: value })}
              allowClear
            />
          </div>
        </Col>
        <Col span={6}>
          <div>
            <Select
              style={{ width: "100%" }}
              placeholder="Sélectionner un secteur"
              options={[
                { value: "INDUSTRIE MANUFACTURIÈRE", label: "Industrie manufacturière" },
                { value: "ÉNERGIE", label: "Énergie" },
                { value: "TECHNOLOGIE", label: "Technologie" },
                { value: "AGRICULTURE", label: "Agriculture" },
                { value: "TOURISME", label: "Tourisme" },
              ]}
              value={filters.secteur}
              onChange={(value) => setFilters({ ...filters, secteur: value })}
              allowClear
            />
          </div>
        </Col>
        <Col span={6}>
          <div>
            <Select
              style={{ width: "100%" }}
              placeholder="Sélectionner un score"
              options={[
                { value: "high", label: "Score élevé (75-100)" },
                { value: "medium", label: "Score moyen (50-74)" },
                { value: "low", label: "Score bas (0-49)" },
              ]}
              value={filters.scoreRange}
              onChange={(value) => setFilters({ ...filters, scoreRange: value })}
              allowClear
            />
          </div>
        </Col>
        <Col span={6}>
          <div>
            <Select
              style={{ width: "100%" }}
              placeholder="Sélectionner un statut"
              options={[
                { value: "Pre-Pipeline", label: "Pre-Pipeline" },
                { value: "Pipeline", label: "Pipeline" },
                { value: "Negotiation", label: "Négociation" },
                { value: "Closed-Won", label: "Gagné" },
                { value: "Closed-Lost", label: "Perdu" },
              ]}
              value={filters.status}
              onChange={(value) => setFilters({ ...filters, status: value })}
              allowClear
            />
          </div>
        </Col>
      </Row>
      <Row gutter={16} justify="end" style={{ marginTop: 16 }}>
        <Col>
          <Space>
            <Button onClick={resetFilters}>Réinitialiser</Button>
            <Button type="primary" onClick={() => console.log("Filtrer clicked")}>
              Filtrer
            </Button>
            <Button type="link" onClick={() => console.log("Réduire clicked")}>
              Réduire ↑
            </Button>
          </Space>
        </Col>
      </Row>
    </div>
  );

  const columns = [
    {
      title: t("crm.activities.fields.title"),
      dataIndex: "leadName",
      key: "leadName",
    },
    {
      title: t("crm.activities.fields.type"),
      dataIndex: "projectType",
      key: "projectType",
    },
    {
      title: t("crm.activities.fields.assigned_to"),
      dataIndex: "assignedTo",
      key: "assignedTo",
    },
    {
      title: t("crm.activities.fields.status"),
      dataIndex: "status",
      key: "status",
    },
    {
      title: t("crm.activities.fields.due_date"),
      dataIndex: "dueDate",
      key: "dueDate",
    },
    {
      title: "Score",
      dataIndex: "score",
      key: "score",
      sorter: (a, b) => a.score - b.score,
      render: (score) => score ?? "N/A",
    },
    {
      title: t("common.actions.title"),
      key: "action",
      align: "center",
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title={t("common.actions.view")}>
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => {
                setViewingRecord(record);
                setViewModalVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title={t("common.actions.edit")}>
            <Button
              type="link"
              style={{ color: "#f5b041" }}
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title={t("common.actions.delete")}>
            <Popconfirm
              title={t("common.messages.confirm_delete")}
              onConfirm={() => handleDelete(record)}
            >
              <Button type="link" style={{ color: "#ec7063" }} icon={<DeleteOutlined />} />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  const flowUpColumns = [
    {
      title: "Type",
      dataIndex: "type",
      key: "type",
    },
    {
      title: "Date",
      dataIndex: "date",
      key: "date",
      render: (date) => dayjs(date).format("DD/MM/YYYY"),
    },
    {
      title: "Statut",
      dataIndex: "status",
      key: "status",
    },
    {
      title: "Notes",
      dataIndex: "notes",
      key: "notes",
    },
  ];

  return (
    <Card
      bordered
      title={<Typography.Title level={4}>{"Listes des leads"}</Typography.Title>}
      extra={
        <Space>
          <Input
            size="large"
            placeholder={t("common.search")}
            suffix={<SearchOutlined style={{ color: "#bfbfbf" }} />}
          />
          <Button
            type="primary"
            icon={<UploadOutlined />}
            onClick={() => setImportModalVisible(true)}
          >
            {"Importer"}
          </Button>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setCreateModalVisible(true)}
          >
            {"Ajouter"}
          </Button>
        </Space>
      }
    >
      <FilterSection />

      <ProTable
        actionRef={tableRef}
        loading={isLoading}
        columns={columns}
        dataSource={filteredDataSource}
        rowKey="key"
        search={false}
        options={false}
        pagination={{ showSizeChanger: true, defaultPageSize: 5 }}
        expandable={{
          expandedRowRender: (record) => (
            <div>
              {record.plan && record.plan.activities && record.plan.activities.length > 0 ? (
                <div>
                  <Typography.Title level={5}>Activités du Plan</Typography.Title>
                  <ProTable
                    columns={[
                      { title: "Activity Name", dataIndex: "nom", key: "nom" },
                      { title: "Description", dataIndex: "resume", key: "resume" },
                      { title: "Scheduled Date", dataIndex: "planifier", key: "planifier" },
                    ]}
                    dataSource={record.plan.activities}
                    rowKey="nom"
                    pagination={false}
                    search={false}
                    options={false}
                  />
                </div>
              ) : (
                <div></div>
              )}
              {record.flowUps && record.flowUps.length > 0 ? (
                <div style={{ marginTop: 16 }}>
                  <Typography.Title level={5}>Historique des Suivis</Typography.Title>
                  <ProTable
                    columns={flowUpColumns}
                    dataSource={record.flowUps}
                    rowKey="id"
                    pagination={false}
                    search={false}
                    options={false}
                  />
                </div>
              ) : (
                <div style={{ marginTop: 16 }}>Aucun suivi disponible.</div>
              )}
            </div>
          ),
          rowExpandable: (record) => (record.plan && record.plan.activities && record.plan.activities.length > 0) || (record.flowUps && record.flowUps.length > 0),
        }}
      />

      {/* Create Modal */}
      <Modal
        title={"Créer un lead"}
        visible={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        footer={null}
        width={1500}
      >
        <CreateTask
          onCancel={() => setCreateModalVisible(false)}
          onSuccess={handleCreateSuccess}
        />
      </Modal>

      {/* Edit Modal */}
      <Modal
        title={"Modifier Lead: " + editingRecord?.leadName}
        visible={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        footer={null}
        width={1500}
      >
        <EditTask
          record={editingRecord}
          onCancel={() => setEditModalVisible(false)}
          onSuccess={handleEditSuccess}
        />
      </Modal>

      {/* View Modal */}
      <Modal
        title={t("crm.activities.view")}
        visible={viewModalVisible}
        onCancel={handleCancel}
        footer={null}
        width={800}
      >
        <ViewTask
          record={viewingRecord}
          onCancel={() => setViewModalVisible(false)}
        />
      </Modal>

      {/* Import Modal */}
      <Modal
        title="Importer des leads depuis un fichier"
        visible={importModalVisible}
        onCancel={() => setImportModalVisible(false)}
        footer={null}
      >
        <div style={{ textAlign: "center", padding: "20px" }}>
          <Upload.Dragger
            name="file"
            multiple={false}
            accept=".xlsx,.xls,.csv"
            beforeUpload={handleFileUpload}
            showUploadList={false}
          >
            <p className="ant-upload-drag-icon">
              <UploadOutlined style={{ fontSize: "48px", color: "#1890ff" }} />
            </p>
            <p className="ant-upload-text">Cliquez ou glissez-déposez un fichier Excel/CSV ici</p>
            <p className="ant-upload-hint">
              Supporte les fichiers Excel (.xlsx, .xls) et CSV. Assurez-vous que la structure correspond au modèle de lead.
            </p>
          </Upload.Dragger>
          <div style={{ marginTop: "20px" }}>
            <Typography.Text strong>Structure attendue:</Typography.Text>
            <Typography.Paragraph>
              Le fichier doit contenir les champs correspondants à la structure des leads (nom du lead, entreprise, type d'investissement, etc.).
            </Typography.Paragraph>
            <Button
              type="link"
              onClick={() => {
                const sampleData = [
                  {
                    "Lead Name": "Sample Lead",
                    Enterprise: "Sample Corp",
                    "Investment Type": "Nouveau (friche industrielle)",
                    "Investment Amount": "10.0 m EUR",
                    Locations: "Tunisia,France",
                    "Project Type": "INDUSTRIE MANUFACTURIÈRE",
                    Status: "Pre-Pipeline",
                    "Lead Source": "Marketing digital",
                    "Flow Ups": JSON.stringify([
                      { id: 1, type: "Email", date: "2025-06-01", status: "En attente", notes: "Sample follow-up" },
                    ]),
                  },
                ];

                const ws = XLSX.utils.json_to_sheet(sampleData);
                const wb = XLSX.utils.book_new();
                XLSX.utils.book_append_sheet(wb, ws, "Leads");
                XLSX.writeFile(wb, "lead_import_template.xlsx");
              }}
            >
              Télécharger un modèle
            </Button>
          </div>
        </div>
      </Modal>
    </Card>
  );
}

export default Tasks;