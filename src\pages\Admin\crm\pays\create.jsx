import { ModalForm } from "@ant-design/pro-components";
import {
  Card,
  Col,
  DatePicker,
  Form,
  Input,
  InputNumber,
  Radio,
  Row,
  Select,
} from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";
function CreatePaysForm({ open, onCancel, onSuccess }) {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  return (
    <ModalForm
      title={t("Ajouter un pays")}
      form={form}
      open={open}
      modalProps={{
        style: { top: 20 },
      }}
      //   onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.save"),
          resetText: t("common.actions.cancel"),
        },
      }}
    >
      <Card className="mt-2">
        <Row gutter={4} >
          <Col className="gutter-row" span={24}>
            <Form.Item name="code" label={t("Code")}>
              <Input />
            </Form.Item>
          </Col>
          <Col className="gutter-row" span={24}>
            <Form.Item
              name="libelle"
              label={t("Libelle")}
              //  rules={[{ required: true, message: "Ce champ est obligatoire" }]}
            >
              <Input />
            </Form.Item>
          </Col>
        </Row>
      </Card>
    </ModalForm>
  );
}
export default CreatePaysForm;
