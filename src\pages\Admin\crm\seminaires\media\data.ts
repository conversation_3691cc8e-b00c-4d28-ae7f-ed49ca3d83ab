import dayjs from "dayjs";

const medias = [
  {
    id: 1,
    action: "Action 100173TF01",
    pays_id: 1,
    type_id: 1,
    region: "nom de la région ",
    responsable : "resposable Fipa",
    propose_par : "Test test" ,
    date_debut: dayjs("2025-01-10"),
    date_fin: dayjs("2025-01-10"),
  },
];
const pays = [
  { id: 1, nom: "France" },
  { id: 2, nom: "Italie" },
  { id: 3, nom: "Allemagne" },
  { id: 4, nom: "Espagne" },
  { id: 5, nom: "États-Unis" },
  { id: 6, nom: "Canada" },
  { id: 7, nom: "Algérie" },
  { id: 8, nom: "Maroc" },
  { id: 9, nom: "Libye" },
  { id: 10, nom: "Turquie" },
  { id: 11, nom: "Arabie Saoudite" },
  { id: 12, nom: "Émirats arabes unis" },
  { id: 13, nom: "Qatar" },
  { id: 14, nom: "Égypte" },
  { id: 15, nom: "Chine" },
  { id: 16, nom: "Japon" },
  { id: 17, nom: "Corée du Sud" },
  { id: 18, nom: "Belgique" },
  { id: 19, nom: "Suisse" },
  { id: 20, nom: "Russie" },
];


const types =[
   { id: 1, type : "Insertion annonce presse" },
   { id: 2, type : "Communiqué de presse" },
   { id: 3, type : "Article de presse" },
   { id: 4, type : "Interview" },
   { id: 5, type : "Publi reportage" },
   { id: 6 , type : "Spécial pays" } ,
   {id : 7 , type : "Conférence de presse"} 
] ;
export { medias, pays  , types};
