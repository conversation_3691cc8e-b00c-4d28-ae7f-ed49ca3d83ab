import { ModalForm } from "@ant-design/pro-components";
import { Card, Col, DatePicker, Form, Input, Row } from "antd";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

function PreparationForm({ open, onCancel, record }) {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  return (
    <ModalForm
      title={t("Préparation du séminaire")}
      form={form}
      open={open}
      width="70%"
      modalProps={{
        style: { top: 20 },
      }}
      //   onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.save"),
          resetText: t("common.actions.cancel"),
        },
      }}
    >
      <Card className="mt-2">
        <Row gutter={16} style={{ marginBottom: "10px" }}>
          <Col span={12}></Col>
          <Col span={4}>Date butoir</Col>
          <Col span={4}>Budget prévu en DT</Col>
          <Col span={4}>Budget réalisé en DT</Col>
        </Row>
        <Row gutter={16} style={{ marginBottom: "10px" }}>
          <Col span={4}>Location salle</Col>
          <Col span={8}>
            <Input />
          </Col>
          <Col span={4}>
            <DatePicker />
          </Col>
          <Col span={4}>
            <Input />
          </Col>
          <Col span={4}>
            <Input />
          </Col>
        </Row>
        <Row gutter={16} style={{ marginBottom: "10px" }}>
          <Col span={4}>Media et communication</Col>
          <Col span={8}>
            <Input />
          </Col>
          <Col span={4}>
            <DatePicker />
          </Col>
          <Col span={4}>
            <Input />
          </Col>
          <Col span={4}>
            <Input />
          </Col>
        </Row>
        <Row gutter={16} style={{ marginBottom: "10px" }}>
          <Col span={4}>Binôme</Col>
          <Col span={8}>
            <Input />
          </Col>
          <Col span={4}>
            <DatePicker />
          </Col>
          <Col span={4}>
            <Input />
          </Col>
          <Col span={4}>
            <Input />
          </Col>
        </Row>
        <Row gutter={16} style={{ marginBottom: "10px" }}>
          <Col span={4}>Autre organisme</Col>
          <Col span={8}>
            <Input />
          </Col>
          <Col span={4}>
            <DatePicker />
          </Col>
          <Col span={4}>
            <Input />
          </Col>
          <Col span={4}>
            <Input />
          </Col>
        </Row>
        <Row gutter={16} style={{ marginBottom: "10px" }}>
          <Col span={12}></Col>
          
          <Col span={4}>
            <DatePicker />
          </Col>
          <Col span={4}>
            <Input />
          </Col>
          <Col span={4}>
            <Input />
          </Col>
        </Row>

        <Row gutter={16}>
          <Col md={24} xs={24}>
            <Form.Item name={"intitule"} label={t("Outils promotionnels")}>
              <Input.TextArea rows ={3} />
            </Form.Item>
          </Col>
        </Row>
      </Card>
    </ModalForm>
  );
}
export default PreparationForm;
