import { ModalForm } from "@ant-design/pro-components";
import {
  <PERSON><PERSON>,
  Card,
  Checkbox,
  Col,
  DatePicker,
  Divider,
  Form,
  Input,
  InputNumber,
  Modal,
  Popconfirm,
  Radio,
  Row,
  Select,
  Steps,
  Table,
} from "antd";

import TextArea from "antd/es/input/TextArea.js";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { pays } from "./data";
import { CheckCircleTwoTone } from "@ant-design/icons";
import Pays from "../../pays";

function EditDemarchageDirect({ open, onCancel, record, show }) {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [steps, setSteps] = useState(record.initialSteps);
  const [form] = Form.useForm();
  useEffect(() => {
    if (open) {
      //setSteps(record.initialSteps)
      form.setFieldsValue(record);
    }
  }, [open]);
  const [completedSteps, setCompletedSteps] = useState([]);
  const [showMotifModal, setShowMotifModal] = useState(false);
  const [selectedStepId, setSelectedStepId] = useState(null);
  const [reason, setReason] = useState("");
  const handleStepClick = (step) => {
    if (step.motif && step.status === "wait") {
      setSelectedStepId(step.id);
      setShowMotifModal(true);
    } else {
      toggleStepStatus(step.id);
    }
  };
  const toggleStepStatus = (id) => {
    const updated = steps.map((step) => {
      if (step.id === id) {
        const newStatus = step.status === "finish" ? "wait" : "finish";
        return { ...step, status: newStatus, reason: reason };
      }
      return step;
    });
    setSteps(updated);
  };
  return (
    <ModalForm
      title={
        show ? t("Afficher un contact : démarchage direct") : t("Modifier un contact : démarchage direct")
      }
      form={form}
      open={open}
      width="80%"
      modalProps={{
        style: { top: 20 },
      }}
      //   onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={ show ? {
         submitButtonProps: { hidden: true },
        searchConfig: {
        
          resetText: t("common.actions.cancel"),
        },
      } : { searchConfig: {
          submitText: t("common.actions.edit"),
          resetText: t("common.actions.cancel"),
        }}}
    >
      <Card className="mt-2">
             <Form.Item name={"action"} label={t("Action")}>
               <Checkbox.Group
                 options={[
                   "Proposée",
                   "Programmée",
                   "Réalisée",
                   "Reportée",
                   "Annulée",
                 ]}
               />
             </Form.Item>
     
             <Row gutter={16}>
               <Col md={24} xs={24}>
                 <Form.Item name={"presentation"} label={t("Présentation")}>
                   <Input.TextArea />
                 </Form.Item>
               </Col>
               <Col md={16} xs={24}>
                 <Form.Item name={"initiateur"} label={t("Initiateur")}>
                   <Select />
                 </Form.Item>
               </Col>
               <Col md={8} xs={24}>
                 <Form.Item name={"inclure"} label={t("Inclure")}>
                   <Select />
                 </Form.Item>
               </Col>
                <Col md={16} xs={24}>
                 <Form.Item name={"secteur"} label={t("Secteur")}>
                   <Select />
                 </Form.Item>
               </Col>
                <Col md={8} xs={24}>
                 <Form.Item name={"groupe_secteur"} label={t("Groupe secteur")}>
                   <Select />
                 </Form.Item>
               </Col>
                   <Col md={8} xs={24}>
                 <Form.Item name={"pays_id"} label={t("Pays")}>
                   <Select />
                 </Form.Item>
               </Col>
                   <Col md={16} xs={24}>
                 <Form.Item name={"régions"} label={t("Régions")}>
                   <Input />
                 </Form.Item>
               </Col>
                <Col md={12} xs={24}>
                 <Form.Item name={"date_debut"} label={t("Date début")}>
                   <DatePicker />
                 </Form.Item>
               </Col>
                <Col md={12} xs={24}>
                 <Form.Item name={"date_fin"} label={t("Date fin")}>
                   <DatePicker />
                 </Form.Item>
               </Col>
                <Col md={12} xs={24}>
                 <Form.Item name={"conjointe"} label={t("Conjointe/Non conjointe")}>
                   <Select />
                 </Form.Item>
               </Col>
                 <Col md={12} xs={24}>
                 <Form.Item name={"cadre_siege"} label={t("Cadre du siège")}>
                   <Select />
                 </Form.Item>
               </Col>
                <Col md={12} xs={24}>
                 <Form.Item name={"objectif_initiateur"} label={t("Objectif contacts intéressants initiateur")}>
                   <Input />
                 </Form.Item>
               </Col>
               <Col md={12} xs={24}>
                 <Form.Item name={"objectif_binôme"} label={t("Objectif contacts intéressants binôme")}>
                   <Input />
                 </Form.Item>
               </Col>
             </Row>
           </Card>
    </ModalForm>
  );
}
export default EditDemarchageDirect;
