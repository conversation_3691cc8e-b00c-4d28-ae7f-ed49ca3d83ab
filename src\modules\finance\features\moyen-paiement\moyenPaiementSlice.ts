import {createAsyncThunk, createSlice} from "@reduxjs/toolkit";
import { default as api } from "@/apis/axiosInterceptor";

export const getMoyenPaiement : any = createAsyncThunk(
    "get/moyenPaiement",
    async(data : { pageNumber : number, code : string, libelle :string }, _thunkAPI) => {
        try {
            let url = `/moyenPaiements`;
            if(data.pageNumber)
            {
                url += `?page=${data.pageNumber}`;
            }
            if(data.code)
            {
                url += `&code=${data.code}`;
            }
            if(data.libelle)
            {
                url += `&libelle=${data.libelle}`;
            }
            const resp = await api.get(url);
            return resp.data;
        } catch (error:any) {
            console.log(error);
        }
    }
);

export const addMoyenPaiement : any = createAsyncThunk(
    "create/moyenPaiement",
    async(data:any, _thunkAPI) => {
        try {
            let url = `/moyenPaiements`;
            const resp = await api.post(url,data);
          _thunkAPI.dispatch(getMoyenPaiement())
          return resp.data;
        } catch (error:any) {
            console.log(error);
        }
    }
);

export const updateMoyenPaiement : any = createAsyncThunk(
    "update/MoyenPaiement",
    async(data:any, _thunkAPI) => {
        try {
            let url = `/moyenPaiements/${data.id}`;
            const resp = await api.patch(url,data);
          _thunkAPI.dispatch(getMoyenPaiement())
          return resp.data;
        } catch (error:any) {
            console.log(error);
        }
    }
);

export const deleteMoyenPaiement : any = createAsyncThunk(
    "delete/MoyenPaiement",
    async(id:any, _thunkAPI) => {
        try {
            let url = `/moyenPaiements/${id}`;
            const resp = await api.delete(url);
          _thunkAPI.dispatch(getMoyenPaiement())
          return resp.data;
        } catch (error:any) {
            console.log(error);
        }
    }
)


export const moyenPaiementsSlice = createSlice({
  name: 'moyenPaiement',
  initialState: {
    data: [],
  },
  reducers: {},
  extraReducers: builder => {
    builder.addCase(getMoyenPaiement.fulfilled, (state, action) => {
      state.data = action.payload
    })
  }
})

export default moyenPaiementsSlice.reducer

