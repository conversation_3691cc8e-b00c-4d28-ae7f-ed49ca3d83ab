import { Form, Input, Switch, Button , message } from "antd";
import postalCodes from "./datas";

const CreatePostalCode = ({ onCancel, setDataSource }) => {
  const handleSubmit = async (values) => {
    try {
      const newKey = Date.now().toString();
      const newPostalCode = { key: newKey, ...values };
      const updatedData = [...postalCodes, newPostalCode];
      setDataSource(updatedData);
      message.success("Code Postal créé avec succès!");
      onCancel();
    } catch (error) {
      console.error(error);
      message.error("Erreur lors de la création du Code Postal");
    }
  };

  return (
    <Form layout="vertical" onFinish={handleSubmit}>
      <Form.Item
        name="code"
        label="Code"
        rules={[{ required: true, message: 'Veuillez saisir le code' }]}
      >
        <Input placeholder="Saisissez le code" />
      </Form.Item>

      <Form.Item
        name="libelle"
        label="Libellé"
        rules={[{ required: true, message: 'Veuillez saisir le libellé' }]}
      >
        <Input placeholder="Saisissez le libellé" />
      </Form.Item>

      <Form.Item
        name="decentralisee"
        label="Décentralisée"
        valuePropName="checked"
      >
        <Switch />
      </Form.Item>

      <Form.Item
        name="tauxPrime"
        label="Taux Prime"
        rules={[{ required: true, message: 'Veuillez saisir le taux prime' }]}
      >
        <Input placeholder="Saisissez le taux prime" />
      </Form.Item>

      <Form.Item>
        <div style={{ textAlign: 'right' }}>
        <Button key="back"  style={{marginRight:'15px'}} >
            Annuler
          </Button>
          <Button type="primary" htmlType="submit">
            Enregistrer
          </Button>
        </div>
      </Form.Item>
    </Form>
  );
};

export default CreatePostalCode;
