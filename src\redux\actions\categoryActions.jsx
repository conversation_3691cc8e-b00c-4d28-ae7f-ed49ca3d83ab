// src/redux/actions/dataActions.js
import axiosInstance from '../../apis/axiosInterceptor';
import { FETCH_CATEGORIES_FAILURE, FETCH_CATEGORIES_START, FETCH_CATEGORIES_SUCCESS } from '../constants/categoryConstants';
import { hideLoader, showLoader } from './loadingActions';
import { toast } from 'sonner';
  
  export const fetchDataStart = () => ({
    type: FETCH_CATEGORIES_START,
  });
  
  export const fetchDataSuccess = (data) => ({
    type: FETCH_CATEGORIES_SUCCESS,
    payload: data,
  });
  
  export const fetchDataFailure = (error) => ({
    type: FETCH_CATEGORIES_FAILURE,
    payload: error,
  });
  
  export const getAllCategories = () => async (dispatch) => {
    dispatch(fetchDataStart());
    dispatch(showLoader())
    try {
      
      const response = await axiosInstance.get('/inventory/categories');
      console.log(response);
      
     
      dispatch(fetchDataSuccess(response.data.data));
      dispatch(hideLoader())
    } catch (error) {
        toast.error("Erreur lors de la récupération des catégories");
      console.log(error);
      dispatch(fetchDataFailure(error.message));
      dispatch(hideLoader())
    }
  };
  