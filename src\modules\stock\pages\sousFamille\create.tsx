import { ModalForm } from "@ant-design/pro-components";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { Form } from "antd";
import SousFamilleForm from "../../components/SousFamilleForm";

function CreateSousFamilleForm({ open, setOpen, tableRef,familles, tvas,}){
    const {t} = useTranslation();
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
  
  
    return(
    <ModalForm
      title={t("sous_familles.add")}
      open={open}
    //   onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
        setOpen();
        }
      }}
      form={form}
      initialValues={{allow_negative_stock:1}}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.save"),
          resetText: t("common.actions.cancel"),
        },
      }}
    >
      <SousFamilleForm familles={familles} tvas={tvas}/>
    </ModalForm>
    )
}
export default CreateSousFamilleForm;