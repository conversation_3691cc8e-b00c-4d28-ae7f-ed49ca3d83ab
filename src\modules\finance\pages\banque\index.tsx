import { useState, useRef, useEffect } from "react";
import { ProTable } from "@ant-design/pro-components";
import {
    Button,
    Popconfirm,
    Space,
    Input,
    Tooltip,
    Typography,
    Card,
} from "antd";
import {
    PlusOutlined,
    EditOutlined,
    DeleteOutlined,
    EyeOutlined,
    SearchOutlined,
} from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { banques } from "./data";
import useDebounce from "@src/hooks/useDebounce";
import CreateBanque from "./create";

function Banques() {
  const { t } = useTranslation();
  const tableRef = useRef();
  const [dataSource, setDataSource] = useState(banques);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [record, setRecord] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [tableParams, setTableParams] = useState([]);
  const [total, setTotal] = useState(dataSource.length);
  const [filteredData, setFilteredData] = useState(dataSource);
  const [pageNumber, setPageNumber] = useState(1);

  const handleSearchData = (params) => {
    const filtered = dataSource.filter((item) => {
      return (
        (!params.libelle ||
          item.libelle?.toLowerCase().includes(params.libelle.toLowerCase())) &&
        (!params.symbole ||
          item.symbole?.toLowerCase().includes(params.symbole.toLowerCase())) &&
        (!params.parentUnity || item.parentUnity === params.parentUnity) &&
        (!params.coefficient ||
          item.coefficient
            ?.toString()
            .toLowerCase()
            .includes(params.coefficient.toLowerCase()))
      );
    });

    setFilteredData(filtered);
  };

  const columns = [
    {
      title: t("finance.banques.libelle"),
      dataIndex: "libelle",
      key: "libelle",
    },
    {
      title: t("common.actions.title"),
      key: "action",
      align: "center",
      sorter: false,
      search: false,
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title={t("common.actions.view")}>
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => {
                console.log(record);
                setRecord(record);
                setShowViewModal(true);
                setShowAddModal(true);
              }}
            />
          </Tooltip>
          <Tooltip title={t("common.actions.edit")}>
            <Button
              type="link"
              style={{ color: "#f5b041" }}
              icon={<EditOutlined />}
              onClick={() => {
                setShowViewModal(false);
                setShowAddModal(true);
                setRecord(record);
              }}
            />
          </Tooltip>
          <Tooltip title={t("common.actions.delete")}>
            <Popconfirm
              title={t("common.messages.confirm_delete")}
              onConfirm={() => {
                setIsLoading(true);
                const newDataSource = dataSource.filter(
                  (item) => item.key !== record.key
                );
                setDataSource(newDataSource);
                setFilteredData(newDataSource);
                setIsLoading(false);
              }}
            >
              <Button
                type="link"
                style={{ color: "#ec7063" }}
                icon={<DeleteOutlined />}
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  const handleSearch = (e) => {
    const searchValue = e.target.value;
    setTableParams({ ...tableParams, search: searchValue });
  };

  const debouncedOnChange = useDebounce(handleSearch, 700);

  useEffect(() => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  }, [dataSource, filteredData]);

  return (
    <Card
      bordered
      title={
        <Typography.Title level={4}>{t("finance.banques.list")}</Typography.Title>
      }
      extra={
        <Space>
          <Input
            size="large"
            placeholder={t("common.search")}
            suffix={<SearchOutlined style={{ color: "#bfbfbf" }} />}
            onChange={debouncedOnChange}
          />
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setShowAddModal(true)}
          >
            {t("finance.banques.add")}
          </Button>
        </Space>
      }
    >
      <ProTable
        actionRef={tableRef}
        loading={isLoading}
        columns={columns}
        dataSource={filteredData}
        rowKey="key"
        search={{
          searchText: t("common.filter"),
          labelWidth: "auto",
          defaultCollapsed: false,
        }}
        options={false}
        params={tableParams}
        scroll={{ x: "max-content" }}
        onSubmit={handleSearchData}
        onReset={() => handleSearchData({})}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: 5,
          total: total,
          onChange: (page) => setPageNumber(page),
        }}
      />
      <CreateBanque
        open={showAddModal}
        onCancel={() => setShowAddModal(false)}
        isEditing={record}
        show={showViewModal}
      />
    </Card>
  );
}

export default Banques;
