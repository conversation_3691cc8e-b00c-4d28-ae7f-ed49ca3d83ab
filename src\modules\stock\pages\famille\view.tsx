import { ModalForm, ProTable } from "@ant-design/pro-components";
import { Divider, Form, Table, Typography } from "antd";

import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { DepotForm } from "../../components/DepotForm";
import { emplacements } from "../emplacements/data";
import { articleEmplacements, produits } from "../articles/data";
import FamilleForm from "../../components/FamilleForm";

function ShowFamilleForm({  open, setOpen, record, tvas,}){
    const {t} = useTranslation();
    const [form] = Form.useForm();
  
    useEffect(() => {
      if (open) {
          form.setFieldsValue(record)
      }
    }, [open]);
    
  
  

    return(
    <ModalForm
      title={t("familles.view")}
      disabled
      modalProps={{
        style:{
          top:20,
        },
      }}
      form={form}
      open={open}
      //onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          setOpen(false);

        }
      }}
      submitter={{
        resetButtonProps:{disabled:false},
        submitButtonProps: {hidden:true},
        searchConfig: {
          resetText: t("common.actions.back"),
        },
      }}
    >
      <FamilleForm tvas={tvas}/>
    </ModalForm>
    )
}
export default ShowFamilleForm;