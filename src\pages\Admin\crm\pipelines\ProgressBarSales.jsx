import { Table, Progress } from "antd";

const taskData = [
  { key: "1", user: "Mourad", completed: 10, pending: 2 },
  { key: "2", user: "<PERSON>uhamed", completed: 7, pending: 5 },
  { key: "3", user: "Dale<PERSON>", completed: 15, pending: 1 },
];

export const TaskTable = () => {
  const columns = [
    { title: "Sales Rep", dataIndex: "user", key: "user" },
    { title: "Completed", dataIndex: "completed", key: "completed", render: (text) => <Progress percent={(text / 20) * 100} status="active" /> },
    { title: "Pending", dataIndex: "pending", key: "pending" },
  ];

  return <Table dataSource={taskData} columns={columns} pagination={false} />;
};