import { Input, Form, Row, Col, Select, Radio } from "antd";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

const TierInfoGeneralForm = ({ disabled, tier }) => {
  const { t } = useTranslation();
  const [entreprises, setEntreprises] = useState([]);
  const [departements, setDepartements] = useState([]);
  const [countries, setCountries] = useState([]);
  const [typeTier, setTypeTier] = useState(tier?.type);
  const [form] = Form.useForm();

  useEffect(() => {
    if (tier) {
      setTypeTier(tier.type);
      form.setFieldsValue(tier);
    }
  }, [tier]);

  useEffect(() => {
    setEntreprises([
      { value: 1, label: "entreprise 1" },
      { value: 2, label: "entreprise 2" },
      { value: 3, label: "entreprise 3" },
    ]);
    setDepartements([
      { value: 1, label: "departement 1" },
      { value: 2, label: "departement 2" },
      { value: 3, label: "departement 3" },
      { value: 4, label: "departement 4" },
    ]);

    fetch("https://restcountries.com/v3.1/all?fields=name")
      .then((response) => response.json())
      .then((data) => {
        setCountries(
          data.map((c, index) => ({ value: index, label: c.name.common }))
        );
      })
      .catch((error) => {
        console.error("Error fetching countries:", error);
      });
  }, []);

  const filterOption = (input, option) =>
    (option?.label ?? "").toLowerCase().includes(input.toLowerCase());

  const filterSort = (optionA, optionB) =>
    (optionA?.label ?? "")
      .toLowerCase()
      .localeCompare((optionB?.label ?? "").toLowerCase());

  return (
    <Form
      initialValues={tier}
      layout="vertical"
      disabled={disabled}
      form={form}
    >
      <Row gutter={16}>
        <Col span={24} className="d-flex justify-content-center">
          <Form.Item label={t("vente.info_general.type")} name="type">
            <Radio.Group
              onChange={(e) => setTypeTier(e.target.value)}
              value={typeTier}
            >
              <Radio.Button value="Entreprise">Entreprise</Radio.Button>
              <Radio.Button value="Particulier">Particulier</Radio.Button>
            </Radio.Group>
          </Form.Item>
        </Col>
        <Col md={12} xs={24}>
          <Form.Item
            name="code"
            label={<span>{t("vente.info_general.code")}</span>}
          >
            <Input
              placeholder={t("vente.info_general.code_placeholder")}
              disabled={true}
            />
          </Form.Item>
        </Col>
        {typeTier === "Entreprise" ? (
          <Col md={12} xs={24}>
            <Form.Item
              name={"raisonSociale"}
              label={t("vente.info_general.raison_sociale")}
              rules={[
                {
                  required: true,
                  message: "Veuillez entrer un raison_sociale",
                },
              ]}
            >
              <Input
                placeholder={t("vente.info_general.raison_sociale_placeholder")}
              />
            </Form.Item>
          </Col>
        ) : (
          typeTier === "Particulier" && (
            <>
              <Col md={12} xs={24}>
                <Form.Item
                  name="lastName"
                  label={t("vente.info_general.lastName")}
                  rules={[
                    { required: true, message: "Veuillez entrer le nom" },
                  ]}
                >
                  <Input
                    placeholder={t("vente.info_general.lastName_placeholder")}
                  />
                </Form.Item>
              </Col>
              <Col md={12} xs={24}>
                <Form.Item
                  name="firstName"
                  label={t("vente.info_general.firstName")}
                  rules={[
                    { required: true, message: "Veuillez entrer le prénom" },
                  ]}
                >
                  <Input
                    placeholder={t("vente.info_general.firstName_placeholder")}
                  />
                </Form.Item>
              </Col>
              <Col md={12} xs={24}>
                <Form.Item
                  name="poste"
                  label={t("vente.info_general.poste")}
                  rules={[
                    {
                      required: true,
                      message: "Veuillez sélectionner un poste",
                    },
                  ]}
                >
                  <Input
                    placeholder={t("vente.info_general.poste_placeholder")}
                  />
                </Form.Item>
              </Col>
              <Col md={12} xs={24}>
                <Form.Item
                  name="civilite"
                  label={t("vente.info_general.civilite")}
                  rules={[
                    {
                      required: true,
                      message: "Veuillez sélectionner une civilité",
                    },
                  ]}
                >
                  <Select
                    allowClear
                    placeholder={t("vente.info_general.civilite_placeholder")}
                    options={[
                      { value: "M.", label: "M." },
                      { value: "Mme", label: "Mme" },
                    ]}
                    showSearch
                    filterOption={filterOption}
                    filterSort={filterSort}
                  ></Select>
                </Form.Item>
              </Col>
            </>
          )
        )}
        <Col md={12} xs={24}>
          <Form.Item
            name="email"
            label={t("vente.info_general.email")}
            rules={[
              {
                required: true,
                message: "Veuillez entrer un email",
              },
              {
                type: "email",
                message: "Veuillez entrer un email valide",
              },
            ]}
          >
            <Input placeholder={t("vente.info_general.email_placeholder")} />
          </Form.Item>
        </Col>
        <Col md={12} xs={24}>
          <Form.Item
            name="phone"
            label={t("vente.info_general.phone")}
            rules={[
              {
                required: true,
                message: "Veuillez entrer le numéro de téléphone",
              },
            ]}
          >
            <Input placeholder={t("vente.info_general.phone_placeholder")} />
          </Form.Item>
        </Col>
        <Col md={12} xs={24}>
          <Form.Item
            name="entrepriseId"
            label={t("vente.info_general.entreprise")}
            rules={[
              {
                required: true,
                message: "Veuillez séléctionner l'entreprise",
              },
            ]}
          >
            <Select
              allowClear
              placeholder={t("vente.info_general.entreprise_placeholder")}
              options={entreprises}
              showSearch
              filterOption={filterOption}
              filterSort={filterSort}
            ></Select>
          </Form.Item>
        </Col>
        <Col md={12} xs={24}>
          <Form.Item
            name="departementId"
            label={t("vente.info_general.departement")}
            rules={[
              {
                required: true,
                message: "Veuillez séléctionner le département",
              },
            ]}
          >
            <Select
              allowClear
              placeholder={t("vente.info_general.departement_placeholder")}
              options={departements}
              showSearch
              filterOption={filterOption}
              filterSort={filterSort}
            ></Select>
          </Form.Item>
        </Col>
        <Col md={12} xs={24}>
          <Form.Item name="adresse_1" label={t("vente.info_general.adresse_1")}>
            <Input.TextArea
              placeholder={t("vente.info_general.adresse_1_placeholder")}
            />
          </Form.Item>
        </Col>
        <Col md={12} xs={24}>
          <Form.Item name="adresse_2" label={t("vente.info_general.adresse_2")}>
            <Input.TextArea
              placeholder={t("vente.info_general.adresse_2_placeholder")}
            />
          </Form.Item>
        </Col>
        <Col md={12} xs={24}>
          <Form.Item
            name="paysId"
            label={t("vente.info_general.pays")}
            rules={[
              {
                required: true,
                message: "Veuillez séléctionner le pays",
              },
            ]}
          >
            <Select
              placeholder={t("vente.info_general.pays_placeholder")}
              options={countries}
              allowClear
              showSearch
              filterOption={filterOption}
              filterSort={filterSort}
            />
          </Form.Item>
        </Col>
        <Col md={12} xs={24}>
          <Form.Item
            name="codePostal"
            label={t("vente.info_general.code_postal")}
          >
            <Input
              placeholder={t("vente.info_general.code_postal_placeholder")}
            />
          </Form.Item>
        </Col>
        <Col md={12} xs={24}>
          <Form.Item
            name="matriculeFiscale"
            label={t("vente.info_general.matricule_fiscale")}
            rules={[
              {
                required: true,
                message: "Veuillez entrer le matricule fiscale",
              },
            ]}
          >
            <Input
              placeholder={t(
                "vente.info_general.matricule_fiscale_placeholder"
              )}
            />
          </Form.Item>
        </Col>
        <Col md={12} xs={24}>
          <Form.Item
            name="assujettiTVA"
            label={t("vente.info_general.assujetti_tva")}
            rules={[
              {
                required: true,
                message: "Sélectionnez l'assujetti TVA",
              },
            ]}
          >
            <Radio.Group>
              <Radio value={true}>Oui</Radio>
              <Radio value={false}>Non</Radio>
            </Radio.Group>
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );
};
export default TierInfoGeneralForm;
