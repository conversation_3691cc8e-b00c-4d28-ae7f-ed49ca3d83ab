import React from 'react';
import { useDrop } from 'react-dnd';
import { Calendar, Badge, message } from 'antd';
import { updateEvent } from './utils/data';

export default function CalendarEdit() {
  const [, drop] = useDrop({
    accept: 'calendar-event',
    drop: (item, monitor) => {
      const delta = monitor.getDifferenceFromInitialOffset();
      const newDate = dayjs(item.event.start).add(delta.x, 'hour');
      
      updateEvent(item.event.id, { start: newDate });
      message.success('Event rescheduled');
    },
  });

  return (
    <div ref={drop}>
      {/* Calendar component with draggable events */}
    </div>
  );
}