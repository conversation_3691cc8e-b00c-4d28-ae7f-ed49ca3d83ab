import {
    FileOutlined,
    MinusCircleOutlined,
    PlusCircleOutlined,
    UploadOutlined,
  } from "@ant-design/icons";
  import { Col, DatePicker, Form, Input, message, Row, Upload } from "antd";
  import { useState } from "react";
  import { useTranslation } from "react-i18next";
  
  const Participant = ({ form, view }) => {
    const { t } = useTranslation();
    const handleAdd = () => {
        const currentdata= form.getFieldValue("participants") || [];
        const pays = form.getFieldValue("code_pays");
        const taux = form.getFieldValue("taux");
        const date_info = form.getFieldValue("date_info");
        if (pays  && taux &&  date_info) {
          const key = Date.now();
          form.setFieldsValue({
            participants: [
              ...currentdata,
              {
                key: key,
               code_pays:pays ,
               taux:taux ,
               date_info:date_info 
              }
            ],
          });
          form.resetFields([
            "code_pays" ,
            "taux" ,
            "date_info"
          ]);
        } else {
          message.error(t("Merci de bien vouloir remplir tous les champs."));
        }
      };
    return (
      <>
        <Row gutter={16} style={{  marginBottom: "10px" }}>
         
          <Col span={12}>Code Pays</Col>
          <Col span={4}>Taux</Col>
          <Col span={4}>Date info</Col>
          <Col span={1}></Col>
        </Row>
        <Form.List name="participants">
          {(fields, { remove }) => (
            <>
              {fields.map((field, index) => (
                <Row
                  gutter={16}
                  align="middle"
                  key={field.key}
                >
                 
                  <Col span={12}>
                    <Form.Item name={[index, "code_pays"]} rules={[]}>
                      <Input
                        allowClear={true}
                        disabled={view}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={4}>
                    <Form.Item name={[index, "taux"]} rules={[]}>
                      <Input
                        allowClear={true}
                        disabled={view}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={7}>
                    <Form.Item name={[index, "date_info"]} rules={[]}>
                      <DatePicker
                        allowClear={true}
                        disabled={view}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={1}>
                    {!view && (
                      <Form.Item>
                        <MinusCircleOutlined
                          style={{
                            color: "red",
                            fontSize: "18px",
                            cursor: "pointer",
                          }}
                          onClick={() => remove(index)}
                        />
                      </Form.Item>
                    )}
                  </Col>
                </Row>
              ))}
            </>
          )}
        </Form.List>
  
        {!view && (
          <Row gutter={16} align="middle" style={{ marginBottom: "5px" }}>
            
            <Col span={12}>
              <Form.Item name={`code_pays`} rules={[]}>
                <Input
                  allowClear={true}
                />
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item name={`taux`} rules={[]}>
                <Input    
                  allowClear={true}
                />
              </Form.Item>
            </Col>
            <Col span={7}>
              <Form.Item name={`date_info`} rules={[]}>
                <DatePicker    
                  allowClear={true}
                />
              </Form.Item>
            </Col>
            <Col span={1}>
              <Form.Item>
                <PlusCircleOutlined
                  style={{
                    color: "green",
                    fontSize: "18px",
                    cursor: "pointer",
                  }}
                  onClick={() => handleAdd()}
                />
              </Form.Item>
            </Col>
          </Row>
        )}
      </>
    );
  };
  
  export default Participant;
  