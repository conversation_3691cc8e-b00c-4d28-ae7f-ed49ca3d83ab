import { Col, Divider, Form, Input, Radio, Row, Select } from "antd";

import { useTranslation } from "react-i18next";


export const DepotForm = ({ form,principale, entreprises, responsables}) => {
  const {TextArea}=Input
  const {t} = useTranslation();

  const filterOption = (input, option) =>
    (option?.label ?? "").toLowerCase().includes(input.toLowerCase());
  const filterSort = (optionA, optionB) =>
    (optionA?.label ?? "")
    .toLowerCase()
    .localeCompare((optionB?.label ?? "").toLowerCase());
  
  function onManagerChange(value:number){
    const phone=responsables.find(item=>item.id===value)?.phone
    form.setFieldValue("phone",phone)
  }
return (
  <Row gutter={8}>
    <Col md={12} xs={24}>
      <Form.Item
        name="designation"
        label={t("depots.fields.designation")}
        rules={[{required: true, message: "depots.validations.required_designation"}]}
      >
        <Input placeholder={t("depots.fields.designation")} />
      </Form.Item>
    </Col>
    <Col md={12} xs={24}>
        <Form.Item name="type_depot_id" label initialValue={2}>
          <Radio.Group 
            // onChange={onChange} 
            // value={value} 
            options={[
              { value: 1, label: "Dépôt principale", disabled: principale },
              { value: 2, label: "Dépôt secondaire" },
            ]}
          />
        </Form.Item>
    </Col>
    <Col md={12} xs={24}>
      <Form.Item
        name="entreprise_id"
        label={t("depots.fields.entreprise")}
        rules={[{required: true, message: "Veuillez sélectionner une entreprise"}]}
      >
        <Select
          placeholder={t("depots.fields.entreprise")}
          allowClear
          showSearch
          filterOption={filterOption}
          filterSort={filterSort}
          options={entreprises}
        />
      </Form.Item>
    </Col>
    <Col span={24}>
      <Form.Item
        name="adresse"
        label={t("depots.fields.adresse")}
      >
        <TextArea rows={2} />
      </Form.Item>
    </Col>


    <Divider>Responsable de dépôt</Divider>
    <Col md={12} xs={24}>
      <Form.Item
        name="manager_id"
        label={t("depots.fields.manager")}
        rules={[{required: true, message: "Veuillez sélectionner un état"}]}

      >
        <Select
          placeholder={t("depots.fields.manager")}
          allowClear
          showSearch
          onChange={onManagerChange}
          filterOption={filterOption}
          filterSort={filterSort}
          options={responsables}
        />
      </Form.Item>
    </Col>
    <Col md={12} xs={24}>
      <Form.Item
        name="phone"
        label={t("depots.fields.phone")}
        // rules={[{required: true, message: "depots.validations.required_designation"}]}
      >
        <Input disabled placeholder={t("depots.fields.phone")} />
      </Form.Item>
    </Col>
  </Row>
)}
