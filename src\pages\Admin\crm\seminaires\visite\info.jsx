import { PlusCircleOutlined } from "@ant-design/icons";
import { ModalForm } from "@ant-design/pro-components";
import { Card, Checkbox, Col, DatePicker, Form, Input, Row, Select, Upload } from "antd";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

function InfoForm({ open, onCancel, record }) {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  return (
    <ModalForm
      title={t("Information visite")}
      form={form}
      open={open}
       width="70%"
      modalProps={{
        style: { top: 20 },
      }}
      //   onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.save"),
          resetText: t("common.actions.cancel"),
        },
      }}
    >
      <Card className="mt-5">
        <Row gutter={16} style={{ marginBottom: "10px" }}>
          <Col span={4}>Date</Col>
          <Col span={3}>P/R</Col>
          <Col span={5}>Suivi par</Col>
          <Col span={3}>Progr Scanné</Col>
          <Col span={8}>Services fournis / Appréciation</Col>
          <Col span={1}></Col>
        </Row>
        <Form.List name="trackItems">
          {(fields, { remove }) => (
            <>
              {fields.map((field, index) => (
                <Row gutter={16} align="middle" key={field.key}>
                  <Col span={4}>
                    <Form.Item name={[index, "date_suivi"]} rules={[]}>
                      <DatePicker allowClear={true}  />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item name={[index, "action"]} rules={[]}>
                      <Input allowClear={true}  />
                    </Form.Item>
                  </Col>
                  <Col span={4}>
                    <Form.Item name={[index, "date_relance"]} rules={[]}>
                      <DatePicker allowClear={true}  />
                    </Form.Item>
                  </Col>
                  <Col span={4}>
                    <Form.Item name={[index, "responsable"]} rules={[]}>
                      <Input allowClear={true}  />
                    </Form.Item>
                  </Col>
                  <Col span={3}>
                    <Form.Item
                      name={[index, "suivi_done"]}
                      //  label={" "}
                      style={{
                        display: "inline-block",
                        marginLeft: "40px",
                      }}
                    >
                      <Checkbox onChange={(e) => setValue(e.target.checked)} />
                    </Form.Item>
                  </Col>
                  <Col span={1}>                 
                      <Form.Item>
                        <MinusCircleOutlined
                          style={{
                            color: "red",
                            fontSize: "18px",
                            cursor: "pointer",
                          }}
                          onClick={() => remove(index)}
                        />
                      </Form.Item>              
                  </Col>
                </Row>
              ))}
            </>
          )}
        </Form.List>
          <Row gutter={16} align="middle" style={{ marginBottom: "5px" }}>
            <Col span={4}>
              <Form.Item name={`date`} rules={[]}>
                <DatePicker allowClear={true} />
              </Form.Item>
            </Col>
            <Col span={3}>
              <Form.Item name={`pr`} rules={[]}>
                <Select allowClear={true}  />
              </Form.Item>
            </Col>
            <Col span={5}>
              <Form.Item name={`suivi_par`} rules={[]}>
                <Select allowClear={true} />
              </Form.Item>
            </Col>
            <Col span={3}>
              <Form.Item name={`prog_scanne`} rules={[]}>
                <Input allowClear={true} />
              </Form.Item>
            </Col>
             <Col span={8}>
              <Form.Item name={`services`} rules={[]}>
                <Input allowClear={true} />
              </Form.Item>
            </Col>
          
            <Col span={1}>
              <Form.Item>
                <PlusCircleOutlined
                  style={{
                    color: "green",
                    fontSize: "18px",
                    cursor: "pointer",
                  }}
                  onClick={() => handleAdd()}
                />
              </Form.Item>
            </Col>
          </Row>
      </Card>
    </ModalForm>
  );
}
export default InfoForm;
