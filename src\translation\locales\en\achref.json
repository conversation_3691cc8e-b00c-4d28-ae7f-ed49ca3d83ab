{"depots": {"title": "Deposits", "list": "List of deposits", "add": "Add a deposit", "edit": "Edit deposit", "deposit_manager": "Deposit manager", "view": "Deposit details", "fields": {"designation": "Designation", "adresse": "Address", "entreprise": "Company", "manager": "Manager", "phone": "Phone"}, "messages": {"confirm_delete": "Are you sure you want to delete?", "success_delete": "<PERSON><PERSON><PERSON><PERSON> successfully deleted", "success_create": "<PERSON><PERSON>si<PERSON> successfully created", "success_update": "Deposit successfully updated", "error_load": "Error loading data", "error_save": "Error saving"}}, "articles": {"title": "Articles", "list": "List of articles", "add": "Add an article", "edit": "Edit the article", "view": "Article details", "money": "Money", "percentage": "Percentage", "fields": {"code": "Code", "designation": "Designation", "type_article": "Article type", "status": "Status", "famille": "Family", "sous_famille": "Sub-family", "unite_achat": "Purchase unit", "unite_stock": "Stock unit", "unite_vente": "Sales unit", "fournisseur": "Supplier", "description": "Article description", "seuil_min": "Minimum threshold", "seuil_max": "Maximum threshold", "tax": "Tax", "quantity": "Quantity", "prix_achat": "Purchase price", "prix_vente": "Selling price", "frais": "Fees", "prix_revient": "Cost price", "marge": "<PERSON><PERSON>", "prix_ht": "Price excl. tax", "taux_tva": "VAT rate", "prix_ttc": "Price incl. tax", "allow_negative_stock": "Allow negative stock", "replacement_article": "Replacement article"}, "messages": {"confirm_delete": "Are you sure you want to delete?", "success_delete": "Article successfully deleted", "success_create": "Article successfully created", "success_update": "Article successfully updated", "error_load": "Error loading data", "error_save": "Error saving data"}}, "mouvements": {"title": "Article Movement", "fields": {"type_mouvement": "Movement Type", "quantity": "Quantity", "date": "Date"}}}