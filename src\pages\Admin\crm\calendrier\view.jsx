import React from 'react';
import { Descriptions, Tag, But<PERSON>, Badge } from 'antd';
import { statusColors } from './utils/helpers';

export default function EventDetailView({ event, onEdit }) {
  const statusColor = statusColors[event.status] || '#d9d9d9';

  return (
    <div className="event-detail">
      <Descriptions bordered column={1}>
        <Descriptions.Item label="Title">{event.title}</Descriptions.Item>
        <Descriptions.Item label="Date">
          {dayjs(event.start).format('DD/MM/YYYY HH:mm')}
        </Descriptions.Item>
        <Descriptions.Item label="Status">
          <Badge color={statusColor} text={event.status} />
        </Descriptions.Item>
        {event.description && (
          <Descriptions.Item label="Description">
            {event.description}
          </Descriptions.Item>
        )}
      </Descriptions>

      <div style={{ marginTop: 16, textAlign: 'right' }}>
        <Button type="primary" onClick={onEdit}>
          Edit
        </Button>
      </div>
    </div>
  );
}