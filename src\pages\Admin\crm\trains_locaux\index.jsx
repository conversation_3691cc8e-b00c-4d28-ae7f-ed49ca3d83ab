import { useState, useRef, useEffect } from "react";
import { ProTable } from "@ant-design/pro-components";
import { <PERSON>ton, Popconfirm, Space, Tooltip, Typography, Card, Modal } from "antd";
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import trainlocaux from "./datas";
import CreateTerrainLocal from "./create";
import EditTerrainLocal from "./edit";
import ViewTerrainLocal from "./view";

function TrainLocaux() {
  const { t } = useTranslation();
  const tableRef = useRef();

  const [dataSource, setDataSource] = useState(trainlocaux);
  const [filteredData, setFilteredData] = useState(trainlocaux);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [viewModalVisible, setViewModalVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState(null);
  const [viewingRecord, setViewingRecord] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [total, setTotal] = useState(trainlocaux.length);

  const handleCancel = () => {
    setViewModalVisible(false);
  };

  const handleEdit = (record) => {
    setEditingRecord(record);
    setEditModalVisible(true);
  };

  const handleCreate = () => {
    setCreateModalVisible(true);
  };

  const handleDelete = (record) => {
    const newData = dataSource.filter(item => item.key !== record.key);
    setDataSource(newData);
    setFilteredData(newData);
  };


  const handleSearchData = (params) => {

    console.log(params);
    const filtered = dataSource.filter((item) => {
      return (
        (!params.gouvernorat ||
          item.gouvernorat?.toLowerCase().includes(params.gouvernorat.toLowerCase())) &&
        (!params.superficieLocal ||
          item.superficieLocal?.toLowerCase().includes(params.designation.toLowerCase())) &&
        (!params.valeur ||
          item.valeur?.toLowerCase().includes(params.valeur.toLowerCase()))
      );
    });

    setFilteredData(filtered); // Update table with filtered results
  };
  const columns = [
    { 
      title: "Propriétaire", 
      dataIndex: "proprietaire", 
      key: "proprietaire",
      sorter: (a, b) => a.proprietaire.localeCompare(b.proprietaire)
    },
    { 
      title: "Gouvernorat", 
      dataIndex: "gouvernorat", 
      key: "gouvernorat",
      filters: [...new Set(dataSource.map(item => item.gouvernorat))].map(g => ({
        text: g,
        value: g
      })),
      onFilter: (value, record) => record.gouvernorat === value,
      render: (_, record) => <span>{record.gouvernorat}</span>
    },
    { 
      title: "Superficie Local (m²)", 
      key: "superficieLocal",
      render: (_, record) => (
        `${record.superficieMinlocal} - ${record.superficieMaxlocal}`
      ),
      sorter: (a, b) => (a.superficieMinlocal || 0) - (b.superficieMinlocal || 0)
    },
    { 
      title: "Superficie Terrain (m²)", 
      key: "superficieTerrain",
      render: (_, record) => (
        `${record.superficieMinterin} - ${record.superficieMaxterin}`
      ),
      sorter: (a, b) => (a.superficieMinterin || 0) - (b.superficieMinterin || 0)
    },
    {
      title: "Actions",
      key: "action",
      align: "center",
      width: 150,
      fixed: 'right',
      search:false,
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title="Voir">
            <Button 
              type="link" 
              icon={<EyeOutlined />} 
              onClick={() => {
                setViewingRecord(record);
                setViewModalVisible(true);
              }} 
            />
          </Tooltip>
          <Tooltip title="Modifier">
            <Button 
              type="link" 
              icon={<EditOutlined />}
              style={{ color: "#faad14" }}
              onClick={() => {
                setEditingRecord(record);
                setEditModalVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title="Supprimer">
            <Popconfirm
              title="Confirmer la suppression ?"
              onConfirm={() => handleDelete(record)}
            >
              <Button 
                type="link" 
                icon={<DeleteOutlined />}
                style={{ color: "#ff4d4f" }}
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <Card
      bordered
   
      title={<Typography.Title level={4}>Liste des Locaux</Typography.Title>}
      extra={
        <Space>
          <Button type="primary" icon={<PlusOutlined />} onClick={() => setCreateModalVisible(true)}>
            Ajouter 
          </Button>
        </Space>
      }
   
   >
      <ProTable
        actionRef={tableRef}
        loading={isLoading}
        columns={columns}
        dataSource={filteredData}
        rowKey="key"
        search={{
          searchText: t("common.filter"),
          labelWidth: "auto",
          defaultCollapsed: false,
        }}
        onSubmit={handleSearchData}
        onReset={() => handleSearchData({})}
        options={{
          reload: () => setFilteredData(dataSource),
          density: true,
          setting: true
        }}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: 10,
          total: total,
          showTotal: (total) => `Total ${total} items`,
        }}
        scroll={{ x: 'max-content' }}
        
      />
      
      {/* Create Modal */}
      <Modal
        title="Ajouter un Local"
        visible={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        footer={null}
        width={1000}
      >
        <CreateTerrainLocal
          onCancel={() => setCreateModalVisible(false)}
          setDataSource={setDataSource}
        />
      </Modal>

      {/* Edit Modal */}
      <Modal
        title="Modifier un Local"
        visible={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        footer={null}
        width={800}
      >
        <EditTerrainLocal
          record={editingRecord}
          onCancel={() => setEditModalVisible(false)}
          setDataSource={setDataSource}
        />
      </Modal>

      {/* View Modal */}
      <Modal
        title="Détails du Local"
        visible={viewModalVisible}
        onCancel={handleCancel}
        footer={null}
        width={800}
      >
        <ViewTerrainLocal
          record={viewingRecord}
          onCancel={() => setViewModalVisible(false)}
        />
      </Modal>
    </Card>
  );
}

export default TrainLocaux;
