import { useState, useRef } from "react";
import { ProTable } from "@ant-design/pro-components";
import {
  Button,
  Popconfirm,
  Space,
  Input,
  Tooltip,
  Typography,
  Card,
  Form,
} from "antd";

import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  SearchOutlined,
  RiseOutlined,
  HistoryOutlined,
  InfoOutlined,
  InfoCircleOutlined,
  LinkOutlined,
  PrinterOutlined,
} from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { projets, types, secteurs } from "./data";
import CreateProjet from "./create";
import EditProjet from "./edit";
import AvancementProjet from "./avancementProjet";
import HistoricProjet from "./historicProjet";
import InfoProjet from "./infoProjet";
import TrackProjet from "./trackProjet";

// import PdfComponent from "../../components/companies/PdfComponent";

function Projet() {
  const tableRef = useRef();
  const { t } = useTranslation();
  const [tableParams, setTableParams] = useState({}); // Paramètres de recherche et de filtrage
  const [isLoading, setIsLoading] = useState(false); // Indique si les données sont en cours de chargement
  const [data, setData] = useState(projets); // Données affichées dans la table
  const [total, setTotal] = useState(data.length); // Nombre total de lignes affichées
  const [pageNumber, setPageNumber] = useState(1); // Numéro de page actuelle
  const [dataSource, setDataSource] = useState([]);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [show, setShow] = useState(false);
  const [editingRecord, setEditingRecord] = useState(null);
  const [advanceModalVisible, setAdvanceModalVisible] = useState(false);
  const [histModalVisible, setHistModalVisible] = useState(false);
  const [infoModalVisible, setInfoModalVisible] = useState(false);
  const [trackModalVisible, setTrackModalVisible] = useState(false);

  const [loadingDuplicate, setLoadingDuplicate] = useState({
    status: false,
    id: 0,
  });
  const [form] = Form.useForm();
  // Déclenchement de la recherche avec un délai (700ms)
  const handleSearch = (e) => {
    const searchValue = e.target.value; // Récupération de la valeur de recherche
    setTableParams({ ...tableParams, search: searchValue }); // Mise à jour des paramètres
  };

  //  const debouncedOnChange = useDebounce(handleSearch, 700);

  const formatter = new Intl.NumberFormat("fr-TN", {
    style: "currency",
    currency: "TND",
    minimumFractionDigits: 3,
  });
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [tansferLoading, setTansferLoading] = useState(false);
  const showModal = (id) => {
    setIsModalOpen(true);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const columns = [
    {
      title: t("Type projet"),
      dataIndex: "type_id",
      key: "type_id",
      ellipsis: true,
      valueType: "select",
      valueEnum: () =>
        Object.fromEntries(
          types.map((value) => [value.id, { text: value.nom }])
        ),
      order: 1,
      render: (text) => {
        return (
          <Typography.Text ellipsis={{ tooltip: text }}>{text}</Typography.Text>
        );
      },
    },
    {
      title: t("RS en Tunisie"),
      dataIndex: "rs_tunis",
      key: "rs_tunis",
      ellipsis: true,
      order: 1,
      render: (text) => {
        return (
          <Typography.Text ellipsis={{ tooltip: text }}>{text}</Typography.Text>
        );
      },
    },
    {
      title: t("Secteur"),
      dataIndex: "secteur_id",
      key: "secteur_id",
      ellipsis: true,
      valueType: "select",
      valueEnum: () =>
        Object.fromEntries(
          secteurs.map((value) => [value.id, { text: value.secteur }])
        ),
      order: 1,
      render: (text) => {
        return (
          <Typography.Text ellipsis={{ tooltip: text }}>{text}</Typography.Text>
        );
      },
    },
    {
      title: t("RS Etranger"),
      dataIndex: "rs_etranger",
      key: "rs_etranger",
      ellipsis: true,
      order: 1,
      render: (text) => {
        return (
          <Typography.Text ellipsis={{ tooltip: text }}>{text}</Typography.Text>
        );
      },
    },

    {
      title: t("common.actions.title"),
      key: "action",
      align: "center",
      sorter: false,
      search: false,
      render: (_, record) => (
        <Space size="auto">
          <Tooltip title={t("common.actions.view")}>
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => {
                setEditingRecord(record);
                setEditModalVisible(true);
                setShow(true);
              }}
            />
          </Tooltip>
          <Tooltip title={t("common.actions.edit")}>
            <Button
              type="link"
              style={{ color: "#f5b041" }}
              icon={<EditOutlined />}
              onClick={() => {
                setEditingRecord(record);
                setEditModalVisible(true);
                setShow(false);
              }}
            />
          </Tooltip>
          <Tooltip title={t("Avancement du projet")}>
            <Button
              type="link"
              style={{ color: "gray" }}
              icon={<RiseOutlined />}
              onClick={() => {
                setAdvanceModalVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title={t("Historique")}>
            <Button
              type="link"
              style={{ color: "#000ac5" }}
              icon={<HistoryOutlined />}
              onClick={() => {
                setHistModalVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title={t("Info")}>
            <Button
              type="link"
              style={{ color: "#354a44" }}
              icon={<InfoCircleOutlined />}
              onClick={() => {
                setInfoModalVisible(true);
              }}
            ></Button>
          </Tooltip>

          <Tooltip title={t("Suivi du projet")}>
            <Button
              type="link"
              style={{ color: "#2b6110" }}
              icon={<LinkOutlined />}
              onClick={() => {
                setTrackModalVisible(true);
              }}
            />
          </Tooltip>

          <Tooltip title={t("Imprimer la Fiche")}>
            <Button
              type="link"
              style={{ color: "green" }}
              icon={<PrinterOutlined />}
              onClick={() => {
                handleSetRefAndPrint(record);
              }}
            />
          </Tooltip>
          <Tooltip title={t("common.actions.delete")}>
            <Popconfirm
              title={t("common.messages.confirm_delete")}
              onConfirm={() => {
                const newDataSource = dataSource.filter(
                  (item) => item.key !== record.key
                );
                setDataSource(newDataSource);
              }}
            >
              <Button
                type="link"
                style={{ color: "#ec7063" }}
                icon={<DeleteOutlined />}
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  const onFinish = (values) => {
    setTansferLoading(true);
    setTimeout(() => {
      setTansferLoading(false);
      setIsModalOpen(false);
    }, [1000]);
  };

  return (
    <>
      <Card
        title={
          <Typography.Title level={4}>
            {t("Liste des projets")}
          </Typography.Title>
        }
        extra={
          <Space>
            {/* Champ de recherche */}
            <Input
              size="large"
              placeholder={t("common.search")}
              suffix={<SearchOutlined style={{ color: "#bfbfbf" }} />}
              //    onChange={debouncedOnChange}
            />
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setCreateModalVisible(true)}
            >
              {t("Ajouter")}
            </Button>
          </Space>
        }
      >
        <ProTable
          actionRef={tableRef} // Référence pour accéder aux actions
          search={{
            searchText: t("common.filter"),
            labelWidth: "auto",
            defaultCollapsed: false,
          }}
          loading={isLoading}
          columns={columns}
          dataSource={data}
          rowKey="id"
          params={tableParams}
          scroll={{ x: "max-content" }}
          options={false}
          request={async (params) => {
            setIsLoading(true); // Démarrage du chargement
            console.log(params);
            setTimeout(() => {
              // Simule une requête serveur
              setIsLoading(false);
            }, 500);
            return {
              // Retourne les données
              data: data,
              success: true,
            };
          }}
          pagination={{
            showSizeChanger: true, // Permet de modifier la taille des pages
            defaultPageSize: 3, // Taille de page par défaut
            total: total, // Nombre total de lignes
            onChange: (page) => setPageNumber(page), // Fonction de changement de page
          }}
        />
        {createModalVisible && (
          <CreateProjet
            open={createModalVisible}
            onCancel={() => setCreateModalVisible(false)}
            onSuccess={(newDomain) => {
              setDataSource([...dataSource, newDomain]);
              setCreateModalVisible(false);
            }}
          />
        )}

        {editModalVisible && (
          <EditProjet
            open={editModalVisible}
            record={editingRecord}
            onCancel={() => {
              setEditModalVisible(false);
              setEditingRecord(null);
            }}
            show={show}
          />
        )}

        {advanceModalVisible && (
          <AvancementProjet
            open={advanceModalVisible}
            onCancel={() => {
              setAdvanceModalVisible(false);
            }}
          />
        )}
        {histModalVisible && (
          <HistoricProjet
            open={advanceModalVisible}
            onCancel={() => {
              setAdvanceModalVisible(false);
            }}
            show={show}
          />
        )}
        {infoModalVisible && (
          <InfoProjet
            open={infoModalVisible}
            onCancel={() => {
              setInfoModalVisible(false);
            }}
          
          />
        )}
          {trackModalVisible && (
          <TrackProjet
            open={trackModalVisible}
            onCancel={() => {
              setTrackModalVisible(false);
            }}
          
          />
        )}
        {/* {data.map((record) => (
          <div key={record.id} style={{ display: "none" }}>
            <PdfComponent ref={contentToPrintRef} record={record} />
          </div>
        ))} */}
      </Card>
    </>
  );
}
export default Projet;
