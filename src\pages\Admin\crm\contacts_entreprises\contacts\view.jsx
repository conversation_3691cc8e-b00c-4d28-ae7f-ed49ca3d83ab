import { ModalForm } from "@ant-design/pro-components";
import {
  <PERSON><PERSON>,
  <PERSON>,
  Col,
  DatePicker,
  Form,
  Input,
  InputNumber,
  Radio,
  Row,
  Select,
} from "antd";
import { useEffect,  useState } from "react";
import { useTranslation } from "react-i18next";
import { entreprises } from "./data";




function ShowContactForm({ open, onCancel, record }) {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  useEffect(() => {
    if (open) {
      form.setFieldsValue(record);
    }
  }, [open]);
 
  return (
    <ModalForm
      title={t("Consulter un contact")}
      disabled
      form={form}
      open={open}
       width="70%"
      modalProps={{
        style: { top: 20 },
      }}
      //   onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        resetButtonProps: { disabled: false },
        submitButtonProps: { hidden: true },
        searchConfig: {
          resetText: t("common.actions.cancel"),
        },
      }}
    >
    <Card className="mt-2">
        <Row gutter={4} >
          <Col md={16} xs={24}>
            <Form.Item name="entreprise_id" label={t("Raison Sociale")}>
              <Select options={entreprises.map((item) => ({
                  value: item.id,
                  label: item.raison_social,
                }))} />
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item
              name={"total_export"}
              label={" "}
              style={{
                display: "inline-block",
                // width: "90%",
                marginLeft: "60px",
              }}
            >
              <Radio value={1}> {t("Totalement export")} </Radio>
            </Form.Item>
          </Col>
        </Row>
      </Card>
      <Card className="mt-2">
        <Row gutter={4} >
          <Col md={8} xs={24}>
            <Form.Item
              name="cadre"
              label={t("Cadre. FIPA")}
              //  rules={[{ required: true, message: "Ce champ est obligatoire" }]}
            >
              <Select
                allowClear
                placeholder={t("Cadre. FIPA")}
                //   options={pays.map((item) => ({ value: item.id, label: item.libelle }))}
                onSelect={(e) => {}}
                onClear={() => {}}
              />{" "}
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item
              name="date"
              label={t("Date")}
              //  rules={[{ required: true, message: "Ce champ est obligatoire" }]}
            >
              <DatePicker />
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item
              name="date"
              label={t("Nationalité")}
              //  rules={[{ required: true, message: "Ce champ est obligatoire" }]}
            >
              <Select />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name="contact" label={t("Contact")}>
              <Input></Input>
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name="fonction" label={t("Fonction")}>
              <Input></Input>
            </Form.Item>
          </Col>
          <Col md={16} xs={24}>
            <Form.Item name="adresse" label={t("Adresse")}>
              <Input></Input>
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item name="region" label={t("Region")}>
              <Select></Select>
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item name="tel" label={t("Téléphone")}>
              <Input></Input>
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item name="gsm" label={t("GSM")}>
              <Input></Input>
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item name="fax" label={t("FAX")}>
              <Input></Input>
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name="email" label={t("E-mail")}>
              <Input></Input>
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name="site_web" label={t("Site Web")}>
            <Input></Input>
            </Form.Item>
          </Col>
          <Col md={24} xs={24}>
            <Form.Item name="activite" label={t("Activité")}> <Input></Input></Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item name="secteur" label={t("Secteur")}>
              <Select></Select>
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item name="gr_un" label={t("Groupe")}>
              <Select></Select>
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item name="gr_deux" label={t("Groupe 2")}>
              <Select></Select>
            </Form.Item>
          </Col>
        </Row>
      </Card>
      <Card className="mt-2">
        <Row gutter={4} >
          <Col md={8} xs={24}>
            <Form.Item
              name={"long_list"}
              label={" "}
              style={{
                display: "inline-block",
                marginLeft: "30px",
              }}
            >
              <Radio value={1}> {t("Long list")} </Radio>
            </Form.Item>
          </Col>{" "}
          <Col md={8} xs={24}>
            <Form.Item
              name={"shor_list"}
              label={" "}
              style={{
                marginLeft: "20px",
              }}
            >
              <Radio value={1}> {t("Short list")} </Radio>
            </Form.Item>
          </Col>{" "}
          <Col md={8} xs={24}>
            <Form.Item
              name={"interessant"}
              label={" "}
              style={{
                display: "inline-block",
               
                marginLeft: "20px",
              }}
            >
              <Radio value={1}> {t("Intéressant")} </Radio>
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item name="date_first" label={t("Date 1er RDV")}>
<DatePicker />            </Form.Item>
          </Col>
          <Col md={16} xs={24}>
            <Form.Item name="lieu_rencontre" label={t("Lieu de la rencontre")}>
              <Select></Select>
            </Form.Item>
          </Col>
          <Col md={24} xs={24}>
            <Form.Item name="objet" label={t("Objet de l'entretien")}>
            <Input.TextArea allowClear={true}  />
            </Form.Item>
          </Col>
        </Row>
      </Card>
    </ModalForm>
  );
}
export default ShowContactForm;
