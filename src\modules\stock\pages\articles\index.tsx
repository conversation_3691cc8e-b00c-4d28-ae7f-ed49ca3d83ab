import {
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  PlusOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import { ProColumns, ProTable } from "@ant-design/pro-components";
import useDebounce from "@/hooks/useDebounce";
import {
  Button,
  Card,
  Input,
  Popconfirm,
  Space,
  Tooltip,
  Typography,
} from "antd";
import { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { tvas as tvasData } from "@/modules/finance/pages/tva/data";
import CreateArticleForm from "./create";
import { produits,  } from "./data";
import { IArticle } from "../interfaces";
import ShowArticleForm from "./view";
import EditArticleForm from "./edit";
import { famillesData } from "../famille/data";
import { sousFamillesData } from "../sousFamille/data";
import { stockUnities } from "../stock-unities/data";
import { fournisseurs as fournisseursData } from "@/modules/finance/pages/achat/gestion-fournisseurs/data";


function Articles(){
  const tableRef = useRef();
  const {t} = useTranslation();
  const [tableParams, setTableParams] = useState({}); // Paramètres de recherche et de filtrage
  const [isLoading, setIsLoading] = useState(false); // Indique si les données sont en cours de chargement
  const [data, setData] = useState<IArticle[]>(produits); // Données affichées dans la table
  const [total, setTotal] = useState(data.length); // Nombre total de lignes affichées
  const [pageNumber, setPageNumber] = useState(1); // Numéro de page actuelle
  const [unites, setUnites] = useState([{}]);
  const [familles, setFamilles] = useState([]);
  const [fournisseurs, setFournisseurs] = useState([]);
  const [sousFamilles, setSousFamilles] = useState([]);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [viewModalVisible, setViewModalVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState(null);
  const [viewingRecord, setViewingRecord] = useState(null);
  const [tvas, setTvas] = useState([]);
  const [articlesRemplacement, setArticlesRemplacement] = useState([]);

  useEffect(() => {
        setUnites(stockUnities.map((item) => {return {value : item.id, label : item.libelle}}));
        setFamilles(famillesData.map((item) => {return {value : item.id, label : item.libelle}}))
        setSousFamilles(sousFamillesData.map((item) => {return {...item,value : item.id, label : item.libelle}}))
        setFournisseurs(fournisseursData.map((item) => {return {...item,value : item.key, label :item.raisonSociale?? `${item.firstName} ${item.lastName}`}}))
        setTvas(tvasData.map((item) => {return {...item,value : item.key, label : item.designation}}))
        setArticlesRemplacement(data.map((item) => {return {...item,value : item.id, label : item.designation}}))
}, []);
  // Déclenchement de la recherche avec un délai (700ms)
  const handleSearch = (e) => {
  const searchValue = e.target.value; // Récupération de la valeur de recherche
  setTableParams({...tableParams, search: searchValue}); // Mise à jour des paramètres
  };

  const debouncedOnChange = useDebounce(handleSearch, 700);

  const columns: ProColumns<IArticle>[]=[
    {
      title: t('articles.fields.code'),
      dataIndex: "code",
      key: "code",
      ellipsis: true,
      // order: 1,
      render: (text) => {
        return (
          <Typography.Text ellipsis={{tooltip: text}} >
            {text}
          </Typography.Text>
        );
      },
    },
    {
      title: t('articles.fields.designation'),
      dataIndex: "designation",
      key: "designation",
      responsive: ["md"],
      ellipsis: true,
      // order: 3,
      render: (text, record) => {
        return (
          <Typography.Text ellipsis={{tooltip: text}} style={{maxWidth: 'auto'}}>
            {text}
          </Typography.Text>
        );
      },
    },
    {
      title: t('articles.fields.famille'),
      dataIndex: "category_id",
      key: "famille",
      ellipsis: true,
      valueType:"select",
      valueEnum:Object.fromEntries(famillesData.map((value: { id: number; libelle: string }) => [value.id, { text: value.libelle }])),
      // order: 2,
      render: (text) => {
        return (
            <Typography.Text ellipsis={{tooltip: text}} >
              {text}
            </Typography.Text>
        );
      },
    },
    {
      title: t('articles.fields.fournisseur'),
      dataIndex: "supplier_id",
      key: "fournisseur_id",
      valueType:"select",
      valueEnum:Object.fromEntries(fournisseursData.map((value) => [value.key, { text: value.raisonSociale?? `${value.firstName} ${value.lastName}` }])),
      search:false,
      responsive: ["md"],
      ellipsis: true,
      // order: 4,
      render: (text, record) => {
        return (
          <Typography.Text ellipsis={{tooltip: text}} style={{maxWidth: 'auto'}}>
            {text}
          </Typography.Text>
        );
      },
    },
    {
      title: t('articles.fields.prix_achat'),
      dataIndex: "purchase_price",
      key: "prix_achat",
      responsive: ["md"],
      ellipsis: true,
      // order: 5,
      search:false,
      render: (text, record) => {
        return (
          <Typography.Text ellipsis={{tooltip: text}} >
            {text}
          </Typography.Text>
        );
      },
    },
    {
      title: t('articles.fields.prix_vente'),
      dataIndex: "prix_vente",
      key: "prix_vente",
      responsive: ["md"],
      search:false,
      ellipsis: true,
      // order: 6,
      render: (text, record) => {
        return (
          <Typography.Text ellipsis={{tooltip: text}} >
            {text}
          </Typography.Text>
        );
      },
    },
    {
      title: t('articles.fields.tax'),
      dataIndex: "tax",
      key: "tax",
      search:false,
      responsive: ["md"],
      ellipsis: true,
      // order: 7,
      render: (text, record) => {
        return (
          <Typography.Text ellipsis={{tooltip: text}} style={{maxWidth: 'auto'}}>
            {text}
          </Typography.Text>
        );
      },
    },
    {
      title: t('common.actions.title'),
      key: 'action',
      fixed: 'right',
      align: 'center',
      hideInSetting:true,
      width: 200,
      sorter:false,
      search: false,
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title={t('common.actions.view')}>
            <EyeOutlined 
              onClick={() => {
                setViewingRecord(record);
                setViewModalVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title={t('common.actions.edit')}>
            <EditOutlined style={{color: "#f5b041"}} 
              onClick={() => {
                setEditingRecord(record);
                setEditModalVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title={t('common.actions.delete')}>
            <Popconfirm
              title={t('common.messages.confirm_delete')}
              onConfirm={() => {
                const newDataSource = data.filter(item => item.id !== record.id);
                setData(newDataSource);
              }}
            >
              <DeleteOutlined style={{color: "#ec7063"}}/>
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  const formProps = {
    familles,
    fournisseurs,
    sousFamilles,
    tvas,
    unites,
    articles:articlesRemplacement
  };

  return(  
      <Card
        title={
          <Typography.Title /*style={{textWrap: "wrap"}} */level={4}>
            {t('articles.list')}
          </Typography.Title>
        }
        extra={
          <Space>
            {/* Champ de recherche */}
            <Input
              size="large"
              placeholder={t('common.search')}
              suffix={<SearchOutlined style={{color: "#bfbfbf"}}/>}
              onChange={debouncedOnChange}
            />
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined/>}
              onClick={() => setCreateModalVisible(true)}
            >
              {t('articles.add')}
            </Button>
          </Space>
        }
      >
      <ProTable
        actionRef={tableRef} // Référence pour accéder aux actions
        search={{
          searchText: t('common.filter'),
          labelWidth: "auto",
          defaultCollapsed: false,
        }}
        loading={isLoading}
        columns={columns}
        dataSource={data}
        rowKey="id"
        params={tableParams}
        scroll={{x: 'max-content'}}
        options={{setting:true,}}
        request={async (params) => {
          setIsLoading(true); // Démarrage du chargement
          console.log(params);
          setTimeout(() => { // Simule une requête serveur
              setIsLoading(false);
          }, 500);
          return { // Retourne les données
              data: data,
              success: true,
          };
        }}
        pagination={{
          showSizeChanger: true, // Permet de modifier la taille des pages
          defaultPageSize: 3, // Taille de page par défaut
          total: total, // Nombre total de lignes
          onChange: (page) => setPageNumber(page), // Fonction de changement de page
        }}
      />
      {createModalVisible && (
        <CreateArticleForm
          {...formProps}        
          dataRecord={null}
          open={createModalVisible}
          onCancel={() => setCreateModalVisible(false)}
          onSuccess={(newItem) => {
              setData([...data, newItem]);
              setCreateModalVisible(false);
          }}
        />
      )}
      {editModalVisible && (
        <EditArticleForm
          {...formProps}
          dataRecord={editingRecord}
          open={editModalVisible}
          onCancel={() => {
              setEditModalVisible(false);
              setEditingRecord(null);
          }}
        />
      )}
      {viewModalVisible && (
        <ShowArticleForm
          {...formProps}
          dataRecord={viewingRecord}
          open={viewModalVisible}
          onCancel={() => {
              setViewModalVisible(false);
              setViewingRecord(null);
          }}
        />
      )} 
      </Card>
  )
}
export default Articles;