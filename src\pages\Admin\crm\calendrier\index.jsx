import React, { useState, useEffect, useRef } from 'react';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import listPlugin from '@fullcalendar/list';
import { 
  Modal, 
  Button, 
  Form, 
  Input, 
  Select, 
  DatePicker, 
  message, 
  Popconfirm,
  Avatar,
  Tag,
  Card, 
  Radio, 
  Tooltip, 
  Layout, 
  Space, 
  InputNumber,
  Switch
} from 'antd';
import { 
  EditOutlined, 
  DeleteOutlined, 
  PlusOutlined, 
  ShareAltOutlined,
  CalendarOutlined 
} from '@ant-design/icons';
import dayjs from 'dayjs';
import 'dayjs/locale/fr';
import './calendar.css';

dayjs.locale('fr');

const { Option } = Select;
const { RangePicker } = DatePicker;
const { TextArea } = Input;
const { Sider, Content } = Layout;

const getAllCalendarItems = () => {
  const now = dayjs();
  return [
    {
      id: 1,
      title: 'Réunion client',
      start: now.hour(10).minute(0).toDate(),
      end: now.hour(11).minute(30).toDate(),
      resourceIds: ['1', '4'],
      color: '#1890ff',
      status: 'planned',
      location: 'Salle A1',
      description: 'Présentation du nouveau projet',
      participants: ['1', '2'],
      type: 'activity',
      extendedProps: {
        creator: '1',
        createdAt: now.subtract(2, 'day').toISOString()
      }
    },
    {
      id: 2,
      title: 'Formation équipe',
      start: now.add(1, 'day').hour(14).minute(0).toDate(),
      end: now.add(1, 'day').hour(17).minute(0).toDate(),
      resourceIds: ['2', '5'],
      color: '#52c41a',
      status: 'in-progress',
      location: 'Salle de conférence',
      description: 'Formation sur les nouvelles fonctionnalités',
      participants: ['1', '3'],
      type: 'activity',
      extendedProps: {
        creator: '2',
        createdAt: now.subtract(1, 'day').toISOString()
      }
    },
    {
      id: 3,
      title: 'Conférence annuelle',
      start: now.add(2, 'day').hour(9).minute(0).toDate(),
      end: now.add(2, 'day').hour(10).minute(0).toDate(),
      resourceIds: [],
      color: '#ff4d4f',
      status: 'planned',
      location: 'Auditorium',
      description: 'Conférence sur les nouvelles technologies',
      participants: ['1', '2', '3'],
      type: 'event',
      extendedProps: {
        creator: '1',
        createdAt: now.subtract(3, 'day').toISOString(),
        eventType: 'conference',
        eventLocation: 'Auditorium',
        eventCapacity: 100
      }
    }
  ];
};

const getResources = () => [
  { id: '1', title: 'Mohamed Ben Salah', color: '#f56a00', type: 'person' },
  { id: '2', title: 'Soumaya Laarbi', color: '#7265e6', type: 'person' },
  { id: '3', title: 'Mourad Loussaif', color: '#ffbf00', type: 'person' },
  { id: '4', title: 'Salle A1', color: '#00a2ae', type: 'room' },
  { id: '5', title: 'Salle de conférence', color: '#87d068', type: 'room' },
  { id: '6', title: 'Voiture de service', color: '#ff4d4f', type: 'equipment' }
];

const updateEvent = (event) => {
  console.log('Event updated:', event);
  return true;
};

const createEvent = (event) => {
  console.log('Event created:', event);
  return true;
};

const deleteEvent = (eventId) => {
  console.log('Event deleted:', eventId);
  return true;
};

const shareCalendar = (data) => {
  console.log('Calendar shared:', data);
  return `https://calendar.example.com/share/${Math.random().toString(36).substr(2, 8)}`;
};

export default function TeamUpStyleCalendar() {
  const [events, setEvents] = useState([]);
  const [allEvents, setAllEvents] = useState([]);
  const [resources, setResources] = useState([]);
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [resourceModalVisible, setResourceModalVisible] = useState(false);
  const [shareModalVisible, setShareModalVisible] = useState(false);
  const [selectedResources, setFilteredResources] = useState([]);
  const [typeFilter, setTypeFilter] = useState('all');
  const [dateRange, setDateRange] = useState(null);
  const [viewType, setViewType] = useState('dayGridMonth');
  const [calendarDate, setCalendarDate] = useState(dayjs());
  const [shareLink, setShareLink] = useState('');
  const [form] = Form.useForm();
  const [resourceForm] = Form.useForm();
  const [filterForm] = Form.useForm();
  const [shareForm] = Form.useForm();
  const calendarRef = useRef(null);

  useEffect(() => {
    const loadData = async () => {
      try {
        const [eventsData, resourcesData] = await Promise.all([
          new Promise(resolve => setTimeout(() => resolve(getAllCalendarItems()), 500)),
          new Promise(resolve => setTimeout(() => resolve(getResources()), 500))
        ]);
        
        setAllEvents(eventsData);
        setEvents(eventsData);
        setResources(resourcesData);
        setFilteredResources(resourcesData.map(r => r.id));
      } catch (error) {
        message.error('Failed to load calendar data');
        console.error(error);
      }
    };
    
    loadData();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [selectedResources, dateRange, typeFilter, allEvents, viewType]);

  useEffect(() => {
    if (selectedEvent && editModalVisible) {
      form.setFieldsValue({
        type: selectedEvent.extendedProps?.type || 'activity',
        title: selectedEvent.title,
        resourceIds: selectedEvent.extendedProps?.resourceIds || [],
        time: selectedEvent.start && selectedEvent.end ? [dayjs(selectedEvent.start), dayjs(selectedEvent.end)] : null,
        eventDate: selectedEvent.start ? dayjs(selectedEvent.start) : null,
        eventType: selectedEvent.extendedProps?.eventType,
        eventLocation: selectedEvent.extendedProps?.eventLocation || selectedEvent.extendedProps?.location,
        eventCapacity: selectedEvent.extendedProps?.eventCapacity,
        status: selectedEvent.extendedProps?.status,
        description: selectedEvent.extendedProps?.description,
        participants: selectedEvent.extendedProps?.participants || []
      });
    }
  }, [selectedEvent, editModalVisible, form]);

  const applyFilters = () => {
    let filteredEvents = [...allEvents];

    if (selectedResources.length > 0) {
      filteredEvents = filteredEvents.filter(event => 
        !event.resourceIds || event.resourceIds.length === 0 || 
        event.resourceIds.some(id => selectedResources.includes(id))
      );
    }

    if (dateRange && dateRange[0] && dateRange[1]) {
      filteredEvents = filteredEvents.filter(event => {
        const eventStart = dayjs(event.start);
        return eventStart.isAfter(dateRange[0].startOf('day')) && 
               eventStart.isBefore(dateRange[1].endOf('day'));
      });
    }

    if (typeFilter !== 'all') {
      filteredEvents = filteredEvents.filter(event => event.type === typeFilter);
    }

    setEvents(filteredEvents);

    if (calendarRef.current) {
      const calendarApi = calendarRef.current.getApi();
      calendarApi.changeView(viewType);
      calendarApi.refetchEvents();
    }
  };

  const renderEventContent = ({ event }) => {
    const eventProps = event.extendedProps || {};
    return (
      <div className="fc-event-content" style={{ 
        backgroundColor: event.backgroundColor || event.color,
        borderColor: event.borderColor || event.color,
        borderRadius: '4px',
        padding: '6px 8px',
        boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
        color: '#fff',
        overflow: 'hidden'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <strong className="fc-event-title" style={{ fontSize: '13px' }}>
            {event.title}
          </strong>
          {eventProps.status === 'completed' && (
            <span style={{ fontSize: '10px', opacity: 0.8 }}>✓</span>
          )}
        </div>
        <div className="fc-event-time" style={{ fontSize: '11px', opacity: 0.9 }}>
          {dayjs(event.start).format('HH:mm')} {event.end ? `- ${dayjs(event.end).format('HH:mm')}` : ''}
        </div>
        {eventProps.resourceIds && eventProps.resourceIds.length > 0 && (
          <div className="fc-event-resources" style={{ marginTop: '4px', display: 'flex', gap: '4px' }}>
            {eventProps.resourceIds.slice(0, 3).map(id => {
              const resource = resources.find(r => r.id === id);
              return resource ? (
                <Tooltip key={id} title={resource.title}>
                  <Avatar 
                    size={18} 
                    style={{ 
                      backgroundColor: resource.color || '#ccc',
                      fontSize: '10px'
                    }}
                  >
                    {resource.title.charAt(0)}
                  </Avatar>
                </Tooltip>
              ) : null;
            })}
            {eventProps.resourceIds.length > 3 && (
              <Avatar size={18} style={{ fontSize: '10px' }}>
                +{eventProps.resourceIds.length - 3}
              </Avatar>
            )}
          </div>
        )}
        {eventProps.participants && eventProps.participants.length > 0 && (
          <div className="fc-event-participants" style={{ marginTop: '4px', display: 'flex', gap: '4px' }}>
            {eventProps.participants.slice(0, 3).map(id => {
              const participant = resources.find(r => r.id === id);
              return participant ? (
                <Tooltip key={id} title={participant.title}>
                  <Avatar 
                    size={16} 
                    style={{ 
                      backgroundColor: participant.color || '#ccc',
                      fontSize: '9px',
                      border: '1px solid #fff'
                    }}
                  >
                    {participant.title.charAt(0)}
                  </Avatar>
                </Tooltip>
              ) : null;
            })}
            {eventProps.participants.length > 3 && (
              <Avatar size={16} style={{ fontSize: '9px' }}>
                +{eventProps.participants.length - 3}
              </Avatar>
            )}
          </div>
        )}
      </div>
    );
  };

  const handleCreate = async (values) => {
    try {
      const isEvent = values.type === 'event';
      const startDate = isEvent ? values.eventDate : values.time?.[0];
      const endDate = isEvent ? values.eventDate?.add(1, 'hour') : values.time?.[1];

      if (!startDate || (isEvent && (!values.eventType || !values.eventLocation))) {
        message.error('Veuillez remplir tous les champs requis');
        return;
      }
console.log('values.participants',values.participants);
      const newEvent = {
        id: allEvents.length > 0 ? Math.max(...allEvents.map(e => e.id)) + 1 : 1,
        title: values.title,
        start: startDate.toDate(),
        end: endDate ? endDate.toDate() : startDate.toDate(),
        resourceIds: isEvent ? [] : values.resourceIds || [],
        color: isEvent ? '#ff4d4f' : resources.find(r => r.id === values.resourceIds?.[0])?.color || '#1890ff',
        status: isEvent ? 'planned' : values.status || 'planned',
        location: isEvent ? values.eventLocation : values.location,
        description: values.description,
        participants: values.participants || [],
        type: values.type,
        extendedProps: {
          creator: '1',
          createdAt: new Date().toISOString(),
          type: values.type,
          ...(isEvent ? {
            eventType: values.eventType,
            eventLocation: values.eventLocation,
            eventCapacity: values.eventCapacity
          } : {
            resourceIds: values.resourceIds,
            status: values.status,
            location: values.location
          }),
          description: values.description,
          participants: values.participants
        }
      };

      console.log('Creating event:', newEvent);

      const success = createEvent(newEvent);
      if (success) {
        const updatedEvents = [...allEvents, newEvent];
        setAllEvents(updatedEvents);
        setEvents(updatedEvents);
        if (calendarRef.current) {
          const calendarApi = calendarRef.current.getApi();
          calendarApi.addEvent(newEvent);
        }
        setCreateModalVisible(false);
        message.success(`${isEvent ? 'Événement' : 'Activité'} créé(e) avec succès`);
        form.resetFields();
      }
    } catch (error) {
      message.error('Erreur lors de la création');
      console.error(error);
    }
  };

  const handleEdit = async (values) => {
    try {
      const isEvent = values.type === 'event';
      const startDate = isEvent ? values.eventDate : values.time?.[0];
      const endDate = isEvent ? values.eventDate?.add(1, 'hour') : values.time?.[1];

      if (!startDate || (isEvent && (!values.eventType || !values.eventLocation))) {
        message.error('Veuillez remplir tous les champs requis');
        return;
      }
console.log('participants updd',values.participants);
      const updatedEvent = {
        ...selectedEvent,
        title: values.title,
        start: startDate.toDate(),
        end: endDate ? endDate.toDate() : startDate.toDate(),
        resourceIds: isEvent ? [] : values.resourceIds || [],
        color: isEvent ? '#ff4d4f' : resources.find(r => r.id === values.resourceIds?.[0])?.color || selectedEvent.color,
        status: isEvent ? 'planned' : values.status || 'planned',
        location: isEvent ? values.eventLocation : values.location,
        description: values.description,
        participants: values.participants || [],
        type: values.type,
        extendedProps: {
          ...selectedEvent.extendedProps,
          type: values.type,
          ...(isEvent ? {
            eventType: values.eventType,
            eventLocation: values.eventLocation,
            eventCapacity: values.eventCapacity
          } : {
            resourceIds: values.resourceIds,
            status: values.status,
            location: values.location
          }),
          description: values.description,
          participants: values.participants
        }
      };

      console.log('Updating event:', updatedEvent);

      const success = updateEvent(updatedEvent);
      if (success) {
        const updatedEvents = allEvents.map(e => e.id === updatedEvent.id ? updatedEvent : e);
        setAllEvents(updatedEvents);
        setEvents(updatedEvents);
        if (calendarRef.current) {
          const calendarApi = calendarRef.current.getApi();
          const calEvent = calendarApi.getEventById(updatedEvent.id);
          if (calEvent) {
            calEvent.setProp('title', updatedEvent.title);
            calEvent.setStart(updatedEvent.start);
            calEvent.setEnd(updatedEvent.end);
            calEvent.setProp('backgroundColor', updatedEvent.color);
            calEvent.setProp('borderColor', updatedEvent.color);
            calEvent.setExtendedProp('extendedProps', updatedEvent.extendedProps);
          }
        }
        setEditModalVisible(false);
        setSelectedEvent(null);
        message.success(`${isEvent ? 'Événement' : 'Activité'} mis(e) à jour avec succès`);
        form.resetFields();
      }
    } catch (error) {
      message.error('Erreur lors de la mise à jour');
      console.error(error);
    }
  };

  const handleDelete = async () => {
    try {
      const success = deleteEvent(selectedEvent.id);
      if (success) {
        const updatedEvents = allEvents.filter(e => e.id !== selectedEvent.id);
        setAllEvents(updatedEvents);
        setEvents(updatedEvents);
        if (calendarRef.current) {
          const calendarApi = calendarRef.current.getApi();
          const calEvent = calendarApi.getEventById(selectedEvent.id);
          if (calEvent) calEvent.remove();
        }
        setEditModalVisible(false);
        setSelectedEvent(null);
        message.success('Activité supprimée avec succès');
      }
    } catch (error) {
      message.error('Erreur lors de la suppression');
      console.error(error);
    }
  };

  const handleCreateResource = (values) => {
    const newResource = {
      ...values,
      id: resources.length > 0 ? String(Math.max(...resources.map(r => parseInt(r.id))) + 1) : '1'
    };
    setResources([...resources, newResource]);
    setFilteredResources([...selectedResources, newResource.id]);
    setResourceModalVisible(false);
    message.success('Ressource créée avec succès');
    resourceForm.resetFields();
  };

  const handleDateSelect = (selectInfo) => {
    form.setFieldsValue({
      time: [dayjs(selectInfo.start), dayjs(selectInfo.end)],
      eventDate: dayjs(selectInfo.start)
    });
    setCreateModalVisible(true);
  };

  const handleEventClick = (info) => {
    const event = {
      ...info.event.toPlainObject(),
      start: info.event.start,
      end: info.event.end,
      extendedProps: info.event.extendedProps
    };
    
    setSelectedEvent(event);
    setEditModalVisible(true);
  };

  const renderEventStatus = (status) => {
    const statusMap = {
      'planned': { color: 'blue', text: 'Planifié' },
      'in-progress': { color: 'orange', text: 'En cours' },
      'completed': { color: 'green', text: 'Terminé' },
      'cancelled': { color: 'red', text: 'Annulé' }
    };
    
    const statusInfo = statusMap[status] || { color: 'default', text: status };
    return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;
  };

  const handleFilterApply = (values) => {
    setFilteredResources(values.resources || []);
    setDateRange(values.dateRange || null);
    setTypeFilter(values.type || 'all');
  };

  const handleMiniCalendarChange = (date) => {
    setCalendarDate(date);
    if (calendarRef.current) {
      const calendarApi = calendarRef.current.getApi();
      calendarApi.gotoDate(date.toDate());
    }
  };

  const handleShareCalendar = (values) => {
    const shareData = {
      ...values,
      calendarId: 'calendar_1',
      sharedAt: dayjs().toISOString()
    };
    const link = shareCalendar(shareData);
    setShareLink(link);
    message.success('Calendrier partagé avec succès !');
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(shareLink);
    message.success('Lien copié dans le presse-papiers !');
  };

  return (
    <Layout style={{ minHeight: '100vh', backgroundColor: '#f0f2f5' }}>
      {/* Sidebar */}
      <Sider width={280} style={{ backgroundColor: '#fff', padding: '16px', boxShadow: '2px 0 8px rgba(0,0,0,0.1)' }}>
        <Space direction="vertical" size="middle" style={{ width: '100%' }}>
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            block
            onClick={() => setCreateModalVisible(true)}
            style={{ borderRadius: '6px' }}
          >
            Nouvelle activité
          </Button>
          
          <Button 
            icon={<PlusOutlined />}
            block
            onClick={() => setResourceModalVisible(true)}
            style={{ borderRadius: '6px' }}
          >
            Ajouter ressource
          </Button>
          
          <Button 
            icon={<ShareAltOutlined />}
            block
            onClick={() => setShareModalVisible(true)}
            style={{ borderRadius: '6px' }}
          >
            Partager le calendrier
          </Button>
          
          <Card title="Navigation" size="small" style={{ borderRadius: '6px' }}>
            <DatePicker
              picker="month"
              value={calendarDate}
              onChange={handleMiniCalendarChange}
              style={{ width: '100%', marginBottom: '8px' }}
            />
            <DatePicker
              mode="date"
              showToday
              value={calendarDate}
              onChange={handleMiniCalendarChange}
              style={{ width: '100%' }}
            />
          </Card>
          
          <Card title="Vue" size="small" style={{ borderRadius: '6px' }}>
            <Select
              value={viewType}
              onChange={(value) => setViewType(value)}
              style={{ width: '100%' }}
            >
              <Option value="dayGridMonth">Mois</Option>
              <Option value="timeGridWeek">Semaine</Option>
              <Option value="timeGridDay">Jour</Option>
              <Option value="listWeek">Liste</Option>
            </Select>
          </Card>
          
          <Card title="Filtres" size="small" style={{ borderRadius: '6px' }}>
            <Form form={filterForm} onFinish={handleFilterApply} layout="vertical">
              <Form.Item name="resources" label="Ressources">
                <Select
                  mode="multiple"
                  placeholder="Sélectionner des ressources"
                  optionLabelProp="label"
                  value={selectedResources}
                  onChange={(value) => setFilteredResources(value)}
                  style={{ width: '100%' }}
                >
                  {resources.map(resource => (
                    <Option 
                      key={resource.id} 
                      value={resource.id}
                      label={resource.title}
                    >
                      <Avatar 
                        size="small" 
                        style={{ backgroundColor: resource.color, marginRight: 8 }}
                      >
                        {resource.title.charAt(0)}
                      </Avatar>
                      {resource.title} ({resource.type === 'person' ? 'Personne' : 
                                        resource.type === 'room' ? 'Salle' : 'Équipement'})
                    </Option>
                  ))}
                </Select>
              </Form.Item>
              <Form.Item name="dateRange" label="Plage de dates">
                <RangePicker 
                  format="YYYY-MM-DD" 
                  style={{ width: '100%' }}
                  allowClear
                />
              </Form.Item>
              <Form.Item name="type" label="Type">
                <Select
                  value={typeFilter}
                  onChange={(value) => setTypeFilter(value)}
                  style={{ width: '100%' }}
                >
                  <Option value="all">Tous</Option>
                  <Option value="activity">Activités</Option>
                  <Option value="event">Événements</Option>
                </Select>
              </Form.Item>
              <Button type="primary" htmlType="submit" block style={{ borderRadius: '6px' }}>
                Appliquer les filtres
              </Button>
            </Form>
          </Card>
        </Space>
      </Sider>

      {/* Main Calendar Content */}
      <Content style={{ padding: '16px', backgroundColor: '#f0f2f5' }}>
        <FullCalendar
          ref={calendarRef}
          plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin, listPlugin]}
          initialView={viewType}
          headerToolbar={{
            left: 'prev,next today',
            center: 'title',
            right: ''
          }}
          events={events}
          eventContent={renderEventContent}
          eventClick={handleEventClick}
          selectable={true}
          select={handleDateSelect}
          editable={true}
          droppable={true}
          locale="fr"
          height="calc(100vh - 100px)"
          nowIndicator={true}
          eventDisplay="block"
          eventAdd={(event) => console.log('Event added', event)}
          eventChange={(event) => console.log('Event changed', event)}
          eventRemove={(event) => console.log('Event removed', event)}
        />
      </Content>

      {/* Create Event Modal */}
      <Modal
        title="Nouvelle activité"
        open={createModalVisible}
        onCancel={() => {
          setCreateModalVisible(false);
          form.resetFields();
        }}
        onOk={() => form.submit()}
        okText="Enregistrer"
        width={700}
        styles={{ body: { padding: '24px' } }}
      >
        <Form form={form} onFinish={handleCreate} layout="vertical">
          <Form.Item 
            name="type" 
            label="Type" 
            initialValue="activity"
            style={{ marginBottom: '16px' }}
          >
            <Radio.Group>
              <Radio.Button value="activity">Activité</Radio.Button>
              <Radio.Button value="event">Événement</Radio.Button>
            </Radio.Group>
          </Form.Item>

          <Form.Item name="title" label="Titre" rules={[{ required: true, message: 'Veuillez entrer un titre' }]}>
            <Input placeholder="Titre" />
          </Form.Item>

          <Form.Item noStyle shouldUpdate>
            {({ getFieldValue }) => {
              const type = getFieldValue('type') || 'activity';
              
              if (type === 'activity') {
                return (
                  <>
                    <Form.Item 
                      name="resourceIds" 
                      label="Ressources" 
                      rules={[{ required: true, message: 'Veuillez sélectionner au moins une ressource' }]}
                    >
                      <Select 
                        mode="multiple"
                        placeholder="Sélectionner des ressources"
                        optionLabelProp="label"
                      >
                        {resources.map(resource => (
                          <Option 
                            key={resource.id} 
                            value={resource.id}
                            label={resource.title}
                          >
                            <Avatar 
                              size="small" 
                              style={{ backgroundColor: resource.color, marginRight: 8 }}
                            >
                              {resource.title.charAt(0)}
                            </Avatar>
                            {resource.title} ({resource.type === 'person' ? 'Personne' : 
                                             resource.type === 'room' ? 'Salle' : 'Équipement'})
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                    <Form.Item 
                      name="time" 
                      label="Période" 
                      rules={[{ required: true, message: 'Veuillez sélectionner une période' }]}
                    >
                      <RangePicker 
                        showTime 
                        format="YYYY-MM-DD HH:mm" 
                        style={{ width: '100%' }}
                      />
                    </Form.Item>
                    <Form.Item name="status" label="Statut" initialValue="planned">
                      <Select>
                        <Option value="planned">Planifié</Option>
                        <Option value="in-progress">En cours</Option>
                        <Option value="completed">Terminé</Option>
                        <Option value="cancelled">Annulé</Option>
                      </Select>
                    </Form.Item>
                    <Form.Item name="location" label="Lieu">
                      <Input placeholder="Lieu" />
                    </Form.Item>
                  </>
                );
              } else {
                return (
                  <>
                    <Form.Item name="eventType" label="Type d'événement" rules={[{ required: true, message: 'Veuillez sélectionner un type d\'événement' }]}>
                      <Select placeholder="Sélectionner un type">
                        <Option value="meeting">Réunion</Option>
                        <Option value="conference">Conférence</Option>
                        <Option value="workshop">Atelier</Option>
                        <Option value="seminar">Séminaire</Option>
                      </Select>
                    </Form.Item>
                    <Form.Item name="eventDate" label="Date" rules={[{ required: true, message: 'Veuillez sélectionner une date' }]}>
                      <DatePicker showTime format="YYYY-MM-DD HH:mm" style={{ width: '100%' }} />
                    </Form.Item>
                    <Form.Item name="eventLocation" label="Lieu" rules={[{ required: true, message: 'Veuillez entrer un lieu' }]}>
                      <Input placeholder="Lieu de l'événement" />
                    </Form.Item>
                    <Form.Item name="eventCapacity" label="Capacité">
                      <InputNumber min={1} style={{ width: '100%' }} />
                    </Form.Item>
                  </>
                );
              }
            }}
          </Form.Item>

          <Form.Item name="participants" label="Participants">
            <Select 
              mode="multiple" 
              placeholder="Sélectionner des participants"
              optionLabelProp="label"
            >
              {resources.filter(r => r.type === 'person').map(person => (
                <Option 
                  key={person.id} 
                  value={person.id}
                  label={person.title}
                >
                  <Avatar 
                    size="small" 
                    style={{ backgroundColor: person.color, marginRight: 8 }}
                  >
                    {person.title.charAt(0)}
                  </Avatar>
                  {person.title}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item name="description" label="Description">
            <TextArea rows='4' placeholder="Description" />
          </Form.Item>
        </Form>
      </Modal>

      {/* Edit Event Modal */}
      <Modal
        title={
          <div>
            <EditOutlined style={{ marginRight: 8 }} />
            Modifier l'activité
          </div>
        }
        open={editModalVisible}
        onCancel={() => {
          setEditModalVisible(false);
          setSelectedEvent(null);
          form.resetFields();
        }}
        footer={[
          <Popconfirm
            key="delete"
            title="Êtes-vous sûr de vouloir supprimer cette activité ?"
            onConfirm={handleDelete}
            okText="Oui"
            cancelText="Non"
          >
            <Button danger icon={<DeleteOutlined />}>
              Supprimer
            </Button>
          </Popconfirm>,
          <Button key="cancel" onClick={() => {
            setEditModalVisible(false);
            setSelectedEvent(null);
            form.resetFields();
          }}>
            Annuler
          </Button>,
          <Button key="submit" type="primary" onClick={() => form.submit()}>
            Enregistrer
          </Button>
        ]}
        width={700}
        styles={{ body: { padding: '24px' } }}
      >
        {selectedEvent && (
          <Form form={form} onFinish={handleEdit} layout="vertical">
            <div style={{ marginBottom: 16 }}>
              <strong>Statut : </strong>
              {renderEventStatus(selectedEvent.extendedProps?.status)}
            </div>
            <Form.Item 
              name="type" 
              label="Type" 
              style={{ marginBottom: '16px' }}
            >
              <Radio.Group>
                <Radio.Button value="activity">Activité</Radio.Button>
                <Radio.Button value="event">Événement</Radio.Button>
              </Radio.Group>
            </Form.Item>

            <Form.Item name="title" label="Titre" rules={[{ required: true, message: 'Veuillez entrer un titre' }]}>
              <Input placeholder="Titre" />
            </Form.Item>

            <Form.Item noStyle shouldUpdate>
              {({ getFieldValue }) => {
                const type = getFieldValue('type') || 'activity';
                
                if (type === 'activity') {
                  return (
                    <>
                      <Form.Item 
                        name="resourceIds" 
                        label="Ressources" 
                        rules={[{ required: true, message: 'Veuillez sélectionner au moins une ressource' }]}
                      >
                        <Select 
                          mode="multiple"
                          placeholder="Sélectionner des ressources"
                          optionLabelProp="label"
                        >
                          {resources.map(resource => (
                            <Option 
                              key={resource.id} 
                              value={resource.id}
                              label={resource.title}
                            >
                              <Avatar 
                                size="small" 
                                style={{ backgroundColor: resource.color, marginRight: 8 }}
                              >
                                {resource.title.charAt(0)}
                              </Avatar>
                              {resource.title} ({resource.type === 'person' ? 'Personne' : 
                                               resource.type === 'room' ? 'Salle' : 'Équipement'})
                            </Option>
                          ))}
                        </Select>
                      </Form.Item>
                      <Form.Item 
                        name="time" 
                        label="Période" 
                        rules={[{ required: true, message: 'Veuillez sélectionner une période' }]}
                      >
                        <RangePicker 
                          showTime 
                          format="YYYY-MM-DD HH:mm" 
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                      <Form.Item name="status" label="Statut">
                        <Select>
                          <Option value="planned">Planifié</Option>
                          <Option value="in-progress">En cours</Option>
                          <Option value="completed">Terminé</Option>
                          <Option value="cancelled">Annulé</Option>
                        </Select>
                      </Form.Item>
                      <Form.Item name="location" label="Lieu">
                        <Input placeholder="Lieu" />
                      </Form.Item>
                    </>
                  );
                } else {
                  return (
                    <>
                      <Form.Item name="eventType" label="Type d'événement" rules={[{ required: true, message: 'Veuillez sélectionner un type d\'événement' }]}>
                        <Select placeholder="Sélectionner un type">
                          <Option value="meeting">Réunion</Option>
                          <Option value="conference">Conférence</Option>
                          <Option value="workshop">Atelier</Option>
                          <Option value="seminar">Séminaire</Option>
                        </Select>
                      </Form.Item>
                      <Form.Item name="eventDate" label="Date" rules={[{ required: true, message: 'Veuillez sélectionner une date' }]}>
                        <DatePicker showTime format="YYYY-MM-DD HH:mm" style={{ width: '100%' }} />
                      </Form.Item>
                      <Form.Item name="eventLocation" label="Lieu" rules={[{ required: true, message: 'Veuillez entrer un lieu' }]}>
                        <Input placeholder="Lieu de l'événement" />
                      </Form.Item>
                      <Form.Item name="eventCapacity" label="Capacité">
                        <InputNumber min={1} style={{ width: '100%' }} />
                      </Form.Item>
                    </>
                  );
                }
              }}
            </Form.Item>

            <Form.Item name="participants" label="Participants">
              <Select 
                mode="multiple" 
                placeholder="Sélectionner des participants"
                optionLabelProp="label"
              >
                {resources.filter(r => r.type === 'person').map(person => (
                  <Option 
                    key={person.id} 
                    value={person.id}
                    label={person.title}
                  >
                    <Avatar 
                      size="small" 
                      style={{ backgroundColor: person.color, marginRight: 8 }}
                    >
                      {person.title.charAt(0)}
                    </Avatar>
                    {person.title}
                  </Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item name="description" label="Description">
              <TextArea rows="4" placeholder="Description" />
            </Form.Item>
          </Form>
        )}
      </Modal>

      {/* Create Resource Modal */}
      <Modal
        title="Nouvelle ressource"
        open={resourceModalVisible}
        onCancel={() => {
          setResourceModalVisible(false);
          resourceForm.resetFields();
        }}
        onOk={() => resourceForm.submit()}
        okText="Enregistrer"
        styles={{ body: { padding: '24px' } }}
      >
        <Form form={resourceForm} onFinish={handleCreateResource} layout="vertical">
          <Form.Item name="title" label="Nom" rules={[{ required: true, message: 'Veuillez entrer un nom' }]}>
            <Input placeholder="Nom de la ressource" />
          </Form.Item>
          <Form.Item name="type" label="Type" initialValue="person">
            <Select>
              <Option value="person">Personne</Option>
              <Option value="room">Salle</Option>
              <Option value="equipment">Équipement</Option>
            </Select>
          </Form.Item>
          <Form.Item name="color" label="Couleur" initialValue="#1890ff">
            <Select>
              <Option value="#f56a00">Orange</Option>
              <Option value="#7265e6">Violet</Option>
              <Option value="#ffbf00">Jaune</Option>
              <Option value="#00a2ae">Turquoise</Option>
              <Option value="#87d068">Vert</Option>
              <Option value="#1890ff">Bleu</Option>
              <Option value="#ff4d4f">Rouge</Option>
            </Select>
          </Form.Item>
          <Form.Item name="description" label="Description">
            <TextArea rows="3" placeholder="Description de la ressource" />
          </Form.Item>
        </Form>
      </Modal>

      {/* Share Calendar Modal */}
      <Modal
        title="Partager le calendrier"
        open={shareModalVisible}
        onCancel={() => {
          setShareModalVisible(false);
          setShareLink('');
          shareForm.resetFields();
        }}
        onOk={() => shareForm.submit()}
        okText="Enregistrer"
        width={600}
        styles={{ body: { padding: '24px' } }}
      >
        <Form form={shareForm} onFinish={handleShareCalendar} layout="vertical">
          <Form.Item
            name="emails"
            label="Inviter des personnes"
            rules={[{ type: 'array', message: 'Veuillez entrer des adresses email valides' }]}
          >
            <Select
              mode="tags"
              placeholder="Entrez les adresses email"
              style={{ width: '100%' }}
              tokenSeparators={[',', ' ']}
            />
          </Form.Item>
          <Form.Item name="permission" label="Permission" initialValue="view">
            <Select>
              <Option value="view">Lecture seule</Option>
              <Option value="edit">Lecture et modification</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="expirationDays"
            label="Expiration du partage (jours)"
            initialValue={30}
          >
            <InputNumber min={1} max={365} style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item
            name="publicAccess"
            label="Accès public"
            valuePropName="checked"
          >
            <Switch checkedChildren="Activé" unCheckedChildren="Désactivé" />
          </Form.Item>
          {shareLink && (
            <Form.Item label="Lien de partage">
              <Input
                value={shareLink}
                readOnly
                addonAfter={
                  <Button type="link" onClick={copyToClipboard}>
                    Copier
                  </Button>
                }
              />
            </Form.Item>
          )}
        </Form>
      </Modal>
    </Layout>
  );
}