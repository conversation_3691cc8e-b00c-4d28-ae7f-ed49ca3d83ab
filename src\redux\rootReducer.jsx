// src/redux/rootReducer.js
import { combineReducers } from 'redux';
import counterReducer from './reducers/counterReducer';
import loadingReducer from './reducers/loadingReducer';
import postReducer from './reducers/postReducer';
import categoryReducer from './reducers/categoryReducer';
import authReducer from './reducers/authReducer';

const rootReducer = combineReducers({
  auth: authReducer,
  counter: counterReducer,
  loading: loadingReducer,
  post : postReducer,
  category  :categoryReducer
});

export default rootReducer;
