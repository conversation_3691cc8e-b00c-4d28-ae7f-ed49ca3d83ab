import { ModalForm } from "@ant-design/pro-components";
import { Card, Col, DatePicker, Form, Input, Row } from "antd";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

function BesoinForm({ open, onCancel, record }) {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  return (
    <ModalForm
      title={t("Besoins pour l’action")}
      form={form}
      open={open}
      width="70%"
      modalProps={{
        style: { top: 20 },
      }}
      //   onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.save"),
          resetText: t("common.actions.cancel"),
        },
      }}
    >
      <Card className="mt-2">
        <Row gutter={16} style={{ marginBottom: "10px" }}>
          <Col span={4}></Col>
          <Col span={8}>Action</Col>
          <Col span={4}>Date butoir</Col>
          <Col span={4}>Budget prévu en DT</Col>
          <Col span={4}>Budget réalisé en DT</Col>
        </Row>
        <Row gutter={16} style={{ marginBottom: "10px" }}>
          <Col span={4}>Besoins logistiques.</Col>
          <Col span={8}>
            <Input />
          </Col>
          <Col span={4}>
            <DatePicker />
          </Col>
          <Col span={4}>
            <Input />
          </Col>
          <Col span={4}>
            <Input />
          </Col>
       
        </Row>
        <Row gutter={16} style={{ marginBottom: "10px" }}>
          <Col span={4}>Frais de déplacement</Col>
          <Col span={8}>
            <Input />
          </Col>
          <Col span={4}>
            <DatePicker />
          </Col>
          <Col span={4}>
            <Input />
          </Col>
          <Col span={4}>
            <Input />
          </Col>
        </Row>
        <Row gutter={16} style={{ marginBottom: "10px" }}>
          <Col span={4}>Frais de mission</Col>
          <Col span={8}>
            <Input />
          </Col>
          <Col span={4}>
            <DatePicker />
          </Col>
          <Col span={4}>
            <Input />
          </Col>
          <Col span={4}>
            <Input />
          </Col>
        </Row>
      </Card>
    </ModalForm>
  );
}
export default BesoinForm;
