import { useState, useRef } from "react";
import { ProTable } from "@ant-design/pro-components";
import { Button, Popconfirm, Tag, Space, Input, Tooltip, Typography, Card, Modal } from "antd";
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined, SearchOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import useDebounce from "@hooks/useDebounce";

import CreateActivity from "./create";
import EditActivity from "./edit";
import ViewActivity from "./view";
import { datas } from "./datas";

function Activities() {
  const { t } = useTranslation();
  const tableRef = useRef();

  const [dataSource, setDataSource] = useState(datas);

  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [viewModalVisible, setViewModalVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState(null);
  const [viewingRecord, setViewingRecord] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [tableParams, setTableParams] = useState([]);


  const handleCancel = () => {

    setViewModalVisible(false);

  };

  const handleEdit = (record) => {
    console.log("record ",record);
    setEditingRecord(record);  
    setEditModalVisible(true); 
  };
  const columns = [
    {
      title: "Nom",
      dataIndex: "nom",
      key: "nom",
    },
    {
      title: "Résumé",
      dataIndex: "resume",
      key: "resume",
    },
  
    {
      title: t("common.actions.title"),
      key: "action",
      align: "center",
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title={t("common.actions.view")}>
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => {
                setViewingRecord(record);
                setViewModalVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title={t("common.actions.edit")}>
            <Button
              type="link"
              style={{ color: "#f5b041" }}
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}

            />
          </Tooltip>
          <Tooltip title={t("common.actions.delete")}>
            <Popconfirm
              title={t("common.messages.confirm_delete")}
              onConfirm={() => {
                const newDataSource = dataSource.filter(item => item.key !== record.key);
                setDataSource(newDataSource);
              }}
            >
              <Button type="link" style={{ color: "#ec7063" }} icon={<DeleteOutlined />} />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <Card
      bordered
      title={<Typography.Title level={4}>{"Liste des types des tâches "}</Typography.Title>}
      extra={
        <Space>
          <Input
            size="large"
            placeholder={t("common.search")}
            suffix={<SearchOutlined style={{ color: "#bfbfbf" }} />}
          />
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setCreateModalVisible(true)}
          >
            {t("crm.activities.add")}
          </Button>
        </Space>
      }
    >
      <ProTable
        actionRef={tableRef}
        loading={isLoading}
        columns={columns}
        dataSource={dataSource}
        rowKey="key"
        search={false}
        options={false}
        pagination={{ showSizeChanger: true, defaultPageSize: 5 }}
      />

      {/* Create  */}
      <Modal
        title={"Ajouter une tâche"}
        visible={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        footer={null}
        width={800}
      >
        <CreateActivity 
         onCancel={() => setCreateModalVisible(false)}
         setDataSource={setDataSource} 
        />
      </Modal>

      {/* Edit  */}
      <Modal
        title={t("crm.activities.edit")}
        visible={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        footer={null}
        width={800}
      >
        <EditActivity
          record={editingRecord}
          onCancel={() => setEditModalVisible(false)}
          setDataSource={setDataSource} 
        />
      </Modal>

      {/* View  */}
      <Modal
        title={t("crm.activities.view")}
        visible={viewModalVisible}
        onCancel={handleCancel}
        footer={null}
        width={800}
      >
        <ViewActivity
          record={viewingRecord}
          onCancel={() => setViewModalVisible(false)}
        />
      </Modal>
    </Card>
  );
}

export default Activities;
