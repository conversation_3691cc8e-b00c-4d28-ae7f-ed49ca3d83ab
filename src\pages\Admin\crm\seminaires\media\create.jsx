import {
  CheckCircleOutlined,
  CheckCircleTwoTone,
  PlusCircleFilled,
} from "@ant-design/icons";
import { ModalForm } from "@ant-design/pro-components";
import {
  Button,
  Card,
  Checkbox,
  Col,
  DatePicker,
  Divider,
  Form,
  Input,
  message,
  Modal,
  Popover,
  Radio,
  Row,
  Select,
  Steps,
} from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";
// import {
//   activites,
//   categories,
//   delegations,
//   gouvernorats,
//   nationalites,
//   secteurs,
// } from "./data";
const initialSteps = [
  {
    id: 1,
    title: "Proposée",
    done: false,
    motif: false,
    reason: "",
    status: "finish",
  },
  {
    id: 2,
    title: "Programmée",
    done: false,
    motif: false,
    reason: "",
    status: "finish",
  },
  {
    id: 3,
    title: "Réalisée",
    done: false,
    motif: false,
    reason: "",
    status: "wait",
  },
  {
    id: 4,
    title: "Reportée",
    done: false,
    motif: true,
    reason: "",
    status: "finish",
  },
  {
    id: 5,
    title: "Ann<PERSON>ée",
    done: false,
    motif: true,
    reason: "",
    status: "finish",
  },
  {
    id: 6,
    title: "Non programmée",
    done: false,
    motif: false,
    reason: "",
    status: "wait",
  },
  {
    id: 7,
    title: "Validée",
    done: false,
    motif: false,
    reason: "",
    status: "wait",
  },
];
function CreateMedia({ open, onCancel, onSuccess }) {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const handleSubmit = (values) => {};
  const [steps, setSteps] = useState(initialSteps);
  const [completedSteps, setCompletedSteps] = useState([]);
  const [showMotifModal, setShowMotifModal] = useState(false);
  const [selectedStepId, setSelectedStepId] = useState(null);
  const [reason, setReason] = useState("");
  const handleStepClick = (step) => {
    if (step.motif && step.status === "wait") {
      setSelectedStepId(step.id);
      setShowMotifModal(true);
    } else {
      toggleStepStatus(step.id);
    }
  };
  const toggleStepStatus = (id) => {
    const updated = steps.map((step) => {
      if (step.id === id) {
        const newStatus = step.status === "finish" ? "wait" : "finish";
        return { ...step, status: newStatus, reason: reason };
      }
      return step;
    });
    setSteps(updated);
  };

  return (
    <ModalForm
      title={t("Ajouter un Média")}
      form={form}
      open={open}
      modalProps={{
        style: { top: 20 },
      }}
      onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.save"),
          resetText: t("common.actions.cancel"),
        },
      }}
    >
      <Card className="mt-2">
        <Form.Item name={"action"} label={t("")}>
          <Checkbox.Group
            options={[
              "Proposée",
              "Programmée",
              "Réalisée",
              "Reportée",
              "Annulée",
            ]}
          />
        </Form.Item>

        <Row gutter={16}>
          <Col md={12} xs={24}>
            <Form.Item name={"action"} label={t("Action")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name={"propose_par"} label={t("Proposée par ")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name={"responsable"} label={t("Responsable bureau")}>
              <Select />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name={"vav_siege"} label={t("VAV siège")}>
              <Select />
            </Form.Item>
          </Col>
        </Row>
      </Card>

      <Divider>Identification de l'action</Divider>
      <Card className="mt-2">
        <Row gutter={16}>
          <Col md={12} xs={24}>
            <Form.Item name={"intitule"} label={t("Type de l'action")}>
              <Select />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name={"num_edition"} label={t("Durée")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name={"site_web"} label={t("Zone d'impact")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name={"organisateur"} label={t("Cible")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name={"convention"} label={t("Objectif")}>
              <Select />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name={"organisateur"} label={t("Résultat attendus")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item name={"organisateur"} label={t("Budget")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={4} xs={24}>
            <Form.Item name={"organisateur"} label={t("   ")}>
              <Select />
            </Form.Item>
          </Col>
          <Col md={12} xs={24}>
            <Form.Item name={"convention"} label={t("Imputation financière")}>
              <Select />
            </Form.Item>
          </Col>

          <Col md={12} xs={24}>
            <Form.Item name={"date_debut"} label={t("Date début")}>
              <DatePicker />
            </Form.Item>
          </Col>

          <Col md={12} xs={24}>
            <Form.Item name={"date_fin"} label={t("Date fin")}>
              <DatePicker />
            </Form.Item>
          </Col>
        </Row>
      </Card>

      <Divider>Identification du Média</Divider>
      <Card className="mt-2">
        <Row gutter={16}>
          <Col md={24} xs={24}>
            <Form.Item name={""} label={t("Type du Media")}>
              <Select />
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item name={""} label={t("Nationalité")}>
              <Select />
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item name={""} label={t("Langue")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={8} xs={24}>
            <Form.Item name={""} label={t("Tirage/audience")}>
              <Select />
            </Form.Item>
          </Col>
          <Col md={24} xs={24}>
            <Form.Item name={""} label={t("Composition du lectorat/audience")}>
              <Input />
            </Form.Item>
          </Col>
          <Col md={24} xs={24}>
            <Form.Item name={""} label={t("Collaboration antérieure avec FIPA")}>
              <Input.TextArea />
            </Form.Item>
          </Col>
          <Col md={24} xs={24}>
            <Form.Item name={""} label={t("Volume de la couverture media sur la Tunisie ")}>
              <Input.TextArea />
            </Form.Item>
          </Col>

          <Col md={24} xs={24}>
            <Form.Item name={""} label={t("Régie publicitaire ")}>
              <Input.TextArea />
            </Form.Item>
          </Col>

          <Col md={24} xs={24}>
            <Form.Item name={""} label={t("Media")}>
              <Input.TextArea />
            </Form.Item>
          </Col>

         
        </Row>
      </Card>
  
      <Modal
        title="Justification du changement d'état"
        open={showMotifModal}
        onCancel={() => {
          setShowMotifModal(false);
          setReason("");
        }}
        onOk={() => {
          if (!reason.trim()) {
            message.error("Veuillez remplir le champ de justification.");
            return;
          }
          toggleStepStatus(selectedStepId);
          setShowMotifModal(false);
          setReason("");
        }}
        okText="Valider"
        cancelText="Annuler"
      >
        <p>Veuillez justifier ce changement :</p>
        <Input.TextArea
          rows={4}
          value={reason}
          onChange={(e) => setReason(e.target.value)}
          placeholder="Saisissez votre justification ici"
        />
      </Modal>
    </ModalForm>
  );
}
export default CreateMedia;
