import {
  Button,
  Col,
  Form,
  Input,
  InputNumber,
  Popconfirm,
  Row,
  Select,
  Table,
} from "antd";

import { useTranslation } from "react-i18next";
import { tvas, unites, articles ,StockArticles } from "../../pages/sales/deliveries/data";
import { useEffect, useState } from "react";
import { DeleteOutlined, PlusOutlined } from "@ant-design/icons";

const DeliveryItem = ({ form , record, open ,depot}) => {
  const { t } = useTranslation();
  const [totalHT, setTotalHT] = useState(0);
  const [totalTVA, setTotalTVA] = useState(0);
  const [totalTTC, setTotalTTC] = useState(0);
  const [totalHTRemise, setTotalHTRemise] = useState(null);
  const [remise, setRemise] = useState(null);
   useEffect(() => {
    if (open) {

      setTotalHT(record?.total_ht);
      setTotalTTC(record?.total_ttc) ;
      setTotalTVA(record?.total_tva) ;
    }
    } ,[open])
  const footer = () => {
    return (
      <>
        <div style={{ textAlign: "right" }}>
          <div
            style={{
              display: "inline-block",
              borderRight: "1px solid #ddd",
              padding: "0 16px",
            }}
          >
            <strong>{t("sales.total_ht")}: </strong>
            <InputNumber
              style={{ width: "150px" }}
              disabled={true}
              value={totalHT}
              min={0}
              precision={3}
              step={0.001}
            />
            <br />
            <br />
            <br />
          </div>
          <div
            style={{
              display: "inline-block",
              borderRight: "1px solid #ddd",
              padding: "0 16px",
            }}
          >
            <strong>{t("sales.remise")}: </strong>
            <InputNumber
              min={0}
              style={{ width: "150px" }}
              value={remise}
              //onChange={(value) => handleRemiseDevis(value)}
              disabled={true}
            />
            <br />
            <br />
            <strong>{t("sales.totalht_remise")} : </strong>
            <InputNumber
              style={{ width: "150px" }}
              disabled={true}
              // value={totalHTRemise}
              min={0}
              precision={3}
              step={0.001}
            />
          </div>

          <div
            style={{
              display: "inline-block",
              padding: "0 16px",
            }}
          >
            <strong>{t("sales.total_tva")} : </strong>
            <InputNumber
              style={{ width: "150px" }}
              disabled={true}
              value={totalTVA}
              min={0}
              precision={3}
              step={0.001}
            />
            <br />
            <br />
            <strong>{t("sales.total_ttc")} : </strong>
            <InputNumber
              style={{ width: "150px" }}
              disabled={true}
              value={totalTTC}
              min={0}
              precision={3}
              step={0.001}
            />
          </div>
        </div>
      </>
    );
  };
  const getRowDataByIndex = (index) => {
    const allData = form.getFieldValue("articles") || [];
    const rowData = allData[index];
    return rowData;
  };
  const countTotals = () => {
    let total_ht = 0;
    let total_tva = 0;
    let total_ttc = 0;

    const articlesValues = form.getFieldValue("articles");
    if (articlesValues) {
      articlesValues.forEach((item) => {
        var tva = tvas.find((element) => element.id === item.tva_id);
        var tauxTva = tva?.value || 0;
        var ht_item = item?.totalhtc;
        var tva_item = ht_item * (tauxTva / 100);
        var ttc_item = ht_item + tva_item;

        total_ht += ht_item;
        total_tva += tva_item;
        total_ttc += ttc_item;
      });
      setTotalHT(total_ht);
      setTotalTVA(total_tva);
      setTotalTTC(total_ttc);
    }
  };
  const countTotalByRow = (index) => {
    var selectedRow = getRowDataByIndex(index);
    var tva = tvas.find((element) => element.id === selectedRow.tva_id);
    var tauxTva = tva?.value || 0;
    var tauxRemise = selectedRow.remise || 0;
    var total_ht = 0;
    var total_tva = 0;

    total_ht =
      selectedRow.quantite * selectedRow.pu_ht * (1 - tauxRemise / 100);
    total_tva = total_ht * (tauxTva / 100);

    var prix_ttc = total_ht + total_tva;

    form.setFieldsValue({
      articles: {
        [index]: {
          totalhtc: total_ht,
          montantTTC: prix_ttc,
        },
      },
    });
    countTotals();
  };
  const handleSelectCode = (code, index) => {
    const articlesValues = form.getFieldValue("articles") || [];

    const selectedArticle = articles.find((article) => article.id === code);

    // Assurez-vous que la liste `articlesValues` existe
    const newArticles = [...articlesValues];

    // Mise à jour de l'article à l'index donné
    newArticles[index] = {
      article_id: selectedArticle?.id || null,
      designation: selectedArticle?.designation || null,
      pu_ht: selectedArticle?.pu_ht || null,
      tva_id: selectedArticle?.tva_id || null,
      unite_vente_id: selectedArticle?.unite_vente_id || null,
      quantite: null,
      remise: null,
      totalhtc: null,
      montantTTC: null,
    };

    // Mise à jour du formulaire avec la liste complète
    form.setFieldsValue({ articles: newArticles });
  };
  const handleQuantiteItem = (quantite, index) => {
    form.setFieldsValue({
      articles: {
        [index]: {
          quantite: quantite,
        },
      },
    });
    countTotalByRow(index);
  };

  const myList = form.getFieldValue("articles") ;
  useEffect(() => {
   myList?.map((element,index) => {
    
    validateQuantite(index,element ) ;
   }) ;
  }, [myList]);
  const [errors, setErrors] = useState({});
  const validateQuantite = (index, value) => {
    var selectedRow = getRowDataByIndex(index);
    const newErrors = {}; 
    const article = StockArticles.find( (element) => (element.article_id === selectedRow.article_id ) && (element.depot_id === depot));
    if (!value || selectedRow?.quantite > article?.quantite) {
      newErrors[index] = "Quantité insuffisante.";
    } else {
      newErrors[index] = "";
    }
    setErrors((prevErrors) => ({ ...prevErrors, ...newErrors }));
  };
  const handleRemiseItem = (remise, index) => {
    form.setFieldsValue({
      articles: {
        [index]: {
          remise: remise,
        },
      },
    });
    countTotalByRow(index);
  };
  const handleTVAItem = (tva_id, index) => {
    form.setFieldsValue({
      articles: {
        [index]: {
          tva_id: tva_id,
        },
      },
    });
    countTotalByRow(index);
  };
  const handlePUItem = (pu_ht, index) => {
    form.setFieldsValue({
      articles: {
        [index]: {
          pu_ht: pu_ht,
        },
      },
    });
    countTotalByRow(index);
  };
  const handleRemove = (index) => {
    // Remove the row at the given index
    const allData = form.getFieldValue("articles");
    console.log(allData);
    const updatedData = allData.filter((_, idx) => idx !== index);
    form.setFieldsValue({ articles: updatedData });
    countTotals();
  };
  return (
    <Row className="mt-0" gutter={[12, 24]}>
      <Col span={24}>
        <Form.List name="articles">
          {(fields, { add, remove }) => (
            <>
              <Table
                dataSource={fields}
                pagination={false}
                rowKey="key"
                tableLayout="auto"
                footer={footer}
              >
                <Table.Column
                  title={t("sales.code")}
                  dataIndex="article_id"
                  render={(text, record, index) => (
                    <Form.Item
                      name={[index, "article_id"]}
                      rules={[
                        {
                          required: true,
                          message: t("sales_message.required_field"),
                        },
                      ]}
                    >
                      <Select
                        allowClear
                        placeholder={t("sales.code")}
                        options={articles.map((item) => ({
                          value: item.id,
                          label: item.code,
                        }))}
                        onChange={(value) => handleSelectCode(value, index)}
                      />
                    </Form.Item>
                  )}
                />
                <Table.Column
                  title={t("sales.designation")}
                  dataIndex="designation"
                  render={(text, record, index) => (
                    <Form.Item
                      name={[index, "designation"]}
                      rules={[
                        {
                          required: true,
                          message: t("sales_message.required_field"),
                        },
                      ]}
                    >
                      <Input style={{ width: 250 }} />
                    </Form.Item>
                  )}
                />

                <Table.Column
                  title={t("sales.quantite")}
                  dataIndex="quantite"
                  render={(text, record, index) => (
                    <Form.Item
                      name={[index, "quantite"]}
                       validateTrigger="onChange"
                      rules={[
                        {
                          required: true,
                          message: t("sales_message.required_field"),
                        },
                      ]}
                      help={errors[record.key] && (
                        <div style={{ color: "red", fontSize: "12px" }}>
                          {errors[record.key]}
                        </div>
                      )}
                    >
                      <InputNumber
                        min={1}
                        step={1}
                        precision={0}
                        onChange={(value) => {
                          handleQuantiteItem(value, record.key) ;
                          validateQuantite(record.key, value);
                        }}
                        //disabled={show}
                      />
                    </Form.Item>
                  )}
                />
                <Table.Column
                  title={t("sales.unite")}
                  dataIndex="unite_vente_id"
                  render={(text, record, index) => (
                    <Form.Item name={[index, "unite_vente_id"]}>
                      <Select
                        allowClear
                        placeholder={t("sales.unite")}
                        options={unites.map((item) => ({
                          value: item.id,
                          label: item.code,
                        }))}
                        disabled={true}
                      />
                    </Form.Item>
                  )}
                />
                <Table.Column
                  title={t("sales.pu_ht")}
                  dataIndex="pu_ht"
                  render={(text, record, index) => (
                    <Form.Item
                      name={[index, "pu_ht"]}
                      rules={[
                        {
                          required: true,
                          message: t("sales_message.required_field"),
                        },
                      ]}
                    >
                      <InputNumber
                        value={record.pu_ht}
                        min={0}
                        precision={3}
                        step={0.001}
                        style={{ width: 120 }}
                        onChange={(value) => {
                          handlePUItem(value, index);
                        }}
                        //  disabled={show}
                      />
                    </Form.Item>
                  )}
                />
                <Table.Column
                  title={t("sales.remise")}
                  dataIndex="remise"
                  render={(text, record, index) => (
                    <Form.Item name={[index, "remise"]}>
                      <InputNumber
                        min={1}
                        value={record.remise}
                        onChange={(value) =>
                          handleRemiseItem(value, record.key)
                        }
                      />
                    </Form.Item>
                  )}
                />

                <Table.Column
                  title={t("sales.total_ht")}
                  dataIndex="totalhtc"
                  render={(text, record, index) => (
                    <Form.Item
                      name={[index, "totalhtc"]}
                      rules={[
                        {
                          required: true,
                          message: t("sales_message.required_field"),
                        },
                      ]}
                    >
                      <InputNumber
                        min={1}
                        precision={3}
                        step={0.001}
                        disabled={true}
                      />
                    </Form.Item>
                  )}
                />
                <Table.Column
                  title={t("sales.tva")}
                  dataIndex="tva_id"
                  render={(text, record, index) => (
                    <Form.Item
                      name={[index, "tva_id"]}
                      rules={[
                        {
                          required: true,
                          message: t("sales_message.required_field"),
                        },
                      ]}
                    >
                      <Select
                        showSearch
                        allowClear
                        placeholder={`TVA`}
                        options={tvas.map((item) => ({
                          value: item.id,
                          label: item.value + " %",
                        }))}
                        onChange={(value) => handleTVAItem(value, record.key)}
                        // disabled={show}
                      />
                    </Form.Item>
                  )}
                />
                <Table.Column
                  title="Actions"
                  render={(_, record, index) => (
                    <Form.Item>
                      <>
                        <Popconfirm
                          title="Voulez-vous vraiment supprimer  ?"
                          onConfirm={() => {
                            /*   remove (index)*/
                            handleRemove(index);
                          }}
                          okText="Oui"
                          cancelText="Non"
                        >
                          <Button
                            danger
                            style={{ marginTop: "5px" }}
                            icon={<DeleteOutlined />}
                            // disabled={show}
                          />
                        </Popconfirm>
                      </>
                    </Form.Item>
                  )}
                />
              </Table>
              {false ? (
                <></>
              ) : (
                <>
                  {" "}
                  <Button
                    onClick={() =>
                      add({
                        article_id: null, // default values for the new row
                        designation: null,
                        quantite: null,
                        pu_ht: null,
                        remise: null,
                        tva_id: null,
                        totalhtc: null,
                      })
                    }
                    type="link"
                    style={{ marginBottom: 16 }}
                  >
                    <PlusOutlined /> {t("sales.add_article")}
                  </Button>
                </>
              )}
            </>
          )}
        </Form.List>
      </Col>
    </Row>
  );
};

export default DeliveryItem;
