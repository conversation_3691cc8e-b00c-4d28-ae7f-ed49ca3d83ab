// Simple test to verify the module system is working
import { 
  MODULE_CONFIG, 
  extractModulesFromRoutes, 
  getPrimaryRouteForModule 
} from './crmModuleUtils.jsx';
import crmRoutes from '../routes/crm-routes';

// Test function to verify module system
export const testModuleSystem = () => {
  console.log('🧪 Testing CRM Module System...');
  
  // Test 1: Check if MODULE_CONFIG is properly loaded
  console.log('📋 Available modules:', Object.keys(MODULE_CONFIG));
  
  // Test 2: Extract modules from routes
  const extractedModules = extractModulesFromRoutes(crmRoutes);
  console.log('🔍 Extracted modules from routes:', extractedModules);
  
  // Test 3: Test primary route detection
  extractedModules.forEach(module => {
    const primaryRoute = getPrimaryRouteForModule(crmRoutes, module);
    console.log(`🎯 Primary route for ${module}:`, primaryRoute);
  });
  
  // Test 4: Check if all routes have valid modules
  const routesWithInvalidModules = crmRoutes.filter(route => 
    route.module && !MODULE_CONFIG[route.module]
  );
  
  if (routesWithInvalidModules.length > 0) {
    console.warn('⚠️ Routes with invalid modules:', routesWithInvalidModules);
  } else {
    console.log('✅ All routes have valid modules');
  }
  
  // Test 5: Check module configuration completeness
  extractedModules.forEach(module => {
    const config = MODULE_CONFIG[module];
    if (!config.title || !config.icon || !config.description) {
      console.warn(`⚠️ Incomplete configuration for module: ${module}`);
    }
  });
  
  console.log('✅ Module system test completed');
  
  return {
    totalModules: Object.keys(MODULE_CONFIG).length,
    extractedModules: extractedModules.length,
    totalRoutes: crmRoutes.length,
    routesWithModules: crmRoutes.filter(route => route.module).length
  };
};

// Run test if in development mode
if (process.env.NODE_ENV === 'development') {
  // Uncomment the line below to run tests in console
  // testModuleSystem();
}
