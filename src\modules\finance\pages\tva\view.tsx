import { useTranslation } from "react-i18next";
import TvaForm from "../../components/TvaForm";
import { Modal } from "antd";

const ShowTvaModal = ({ open, onCancel, tva }) => {
  const { t } = useTranslation();

  return (
    <Modal
      width={800}
      title={t("tvas.view")}
      open={open}
      onOk={onCancel}
      onCancel={onCancel}
    >
      <TvaForm disabled={true} tva={tva} />
    </Modal>
  );
};

export default ShowTvaModal;
