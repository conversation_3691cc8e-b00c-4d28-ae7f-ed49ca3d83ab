export const mockProfiles = [
  {
    id: 1,
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    address: '12 Rue de la République, 75001 Paris',
    phone: '+33 6 12 34 56 78',
    email: '<EMAIL>',
    age: 34,
    city: 'Paris',
    currentPosition: 'Développeur Web',
    company: 'TechSolutions',
    sector: 'TIC',
    experience: 'Senior',
    competences: ['React', 'Node.js', 'TypeScript'],
    country: 'France',
    segment: 'Talent Clé',
    positionHistory: [
      {
        position: 'Développeur Front-end',
        company: 'WebInnovate',
        startDate: '2018-03-01',
        endDate: '2020-12-31',
        description: 'Développement d’interfaces utilisateur avec React et Redux.'
      },
      {
        position: 'Stagiaire Développeur',
        company: 'StartUpTech',
        startDate: '2017-06-01',
        endDate: '2017-12-31',
        description: 'Contribution au développement d’une application web.'
      }
    ]
  },
  {
    id: 2,
    firstName: '<PERSON>',
    lastName: '<PERSON><PERSON><PERSON>',
    address: '25 Avenue de Tunis, 1000 Tunis',
    phone: '+216 22 33 44 55',
    email: '<EMAIL>',
    age: 28,
    city: 'Tunis',
    currentPosition: 'Analyste Financier',
    company: 'FinancePro',
    sector: 'Finance',
    experience: 'Junior',
    competences: ['Comptabilité', 'Audit', 'Excel'],
    country: 'Tunisie',
    segment: 'Général',
    positionHistory: [
      {
        position: 'Assistante Comptable',
        company: 'ComptaPlus',
        startDate: '2022-01-15',
        endDate: '2023-06-30',
        description: 'Gestion des comptes clients et fournisseurs.'
      }
    ]
  },
  {
    id: 3,
    firstName: 'Youssef',
    lastName: 'Trabelsi',
    address: '45 Boulevard Saint-Germain, 75005 Paris',
    phone: '+33 6 98 76 54 32',
    email: '<EMAIL>',
    age: 40,
    city: 'Paris',
    currentPosition: 'Chercheur',
    company: 'Université de Paris',
    sector: 'Recherche',
    experience: 'Senior',
    competences: ['IA', 'Machine Learning', 'Python'],
    country: 'France',
    segment: 'Talent Clé',
    positionHistory: [
      {
        position: 'Post-doctorant',
        company: 'CNRS',
        startDate: '2015-09-01',
        endDate: '2019-08-31',
        description: 'Recherche sur les algorithmes d’apprentissage profond.'
      },
      {
        position: 'Doctorant',
        company: 'Université de Lyon',
        startDate: '2012-09-01',
        endDate: '2015-08-31',
        description: 'Thèse sur l’intelligence artificielle.'
      }
    ]
  },
  {
    id: 4,
    firstName: 'Nour',
    lastName: 'Haddad',
    address: '123 Maple Street, Toronto, ON M5V 2T6',
    phone: '****** 555 1234',
    email: '<EMAIL>',
    age: 36,
    city: 'Toronto',
    currentPosition: 'Consultante',
    company: 'BizExpert',
    sector: 'Industrie',
    experience: 'Senior',
    competences: ['Stratégie', 'Analyse marché', 'Gestion de projet'],
    country: 'Canada',
    segment: 'Talent Clé',
    positionHistory: [
      {
        position: 'Analyste Stratégique',
        company: 'GrowEasy',
        startDate: '2019-04-01',
        endDate: '2022-03-31',
        description: 'Analyse des tendances de marché pour les clients industriels.'
      }
    ]
  },
  {
    id: 5,
    firstName: 'Karim',
    lastName: 'Zouari',
    address: '78 Rue Carthage, 2000 Sousse',
    phone: '+216 55 66 77 88',
    email: '<EMAIL>',
    age: 25,
    city: 'Sousse',
    currentPosition: 'Développeur Mobile',
    company: 'AppCraft',
    sector: 'TIC',
    experience: 'Junior',
    competences: ['Flutter', 'Firebase', 'Dart'],
    country: 'Tunisie',
    segment: 'Général',
    positionHistory: [
      {
        position: 'Stagiaire Mobile',
        company: 'MobileLab',
        startDate: '2023-01-15',
        endDate: '2023-07-15',
        description: 'Développement d’une application mobile avec Flutter.'
      }
    ]
  },
  {
    id: 6,
    firstName: 'Leila',
    lastName: 'Baccar',
    address: '15 Hauptstraße, 10115 Berlin',
    phone: '+49 30 12345678',
    email: '<EMAIL>',
    age: 38,
    city: 'Berlin',
    currentPosition: 'Data Analyst',
    company: 'InsightLab',
    sector: 'Finance',
    experience: 'Senior',
    competences: ['SQL', 'Python', 'Tableau'],
    country: 'Allemagne',
    segment: 'Talent Clé',
    positionHistory: [
      {
        position: 'Analyste de Données',
        company: 'DataCorp',
        startDate: '2018-06-01',
        endDate: '2021-05-31',
        description: 'Analyse de données financières avec SQL et Python.'
      }
    ]
  },
  {
    id: 7,
    firstName: 'Omar',
    lastName: 'Mejri',
    address: '456 Oak Avenue, San Francisco, CA 94103',
    phone: '****** 555 9876',
    email: '<EMAIL>',
    age: 42,
    city: 'San Francisco',
    currentPosition: 'Ingénieur en Mécatronique',
    company: 'AutoTech',
    sector: 'Industrie',
    experience: 'Senior',
    competences: ['Automatisation', 'Robotique', 'PLC'],
    country: 'États-Unis',
    segment: 'Talent Clé',
    positionHistory: [
      {
        position: 'Ingénieur Automatisation',
        company: 'TechMach',
        startDate: '2017-02-01',
        endDate: '2020-01-31',
        description: 'Conception de systèmes automatisés pour la production.'
      }
    ]
  },
  {
    id: 8,
    firstName: 'Ameni',
    lastName: 'Gharbi',
    address: '33 Avenue Habib Bourguiba, 3000 Sfax',
    phone: '+216 44 55 66 77',
    email: '<EMAIL>',
    age: 27,
    city: 'Sfax',
    currentPosition: 'UX Designer',
    company: 'Designify',
    sector: 'TIC',
    experience: 'Junior',
    competences: ['Figma', 'UI/UX', 'Adobe XD'],
    country: 'Tunisie',
    segment: 'Général',
    positionHistory: [
      {
        position: 'Stagiaire UX',
        company: 'CreativeStudio',
        startDate: '2022-09-01',
        endDate: '2023-03-31',
        description: 'Conception d’interfaces utilisateur avec Figma.'
      }
    ]
  },
  {
    id: 9,
    firstName: 'Nader',
    lastName: 'Chahed',
    address: '7 Sheikh Zayed Road, Dubai',
    phone: '+971 50 123 4567',
    email: '<EMAIL>',
    age: 39,
    city: 'Dubai',
    currentPosition: 'Spécialiste Blockchain',
    company: 'ChainSecure',
    sector: 'Finance',
    experience: 'Senior',
    competences: ['Solidity', 'Smart Contracts', 'Ethereum'],
    country: 'Émirats Arabes Unis',
    segment: 'Talent Clé',
    positionHistory: [
      {
        position: 'Développeur Blockchain',
        company: 'CryptoTech',
        startDate: '2019-07-01',
        endDate: '2022-06-30',
        description: 'Développement de smart contracts pour la finance décentralisée.'
      }
    ]
  },
  {
    id: 10,
    firstName: 'Hanen',
    lastName: 'Sassi',
    address: '56 Rue de Sousse, 4000 Monastir',
    phone: '+216 77 88 99 00',
    email: '<EMAIL>',
    age: 29,
    city: 'Monastir',
    currentPosition: 'Chargée de projet R&D',
    company: 'InnovLab',
    sector: 'Recherche',
    experience: 'Junior',
    competences: ['Veille technologique', 'Analyse scientifique', 'Gestion de projet'],
    country: 'Tunisie',
    segment: 'Général',
    positionHistory: [
      {
        position: 'Assistante R&D',
        company: 'TechResearch',
        startDate: '2022-05-01',
        endDate: '2023-04-30',
        description: 'Support à la gestion de projets de recherche.'
      }
    ]
  }
];