// src/pages/activities/datas.js
export const datas = [
  // Pipeline 1: Démarchage LG
  {
    key: "demarchage-lg-pre-pipeline",
    name: "Pre-Pipeline",
    description: "Phase initiale avant contact",
    status: "Open",
    pipeline: "Démarchage LG",
    stages: [
      { id: 1, name: "Entreprise non encore contactée", success: false, probability: 0 },
    ],
  },
  {
    key: "demarchage-lg-outreach",
    name: "Outreach",
    description: "Phase de prise de contact",
    status: "Open",
    pipeline: "Démarchage LG",
    stages: [
      { id: 2, name: "Entreprise contactée", success: false, probability: 0 },
      { id: 3, name: "Appel téléphonique réalisé", success: false, probability: 0 },
      { id: 4, name: "Réunion tenue", success: false, probability: 0 },
    ],
  },
  {
    key: "demarchage-lg-lead",
    name: "Lead",
    description: "Phase de qualification du lead",
    status: "Open",
    pipeline: "Démarchage LG",
    stages: [
      { id: 5, name: "Intérêt précoce manifesté", success: false, probability: 0 },
    ],
  },
  {
    key: "demarchage-lg-prospect",
    name: "Prospect",
    description: "Phase de confirmation d'implantation",
    status: "Open",
    pipeline: "Démarchage LG",
    stages: [
      { id: 6, name: "Confirmation d'implantation", success: false, probability: 0 },
    ],
  },
  {
    key: "demarchage-lg-active-project",
    name: "Active Project",
    description: "Phase de gestion active du projet",
    status: "Open",
    pipeline: "Démarchage LG",
    stages: [
      { id: 7, name: "Projet identifiée", success: false, probability: 0 },
      { id: 8, name: "Idée de projet communiquée", success: false, probability: 0 },
      { id: 9, name: "Visite de prospection", success: false, probability: 0 },
      { id: 10, name: "Projet en négociation", success: false, probability: 0 },
      { id: 11, name: "Projet en attente", success: false, probability: 0 },
    ],
  },
  {
    key: "demarchage-lg-investment-success",
    name: "Investment Success",
    description: "Phase de succès de l'investissement",
    status: "Won",
    pipeline: "Démarchage LG",
    stages: [
      { id: 12, name: "Projet annoncé", success: true, probability: 0 },
      { id: 13, name: "Projet déclaré", success: true, probability: 0 },
    ],
  },
  {
    key: "demarchage-lg-closed-inactive",
    name: "Closed / Inactive",
    description: "Phase de fermeture ou inactivité",
    status: "Lost",
    pipeline: "Démarchage LG",
    stages: [
      { id: 14, name: "Aucun intérêt", success: false, probability: 0 },
      { id: 15, name: "Opportunité perdue", success: false, probability: 0 },
    ],
  },

  // Pipeline 2: Suivi de Projet
  {
    key: "suivi-projet-incentives",
    name: "Incentives",
    description: "Phase des incitations",
    status: "Open",
    pipeline: "Suivi de Projet",
    stages: [
      { id: 1, name: "Signature du paquet d'incitations", success: false, probability: 0 },
    ],
  },
  {
    key: "suivi-projet-business-setup",
    name: "Business Setup Application",
    description: "Phase de mise en place de l'entreprise",
    status: "Open",
    pipeline: "Suivi de Projet",
    stages: [
      { id: 2, name: "Incentive negotiation", success: false, probability: 0 },
      { id: 3, name: "Formulaire de demande d'investissement", success: false, probability: 0 },
      { id: 4, name: "Enregistrement de l'entreprise terminé", success: false, probability: 0 },
      { id: 5, name: "Site de l'accord approuvé", success: false, probability: 0 },
    ],
  },
  {
    key: "suivi-projet-investment-startup",
    name: "Investment Startup",
    description: "Phase de démarrage de l'investissement",
    status: "Open",
    pipeline: "Suivi de Projet",
    stages: [
      { id: 6, name: "Recrutement de la main-d'œuvre entamé", success: false, probability: 0 },
    ],
  },
  {
    key: "suivi-projet-operational",
    name: "Operational",
    description: "Phase opérationnelle",
    status: "Won",
    pipeline: "Suivi de Projet",
    stages: [
      { id: 7, name: "Facilité opérationnelle", success: true, probability: 0 },
    ],
  },
  {
    key: "suivi-projet-cancelled",
    name: "Cancelled",
    description: "Phase annulée",
    status: "Lost",
    pipeline: "Suivi de Projet",
    stages: [
      { id: 8, name: "Projet annulé/ abandonné", success: false, probability: 0 },
    ],
  },

  // Pipeline 3: Aftercare
  {
    key: "aftercare-projet",
    name: "Projet",
    description: "Phase de suivi du projet existant",
    status: "Open",
    pipeline: "Aftercare",
    stages: [
      { id: 1, name: "Projet / Investisseur", success: false, probability: 0 },
    ],
  },
  {
    key: "aftercare-engagement-entreprise",
    name: "Engagement de l'entreprise",
    description: "Phase d'engagement avec l'entreprise",
    status: "Open",
    pipeline: "Aftercare",
    stages: [
      { id: 2, name: "Appel / réunion annuel", success: false, probability: 0 },
      { id: 3, name: "Mise à jour des données", success: false, probability: 0 },
    ],
  },
  {
    key: "aftercare-expansion-projet",
    name: "Expansion Projet",
    description: "Phase d'expansion du projet",
    status: "Open",
    pipeline: "Aftercare",
    stages: [
      { id: 4, name: "Projet d'extension prévu", success: false, probability: 0 },
    ],
  },
  {
    key: "aftercare-resolution-probleme",
    name: "Résolution de Problème",
    description: "Phase de résolution des problèmes",
    status: "Open",
    pipeline: "Aftercare",
    stages: [
      { id: 5, name: "Assistance identifiée", success: false, probability: 0 },
    ],
  },
  {
    key: "aftercare-investor-development",
    name: "Investor Development",
    description: "Phase de développement de l'investisseur",
    status: "Open",
    pipeline: "Aftercare",
    stages: [
      { id: 6, name: "Besoin d'approvisionnement identifié", success: false, probability: 0 },
      { id: 7, name: "Besoin RH / formation identifié", success: false, probability: 0 },
      { id: 8, name: "Besoin terrains et locaux identifié", success: false, probability: 0 },
      { id: 9, name: "Besoin en matière d'export identifié", success: false, probability: 0 },
      { id: 10, name: "Besoin en R&D / innovation identifié", success: false, probability: 0 },
    ],
  },
  {
    key: "aftercare-projet-reussi",
    name: "Projet Réussi",
    description: "Phase de succès du projet",
    status: "Won",
    pipeline: "Aftercare",
    stages: [
      { id: 11, name: "Projet d'extension annoncé", success: false, probability: 0 },
      { id: 12, name: "Projet d'extension réalisé", success: false, probability: 0 },
      { id: 13, name: "Assistance fournie", success: false, probability: 0 },
      { id: 14, name: "Projet d'extension concrétisé", success: false, probability: 0 },
    ],
  },
  {
    key: "aftercare-annule",
    name: "Annulé",
    description: "Phase annulée",
    status: "Lost",
    pipeline: "Aftercare",
    stages: [
      { id: 15, name: "Engagement refusé", success: false, probability: 0 },
      { id: 16, name: "Projet d'extension annulé", success: false, probability: 0 },
      { id: 17, name: "Projet d'extension non abouti", success: false, probability: 0 },
      { id: 18, name: "Assistance infructueuse", success: false, probability: 0 },
    ],
  },

  // Pipeline 4: Séminaires et Evénements
  {
    key: "seminaires-pre-invitation",
    name: "Pre-Invitation",
    description: "Phase avant invitation",
    status: "Open",
    pipeline: "Séminaires et Evénements",
    stages: [
      { id: 1, name: "Invitation en attente", success: false, probability: 0 },
    ],
  },
  {
    key: "seminaires-invitation",
    name: "Invitation",
    description: "Phase d'invitation",
    status: "Open",
    pipeline: "Séminaires et Evénements",
    stages: [
      { id: 2, name: "Invitation envoyée", success: false, probability: 0 },
      { id: 3, name: "Confirmation reçue", success: false, probability: 0 },
      { id: 4, name: "Itinéraire / Détails envoyés", success: false, probability: 0 },
    ],
  },
  {
    key: "seminaires-attended",
    name: "Attended",
    description: "Phase de participation",
    status: "Won",
    pipeline: "Séminaires et Evénements",
    stages: [
      { id: 5, name: "Participation à l'événement - Suivi requis", success: true, probability: 0 },
      { id: 6, name: "Participation à l'événement - aucun suivi requis", success: true, probability: 0 },
    ],
  },
  {
    key: "seminaires-closed",
    name: "Closed",
    description: "Phase fermée",
    status: "Lost",
    pipeline: "Séminaires et Evénements",
    stages: [
      { id: 7, name: "Invitation déclinée", success: false, probability: 0 },
      { id: 8, name: "Absent", success: false, probability: 0 },
      { id: 9, name: "Aucune réponse", success: false, probability: 0 },
    ],
  },
];