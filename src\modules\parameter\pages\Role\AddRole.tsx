import React, { useEffect, useState } from "react";
import {
  Button,
  Drawer,
  Input,
  Form,
  Row,
  Col,
  Select,
  Divider,
  Space,
} from "antd";
import { useDispatch, useSelector } from "react-redux";
import { addRoles } from "@/modules/parameter/features/roles/roleSlice";

const AddRole = ({ setVisible, visible, messageApi, tableRef}) => {
  const dispatch = useDispatch();
  const [buttonLoading, setButtonLoading] = useState<boolean>(false);
  const onClose = () => {
    setVisible(false);
  };
  const handleAddRole = (e) => {
    setButtonLoading(true);
    messageApi.open({
      key: "updatable",
      type: "loading",
      content: "Loading...",
    });
    dispatch(addRoles(e))
      .unwrap()
      .then((originalPromiseResult) => {
        messageApi.open({
          key: "updatable",
          type: "success",
          content: "Success ",
          duration: 2,
        });
        tableRef.current.reload()
        setButtonLoading(false);
        setVisible(false);
      })
      .catch((rejectedValueOrSerializedError) => {
        messageApi.open({
          key: "updatable",
          type: "error",
          content: "Error",
          duration: 2,
        });
        setButtonLoading(false);
        console.log(rejectedValueOrSerializedError);
        return [];
      });
  };

  return (
    <Drawer
      title="Nouveau rôle"
      width={window.innerWidth > 520 ? 480 : "90%"}
      className="CautionForm"
      onClose={onClose}
      open={visible}
      styles={{ body: { paddingBottom: 80 } }}

    >
      <Form layout="vertical" requiredMark={false} onFinish={handleAddRole}>
        <Row gutter={16}>
          <Col xs={24} sm={24} md={24} lg={24} xl={24} xxl={24}>
            <Form.Item
              name="name"
              label="Rôle"
              rules={[
                {
                  required: true,
                  message: "Veuillez entrer le Rôle",
                },
              ]}
            >
              <Input placeholder="Label du rôle" />
            </Form.Item>
          </Col>

          <Form.Item
            style={{
              width: "100%",
              textAlign: "right",
              marginRight: "10px",
            }}
          >
            <Button htmlType="reset" style={{ marginRight: "10px" }}>
              Annuler
            </Button>
            <Button type="primary" htmlType="submit" loading={buttonLoading}>
              Ajouter
            </Button>
          </Form.Item>
        </Row>
      </Form>
    </Drawer>
  )
}

export default AddRole
