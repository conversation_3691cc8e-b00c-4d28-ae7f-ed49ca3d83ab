import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL,
});

api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

export interface IPendingMenu {
  id: number;
  designation: string;
  ordre?: any;
  menu_parant: number;
  parent_menu?: any;
  link?: string;
  created_at?: any;
  updated_at?: any;
  deleted_at?: any;
  notes: string;
  icon: string;
  etat: number;
}
export const getPendingMenu: any = createAsyncThunk(
  "pending_menus",
  async (data:{designation:string,etat:number,pageNumber:number,perPage:number}, thunkAPI) => {
    try {
      let url = `/pending_menus?page=${data['pageNumber']}&perPage=${data.perPage}`;
            if(data.designation){
                url += `&designation=${data.designation}`;
              }
              if(data.etat){
                url += `&etat=${data.etat}`;
              }
      const resp = await api.get(url);
      return resp.data;
    } catch (error: any) {
      return thunkAPI.rejectWithValue("something went wrong");
    }
  }
);
export const AddPendingMenu: any = createAsyncThunk(
  "pending_menus",
  async (data, thunkAPI) => {
    try {
      let url = `/pending_menus`;
      const resp = await api.post(url,data);
      return resp.data;
    } catch (error) {
      return thunkAPI.rejectWithValue("something went wrong");
    }
  }
);
export const updatePendingMenu: any = createAsyncThunk(
  "pending_menus",
  async (data:{id:number, designation:string, ordre:number, menu_parant:number, link:string, icon:string,notes:string,permission_id:number}, thunkAPI) => {
    try {
      let url = `/pending_menus/${data.id}`;
      const resp = await api.put(url,data);
      return resp.data;
    } catch (error) {
      return thunkAPI.rejectWithValue("something went wrong");
    }
  }
);
export const publierMenu: any = createAsyncThunk(
  "menus",
  async (id, thunkAPI) => {
    try {
      let url = `/pending_menus/publier_menu/${id}`;
      const resp = await api.get(url);
      return resp.data;
    } catch (error) {
      return thunkAPI.rejectWithValue("something went wrong");
    }
  }
);
