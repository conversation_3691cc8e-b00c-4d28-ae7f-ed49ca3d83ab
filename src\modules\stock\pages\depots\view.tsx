import { ModalForm, ProTable } from "@ant-design/pro-components";
import { Divider, Form, Table, Typography } from "antd";

import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { DepotForm } from "../../components/DepotForm";
import { emplacements } from "../emplacements/data";
import { articleEmplacements, produits } from "../articles/data";

function ShowDepotForm({  open, setOpen, principale, tableRef, entreprises, responsables ,record}){
    const {t} = useTranslation();
    const [loading, setLoading] = useState(false);
    const [form] = Form.useForm();
    const [emplacementsData, setEmplacementsData] = useState([]);
    useEffect(() => {
      if (open) {
          form.setFieldsValue(record);
          setEmplacementsData(emplacements.filter(item=>item.depot_id===record.id))
      }
    }, [open]);
    const options={
      principale, 
      entreprises, 
      responsables,
      form
    }
    const expandedRowRender = (record) => {

      const getArticlesByEmplacement = (emplacementId) => {
        return articleEmplacements
          .filter(item => item.emplacement_id === emplacementId)
          .map(item => {
            const produit = produits.find(prod => prod.id === item.article_id);
            return produit ? { ...produit, quantity: item.quantity } : null;
          })
          .filter(Boolean);
      };
      const articles = getArticlesByEmplacement(record.id)
      const columns = [
        { title: t("articles.fields.code"), dataIndex: "code", key: "code" },
        { title: t("articles.fields.designation"), dataIndex: "designation", key: "designation" },
        { title: t("articles.fields.quantity"), dataIndex: "quantity", key: "quantiy" },
      ];
      return articles && articles.length > 0 ? (
        <Table columns={columns} dataSource={articles} /*pagination={}*/ rowKey={"id"} />
      ) : (
      <></>
      );
    };
    const columns = [
      {
        title: t("emplacements.fields.code"),
        dataIndex: "code",
        key: "code",
      },
      {
        title: t("emplacements.fields.libelle"),
        dataIndex: "libelle",
        key: "libelle",
      }, 
    ];
  
  

    return(
    <ModalForm
      title={t("depots.view")}
      disabled
      modalProps={{
        style:{
          top:20,
        },
      }}
      form={form}
      open={open}
      //onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          setOpen(false);

        }
      }}
      submitter={{
        resetButtonProps:{disabled:false},
        submitButtonProps: {hidden:true},
        searchConfig: {
          resetText: t("common.actions.back"),
        },
      }}
    >
      <DepotForm {...options}/>
      <Divider>{t("emplacements.list")}</Divider>
      <span >
      <ProTable
        // actionRef={tableRef}
        // loading={isLoading}
        columns={columns}
        dataSource={emplacementsData}
        rowKey="id"
        search={false}
        options={false}
        pagination={{ showSizeChanger: true, defaultPageSize: 5 , disabled:false}}
        expandable={{ expandedRowRender }}
      />
      </span>
    </ModalForm>
    )
}
export default ShowDepotForm;