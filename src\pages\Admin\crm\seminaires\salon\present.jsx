import {
  DeleteOutlined,
  DownloadOutlined,
  MinusCircleOutlined,
  PlusCircleOutlined,
  UploadOutlined,
} from "@ant-design/icons";
import { ModalForm } from "@ant-design/pro-components";
import {
  <PERSON><PERSON>,
  Card,
  Col,
  Collapse,
  DatePicker,
  Divider,
  Form,
  Input,
  message,
  Row,
  Select,
  Space,
  Table,
  Upload,
} from "antd";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

function PresentForm({ open, onCancel }) {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [activeKey, setActiveKey] = useState(["1"]);
  const handleAdd = () => {
    const presents = form.getFieldValue("presents") || [];
    const key = Date.now();
    form.setFieldsValue({
      presents: [
        ...presents,
        {
          key: key,
        },
      ],
    });
  };
  const handleSubmit = async (values) => {
    try {
      const newKey = Date.now();
      const newPlanActivity = {
        key: newKey,
        name: values.name,
        description: values.description,
        activities: activities.map((activity, index) => ({
          nom: values[`nom_${index}`],
          resume: values[`resume_${index}`],
          planifier: values[`planifier_${index}`],
        })),
      };
      setDataSource((prevData) => [...prevData, newPlanActivity]);
      message.success("Plan d'activité créé avec succès!");
      onCancel();
    } catch (error) {
      console.log(error);
      message.error("Erreur lors de la création");
    }
  };
const [file, setFile] = useState(null);
  const [fileUrl, setFileUrl] = useState("");

  const handleUpload = (info) => {
    if (info.file.status === "done") {
      const uploadedFile = info.file.originFileObj;
      const url = URL.createObjectURL(uploadedFile);
      setFile(uploadedFile);
      setFileUrl(url);
      message.success(`${info.file.name} est importé avec succès`);
    }
  };
  const beforeUpload = (file) => {
    const isPDF = file.type === "application/pdf";
    if (!isPDF) {
      message.error("Vous pouvez uniquement importer des fichiers PDF!");
      return Upload.LIST_IGNORE;
    }
    return true;
  };
  const handleDownload = () => {
    if (!fileUrl || !file) return;

    const link = document.createElement("a");
    link.href = fileUrl;
    link.download = file.name || "document.pdf";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };
  return (
    <ModalForm
      title={t("Liste des présents")}
      form={form}
      open={open}
      width="55%"
      modalProps={{
        style: { top: 20 },
      }}
      onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.save"),
          resetText: t("common.actions.cancel"),
        },
      }}
    >
      <Card className="mt-2">
        <Space direction="horizontal" style={{ width: "100%" }}>
          <Upload
            accept=".pdf"
            beforeUpload={beforeUpload}
            customRequest={({ onSuccess }) =>
              setTimeout(() => onSuccess("ok"), 0)
            }
            onChange={handleUpload}
            maxCount={1}
            showUploadList={false}
          >
            <Button icon={<UploadOutlined />}>
              Sélectionner la liste (PDF)
            </Button>
          </Upload>
          {file && (
            <Button
              key="download"
              type="primary"
              icon={<DownloadOutlined />}
              onClick={handleDownload}
              disabled={!file}
            >
              Télécharger la liste des présents (PDF)
            </Button>
          )}
        </Space>
      </Card>
    </ModalForm>
  );
}
export default PresentForm;
