import React, { useState, useRef, useMemo } from 'react';
import { <PERSON><PERSON>, <PERSON>dal, Card, Popconfirm, Space, Tooltip, Select, Tag, Input, Row, Col } from 'antd';
import { ProTable } from '@ant-design/pro-components';
import { mockProfiles } from './data';
import ProfileCreate from './create';
import ProfileEdit from './edit';
import ProfileView from './view';
import { DeleteOutlined, EditOutlined, EyeOutlined, FilterOutlined, PlusOutlined } from '@ant-design/icons';
import dayjs from 'dayjs'; // Import dayjs for date handling
import InteractionCreate from '../interactions_dias/create';
const { Option } = Select;
const { Search } = Input;

const ProfilesList = () => {
  const [profiles, setProfiles] = useState(
    mockProfiles.map(profile => ({
      ...profile,
      positionHistory: profile.positionHistory?.map(pos => ({
        ...pos,
        startDate: pos.startDate ? dayjs(pos.startDate) : null,
        endDate: pos.endDate ? dayjs(pos.endDate) : null,
      })),
      interactions: profile.interactions || [], // Initialize interactions array
    }))
  );
  const [selectedProfile, setSelectedProfile] = useState(null);
  const [modalType, setModalType] = useState(null);
  const [filters, setFilters] = useState({
    sector: null,
    experience: null,
    country: null,
    segment: null,
    search: '',
  });

  const tableRef = useRef();

  // Get unique values for filter dropdowns, sorted for better UX
  const getUniqueValues = (key) => {
    return [...new Set(profiles.map(p => p[key]).filter(Boolean))].sort();
  };

  // Enhanced filtering logic
  const filteredProfiles = useMemo(() => {
    const searchTerm = filters.search.toLowerCase().trim();

    return profiles.filter(profile => {
      const matchesSearch = !searchTerm || [
        profile.firstName,
        profile.lastName,
        profile.currentPosition,
        profile.sector,
        profile.experience,
        profile.country,
        profile.segment,
        ...(profile.positionHistory?.map(pos => pos.company) || []),
        ...(profile.positionHistory?.map(pos => pos.position) || []),
        ...(profile.interactions?.map(int => int.description) || []), // Include interaction descriptions in search
      ].some(field => field && field.toString().toLowerCase().includes(searchTerm));

      const matchesFilters =
        (filters.sector === null || profile.sector === filters.sector) &&
        (filters.experience === null || profile.experience === filters.experience) &&
        (filters.country === null || profile.country === filters.country) &&
        (filters.segment === null || profile.segment === filters.segment);

      return matchesSearch && matchesFilters;
    });
  }, [profiles, filters]);

  // Profile CRUD operations
  const handleCreate = (newProfile) => {
    const formattedProfile = {
      ...newProfile,
      id: profiles.length + 1,
      positionHistory: newProfile.positionHistory?.map(pos => ({
        ...pos,
        startDate: pos.startDate ? dayjs(pos.startDate) : null,
        endDate: pos.endDate ? dayjs(pos.endDate) : null,
      })),
      interactions: [], // Initialize empty interactions for new profiles
    };
    setProfiles([...profiles, formattedProfile]);
    setModalType(null);
  };

  const handleUpdate = (updatedProfile) => {
    const formattedProfile = {
      ...updatedProfile,
      positionHistory: updatedProfile.positionHistory?.map(pos => ({
        ...pos,
        startDate: pos.startDate ? dayjs(pos.startDate) : null,
        endDate: pos.endDate ? dayjs(pos.endDate) : null,
      })),
      interactions: updatedProfile.interactions || [], // Preserve interactions
    };
    setProfiles(profiles.map(p => (p.id === formattedProfile.id ? formattedProfile : p)));
    setModalType(null);
  };

  const handleDelete = (id) => {
    setProfiles(profiles.filter(p => p.id !== id));
  };

  // Handle interaction creation
  const handleCreateInteraction = (profileId, newInteraction) => {
    const formattedInteraction = {
      ...newInteraction,
      id: Date.now(), // Unique ID for interaction
      date: newInteraction.date ? dayjs(newInteraction.date) : null, // Format date with dayjs
    };
    setProfiles(profiles.map(profile =>
      profile.id === profileId
        ? { ...profile, interactions: [...(profile.interactions || []), formattedInteraction] }
        : profile
    ));
    setModalType(null);
  };

  const handleResetFilters = () => {
    setFilters({ sector: null, experience: null, country: null, segment: null, search: '' });
    tableRef.current?.reset();
  };

  // Render filter dropdown component
  const renderFilterDropdown = (filterKey, placeholder) => (
    <Select
      value={filters[filterKey]}
      onChange={value => setFilters({ ...filters, [filterKey]: value })}
      style={{ width: 180 }}
      allowClear
      placeholder={placeholder}
      dropdownMatchSelectWidth={false}
      showSearch
      optionFilterProp="children"
    >
      {getUniqueValues(filterKey).map(value => (
        <Option key={value} value={value}>{value}</Option>
      ))}
    </Select>
  );

  // Render filter bar with search and reset
  const renderFilterBar = () => (
    <div style={{ marginBottom: 16 }}>
      <Row gutter={[16, 16]} align="middle">
        <Col>
          <Search
            placeholder="Rechercher par nom, poste, entreprise, interaction, etc."
            value={filters.search}
            onChange={(e) => setFilters({ ...filters, search: e.target.value })}
            onSearch={(value) => setFilters({ ...filters, search: value })}
            style={{ width: 300 }}
          />
        </Col>
        <Col>{renderFilterDropdown('sector', 'Tous les secteurs')}</Col>
        <Col>{renderFilterDropdown('experience', 'Tous les niveaux')}</Col>
        <Col>{renderFilterDropdown('country', 'Tous les pays')}</Col>
        <Col>{renderFilterDropdown('segment', 'Tous les segments')}</Col>
        <Col>
          <Button icon={<FilterOutlined />} onClick={handleResetFilters}>
            Réinitialiser
          </Button>
        </Col>
      </Row>
    </div>
  );

  // Table columns configuration
  const columns = [
    {
      title: 'Nom complet',
      key: 'fullName',
      render: (_, record) => `${record.firstName || ''} ${record.lastName || ''}`,
      hideInSearch: true,
    },
    {
      title: 'Poste',
      dataIndex: 'currentPosition',
      key: 'currentPosition',
      hideInSearch: true,
      render: (text) => <Tooltip title={text || ''}><span>{text || '-'}</span></Tooltip>,
    },
    {
      title: 'Secteur',
      dataIndex: 'sector',
      key: 'sector',
      render: (text) => (text ? <Tag color="cyan">{text}</Tag> : '-'),
    },
    {
      title: 'Expérience',
      dataIndex: 'experience',
      key: 'experience',
      render: (text) => {
        const color = text === 'Senior' ? 'green' : text === 'Junior' ? 'orange' : 'blue';
        return text ? <Tag color={color}>{text}</Tag> : '-';
      },
    },
    {
      title: 'Pays',
      dataIndex: 'country',
      key: 'country',
      render: (text) => text || '-',
    },
    {
      title: 'Segment',
      dataIndex: 'segment',
      key: 'segment',
      render: (text) => (text ? <Tag color="purple">{text}</Tag> : '-'),
    },
    {
      title: 'Actions',
      key: 'action',
      hideInSearch: true,
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title="Voir">
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => {
                setSelectedProfile(record);
                setModalType('view');
              }}
            />
          </Tooltip>
          <Tooltip title="Modifier">
            <Button
              type="link"
              style={{ color: '#f5b041' }}
              icon={<EditOutlined />}
              onClick={() => {
                setSelectedProfile(record);
                setModalType('edit');
              }}
            />
          </Tooltip>
          <Tooltip title="Ajouter une interaction">
            <Button
              type="link"
              style={{ color: '#1890ff' }}
              icon={<PlusOutlined />}
              onClick={() => {
                setSelectedProfile(record);
                setModalType('createInteraction');
              }}
            />
          </Tooltip>
          <Tooltip title="Supprimer">
            <Popconfirm
              title="Confirmer la suppression ?"
              onConfirm={() => handleDelete(record.id)}
              okText="Oui"
              cancelText="Non"
            >
              <Button type="link" style={{ color: '#ec7063' }} icon={<DeleteOutlined />} />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <Card
      title="Gestion des Profils de la Diaspora"
      extra={
        <Button type="primary" onClick={() => setModalType('create')}>
          Ajouter un profil
        </Button>
      }
    >
      {renderFilterBar()}
      <ProTable
        actionRef={tableRef}
        columns={columns}
        dataSource={filteredProfiles}
        rowKey="id"
        search={false}
        options={{
          reload: () => setProfiles(
            mockProfiles.map(profile => ({
              ...profile,
              positionHistory: profile.positionHistory?.map(pos => ({
                ...pos,
                startDate: pos.startDate ? dayjs(pos.startDate) : null,
                endDate: pos.endDate ? dayjs(pos.endDate) : null,
              })),
              interactions: profile.interactions || [],
            }))
          ),
          density: true,
          setting: true,
        }}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: 10,
          pageSizeOptions: ['10', '20', '50'],
          showTotal: (total) => `Total ${total} profils`,
        }}
        scroll={{ x: 'max-content' }}
      />

      {/* Modals for CRUD operations */}
      <Modal
        title="Ajouter un nouveau profil"
        open={modalType === 'create'}
        onCancel={() => setModalType(null)}
        footer={null}
        width={800}
        destroyOnClose
      >
        <ProfileCreate onCreate={handleCreate} />
      </Modal>

      <Modal
        title="Modifier le profil"
        open={modalType === 'edit'}
        onCancel={() => setModalType(null)}
        footer={null}
        width={800}
        destroyOnClose
      >
        <ProfileEdit initialValues={selectedProfile} onUpdate={handleUpdate} />
      </Modal>

      <Modal
        title="Détails du profil"
        open={modalType === 'view'}
        onCancel={() => setModalType(null)}
        footer={null}
        width={800}
        destroyOnClose
      >
        <ProfileView profile={selectedProfile} />
      </Modal>

      <Modal
        title="Ajouter une nouvelle interaction"
        open={modalType === 'createInteraction'}
        onCancel={() => setModalType(null)}
        footer={null}
        width={600}
        destroyOnClose
      >
        <InteractionCreate
          onCreate={(newInteraction) => handleCreateInteraction(selectedProfile?.id, newInteraction)}
          profileId={selectedProfile?.id}
        />
      </Modal>
    </Card>
  );
};

export default ProfilesList;