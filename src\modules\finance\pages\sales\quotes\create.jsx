import { ModalForm } from "@ant-design/pro-components";
import { Card, Col, DatePicker, Form, Input, InputNumber,Row, Select } from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { entreprises , departements , etats, clients , articles } from "./data";
import DevisItemTable from "@src/modules/finance/components/quotes/DevisItemTable";


function CreateQuoteForm({ open, onCancel, onSuccess }){
    const {t} = useTranslation();
    const [loading, setLoading] = useState(false);
    const [value, setValue] = useState(2);

    const [entrepriseId , setEntrepriseId] =  useState(null);
    const [departementId , setDepartementId] =  useState(null);
    const [tierId , setTierId] =  useState(null);
    const dateFormat = "YYYY-MM-DD";
  
    const [form] = Form.useForm();
    const addDevis = (values) => {
    }


    const showTierData = (e) => {
      const tier = clients.find((t) => t.id == e);
      form.setFieldsValue({
        email: tier?.email,
        adresse: tier?.adresse,
        phone: tier?.phone,
        matricule_fiscale: tier?.matricule_fiscale,
      });
    };

    const resetTierData = () => {
      form.setFieldsValue({
        email: null ,
        adresse: null,
        phone: null,
        matricule_fiscale:  null,
      });
    } 

  
    return(
    <ModalForm
      title={t("devis.add")}
      width ='90%'
      form={form}
      open={open}
      modalProps={{
        style: { top: 50 }, 
      }}
    //   onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.save"),
          resetText: t("common.actions.cancel"),
        },
      }}
     
    >
    
      <Row className="mt-0" gutter={[12, 24]}>
            <Col span={12}>
              <Card title= {t('devis.general_info')} >
                <Form.Item
                  name={"num_devis"}
                  label={t('devis_cols.num_devis')}
                  style={{
                    display: "inline-block",
                    width: "45%",
                    margin: "0 8px 8px",
                  }}
                >
                  <Input disabled={true} />
                </Form.Item>
                <Form.Item
                  name={"duree_vie"}
                  label={t('devis_cols.valid_day')}
                  style={{
                    display: "inline-block",
                    width: "45%",
                    margin: "0 8px 8px",
                  }}
                >
                  <InputNumber
                    min={1}
                    step={1}
                    precision={0}
                    style={{ width: "100%" }}
                  />
                </Form.Item>
                <Form.Item
                  name={"entreprise_id"}
                  label={
                    <span>
                      {t('devis_cols.entreprise')} <span style={{ color: "red" }}>*</span>
                    </span>
                  }
                  style={{
                    display: "inline-block",
                    width: "45%",
                    margin: "0 8px 8px",
                  }}
                  rules={[
                    {
                      required: true,
                      message:  t('devis_message.required_field')   , 
                    },
                  ]}
                >
                  <Select  
                    allowClear
                    placeholder= {t('devis_cols.entreprise')}
                    options={entreprises.map((item) => ({ value: item.id, label: item.nom }))}  
                    value={entrepriseId}   
                    onSelect={(e) => {
                     setEntrepriseId(e);
                    }}
                    onClear={() => {
                    setEntrepriseId(null);
                    setDepartementId(null);
                    setTierId(null);
                    }}         
                  />
                </Form.Item>
                <Form.Item
                  name="departement_id"
                  label={
                    <span>
                       {t('devis_cols.departement')}  <span style={{ color: "red" }}>*</span>
                    </span>
                  }
                  style={{
                    display: "inline-block",
                    width: "45%",
                    margin: "0 8px 8px",
                  }}
                  rules={[
                    {
                      required: true,
                      message:  t('devis_message.required_field')   , 
                    },
                  ]}
                >
                  <Select
                    allowClear
                    placeholder= {t('devis_cols.departement')}  
                    value={departementId}   
                    onSelect={(e) => {setDepartementId(e);}}  
                    onClear={() => { setDepartementId(null); }}  
                    options={departements.filter( (dept) => dept.entreprise_id === entrepriseId).map((item) => ({ value: item.id, label: item.nom }))}                 
                  />
                </Form.Item>
                <Form.Item
                  name={"date_devis"}
                  label={
                    <span>
                   {t('devis_cols.date_devis')} <span style={{ color: "red" }}>*</span>
                    </span>
                  }
                  style={{
                    display: "inline-block",
                    width: "45%",
                    margin: "0 8px 8px",
                  }}
                  rules={[
                    { required: true,  message:  t('devis_message.required_field') } 
                  ]}
                >
                  <DatePicker
                    format={dateFormat}
                    style={{ width: "100%" }}
                    placeholder ={t('devis_cols.date_devis')}
                  />
                </Form.Item>
                <Form.Item
                  name={"etat_id"}
                  label={
                    <span>
                     {t('devis_cols.etat')} <span style={{ color: "red" }}>*</span>
                    </span>
                  }
                  style={{
                    display: "inline-block",
                    width: "45%",
                    margin: "0 8px 8px",
                  }}
                  rules={[
                    { required: true, message: t('devis_message.required_field') },
                  ]}
                >
                  <Select
                    allowClear
                    placeholder={t('devis_cols.etat')}
                   
                    options={etats.map((item) => ({ value: item.id, label: item.nom }))}  
                  />
                </Form.Item>
              </Card>
            </Col>
            <Col span={12}>
              <Card title= {t('devis.client_info')}  >
                <Form.Item
                  name={"tier_id"}
                  label={
                    <span>
                     {t('devis_cols.client')} <span style={{ color: "red" }}>*</span>
                    </span>
                  }
                  style={{
                    display: "inline-block",
                    width: "45%",
                    margin: "0 8px 8px",
                  }}
                  rules={[
                    { required: true, message: t('devis_message.required_field') },
                  ]}
                >
                  <Select
                    allowClear
                    placeholder={t('devis_cols.client')}
                    value={tierId}   
                    onSelect={(e) => {setTierId(e);showTierData(e);}}  
                    onClear={() => { setTierId(null);resetTierData() }} 
                    options={clients.filter(tier => tier.entreprise_id === entrepriseId &&  tier.departement_id === departementId).map((item) => ({ value: item.id, label: item.nom }))}  
                  />
                </Form.Item>
                <Form.Item
                  name={"matricule_fiscale"}
                  label={t('devis.matricule')}
                  style={{
                    display: "inline-block",
                    width: "45%",
                    margin: "0 8px 8px",
                  }}
                >
                  <Input autoFocus placeholder={t('devis.matricule')} />
                 </Form.Item>

                <Form.Item
                  name="email"
                  label={t('devis.email')}
                  style={{
                    display: "inline-block",
                    width: "45%",
                    margin: "0 8px 8px",
                  }}
                >
                  <Input autoFocus placeholder={t('devis.email')} />
                </Form.Item>
                <Form.Item
                  name="phone"
                  label={t('devis.tel')}
                  style={{
                    display: "inline-block",
                    width: "45%",
                    margin: "0 8px 8px",
                  }}
                >
                  <Input autoFocus  placeholder={t('devis.tel')}/>
                </Form.Item>
                <Form.Item
                  name="adresse"
                  label={t('devis.address')}
                  style={{
                    display: "inline-block",
                    width: "94%",
                    margin: "0 8px 8px",
                  }}
                >
                  <Input autoFocus placeholder={t('devis.address')} />
                </Form.Item>
              </Card>
            </Col>

           
            <Col span={24}></Col>
            
          </Row>
          <DevisItemTable form={form} articles ={articles}/>
    </ModalForm>
    )
}
export default CreateQuoteForm;