import dayjs from "dayjs";

const salons = [
  {
    id: 1,
    intitule: "100173TF01",
    pays_id: 1,
    region: "nom de la région ",
    responsable : "resposable Fipa",
    organisateur_id : 1 ,
    propose_par : "Proposé par",
    objectifs : " Ad distinctio cupiditate et accusantium perferendis quo nihil voluptas ea delectus corrupti ut praesentium quisquam sit earum obcaecati sit doloribus culpa. ",
    theme: "Test",
    lieu: "lieu du séminaire ",
    action_conjointe : 1 ,
    date_debut: dayjs("2025-01-10"),
    date_fin: dayjs("2025-01-10"),
    initialSteps: [
      {
        id: 1,
        title: "Proposée",
        done: false,
        motif: false,
        reason: "",
        status: "finish",
      },
      {
        id: 2,
        title: "Programmée",
        done: false,
        motif: false,
        reason: "",
        status: "finish",
      },
      {
        id: 3,
        title: "<PERSON><PERSON><PERSON><PERSON>",
        done: false,
        motif: false,
        reason: "",
        status: "wait",
      },
      {
        id: 4,
        title: "Reportée",
        done: false,
        motif: true,
        reason: "",
        status: "finish",
      },
      {
        id: 5,
        title: "Ann<PERSON><PERSON>",
        done: false,
        motif: true,
        reason: "",
        status: "finish",
      },
      {
        id: 6,
        title: "Non programmée",
        done: false,
        motif: false,
        reason: "",
        status: "wait",
      },
      {
        id: 7,
        title: "Validée",
        done: false,
        motif: false,
        reason: "",
        status: "wait",
      },
    ],
  },
];
const pays = [
  { id: 1, nom: "France" },
  { id: 2, nom: "Italie" },
  { id: 3, nom: "Allemagne" },
  { id: 4, nom: "Espagne" },
  { id: 5, nom: "États-Unis" },
  { id: 6, nom: "Canada" },
  { id: 7, nom: "Algérie" },
  { id: 8, nom: "Maroc" },
  { id: 9, nom: "Libye" },
  { id: 10, nom: "Turquie" },
  { id: 11, nom: "Arabie Saoudite" },
  { id: 12, nom: "Émirats arabes unis" },
  { id: 13, nom: "Qatar" },
  { id: 14, nom: "Égypte" },
  { id: 15, nom: "Chine" },
  { id: 16, nom: "Japon" },
  { id: 17, nom: "Corée du Sud" },
  { id: 18, nom: "Belgique" },
  { id: 19, nom: "Suisse" },
  { id: 20, nom: "Russie" },
];

const organisateurs =[
   { id: 1, nom: "IFEMA" },
] ;
export { salons, pays ,organisateurs };
