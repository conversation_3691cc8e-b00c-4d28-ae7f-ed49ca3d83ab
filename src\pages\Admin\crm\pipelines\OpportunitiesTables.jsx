import React, { useState, useEffect, useRef } from "react";
import { Row, Col, Card, Select } from "antd";
import { Typography } from "antd";
import ProTable from "@ant-design/pro-table";
import { datas } from "./datas"; // Ensure the path to your datas.json is correct

const { Option } = Select;
const { Title } = Typography;

const OpportunitiesTable = () => {
  const [selectedOpportunity, setSelectedOpportunity] = useState("all");
  const [selectedResponsible, setSelectedResponsible] = useState("all");
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);

  const tableRefactivities = useRef();
  const tableRefChild = useRef();

  useEffect(() => {
    setLoading(true);
    setTimeout(() => {
      setData(datas); // Simulating data loading
      setLoading(false);
    }, 500);
  }, []);

  // Filter data by selected opportunity and responsible
  const filteredData = data.filter((item) => {
    const filterByOpportunity = selectedOpportunity === "all" || item.opportunity_name === selectedOpportunity;
    const filterByResponsible = selectedResponsible === "all" || item.responsible === selectedResponsible;
    return filterByOpportunity && filterByResponsible;
  });

  const columns = [
    {
      title: "Nom de l'opportunité",
      dataIndex: "opportunity_name",
    },
    {
      title: "Responsable",
      dataIndex: "responsible",
    },
  ];

  const nestedColumns = [
    {
      title: "Assigné à",
      dataIndex: "assigned_to",
    },
    {
      title: "Date Planifiée",
      dataIndex: "date_planifier",
    },
    {
      title: "Type",
      dataIndex: "type",
      render: (type) => <span>{type || "N/A"}</span>,
    },
  ];

  return (
    <div>
      <Row className="mt-5" gutter={[12, 24]}>
        <Col xs={24}>
          <Card title={<Title level={3}>Liste des Opportunités</Title>} bordered={false}>
            {/* Select Filters (Centered) */}
            <div style={{ display: "flex", justifyContent: "center", gap: "20px", marginBottom: "20px" }}>
              <Select
                style={{ width: 250 }}
                value={selectedOpportunity}
                onChange={setSelectedOpportunity}
              >
                <Option value="all">Toutes les Opportunités</Option>
                {data.map((item) => (
                  <Option key={item.id} value={item.opportunity_name}>
                    {item.opportunity_name}
                  </Option>
                ))}
              </Select>

              <Select
                style={{ width: 250 }}
                value={selectedResponsible}
                onChange={setSelectedResponsible}
              >
                <Option value="all">Tous les Responsables</Option>
                {data.map((item) => (
                  <Option key={item.id} value={item.responsible}>
                    {item.responsible}
                  </Option>
                ))}
              </Select>
            </div>

            {/* ProTable */}
            <ProTable
              search={false}
              columns={columns}
              actionRef={tableRefactivities}
              cardBordered
              loading={loading}
              rowKey="id"
              dataSource={filteredData}
              pagination={{
                pageSize: 10,
                showSizeChanger: false,
              }}
              expandable={{
                expandedRowRender: (record) => {
                  // Extract details from plans
                  let detailsList = [];

                  record?.plans?.forEach((plan) => {
                    plan?.plan?.details?.forEach((detail) => {
                      detailsList.push({
                        ...detail,
                        type: record.types.join(", "), // Convert array to string
                      });
                    });
                  });

                  return (
                    <ProTable
                      actionRef={tableRefChild}
                      search={false}
                      cardBordered
                      loading={loading}
                      headerTitle="Détails de l'opportunité"
                      columns={nestedColumns}
                      dataSource={detailsList.map((item) => ({
                        ...item,
                        key: item.id,
                      }))}
                      pagination={{ pageSize: 5 }}
                    />
                  );
                },
              }}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default OpportunitiesTable;
