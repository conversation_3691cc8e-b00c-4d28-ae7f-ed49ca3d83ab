import { Col, Input, Row, Tabs,Form, Card, Typography, Divider, Checkbox, Select } from "antd";
import InforGenerale from "../pages/articles/InfoGenerale";
import TarifArticle from "../pages/articles/TarifArticle";
import { useTranslation } from "react-i18next";
import { ProColumns, ProTable } from "@ant-design/pro-components";
import { useEffect, useState } from "react";
import { articleMouvements } from "../pages/articles/data";

export const ArticleForm = ({unites, familles, dataRecord, sousFamilles, fournisseurs, tvas, isBien, 
  setIsBien, handleTabChange, activeKey, resetFields, setTarifValues, articles, disabled=false}) => {
    const [mouvement, setMouvement] = useState(null);

    const filterOption = (input, option) =>
      (option?.label ?? "").toLowerCase().includes(input.toLowerCase());
    const filterSort = (optionA, optionB) =>
      (optionA?.label ?? "")
      .toLowerCase()
      .localeCompare((optionB?.label ?? "").toLowerCase());
    
    useEffect(()=>{
      if(dataRecord){
        setMouvement(articleMouvements.filter( item=> item.article_id==dataRecord.id))
      }
    },[dataRecord])

    const {t} = useTranslation();
    const columns : ProColumns[]=[
      {
        title: t("mouvements.fields.type_mouvement"),
        dataIndex: "type_mouvement",
        key: "type_mouvement",
      },
      {
        title: t("mouvements.fields.quantity"),
        dataIndex: "quantity",
        key: "quantity",
        search:false,
      }, 
      {
        title: t("mouvements.fields.date"),
        dataIndex: "date",
        key: "date",
        valueType:"date",
      }, 
    ];
  
    return (
      <Tabs
        onChange={handleTabChange}
        activeKey={activeKey}
        tabBarStyle={{marginBottom:8}}
        defaultActiveKey="1"
        items={[
          {
            label: "Informations générales",
            key: "1",
            children:
              <InforGenerale 
                familles={familles} sous_familles={sousFamilles}
                unites={unites} fournisseurs={fournisseurs} 
                setIsBien={setIsBien} disabled={disabled}
              />
          },
          {
            label : "Stock",
            key : "2",
            disabled : !isBien,
            children : <>
              <Row gutter={6} >
                <Col md={12} xs={24} >
                  <Form.Item name="min_threshold" label={t('articles.fields.seuil_min')} >
                    <Input placeholder="Veuillez entrer le seuil min" type="number" min={0} disabled={disabled}/>
                  </Form.Item>
                </Col>
                <Col md={12} xs={24} >
                  <Form.Item name="max_threshold"  label={t('articles.fields.seuil_max')}>     
                    <Input placeholder="Veuillez entrer le seuil max" type="number" min={0} disabled={disabled}/>
                  </Form.Item>
                </Col>
              </Row>
              <Divider style={{marginBlock:8}}>{t("mouvements.title")}</Divider>

                <Card styles={{body:{padding:8}}} >
                  <ProTable
                    // actionRef={tableRef}
                    // loading={isLoading}
                    columns={columns}
                    dataSource={mouvement}
                    rowKey="id"
                    search={{
                      layout:"inline",
                      labelWidth: "auto", // Ajuste automatiquement la largeur des labels
                      span: 8, // Ajuste la largeur des champs (essaie différentes valeurs)
                    }}
                    options={false}
                    pagination={{ showSizeChanger: true, defaultPageSize: 5 }}
                  />
                </Card>
                <Col md={12} xs={24} >
                  <Form.Item name="replacement_article_id" label={t('articles.fields.replacement_article')} >
                    <Select 
                      disabled={disabled}
                      filterOption={filterOption}
                      filterSort={filterSort}
                      allowClear
                      showSearch
                      options={articles.filter((item) => item?.id !== dataRecord?.id)}
                    />
                  </Form.Item>
                </Col>
                <Form.Item
                  name="allow_negative_stock"
                  valuePropName="checked" // Permet de gérer l'état de la checkbox
                >
                  <Checkbox   disabled={disabled}>{t('articles.fields.allow_negative_stock')}</Checkbox>
                </Form.Item>
            </>
          },
          {
            label: "Tarif",
            key: "3",
            children : 
              <TarifArticle
                tvas={tvas} resetFields={resetFields} data={dataRecord}
                setTarifValues={setTarifValues}  disabled={disabled}
              />
          }
        ]}
      />
    );
}