// src/redux/reducers/dataReducer.js

import { FETCH_CATEGORIES_FAILURE, FETCH_CATEGORIES_START, FETCH_CATEGORIES_SUCCESS } from "../constants/categoryConstants";

  
  const initialState = {
    categorys: [],
    error: null,
  };
  
  const categoryReducer = (state = initialState, action) => {
    switch (action.type) {
      case FETCH_CATEGORIES_START:
        return { ...state, isLoading: true, error: null };
      case FETCH_CATEGORIES_SUCCESS:
        return { ...state, isLoading: false, categorys: action.payload };
      case FETCH_CATEGORIES_FAILURE:
        return { ...state, isLoading: false, error: action.payload };
      default:
        return state;
    }
  };
  
  export default categoryReducer;
  