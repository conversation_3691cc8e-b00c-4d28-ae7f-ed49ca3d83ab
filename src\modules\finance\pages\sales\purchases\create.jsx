import { ModalForm } from "@ant-design/pro-components";
import {  Col, Form, Row } from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import PurchaseInfo from "@src/modules/finance/components/purchases/PurchaseInfo";
import PurchaseItem from "@src/modules/finance/components/purchases/PurchaseItem";

function CreateCmdForm({ open, onCancel, onSuccess }){
    const {t} = useTranslation();
    const [loading, setLoading] = useState(false); 
    const [form] = Form.useForm();
    const handleSubmit = (values) => {
      //console.log(values);
    }
  
    return(
    <ModalForm
      title={t("cmd.add")}
      width ='90%'
      form={form}
      open={open}
      modalProps={{
        style: { top: 30 }, 
      }}
      onFinish={handleSubmit}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.save"),
          resetText: t("common.actions.cancel"),
        },
      }}
     
    >
     
    <PurchaseInfo form={form} open={open}/>
    <PurchaseItem  form={form} open={open}/>
   
    </ModalForm>
    )
}
export default CreateCmdForm;