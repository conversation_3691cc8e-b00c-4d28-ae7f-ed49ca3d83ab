// src/pages/activities/viewStatus.jsx
import { Card, Spin } from "antd";

const ViewActivityStatus = ({ record }) => {
  if (!record) {
    return <Spin size="large" />;
  }

  return (
    <>
      <h2>Détails de la phase</h2>
      <Card title={record.name} bordered={false}>
        <p><strong>Nom:</strong> {record.name}</p>
        <p><strong>Description:</strong> {record.description}</p>
        <p><strong>Étapes:</strong></p>
        <ul>
          {record.stages.map((stage, index) => (
            <li key={index}>
              {stage.name}
            </li>
          ))}
        </ul>
      </Card>
    </>
  );
};

export default ViewActivityStatus;