import {
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  PlusOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import { ProColumns, ProTable } from "@ant-design/pro-components";
import useDebounce from "@/hooks/useDebounce";
import {
  Button,
  Card,
  Input,
  Popconfirm,
  Space,
  Tooltip,
  Typography,
} from "antd";
import { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";

import { IFamille } from "../interfaces";
import CreateFamilleForm from "./create";
import EditFamilleForm from "./edit";
import ShowFamilleForm from "./view";
import { famillesData } from "./data";
import { tvas as tvasData } from "@/modules/finance/pages/tva/data";

function Famille(){
  const tableRef = useRef();
  const {t} = useTranslation();
  const [tableParams, setTableParams] = useState({}); // Paramètres de recherche et de filtrage
  const [isLoading, setIsLoading] = useState(false); // Indique si les données sont en cours de chargement
  const [data, setData] = useState<IFamille[]>(famillesData); // Données affichées dans la table
  const [total, setTotal] = useState(data.length); // Nombre total de lignes affichées
  const [pageNumber, setPageNumber] = useState(1); // Numéro de page actuelle
  const [dataSource, setDataSource] = useState([]);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [viewModalVisible, setViewModalVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState(null);
  const [viewingRecord, setViewingRecord] = useState(null);
  const [tvas, setTvas] = useState([]);
  
  // Déclenchement de la recherche avec un délai (700ms)
  const handleSearch = (e) => {
  const searchValue = e.target.value; // Récupération de la valeur de recherche
  setTableParams({...tableParams, search: searchValue}); // Mise à jour des paramètres
  };

  const debouncedOnChange = useDebounce(handleSearch, 700);

  useEffect(() => {
    setTvas(tvasData.map((item) => {return {...item,value : item.key, label : item.designation}}))
  }, []);

  const columns : ProColumns<IFamille>[] =[
    {
      title: t('familles.fields.code'),
      dataIndex: "code",
      key: "code",
      ellipsis: true,
      order: 1,
    },
    {
      title: t('familles.fields.libelle'),
      dataIndex: "libelle",
      key: "libelle",
      ellipsis: true,
      order: 1,
    },
    {
      title: t('familles.fields.tva'),
      dataIndex: "tva_id",
      key: "tva_id",
      ellipsis: true,
      valueType:"select",
      valueEnum:()=>Object.fromEntries(tvasData.map((value: { key: number; designation: string }) => [value.key, { text: value.designation }])),
    },
    {
      title: t('familles.fields.remise'),
      dataIndex: "remise",
      key: "remise",
      ellipsis: true,
    },
    {
      title: t('common.actions.title'),
      key: 'action',
      align: "center",
      sorter:false,
      search: false,
      width:"3rem",
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title={t('common.actions.view')}>
            <Button
              type="link"
              icon={<EyeOutlined/>}
              onClick={() => {
                setViewingRecord(record);
                setViewModalVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title={t('common.actions.edit')}>
            <Button
              type="link"
              style={{color: "#f5b041"}}
              icon={<EditOutlined/>}
              onClick={() => {
              
                setEditingRecord(record);
                setEditModalVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title={t('common.actions.delete')}>
            <Popconfirm
              title={t('common.messages.confirm_delete')}
              onConfirm={() => {
                const newDataSource = dataSource.filter(item => item.id !== record.id);
                setDataSource(newDataSource);
              }}
            >
              <Button type="link" style={{color: "#ec7063"}} icon={<DeleteOutlined/>}/>
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

 
  const createObj={
    open: createModalVisible,
    setOpen : setCreateModalVisible,
    tableRef,
    tvas,
  }
  const editObj={
    open: editModalVisible,
    setOpen : setEditModalVisible,
    tableRef,
    record: editingRecord,
    tvas,
  }
  const showObj={
    open: viewModalVisible,
    setOpen : setViewModalVisible,
    record : viewingRecord,
    tvas,
  }
  return(  
      <Card
          bordered
          title={
              <Typography.Title /*style={{textWrap: "wrap"}} */level={4}>
                {t('familles.list')}
              </Typography.Title>
          }
          extra={
              <Space>
                {/* Champ de recherche */}
                <Input
                  size="large"
                  placeholder={t('common.search')}
                  suffix={<SearchOutlined style={{color: "#bfbfbf"}}/>}
                  onChange={debouncedOnChange}
                />
                <Button
                  key="add"
                  type="primary"
                  icon={<PlusOutlined/>}
                  onClick={() =>{
                    setCreateModalVisible(true)
                  }}
                >
                  {t('familles.add')}
                </Button>
              </Space>
            }
      >

      <ProTable
          actionRef={tableRef} // Référence pour accéder aux actions
          search={{
              searchText: t('common.filter'),
              labelWidth: "auto",
              defaultCollapsed: false,
          }}
          loading={isLoading}
          columns={columns}
          dataSource={data}
          rowKey="id"
          params={tableParams}
          scroll={{x: 'max-content'}}
          options={false}
          request={async (params) => {
              setIsLoading(true); // Démarrage du chargement
              console.log(params);
              setTimeout(() => { // Simule une requête serveur
                  setIsLoading(false);
              }, 500);
              return { // Retourne les données
                  data: data,
                  success: true,
              };
          }}
          pagination={{
              showSizeChanger: true, // Permet de modifier la taille des pages
              defaultPageSize: 3, // Taille de page par défaut
              total: total, // Nombre total de lignes
              onChange: (page) => setPageNumber(page), // Fonction de changement de page
          }}
      />
      {createModalVisible && (
          <CreateFamilleForm {...createObj} />
      )}
      {editModalVisible && (
          <EditFamilleForm {...editObj} />
      )}
      {viewModalVisible && (
          <ShowFamilleForm {...showObj} />
      )}
      </Card>
  )
}
export default Famille;