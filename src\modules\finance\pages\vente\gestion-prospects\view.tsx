import { useTranslation } from "react-i18next";
import { Modal } from "antd";
import TierInfoGeneralForm from "@src/modules/finance/components/TierInfoGeneralForm";

const ShowProspectModal = ({ open, onCancel, prospect }) => {
  const { t } = useTranslation();

  return (
    <Modal
      title={t("vente.prospect.show")}
      open={open}
      onOk={onCancel}
      onCancel={onCancel}
      width={900}
    >
      <div className="px-2">
        <TierInfoGeneralForm disabled={true} tier={prospect} />
      </div>
    </Modal>
  );
};

export default ShowProspectModal;
