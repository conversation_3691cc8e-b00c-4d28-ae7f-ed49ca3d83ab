import { useState, useRef } from "react";
import { ProTable } from "@ant-design/pro-components";
import { Button, Popconfirm, Space, Input, Tooltip, Typography, Card, Modal, Table } from "antd";
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined, SearchOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import useDebounce from "@hooks/useDebounce";
import contacts from "./datas";
import CreateContact from "./create";
import EditC from "./edit";
import ViewContact from "./view";
function Contacts() {
  const { t } = useTranslation();
  const tableRef = useRef();

  const [dataSource, setDataSource] = useState(contacts);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [viewModalVisible, setViewModalVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState(null);
  const [viewingRecord, setViewingRecord] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleCancel = () => {
    setViewModalVisible(false);
  };

  const handleEdit = (record) => {
    setEditingRecord(record);
    setEditModalVisible(true);
  };

  const columns = [
    { title: "Nom", dataIndex: "nom", key: "nom" },
    { title: "Prénom", dataIndex: "prenom", key: "prenom" },
    { title: "Titre / Fonction", dataIndex: "titre", key: "titre" },
    { title: "Entreprise", dataIndex: "entreprise", key: "entreprise" },
    { title: "Email", dataIndex: "email", key: "email" },
    { title: "Téléphone", dataIndex: "telephone", key: "telephone" },
    { title: "Statut", dataIndex: "statutContact", key: "statutContact" },
    {
      title: "Actions",
      key: "action",
      align: "center",
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title="Voir">
            <Button type="link" icon={<EyeOutlined />} onClick={() => {
              setViewingRecord(record);
              setViewModalVisible(true);
            }} />
          </Tooltip>
          <Tooltip title="Modifier">
            <Button type="link" style={{ color: "#f5b041" }} icon={<EditOutlined />} onClick={() => handleEdit(record)} />
          </Tooltip>
          <Tooltip title="Supprimer">
            <Popconfirm title="Confirmer la suppression ?" onConfirm={() => {
              const newDataSource = dataSource.filter(item => item.key !== record.key);
              setDataSource(newDataSource);
            }}>
              <Button type="link" style={{ color: "#ec7063" }} icon={<DeleteOutlined />} />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <Card
      bordered
      title={<Typography.Title level={4}>Liste des Contacts</Typography.Title>}
      extra={
        <Space>
          <Input size="large" placeholder="Rechercher" suffix={<SearchOutlined style={{ color: "#bfbfbf" }} />} />
          <Button type="primary" icon={<PlusOutlined />} onClick={() => setCreateModalVisible(true)}>
            Ajouter
          </Button>
        </Space>
      }
    >
      <ProTable
        actionRef={tableRef}
        loading={isLoading}
        columns={columns}
        dataSource={dataSource}
        rowKey="key"
        search={false}
        options={false}
        pagination={{ showSizeChanger: true, defaultPageSize: 5 }}
      />
        {/* Create Modal */}
        <Modal
        title={t("crm.tasks.add")}
        visible={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        footer={null}
        width={1000}
      >
        <CreateContact
          onCancel={() => setCreateModalVisible(false)}
          setDataSource={setDataSource}
        />
      </Modal>

      {/* Edit Modal */}
      <Modal
        title={t("crm.tasks.edit")}
        visible={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        footer={null}
        width={800}
      >
        <EditC
          record={editingRecord}
          onCancel={() => setEditModalVisible(false)}
          setDataSource={setDataSource}
        />
      </Modal>

      {/* View Modal */}
      <Modal
        title={t("crm.tasks.view")}
        visible={viewModalVisible}
        onCancel={handleCancel}
        footer={null}
        width={800}
      >
        <ViewContact
          record={viewingRecord}
          onCancel={() => setEditModalVisible(false)}
          setDataSource={setDataSource}
        />
      </Modal>
      
    </Card>
  );
}

export default Contacts;
