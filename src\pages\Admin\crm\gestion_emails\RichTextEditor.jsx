import React, { useState } from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';

const  RichTextEditor = ({ onChange, placeholder, initialValue }) => {
  const [value, setValue] = useState(initialValue || '');

  const modules = {
    toolbar: [
      [{ 'header': [1, 2, 3, false] }],
      ['bold', 'italic', 'underline', 'strike'],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      ['link', 'image'],
      ['clean']
    ],
  };

  const formats = [
    'header',
    'bold', 'italic', 'underline', 'strike',
    'list', 'bullet',
    'link', 'image'
  ];

  const handleChange = (content) => {
    setValue(content);
    onChange(content);
  };

  return (
    <ReactQuill
      theme="snow"
      value={value}
      onChange={handleChange}
      placeholder={placeholder}
      modules={modules}
      formats={formats}
      style={{ Height: 800 }}
    />
  );
};

export default RichTextEditor;