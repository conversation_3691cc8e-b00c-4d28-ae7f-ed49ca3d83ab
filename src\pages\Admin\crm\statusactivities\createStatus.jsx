// src/pages/activities/createStatus.jsx
import { useState } from "react";
import { ProForm, ProFormText } from "@ant-design/pro-form";
import { Button, Collapse, message, Divider } from "antd";
import { DeleteOutlined } from "@ant-design/icons";

const CreateActivityStatus = ({ onCancel, setDataSource }) => {
  const [stages, setStages] = useState([{ id: Date.now(), name: "" }]);
  const [activeKey, setActiveKey] = useState(["1"]);

  const addStage = () => {
    const newStage = { id: Date.now(), name: "" };
    setStages([...stages, newStage]);
    setActiveKey([newStage.id]);
  };

  const removeStage = (keyToRemove) => {
    setStages((stages) => stages.filter((stage) => stage.id !== keyToRemove));
  };

  const handleSubmit = async (values) => {
    try {
      const newKey = Date.now();
      const newPhase = {
        key: newKey,
        name: values.name,
        description: values.description,
        stages: stages.map((stage, index) => ({
          id: stage.id,
          name: values[`stage_name_${index}`] || "",
        })),
      };
      const updatedData = [...(datas || []), newPhase];
      setDataSource(updatedData);
      message.success("Phase créée avec succès!");
      onCancel();
    } catch (error) {
      console.log(error);
      message.error("Erreur lors de la création");
    }
  };

  return (
    <>
      <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", marginBottom: "15px" }}>
        <h2>Créer une phase</h2>
        <Button onClick={addStage} type="primary">+</Button>
      </div>

      <ProForm onFinish={handleSubmit}>
        <ProFormText
          name="name"
          label="Nom de la phase"
          placeholder="Saisissez le nom de la phase"
          rules={[{ required: true }]}
        />
        <ProFormText
          name="description"
          label="Description"
          placeholder="Saisissez la description"
          rules={[{ required: true }]}
        />

        <Divider>Étapes</Divider>

        {stages.map((stage, index) => (
          <div key={stage.id} style={{ display: "flex", alignItems: "center", gap: "10px", width: "100%" }}>
            <Collapse
              style={{ flex: 1, marginBottom: "10px" }}
              activeKey={activeKey}
              onChange={(keys) => setActiveKey(keys)}
              items={[
                {
                  key: stage.id,
                  label: `Étape ${index + 1}`,
                  children: (
                    <div className="stage-group">
                      <ProFormText
                        name={`stage_name_${index}`}
                        label="Nom de l'étape"
                        placeholder="Saisissez le nom de l'étape"
                        rules={[{ required: true }]}
                      />
                    </div>
                  ),
                },
              ]}
            />
            {index !== 0 && (
              <Button onClick={() => removeStage(stage.id)} danger icon={<DeleteOutlined />} />
            )}
          </div>
        ))}
      </ProForm>
    </>
  );
};

export default CreateActivityStatus;