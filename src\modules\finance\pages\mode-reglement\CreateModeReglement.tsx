import {addModeReglement} from "@/modules/finance/features/mode-reglement/ModeReglementSlice";
import {DeleteOutlined, PlusOutlined} from "@ant-design/icons";
import {Button, Col, Drawer, Form, Input, Row, Select, Space, Table} from "antd";
import {useState} from "react";
import {useDispatch} from "react-redux";
import {useTranslation} from "react-i18next";

const CreateModeReglement: React.FC<{
  visible: boolean,
  setVisible: React.Dispatch<React.SetStateAction<boolean>>,
  messageApi: any;
  tableRef: any;
  moyenPaiements: any;
}> = ({visible, setVisible, messageApi, tableRef, moyenPaiements}) => {
  const dispatch = useDispatch();
  const {t} = useTranslation();
  const [buttonLoading, setButtonLoading] = useState(false);
  const [form] = Form.useForm();
  const [data, setData] = useState([{id: 1}]);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);

  const filterOption = (input, option) =>
    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
  const filterSort = (optionA, optionB) =>
    (optionA?.label ?? '')
      .toLowerCase()
      .localeCompare((optionB?.label ?? '').toLowerCase())

  const handleAddRow = () => {
    const newData = [...data, {id: data.length + 1}];
    setData(newData);
    setSelectedRowKeys([newData.length]);
  };
  const handleDeleteRows = (id) => {
    const newData = data.filter((item) => !selectedRowKeys.includes(item.id));
    setData(newData);
    setSelectedRowKeys([]);
  };
  const columns = [
    {
      title: t("mode_reglement.pourcentage"),
      dataIndex: 'pourcentage',
      render: (_, record) => (
        <Input style={{width: '80px'}}
               placeholder="Pourcentage %" name="pourcentage"
               type='number' min={0} max={100}
               required
               onChange={(e) => handleInputChange(record.id, "pourcentage", e.target.value)}
        />
      ),
    },
    {
      title: t("mode_reglement.nbr_jours"),
      dataIndex: 'nbr_jours',
      render: (_, record) => (
        <Input style={{width: '80px'}}
               placeholder="Nombre de jours" name="nbr_jour" required
               onChange={(e) => handleInputChange(record.id, "nbr_jours", e.target.value)}

        />
      ),
    },

    {
      title: t("mode_reglement.type_reglement"),
      dataIndex: 'type_reglement',
      render: (_, record) => (
        <Select style={{width: '130px'}}
                placeholder="Type de règlement" aria-required
                options={
                  [
                    {label: "comptant", value: "comptant"},
                    {label: "net", value: "net"},
                    {label: "net_le", value: "net_le"},
                  ]
                }
                onChange={(value) => handleInputChange(record.id, "type_reglement", value)}
                filterOption={filterOption}
                filterSort={filterSort}
                allowClear
                showSearch
        >
        </Select>
      ),
    },
    {
      title: t("mode_reglement.jour_reglement"),
      dataIndex: 'jour_reglement',
      render: (_, record) => (
        <Input style={{width: '90px'}}
               placeholder="Jour de règlement" name="jour_reglement"
               type="number"
               min={0}
               max={31}
               onChange={(e) => handleInputChange(record.id, "jour_reglement", e.target.value)}
               required={record.type_reglement === "net_le"}
               disabled={record.type_reglement !== "net_le"}
        />
      ),
    },

    {
      title: t("mode_reglement.moyen_paiement"),
      dataIndex: 'moyen_paiement_id',
      render: (_, record) => (
        <Select style={{width: '130px'}}
                placeholder="Type" aria-required
                options={moyenPaiements}
                onChange={(value) => handleInputChange(record.id, "moyen_paiement_id", value)}
                filterOption={filterOption}
                filterSort={filterSort}
                allowClear
                showSearch
        >
        </Select>
      ),
    },
    {
      title: t("action"),
      dataIndex: 'actions',
      render: (_, record) => (
        <Space size="middle">
          <Button icon={<DeleteOutlined/>} title="supprimer"
                  onClick={() => handleDeleteRows(record.id)}>
          </Button>
        </Space>
      ),
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedKeys) => setSelectedRowKeys(selectedKeys),
  };
  const onClose = () => {
    setVisible(false);
    form.resetFields();
    setData([{id: 1}]);
    setSelectedRowKeys([]);
  };

  const handleSubmit = (values) => {
    values = {
      ...values,
      data: data,
    }
    let pourcentageSum = 0;
    data.forEach((item, index) => {
      pourcentageSum += parseInt(item['pourcentage']);
    });
    if (pourcentageSum !== 100) {
      // alert("pourcentage must be equal to 100%");
      messageApi.open({
        key: "updatable",
        error: "error",
        type: "error",
        content: "Erreur, pourcentage doit être égale à 100%",
        duration: 3
      });
      return;
    }
    setButtonLoading(true);
    messageApi.open({
      key: "updatable",
      type: "loading",
      content: "Loading...",
    });
    dispatch(addModeReglement(values))
      .unwrap()
      .then((res) => {
        setButtonLoading(false);
        messageApi.open({
          key: "updatable",
          type: "success",
          content: res.message,
          duration: 2,
        });
        setVisible(false);
        tableRef.current.reload();
      })
      .catch(() => {
        setButtonLoading(false);
        messageApi.open({
          key: "updatable",
          type: "error",
          content: "Error",
          duration: 2,
        });
        return [];
      });
  };
  const handleInputChange = (id, field, value) => {
    const updatedData = data.map((item) =>
      item.id === id ? {...item, [field]: value} : item
    );
    setData(updatedData);
  };

  return (
    <Drawer
      title={t("mode_reglement.ajouter_nouvelle")}
      className="CautionForm"
      onClose={onClose}
      open={visible}
      bodyStyle={{
        paddingBottom: 80,
      }}
      width={870}
    >
      <Form layout="vertical"
            hideRequiredMark
            onFinish={handleSubmit}
            form={form}
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="code"
              label={<span>{t("code")} <span style={{color: 'red'}}>*</span></span>}
              rules={[
                {
                  required: true,
                  message: "Veuillez entrer un code",
                },
              ]}
            >
              <Input placeholder="Veuillez entrer un code"/>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="libelle"
              label={<span>{t("libelle")} <span style={{color: 'red'}}>*</span></span>}
              rules={[
                {
                  required: true,
                  message: "Veuillez entrer un Libellé",
                },
              ]}
            >
              <Input placeholder="Veuillez entrer un Libellé"/>
            </Form.Item>
          </Col>
          <Col span={24}>
            <Table
              rowKey="id"
              dataSource={data}
              columns={columns}
              rowSelection={rowSelection}
              pagination={false}
            />
          </Col>
          <Col span={24} className="mt-3">
            <Button className="mx-1" type="dashed" icon={<PlusOutlined/>} onClick={handleAddRow}>
              {t("mode_reglement.ajouter_ligne")}
            </Button>
            <Button className="mx-1" danger icon={<DeleteOutlined/>} onClick={handleDeleteRows}>
              {t("mode_reglement.supprimer_ligne")}
            </Button>
          </Col>
          <Col span={24} style={{marginTop: "10px"}}>
            <Form.Item style={{textAlign: "right"}}>
              <Button
                className="btnAnnuler"
                htmlType="reset"
                style={{marginRight: "10px"}}
              >
                {t("annuler")}
              </Button>
              <Button type="primary" htmlType="submit" style={{marginRight: "10px"}}
                      loading={buttonLoading}>
                {t("envoyer")}
              </Button>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Drawer>
  );
};

export default CreateModeReglement;
