import React, { useEffect } from "react";
import { Form, Input, Select, Button, message } from "antd";
import { categories } from "./data"; // Import your categories data

const EditModal = ({ record, onCancel, onSuccess }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = React.useState(false);

  useEffect(() => {
    if (record) {
      form.setFieldsValue({
        name: record.name,
        type: record.type
      });
    } else {
      form.resetFields();
    }
  }, [record, form]);

  const handleSubmit = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      
      // Simulate API call with your local data
      const updatedCategories = categories.map(cat => 
        cat.id === record.id ? { ...cat, ...values } : cat
      );
      
      // In a real app, you would do:
      // await axios.put(`/api/categories/${record.id}`, values);
      
      message.success("Catégorie mise à jour avec succès !");
      onSuccess(updatedCategories);
      onCancel();
    } catch (error) {
      console.error(error);
      message.error("Erreur lors de la mise à jour de la catégorie");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Form form={form} layout="vertical">
      <Form.Item 
        name="name" 
        label="Nom" 
        rules={[{ required: true, message: "Ce champ est obligatoire" }]}
      >
        <Input />
      </Form.Item>
      <Form.Item 
        name="type" 
        label="Type" 
        rules={[{ required: true, message: "Ce champ est obligatoire" }]}
      >
        <Select>
          <Select.Option value="Document">Document</Select.Option>
          <Select.Option value="Email">Email</Select.Option>
        </Select>
      </Form.Item>
      <div style={{ textAlign: 'right', marginTop: 16 }}>
        <Button style={{ marginRight: 8 }} onClick={onCancel}>
          Annuler
        </Button>
        <Button 
          type="primary" 
          onClick={handleSubmit}
          loading={loading}
        >
          Enregistrer
        </Button>
      </div>
    </Form>
  );
};

export default EditModal;