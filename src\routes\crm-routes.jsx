import { lazy } from "react";
import AuthGuard from "../helpers/Guards/AuthGuard";
import RoleGuard from "../helpers/Guards/RoleGuard";
import Activities from "../pages/Admin/crm/typesactivities";
import PlansActivities from "../pages/Admin/crm/plansactivities";
import ActivityStatuses from "../pages/Admin/crm/statusactivities";
import Tasks from "../pages/Admin/crm/activities";
import Contacts from "../pages/Admin/crm/contacts";
import ManagePipeline from "../pages/Admin/crm/pipelines/DataFetch";
import EquipeCommerce from "../pages/Admin/crm/equipecommerciale";
import Dashboard from "../pages/Admin/crm/pipelines/Dashboard";
import Events from "../pages/Admin/crm/events/index";
import Dashboardevents from "../pages/Admin/crm/events/dashboardevents";
import Gouvernorats from "../pages/Admin/crm/governaurats";
import Delegation from "../pages/Admin/crm/delegations";
import CodePostaux from "../pages/Admin/crm/codes_postaux";
import TrainLocaux from "../pages/Admin/crm/trains_locaux/index";
import Demandes from "../pages/Admin/crm/demandes-locals/index";
import Statterain from "../pages/Admin/crm/statitiques";
import ProfilDias from "../pages/Admin/crm/profiles_dias";
import InteractionDias from "../pages/Admin/crm/interactions_dias";
import AnalyseDias from "../pages/Admin/crm/reporting_dias";
import Calendrier from "../pages/Admin/crm/calendrier/index";
const Companies = lazy(() => import("../pages/Admin/crm/companies"));
const CompanyDashboard = lazy(() =>
  import("../pages/Admin/crm/companyDashbord")
);
import InteractionHistory from "../pages/Admin/crm/interactionHistory";
import DemandeIncident from "../pages/Admin/crm/demandes";
import CompanyActivity from "../pages/Admin/crm/companyActivity";
import Secteur from "../pages/Admin/crm/companySecteur";
import Pays from "../pages/Admin/crm/pays";
import GroupPays from "../pages/Admin/crm/groupPays";
import Departement from "../pages/Admin/crm/departements";
import Contact from "../pages/Admin/crm/contacts_entreprises/contacts";
import Company from "../pages/Admin/crm/contacts_entreprises/entreprises";
import SeminaireMP from "../pages/Admin/crm/seminaires/seminaireMp";
import SeminaireMS from "../pages/Admin/crm/seminaires/seminaireMS";
import Salons from "../pages/Admin/crm/seminaires/salon";
import Media from "../pages/Admin/crm/seminaires/media";
import SeminaireDelegation from "../pages/Admin/crm/seminaires/delegation";
import Ctes from "../pages/Admin/crm/seminaires/cte";
import DemarchageDirect from "../pages/Admin/crm/seminaires/demarchageDirect";
import AutreSourcesContact from "../pages/Admin/crm/seminaires/autreSourcesContact";
import Visite from "../pages/Admin/crm/seminaires/visite";
import Projet from "../pages/Admin/crm/projets";
import Scoring from "../pages/Admin/crm/scorings/index";
import GestionBiblitheques from "../pages/Admin/crm/gestion_documents/index";
import GestionEmailsTemplates from "../pages/Admin/crm/gestion_emails/index";
import GestionCategory from "../pages/Admin/crm/params_categories/index";

const isAuthenticated = true;
const userRole = "admin";

const routes = [
  {
    path: "crm/dashboard",
    icon: "File",
    name: "Dashboard",
    module: "dashboard",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <Dashboard />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/typeactivities",
    module: "activities",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <Activities />
        </RoleGuard>
      </AuthGuard>
    ),
  },

  // Routes pour les Plans d'activités
  {
    path: "crm/plansactivities",
    module: "activities",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <PlansActivities />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  // Routes pour les Status des activités
  {
    path: "crm/activitystatuses",
    module: "activities",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <ActivityStatuses />
        </RoleGuard>
      </AuthGuard>
    ),
  },

  {
    path: "crm/leads",
    module: "leads",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <Tasks />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/gestion_documents",
    module: "documents",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <GestionBiblitheques />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/templates_emails",
    module: "documents",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <GestionEmailsTemplates />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/contacts",
    module: "contacts",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <Contacts />
        </RoleGuard>
      </AuthGuard>
    ),
  },

  {
    path: "crm/piplines",
    module: "opportunities",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <ManagePipeline />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/equipescommerciales",
    module: "settings",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <EquipeCommerce />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/dashboardevents",
    module: "events",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <Dashboardevents />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/events",
    module: "events",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <Events />
        </RoleGuard>
      </AuthGuard>
    ),
  },

  {
    path: "crm/events",
    module: "events",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <Events />
        </RoleGuard>
      </AuthGuard>
    ),
  },

  {
    path: "crm/gouvernorats",
    module: "geography",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <Gouvernorats />
        </RoleGuard>
      </AuthGuard>
    ),
  },

  {
    path: "crm/delegation",
    module: "geography",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <Delegation />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/code-postaux",
    module: "geography",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <CodePostaux />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/train-locaux",
    module: "geography",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <TrainLocaux />
        </RoleGuard>
      </AuthGuard>
    ),
  },

  {
    path: "crm/demandes",
    module: "geography",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <Demandes />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/stat_terain",
    module: "reports",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <Statterain />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/companies-dashboard",
    module: "companies",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <CompanyDashboard />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/entreprises-etrangeres",
    module: "companies",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <Companies />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/entreprises-interactions",
    module: "companies",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <InteractionHistory />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/entreprises-demandes",
    module: "companies",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <DemandeIncident />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/parametres/activites",
    module: "settings",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <CompanyActivity />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/parametres/secteurs",
    module: "settings",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <Secteur />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/parametres/pays",
    module: "settings",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <Pays />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/parametres/group-pays",
    module: "settings",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <GroupPays />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/parametres/departements",
    module: "settings",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <Departement />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/entreprises-contacts",
    module: "companies",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <Contact />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/entreprises",
    module: "companies",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <Company />
        </RoleGuard>
      </AuthGuard>
    ),
  },

  // Gestion des profils diaspora
  {
    path: "crm/gestion_profil_dias",
    module: "diaspora",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <ProfilDias />
        </RoleGuard>
      </AuthGuard>
    ),
  },

  // Interaction avec diaspora
  {
    path: "crm/interraction_dias",
    module: "diaspora",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <InteractionDias />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  // Reporting et analyse
  {
    path: "crm/report_analyse_dias",
    module: "diaspora",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <AnalyseDias />
        </RoleGuard>
      </AuthGuard>
    ),
  },

  {
    path: "crm/seminaires/pays",
    module: "events",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <SeminaireMP />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/seminaires/secteur",
    module: "events",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <SeminaireMS />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/seminaires/salons",
    module: "events",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <Salons />
        </RoleGuard>
      </AuthGuard>
    ),
  },

  {
    path: "crm/seminaires/media",
    module: "events",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <Media />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/interraction_dias",
    module: "diaspora",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <InteractionDias />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  // Reporting et analyse
  {
    path: "crm/report_analyse_dias",
    module: "diaspora",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <AnalyseDias />
        </RoleGuard>
      </AuthGuard>
    ),
  },

  // Scoring
  {
    path: "crm/gestion_scorring",
    module: "scoring",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <Scoring />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/seminaires/ctes",
    module: "events",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <Ctes />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/seminaires/delegations",
    module: "events",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <SeminaireDelegation />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/seminaires/demarchage-direct",
    module: "events",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <DemarchageDirect />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/seminaires/autre-source",
    module: "events",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <AutreSourcesContact />
        </RoleGuard>
      </AuthGuard>
    ),
  },

  {
    path: "crm/gestion_calendrier",
    module: "calendar",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <Calendrier />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/seminaires/visites",
    module: "events",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <Visite />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/projets",
    module: "projects",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <Projet />
        </RoleGuard>
      </AuthGuard>
    ),
  },
  {
    path: "crm/entreprises-statistiques",
    module: "reports",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <CompanyDashboard />
        </RoleGuard>
      </AuthGuard>
    ),
  },

  {
    path: "crm/parametres/category",
    module: "settings",
    element: (
      <AuthGuard isAuthenticated={isAuthenticated}>
        <RoleGuard userRole={userRole} allowedRoles={["admin"]}>
          <GestionCategory />
        </RoleGuard>
      </AuthGuard>
    ),
  },
];

export default routes;
