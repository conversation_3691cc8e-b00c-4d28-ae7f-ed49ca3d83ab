import React, { useEffect, useRef, useState } from "react";
import "@/style/modules/Caution.less";
import {
  Button,
  Space,
  Tooltip,
  Card,
  Typography,
  Divider,
  Popconfirm,
  message,
  Badge,
  Spin,
} from "antd";
import {
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
} from "@ant-design/icons";
import {
  ProTable,
  ProColumns,
} from "@ant-design/pro-components";
import { User, Lock } from "lucide-react";

import { useDispatch, useSelector } from "react-redux";
import RolePermissions from "./RolePermissions";
import { deleteRole, getRoles } from "@/modules/parameter/features/roles/roleSlice";
import RoleUsers from "./RoleUsers";
import AddRole from "./AddRole";
import dayjs from "dayjs";
import UpdateRole from "./UpdateRole";
import { RootState } from "@features/store";
import { getPermissions } from "@/modules/parameter/features/permissions/permissionSlice";
const { Paragraph, Title } = Typography;

const Roles: React.FC = () => {
  const dispatch = useDispatch();
  const tableRef = useRef<any>();
  const [messageApi, contextHolder] = message.useMessage();
  const [visibleUsersList, setVisibleUsersList] = useState(false);
  const [visibleForm, setVisibleForm] = useState(false);
  const [visibleDetails, setVisibleDetails] = useState(false);
  const [refresh, forceRefresh] = useState(0);
  const [expandedRowKeys, setExpandedRowKeys] = useState([]);
  const [role, setRole] = useState({});
  const [permissions, setPermissions] = useState([]);
  const [roles, setRoles] = useState([]);

  const columns: ProColumns<any>[] = [
    // {
    //   title: "Référence",
    //   dataIndex: "key",
    //   key: "Référence",
    //   search: false,
    // },
    {
      title: "Rôle",
      dataIndex: "role",
      key: "role",
    },
    {
      title: "Nom du guard",
      dataIndex: "guard",
      key: "Guard",
      search: false,
      responsive: ["md"],
    },
    {
      title: "Date de création",
      dataIndex: "date",
      key: "Date de création",
      render: (_, data) => (
        <>{data.date ? dayjs(data.date).format("YYYY-MM-DD HH:mm") : "-"}</>
      ),
      valueType: "dateRange",
      search: {
        transform: (value) => {
          return {
            startTime: value[0],
            endTime: value[1],
          };
        },
      },
    },
    {
      title: "Action",
      valueType: "option",
      key: "option",
      fixed: "right",
      render: (text, role, _, action) => [
        <Space size="small" key="0">
          <a
            onClick={() => {
              if (expandedRowKeys.indexOf(role.key) === -1) {
                setExpandedRowKeys([...expandedRowKeys, role.key]);
              } else {
                setExpandedRowKeys(
                  expandedRowKeys.filter((item) => item !== role.key)
                );
              }
            }}
          >
            <Tooltip title="Liste des permissions">
              <Lock />
            </Tooltip>
          </a>
          <Divider type="vertical" />
          <a
            onClick={() => {
              setRole(role);
              setVisibleUsersList(true);
            }}
          >
            <Tooltip title="Liste des utilisateurs ayant ce rôle">
              <User />
            </Tooltip>
          </a>
          <Divider type="vertical" />
          <a
            onClick={() => {
              setRole(role);
              setVisibleDetails(true);
            }}
          >
            <Tooltip title="Modifier rôle">
              <EditOutlined />
            </Tooltip>
          </a>
          <Divider type="vertical" />
          <a>
            <Popconfirm
              title="voulez-vous vraiment supprimer ce rôle ?"
              onConfirm={() => handleDeleteRole(role.id)}
              okText="Oui"
              cancelText="Non"
            >
              <Tooltip title="Supprimer rôle">
                <DeleteOutlined />
              </Tooltip>
            </Popconfirm>
          </a>
        </Space>,
      ],
    },
  ];

  const allRoles = useSelector(
    (state: RootState) => state.role.data
  );
  const allPermissions = useSelector(
    (state: RootState) => state.permission.data
  );
  const loadData = async () => {
    if (!allRoles?.length) {
      await dispatch(getRoles());
      console.log("roles fetching");
    }
    if (!allPermissions) {
      await dispatch(getPermissions());
      console.log("permissions fetching");
    }
  };

  useEffect(() => {
    console.log("roles mounted");
    loadData();
  }, []);

  useEffect(() => {
    if (allPermissions && typeof allPermissions === "object") {
      setPermissions(
        Object.keys(allPermissions).reduce(
          (acc, title, index) => {
            acc.push({
              title,
              key: title,
              children: allPermissions[title].map(
                (item, cp) => {
                  return {
                    id: item.id,
                    title: item.name,
                    icon: (
                      <Badge
                        color="#1890FF"
                        count={item.related_permission?.length}
                      />
                    ),
                    related_permission: item.related_permission,
                    key: title + "-" + item.id.toString(),
                  };
                }
              ),
            });
            return acc;
          },
          []
        )
      );
    }
  }, [allPermissions]);

  useEffect(() => {
    if (allRoles?.length) {
      setRoles(allRoles?.map((item) => {
        return {
          id:item.id,
          key: item.id,
          role: item.name,
          guard: item.guard_name,
          date: item.created_at,
          users: item.users,
          permissions:      Object.keys(item.allowedPermissions).reduce(
            (acc, title, index) => {
              acc.push({
                title,
                key: title,
                children: item.allowedPermissions[title].map(
                  (item, cp) => {
                    return {
                      id:item.id,
                      title: item.name,
                      key: title+'-'+item.id.toString(),
                    };
                  }
                ),
              });
              return acc;
            },
            []
          )
        };
      })
      );
      // trigger refresh table
      tableRef.current.reload();
    }
  }, [allRoles]);

  const handleDeleteRole = (id) => {
    messageApi.open({
      key: "updatable",
      type: "loading",
      content: "Loading...",
    });
    dispatch(deleteRole({ id }))
      .unwrap()
      .then((originalPromiseResult) => {
        messageApi.open({
          key: "updatable",
          type: "success",
          content: "Success ",
          duration: 2,
        });
        tableRef.current.reload();
      })
      .catch((rejectedValueOrSerializedError) => {
        messageApi.open({
          key: "updatable",
          type: "error",
          content: "Error",
          duration: 2,
        });
        console.log(rejectedValueOrSerializedError);
        return [];
      });
  };

  useEffect(() => {}, [refresh]);

  return (
    <div>
      {contextHolder}
      <Card title={<Title level={4}>Gestion des rôles</Title>} bordered={false}>
      <ProTable<any>
        actionRef={tableRef}
        columns={columns}
        cardBordered
        bordered
        dateFormatter="string"
        cardProps={{ className: "p-2" }}
        columnsState={{
          persistenceKey: "pro-table-singe-demos",
          persistenceType: "localStorage",
          onChange(value) {
            // console.log("value: ", value);
          },
        }}
        search={{
          labelWidth: "auto",
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          pageSize: 10,
          // onChange: (page) => console.log(page),
        }}
        headerTitle={<Title level={5}>Liste de rôles</Title>}
        request={async (params) => {
          let filtredData = roles
          if (params.role){
            filtredData =  filtredData.filter((item) =>
              item.role
                .toString()
                .toUpperCase()
                .search(params.role.toString().toUpperCase()) === -1
                ? false
                : true
            )
          }
          if (params.endTime && params.startTime){
            filtredData =  filtredData.filter((item) => {
              return dayjs(item.date).isAfter(dayjs(params.startTime)) &&
              dayjs(item.date).isBefore(dayjs(params.endTime))
            })
          }
          return {
            data: filtredData,
            success: true,
          };
        }}
        loading={!roles?.length}
        rowKey="id"
        toolBarRender={() => [
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              setVisibleForm(true);
            }}
          >
            Ajouter un rôle
          </Button>,
        ]}
        expandable={{
          expandedRowRender: (record) => {
            return (
              <div className="flex justify-center">
                <div style={{ width: "90%" }}>
                  <RolePermissions role={record} permissions={permissions} />
                </div>
              </div>
            );
          },
          // rowExpandable: (record) => record.entreprise.length !== 0,
          showExpandColumn: false,
          expandedRowKeys: expandedRowKeys,
        }}
      />
      </Card>
      <RoleUsers
        visible={visibleUsersList}
        setVisible={setVisibleUsersList}
        role={role}
      />
      <AddRole
        visible={visibleForm}
        setVisible={setVisibleForm}
        messageApi={messageApi}
        tableRef={tableRef}
      />
      <UpdateRole
        visible={visibleDetails}
        setVisible={setVisibleDetails}
        role={role}
        messageApi={messageApi}
        tableRef={tableRef}
      />
    </div>
  );
};

export default Roles;
