// src/guards/RoleGuard.js
import { Navigate } from 'react-router-dom';

const RoleGuard = ({ children }) => {
  function checkLinkExists(menuArray) {
          return true

    // if user roles has super_admin return true
    const userRoles = JSON.parse(localStorage.getItem("user"))?.roles;
    if (userRoles?.find((item) => item.name === 'super_admin')) {
      console.log('it is super admin');
      return true
    }
    if (!menuArray) return false;
    // verify if menuArray is an array
    if (Array.isArray(menuArray)) {
      for (const item of menuArray) {
        if (item.link === location.pathname) {
          return true;
        }
        if (item.child_recursive && checkLinkExists(item.child_recursive)) {
          return true;
        }
      }
    }
    return false;
  }
  return checkLinkExists(JSON.parse(localStorage.getItem("menu")) ?? []) ? (
    children
  ) : (
    <Navigate to="/unauthorized" />
  );
};

export default RoleGuard;
