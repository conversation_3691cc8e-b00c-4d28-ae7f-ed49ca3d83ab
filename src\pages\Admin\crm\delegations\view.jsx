import { Card, Descriptions } from "antd";

const ViewDelegation = ({ record, onCancel }) => {
  return (
    <Card title="Détails de la Délégation" bordered={false}>
      <Descriptions bordered>
        <Descriptions.Item label="Code">{record.code}</Descriptions.Item>
        <Descriptions.Item label="Libellé">{record.libelle}</Descriptions.Item>
        <Descriptions.Item label="Décentralisée">{record.decentralisee ? "Oui" : "Non"}</Descriptions.Item>
        <Descriptions.Item label="Taux Prime">{record.tauxPrime}%</Descriptions.Item>
      </Descriptions>
    </Card>
  );
};

export default ViewDelegation;
