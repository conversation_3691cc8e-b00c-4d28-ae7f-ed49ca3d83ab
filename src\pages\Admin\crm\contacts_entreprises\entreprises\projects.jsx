import React, { useState } from "react";
import { Form, Space, Tag,Table, Row, Col ,Typo<PERSON>, <PERSON>ton, Card, Modal, Input, Select, InputNumber} from "antd";
import { ModalForm } from "@ant-design/pro-components";
import { useTranslation } from "react-i18next";
import { DeleteOutlined, EditOutlined, LineChartOutlined, PlusOutlined } from "@ant-design/icons";
const { Title } = Typography;
const ProjectListPanel = ({ open, onCancel, onSuccess }) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [projects, setProjects] = useState([
    {
      key: '1',
      name: 'E-commerce Platform',
      status: 'En préparation',
      chiffreAffaire: 125000,
      description: 'Online shopping platform with payment integration'
    },
    {
      key: '2',
      name: 'CRM System',
      status: 'En cours',
      chiffreAffaire: 85000,
      description: 'Customer relationship management for sales teams'
    },
    {
      key: '3',
      name: 'Mobile Banking App',
      status: 'Achevé',
      chiffreAffaire: 210000,
      description: 'Secure banking application for iOS and Android'
    },
    {
      key: '4',
      name: 'IoT Dashboard',
      status: 'En préparation',
      chiffreAffaire: 176500,
      description: 'Real-time monitoring of industrial sensors'
    }
  ]);
  const [isAddModalVisible, setIsAddModalVisible] = useState(false);
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [currentProject, setCurrentProject] = useState(null);
  // Status tag colors
  const statusColors = {
    'En préparation' : 'green',
    'En cours': 'blue',
    'Achevé': 'gray'
  };
  // Open modal to add new project
  const showAddModal = () => {
    form.resetFields();
    setIsAddModalVisible(true);
  };
  // Open modal to edit project
  const showEditModal = (project) => {
    setCurrentProject(project);
    form.setFieldsValue({
      name: project.name,
      status: project.status,
      chiffreAffaire: project.chiffreAffaire,
      description: project.description
    });
    setIsEditModalVisible(true);
  };
  // Handle form submission for add
  const handleAddSubmit = (values) => {
    const newProject = {
      key: String(projects.length + 1),
      ...values
    };
    setProjects([...projects, newProject]);
    setIsAddModalVisible(false);
  };

  // Handle form submission for edit
  const handleEditSubmit = (values) => {
    const updatedProjects = projects.map(project => {
      if (project.key === currentProject.key) {
        return { ...project, ...values };
      }
      return project;
    });
    setProjects(updatedProjects);
    setIsEditModalVisible(false);
  };

  // Delete a project
  const handleDelete = (key) => {
    Modal.confirm({
      title: 'Supprimer le project',
      content: 'Êtes-vous sûr de vouloir supprimer ce projet ?',
      okText: 'Supprimer',
      okType: 'danger',
      cancelText: 'Annuler',
      onOk: () => {
        setProjects(projects.filter(project => project.key !== key));
      }
    });
  };
  // Calculate total revenue
  const totalRevenue = projects.reduce((sum, project) => sum + project.chiffreAffaire, 0);
  // Table columns
  const columns = [
    {
      title: 'Projet',
      dataIndex: 'name',
      key: 'name',
      sorter: (a, b) => a.name.localeCompare(b.name),
      render: (text) => <strong>{text}</strong>
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      filters: [
        { text: 'En préparation', value: 'En préparation' },
        { text: 'En cours', value: 'En cours' },
        { text: 'Achevé', value: 'Achevé' }
      ],
      onFilter: (value, record) => record.status === value,
      render: (status) => (
        <Tag color={statusColors[status]} key={status}>
          {status}
        </Tag>
      )
    },
    {
      title: 'Chiffre d\'affaire (€)',
      dataIndex: 'chiffreAffaire',
      key: 'chiffreAffaire',
      sorter: (a, b) => a.chiffreAffaire - b.chiffreAffaire,
      render: (value) => (
        <span style={{ fontWeight: 500 }}>
          {new Intl.NumberFormat('fr-FR', {
            style: 'currency',
            currency: 'EUR',
            maximumFractionDigits: 0
          }).format(value)}
        </span>
      )
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      responsive: ['md']
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <div>
          <Button 
            icon={<EditOutlined />} 
            onClick={() => showEditModal(record)}
            style={{ marginRight: 8 }}
          >
            
          </Button>
          <Button 
            icon={<DeleteOutlined />} 
            onClick={() => handleDelete(record.key)}
            danger
          >
            
          </Button>
        </div>
      )
    }
  ];

  return (
    <ModalForm
      title={t("Liste des projets")}
      form={form}
      open={open}
      width="70%"
      modalProps={{
        style: { top: 20 },
      }}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      submitter={{
        searchConfig: {
          submitText: t("common.actions.save"),
          resetText: t("common.actions.cancel"),
        },
      }}
    >
      <Row justify="space-between" align="middle" style={{ marginBottom: '24px' }}>
        <Col></Col>
        <Col>
          <Button 
            type="primary" 
            icon={<PlusOutlined />} 
            onClick={showAddModal}
          >
          </Button>
        </Col>
      </Row> 

      {/* Project Table */}
      <Table 
        columns={columns}
        dataSource={projects}
        pagination={{ pageSize: 5 }}
        rowKey="key"
        bordered
        style={{ boxShadow: '0 2px 8px rgba(0,0,0,0.09)' }}
      />   

      {/* Add Project Modal */}
      <Modal
        title="Ajouter un projet"
        open={isAddModalVisible}
        onCancel={() => setIsAddModalVisible(false)}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleAddSubmit}
        >
          <Form.Item
            label="Nom du projet"
            name="name"
          >
            <Input placeholder="Nom du projet" />
          </Form.Item>
          
          <Form.Item
            label="Status"
            name="status"
          >
            <Select placeholder="Status">
              <Option value="En préparation">En préparation</Option>
              <Option value="En cours">En cours</Option>
              <Option value="Achevé">Achevé</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            label="Chiffre d'affaire (€)"
            name="chiffreAffaire" 
          > <InputNumber
              style={{ width: '100%' }} 
              min={0} 
              formatter={value => `€ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={value => value.replace(/€\s?|(,*)/g, '')}
            />
          </Form.Item>
          
          <Form.Item
            label="Description"
            name="description"
          > <Input.TextArea rows={4} />
          </Form.Item>
          
          <Form.Item>
            <Button type="primary" htmlType="submit">
              Ajouter 
            </Button>
          </Form.Item>
        </Form>
      </Modal> 
           
      {/* Edit Project Modal */}
      <Modal
        title="Modifier un projet"
        open={isEditModalVisible}
        onCancel={() => setIsEditModalVisible(false)}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleEditSubmit}
        >
          <Form.Item
            label="Nom du projet"
            name="name"
           
          >
            <Input  />
          </Form.Item>
          
          <Form.Item
            label="Status"
            name="status"
           
          >
            <Select placeholder="Status">
              <Option value="En préparation">En préparation</Option>
              <Option value="En cours">En cours</Option>
              <Option value="Achevé">Achevé</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            label="Chiffre d'affaire (€)"
            name="chiffreAffaire"
          
          >
            <InputNumber 
              style={{ width: '100%' }} 
              min={0} 
              formatter={value => `€ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={value => value.replace(/€\s?|(,*)/g, '')}
            />
          </Form.Item>
          
          <Form.Item
            label="Description"
            name="description"
          >
            <Input.TextArea rows={4} placeholder="Project description" />
          </Form.Item>
          
          <Form.Item>
            <Button type="primary" htmlType="submit">
              Modifier
            </Button>
          </Form.Item>
        </Form>
      </Modal>

    </ModalForm>
  );
};

export default ProjectListPanel;
