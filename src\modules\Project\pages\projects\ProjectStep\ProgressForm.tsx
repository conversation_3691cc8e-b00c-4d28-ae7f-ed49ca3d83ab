import { ModalForm } from "@ant-design/pro-components";
import { createProjectStepProgress } from "@src/modules/Project/features/projects/projectStepTimelineSlice";
import { Col, DatePicker, Form, InputNumber, Modal, Row, Select } from "antd";
import TextArea from "antd/es/input/TextArea";
import dayjs from "dayjs";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { useMemo, useState } from "react";

const ProgressForm = ({
  step,
  advanceStageOpen,
  setAdvanceStageOpen,
  forceRefresh,
  messageApi,
}) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const [form] = Form.useForm();
  const [progressionType, setProgressionType] = useState(null); // suivi du type sélectionné

  // Déterminer le dernier type de step.timeline
  const availableOptions = useMemo(() => {
    const timeline = step?.timeline || [];
    const lastType =
      timeline.length > 0 ? timeline[timeline.length - 1]?.type : null;

    if (lastType === "suspension") {
      return [
        { value: "reprise", label: t("project_steps.options.resumption") },
      ];
    } else {
      return [
        { value: "advancement", label: t("project_steps.options.advancement") },
        { value: "suspension", label: t("project_steps.options.suspension") },
      ];
    }
  }, [step, t]);

  const handleInputErrors = (error) => {
    const apiErrors = error.errors;
    const fields: { name: string; errors: string[] }[] = Object.entries(
      apiErrors
    ).map(([fieldName, messages]) => ({
      name: fieldName,
      errors: Array.isArray(messages)
        ? messages.map(String)
        : [String(messages)],
    }));
    form.setFields(fields);
  };

  const handleProgress = async (vals) => {
    messageApi.open({
      key: "updatable",
      type: "loading",
      content: t("common.messages.loading"),
    });
    dispatch(
      createProjectStepProgress({
        ...vals,
        project_step_id: step.id,
        date: dayjs(vals.date).format("YYYY-MM-DD"),
      })
    )
      .unwrap()
      .then(() => {
        messageApi.open({
          key: "updatable",
          type: "success",
          content: t("common.messages.success"),
          duration: 2,
        });
        forceRefresh(Math.random());
        setAdvanceStageOpen(false);
        form.resetFields();
        setProgressionType(null);
      })
      .catch((error) => {
        handleInputErrors(error);
        messageApi.open({
          key: "updatable",
          type: "error",
          content: t("common.messages.error"),
          duration: 2,
        });
      });
  };

  return (
    <ModalForm
      key={0}
      width={600}
      title={t("project_steps.messages.progression")}
      open={advanceStageOpen}
      onOpenChange={(open) => {
        if (!open) {
          setAdvanceStageOpen(false);
          form.resetFields();
          setProgressionType(null);
        }
      }}
      form={form}
      onFinish={handleProgress}
      className="mt-5"
      layout="vertical"
      onValuesChange={(changedValues) => {
        const fieldName = Object.keys(changedValues)[0];
        if (fieldName === "progressionType") {
          setProgressionType(changedValues.progressionType);
          form.setFields([{ name: "percentage", errors: [] }]);
          form.setFields([{ name: "date", errors: [] }]);
          form.setFields([{ name: "suspension_reason", errors: [] }]);
        }
      }}
    >
      <Row gutter={4}>
        <Col span={24}>
          <Form.Item
            name="type"
            label={t("project_steps.columns.progression_type")}
            rules={[
              {
                required: true,
                message: t("project_steps.messages.progression_type_required"),
              },
            ]}
          >
            <Select
              placeholder={t("project_steps.placeholders.progression_type")}
              onChange={(value) => setProgressionType(value)}
            >
              {availableOptions.map((option) => (
                <Select.Option key={option.value} value={option.value}>
                  {option.label}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Col>

        {/* Si progressionType est advancement, afficher percentage et date */}
        {progressionType === "advancement" && (
          <>
            <Col span={24}>
              <Form.Item
                name="percentage"
                label={t("project_steps.columns.advance_percentage")}
                rules={[
                  {
                    required: true,
                    message: t(
                      "project_steps.messages.advance_percentage_required"
                    ),
                  },
                ]}
              >
                <InputNumber
                  min={0}
                  max={100}
                  placeholder={t("project_steps.placeholders.percentage")}
                  suffix="%"
                  style={{ width: "100%" }}
                />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                name="date"
                label={t("project_steps.columns.advance_date")}
                rules={[
                  {
                    required: true,
                    message: t("project_steps.messages.advance_date_required"),
                  },
                ]}
              >
                <DatePicker
                  style={{ width: "100%" }}
                  format={"DD-MM-YYYY"}
                  allowClear
                />
              </Form.Item>
            </Col>
          </>
        )}

        {/* Si progressionType est suspension, afficher date et suspension_reason */}
        {progressionType === "suspension" && (
          <>
            <Col span={24}>
              <Form.Item
                name="date"
                label={t("project_steps.columns.suspension_date")}
                rules={[
                  {
                    required: true,
                    message: t(
                      "project_steps.messages.suspension_date_required"
                    ),
                  },
                ]}
              >
                <DatePicker
                  style={{ width: "100%" }}
                  format={"DD-MM-YYYY"}
                  allowClear
                />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                name="reason"
                label={t("project_steps.columns.suspension_reason")}
              >
                <TextArea
                  placeholder={t(
                    "project_steps.placeholders.suspension_reason"
                  )}
                  rows={4}
                  style={{ width: "100%" }}
                />
              </Form.Item>
            </Col>
          </>
        )}

        {/* Si progressionType est resumption, afficher date uniquement */}
        {progressionType === "reprise" && (
          <Col span={24}>
            <Form.Item
              name="date"
              label={t("project_steps.columns.resumption_date")}
              rules={[
                {
                  required: true,
                  message: t("project_steps.messages.resumption_date_required"),
                },
              ]}
            >
              <DatePicker
                style={{ width: "100%" }}
                format={"DD-MM-YYYY"}
                allowClear
              />
            </Form.Item>
          </Col>
        )}
      </Row>
    </ModalForm>
  );
};

export default ProgressForm;
